package com.thedasagroup.suminative.ui.orders;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u00000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\u001ab\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\u0012\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00010\t2\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010\t2\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u00010\tH\u0007\u00a8\u0006\u000f"}, d2 = {"ScheduleOrderScreenTopFunction", "", "viewModel", "Lcom/thedasagroup/suminative/ui/orders/OrderScreenViewModel;", "modifier", "Landroidx/compose/ui/Modifier;", "callOrders", "Lkotlin/Function0;", "onPrintBill", "Lkotlin/Function1;", "Landroid/graphics/Bitmap;", "onTrackingUrlClick", "", "onUpdateShowAllOrders", "", "app_stagingGeneralDebug"})
public final class ScheduleOrdersKt {
    
    @androidx.compose.runtime.Composable()
    public static final void ScheduleOrderScreenTopFunction(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.OrderScreenViewModel viewModel, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> callOrders, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super android.graphics.Bitmap, kotlin.Unit> onPrintBill, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onTrackingUrlClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onUpdateShowAllOrders) {
    }
}