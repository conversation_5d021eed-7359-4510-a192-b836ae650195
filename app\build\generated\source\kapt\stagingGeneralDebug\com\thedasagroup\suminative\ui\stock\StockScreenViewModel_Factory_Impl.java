package com.thedasagroup.suminative.ui.stock;

import dagger.internal.DaggerGenerated;
import dagger.internal.InstanceFactory;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class StockScreenViewModel_Factory_Impl implements StockScreenViewModel.Factory {
  private final StockScreenViewModel_Factory delegateFactory;

  StockScreenViewModel_Factory_Impl(StockScreenViewModel_Factory delegateFactory) {
    this.delegateFactory = delegateFactory;
  }

  @Override
  public StockScreenViewModel create(StockScreenState state) {
    return delegateFactory.get(state);
  }

  public static Provider<StockScreenViewModel.Factory> create(
      StockScreenViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new StockScreenViewModel_Factory_Impl(delegateFactory));
  }

  public static dagger.internal.Provider<StockScreenViewModel.Factory> createFactoryProvider(
      StockScreenViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new StockScreenViewModel_Factory_Impl(delegateFactory));
  }
}
