package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.SalesRepository;
import com.thedasagroup.suminative.ui.sales.TotalSalesUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesSalesUseCaseFactory implements Factory<TotalSalesUseCase> {
  private final Provider<SalesRepository> salesRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  public AppUseCaseModule_ProvidesSalesUseCaseFactory(
      Provider<SalesRepository> salesRepositoryProvider, Provider<Prefs> prefsProvider) {
    this.salesRepositoryProvider = salesRepositoryProvider;
    this.prefsProvider = prefsProvider;
  }

  @Override
  public TotalSalesUseCase get() {
    return providesSalesUseCase(salesRepositoryProvider.get(), prefsProvider.get());
  }

  public static AppUseCaseModule_ProvidesSalesUseCaseFactory create(
      Provider<SalesRepository> salesRepositoryProvider, Provider<Prefs> prefsProvider) {
    return new AppUseCaseModule_ProvidesSalesUseCaseFactory(salesRepositoryProvider, prefsProvider);
  }

  public static TotalSalesUseCase providesSalesUseCase(SalesRepository salesRepository,
      Prefs prefs) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesSalesUseCase(salesRepository, prefs));
  }
}
