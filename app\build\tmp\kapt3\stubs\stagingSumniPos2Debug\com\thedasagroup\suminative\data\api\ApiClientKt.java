package com.thedasagroup.suminative.data.api;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000\u0012\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b9\"\u0017\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0002\u0010\u0003\u001a\u0004\b\u0004\u0010\u0005\"\u0014\u0010\u0006\u001a\u00020\u0007X\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\t\"\u0011\u0010\n\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\t\"\u0011\u0010\f\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\t\"\u0011\u0010\u000e\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\t\"\u0011\u0010\u0010\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\t\"\u0011\u0010\u0012\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\t\"\u0011\u0010\u0014\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\t\"\u0011\u0010\u0016\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\t\"\u0011\u0010\u0018\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\t\"\u0011\u0010\u001a\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\t\"\u0011\u0010\u001c\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\t\"\u0011\u0010\u001e\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\t\"\u0011\u0010 \u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\t\"\u0011\u0010\"\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\t\"\u0011\u0010$\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\t\"\u0011\u0010&\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\t\"\u0011\u0010(\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010\t\"\u0011\u0010*\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010\t\"\u0011\u0010,\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010\t\"\u0011\u0010.\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010\t\"\u0011\u00100\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u0010\t\"\u0011\u00102\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010\t\"\u0011\u00104\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u0010\t\"\u0011\u00106\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u0010\t\"\u0011\u00108\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010\t\"\u0011\u0010:\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u0010\t\"\u0011\u0010<\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u0010\t\"\u0011\u0010>\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u0010\t\u00a8\u0006@"}, d2 = {"apiClient", "Lio/ktor/client/HttpClient;", "getApiClient$annotations", "()V", "getApiClient", "()Lio/ktor/client/HttpClient;", "DOMAIN_ONLY", "", "getDOMAIN_ONLY", "()Ljava/lang/String;", "BASE_DOMAIN", "getBASE_DOMAIN", "BASE_URL", "getBASE_URL", "SOCKET", "getSOCKET", "GET_PENDING_ORDERS", "getGET_PENDING_ORDERS", "GET_ALL_ORDERS", "getGET_ALL_ORDERS", "GET_SCHEDULED_ORDERS", "getGET_SCHEDULED_ORDERS", "GET_STOCK_ITEMS", "getGET_STOCK_ITEMS", "EDIT_STOCK", "getEDIT_STOCK", "LOGIN", "getLOGIN", "PLACE_ORDER", "getPLACE_ORDER", "CLOUD_PRINT", "getCLOUD_PRINT", "SALES", "getSALES", "GET_POS_SETTINGS", "getGET_POS_SETTINGS", "GET_SALES_REPORT", "getGET_SALES_REPORT", "GET_CATEGORY_SORTING", "getGET_CATEGORY_SORTING", "GET_STORE_CONFIGURATIONS", "getGET_STORE_CONFIGURATIONS", "UPLOAD_FILES_URL", "getUPLOAD_FILES_URL", "STORE_USER_LOGIN", "getSTORE_USER_LOGIN", "CLOCK_IN_USER_TIME", "getCLOCK_IN_USER_TIME", "CLOCK_OUT_USER_TIME", "getCLOCK_OUT_USER_TIME", "GET_ACTIVE_RESERVATIONS", "getGET_ACTIVE_RESERVATIONS", "GET_ALL_RESERVATIONS", "getGET_ALL_RESERVATIONS", "CREATE_RESERVATION", "getCREATE_RESERVATION", "EDIT_RESERVATION", "getEDIT_RESERVATION", "CANCEL_RESERVATION", "getCANCEL_RESERVATION", "GET_RESERVATION_AREAS", "getGET_RESERVATION_AREAS", "GET_RESERVATION_TABLES", "getGET_RESERVATION_TABLES", "app_stagingSumniPos2Debug"})
public final class ApiClientKt {
    @org.jetbrains.annotations.NotNull()
    private static final io.ktor.client.HttpClient apiClient = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String DOMAIN_ONLY = "dasasplace.com";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String BASE_DOMAIN = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String BASE_URL = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String SOCKET = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String GET_PENDING_ORDERS = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String GET_ALL_ORDERS = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String GET_SCHEDULED_ORDERS = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String GET_STOCK_ITEMS = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String EDIT_STOCK = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String LOGIN = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PLACE_ORDER = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String CLOUD_PRINT = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String SALES = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String GET_POS_SETTINGS = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String GET_SALES_REPORT = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String GET_CATEGORY_SORTING = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String GET_STORE_CONFIGURATIONS = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String UPLOAD_FILES_URL = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String STORE_USER_LOGIN = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String CLOCK_IN_USER_TIME = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String CLOCK_OUT_USER_TIME = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String GET_ACTIVE_RESERVATIONS = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String GET_ALL_RESERVATIONS = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String CREATE_RESERVATION = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String EDIT_RESERVATION = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String CANCEL_RESERVATION = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String GET_RESERVATION_AREAS = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String GET_RESERVATION_TABLES = null;
    
    @org.jetbrains.annotations.NotNull()
    public static final io.ktor.client.HttpClient getApiClient() {
        return null;
    }
    
    @kotlin.OptIn(markerClass = {kotlinx.serialization.ExperimentalSerializationApi.class})
    @java.lang.Deprecated()
    public static void getApiClient$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getDOMAIN_ONLY() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getBASE_DOMAIN() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getBASE_URL() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getSOCKET() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getGET_PENDING_ORDERS() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getGET_ALL_ORDERS() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getGET_SCHEDULED_ORDERS() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getGET_STOCK_ITEMS() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getEDIT_STOCK() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getLOGIN() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getPLACE_ORDER() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getCLOUD_PRINT() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getSALES() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getGET_POS_SETTINGS() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getGET_SALES_REPORT() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getGET_CATEGORY_SORTING() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getGET_STORE_CONFIGURATIONS() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getUPLOAD_FILES_URL() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getSTORE_USER_LOGIN() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getCLOCK_IN_USER_TIME() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getCLOCK_OUT_USER_TIME() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getGET_ACTIVE_RESERVATIONS() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getGET_ALL_RESERVATIONS() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getCREATE_RESERVATION() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getEDIT_RESERVATION() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getCANCEL_RESERVATION() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getGET_RESERVATION_AREAS() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getGET_RESERVATION_TABLES() {
        return null;
    }
}