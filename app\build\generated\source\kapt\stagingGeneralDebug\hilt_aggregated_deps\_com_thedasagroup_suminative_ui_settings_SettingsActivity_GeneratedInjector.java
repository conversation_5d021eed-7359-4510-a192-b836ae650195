package hilt_aggregated_deps;

import dagger.hilt.processor.internal.aggregateddeps.AggregatedDeps;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedDeps(
    components = "dagger.hilt.android.components.ActivityComponent",
    entryPoints = "com.thedasagroup.suminative.ui.settings.SettingsActivity_GeneratedInjector"
)
public class _com_thedasagroup_suminative_ui_settings_SettingsActivity_GeneratedInjector {
}
