package com.thedasagroup.suminative.data.repo;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000t\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\"\u0010\f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\r2\u0006\u0010\u0010\u001a\u00020\u0011H\u0086@\u00a2\u0006\u0002\u0010\u0012J*\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\r2\u0006\u0010\u0014\u001a\u00020\t2\u0006\u0010\u0015\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u0016J\"\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180\u000e0\r2\u0006\u0010\u0010\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0002\u0010\u001aJ\"\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180\u000e0\r2\u0006\u0010\u0010\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0002\u0010\u001aJ\"\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001d0\u000e0\r2\u0006\u0010\u0014\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u001eJ\"\u0010\u001f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001d0\u000e0\r2\u0006\u0010\u0014\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u001eJ*\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020!0\u000e0\r2\u0006\u0010\"\u001a\u00020#2\u0006\u0010$\u001a\u00020#H\u0086@\u00a2\u0006\u0002\u0010%J\"\u0010&\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\'0\u000e0\r2\u0006\u0010(\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u001eJ\"\u0010)\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020*0\u000e0\r2\u0006\u0010\u0010\u001a\u00020+H\u0086@\u00a2\u0006\u0002\u0010,J\"\u0010-\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020*0\u000e0\r2\u0006\u0010.\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u001eJ\"\u0010/\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020*0\u000e0\r2\u0006\u0010.\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u001eJ*\u00100\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001d0\u000e0\r2\u0006\u0010\u0014\u001a\u00020\t2\u0006\u0010\u0010\u001a\u000201H\u0086@\u00a2\u0006\u0002\u00102J\u0006\u00103\u001a\u00020\tJ\u0006\u00104\u001a\u00020\tR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\u00020\tX\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u00065"}, d2 = {"Lcom/thedasagroup/suminative/data/repo/MyGuavaRepository;", "Lcom/thedasagroup/suminative/data/repo/BaseRepository;", "trueTimeImpl", "Lcom/instacart/truetime/time/TrueTimeImpl;", "prefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "<init>", "(Lcom/instacart/truetime/time/TrueTimeImpl;Lcom/thedasagroup/suminative/data/prefs/Prefs;)V", "testApiKey", "", "getTestApiKey", "()Ljava/lang/String;", "createOrder", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/my_guava/orders/create_order/GuavaOrderResponse;", "request", "Lcom/thedasagroup/suminative/data/model/request/my_guava/orders/CreateOrderRequest;", "(Lcom/thedasagroup/suminative/data/model/request/my_guava/orders/CreateOrderRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "voidOrder", "orderId", "amount", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getListOfOrders", "Lcom/thedasagroup/suminative/data/model/response/my_guava/orders/list_of_orders/GetListOfOrdersResponse;", "Lcom/thedasagroup/suminative/data/model/request/my_guava/orders/GetListOfOrdersRequest;", "(Lcom/thedasagroup/suminative/data/model/request/my_guava/orders/GetListOfOrdersRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getListOfOrders2", "getOrderById", "Lcom/thedasagroup/suminative/data/model/response/my_guava/orders/list_of_orders/GuavaOrder;", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cancelOrder", "getTerminalList", "Lcom/thedasagroup/suminative/data/model/response/my_guava/terminals/GetTerminalListResponse;", "size", "", "page", "(IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTerminalById", "Lcom/thedasagroup/suminative/data/model/response/my_guava/terminals/Terminal;", "terminalId", "createSession", "Lcom/thedasagroup/suminative/data/model/response/my_guava/sessions/Session;", "Lcom/thedasagroup/suminative/data/model/request/my_guava/sessions/CreateSessionRequest;", "(Lcom/thedasagroup/suminative/data/model/request/my_guava/sessions/CreateSessionRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cancelSession", "sessionId", "getSessionById", "updateOrderById", "Lcom/thedasagroup/suminative/data/model/response/my_guava/orders/updateorder/UpdateOrderRequest;", "(Ljava/lang/String;Lcom/thedasagroup/suminative/data/model/response/my_guava/orders/updateorder/UpdateOrderRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getApiKey", "getBaseUrl", "app_stagingGeneralDebug"})
public final class MyGuavaRepository extends com.thedasagroup.suminative.data.repo.BaseRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.instacart.truetime.time.TrueTimeImpl trueTimeImpl = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.prefs.Prefs prefs = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String testApiKey = "8SaAyahCRzCs8c3V1qa8wg==.aa54ebef5d778782781d5821c0f9d3ba";
    
    public MyGuavaRepository(@org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl trueTimeImpl, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTestApiKey() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object createOrder(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.my_guava.orders.CreateOrderRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object voidOrder(@org.jetbrains.annotations.NotNull()
    java.lang.String orderId, @org.jetbrains.annotations.NotNull()
    java.lang.String amount, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getListOfOrders(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.my_guava.orders.GetListOfOrdersRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GetListOfOrdersResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getListOfOrders2(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.my_guava.orders.GetListOfOrdersRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GetListOfOrdersResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getOrderById(@org.jetbrains.annotations.NotNull()
    java.lang.String orderId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GuavaOrder>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object cancelOrder(@org.jetbrains.annotations.NotNull()
    java.lang.String orderId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GuavaOrder>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTerminalList(int size, int page, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.terminals.GetTerminalListResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTerminalById(@org.jetbrains.annotations.NotNull()
    java.lang.String terminalId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.terminals.Terminal>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object createSession(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.my_guava.sessions.CreateSessionRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object cancelSession(@org.jetbrains.annotations.NotNull()
    java.lang.String sessionId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getSessionById(@org.jetbrains.annotations.NotNull()
    java.lang.String sessionId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateOrderById(@org.jetbrains.annotations.NotNull()
    java.lang.String orderId, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.my_guava.orders.updateorder.UpdateOrderRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GuavaOrder>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getApiKey() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBaseUrl() {
        return null;
    }
}