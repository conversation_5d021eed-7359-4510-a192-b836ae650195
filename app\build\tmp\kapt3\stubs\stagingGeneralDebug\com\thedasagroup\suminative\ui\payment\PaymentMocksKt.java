package com.thedasagroup.suminative.ui.payment;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000\u0010\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0001\n\u0000\u001a\u0016\u0010\u0000\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001*\u00020\u0002\u00a8\u0006\u0004"}, d2 = {"mocks", "Lcom/airbnb/mvrx/mocking/MockBuilder;", "Lcom/thedasagroup/suminative/ui/payment/PaymentFragment;", "", "app_stagingGeneralDebug"})
public final class PaymentMocksKt {
    
    @org.jetbrains.annotations.NotNull()
    public static final com.airbnb.mvrx.mocking.MockBuilder mocks(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.payment.PaymentFragment $this$mocks) {
        return null;
    }
}