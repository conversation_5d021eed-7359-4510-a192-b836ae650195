package com.thedasagroup.suminative.ui.guava_orders;

import com.thedasagroup.suminative.data.repo.MyGuavaRepository;
import com.thedasagroup.suminative.domain.myguava.MyGuavaGetOrdersUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class GuavaOrdersViewModel_Factory {
  private final Provider<MyGuavaGetOrdersUseCase> myGuavaGetOrdersUseCaseProvider;

  private final Provider<MyGuavaRepository> myGuavaRepositoryProvider;

  public GuavaOrdersViewModel_Factory(
      Provider<MyGuavaGetOrdersUseCase> myGuavaGetOrdersUseCaseProvider,
      Provider<MyGuavaRepository> myGuavaRepositoryProvider) {
    this.myGuavaGetOrdersUseCaseProvider = myGuavaGetOrdersUseCaseProvider;
    this.myGuavaRepositoryProvider = myGuavaRepositoryProvider;
  }

  public GuavaOrdersViewModel get(GuavaOrdersState initialState) {
    return newInstance(initialState, myGuavaGetOrdersUseCaseProvider.get(), myGuavaRepositoryProvider.get());
  }

  public static GuavaOrdersViewModel_Factory create(
      Provider<MyGuavaGetOrdersUseCase> myGuavaGetOrdersUseCaseProvider,
      Provider<MyGuavaRepository> myGuavaRepositoryProvider) {
    return new GuavaOrdersViewModel_Factory(myGuavaGetOrdersUseCaseProvider, myGuavaRepositoryProvider);
  }

  public static GuavaOrdersViewModel newInstance(GuavaOrdersState initialState,
      MyGuavaGetOrdersUseCase myGuavaGetOrdersUseCase, MyGuavaRepository myGuavaRepository) {
    return new GuavaOrdersViewModel(initialState, myGuavaGetOrdersUseCase, myGuavaRepository);
  }
}
