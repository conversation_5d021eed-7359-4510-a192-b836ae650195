package com.thedasagroup.suminative.ui.products;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000p\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\b\b\u0016\u0018\u00002\u00020\u0001B/\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0004\b\f\u0010\rJ\"\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u00100\u000f2\u0006\u0010\u0012\u001a\u00020\u0013H\u0086B\u00a2\u0006\u0002\u0010\u0014J \u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J\u001a\u0010\u001b\u001a\u0004\u0018\u00010\u00182\u000e\u0010\u001c\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u001dH\u0002J\u001e\u0010\u001e\u001a\u00020\u00112\u0006\u0010\u001f\u001a\u00020\u00162\u000e\b\u0002\u0010 \u001a\b\u0012\u0004\u0012\u00020!0\u001dJ \u0010\"\u001a\u00020#2\u0006\u0010\u001f\u001a\u00020\u00162\u000e\b\u0002\u0010 \u001a\b\u0012\u0004\u0012\u00020!0\u001dH\u0002J\u0010\u0010$\u001a\u00020%2\u0006\u0010&\u001a\u00020\u0018H\u0002J\u0012\u0010\'\u001a\u00020%2\b\u0010(\u001a\u0004\u0018\u00010\u0018H\u0002J\u0010\u0010)\u001a\u00020%2\u0006\u0010*\u001a\u00020\u0018H\u0002J\u0018\u0010+\u001a\u0004\u0018\u00010\u00182\f\u0010,\u001a\b\u0012\u0004\u0012\u00020!0\u001dH\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006-"}, d2 = {"Lcom/thedasagroup/suminative/ui/products/OrderUseCase;", "", "stockRepository", "Lcom/thedasagroup/suminative/data/repo/StockRepository;", "guavaRepository", "Lcom/thedasagroup/suminative/data/repo/MyGuavaRepository;", "localOrderRepository", "Lcom/thedasagroup/suminative/data/database/LocalOrderRepository;", "prefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "trueTimeImpl", "Lcom/instacart/truetime/time/TrueTimeImpl;", "<init>", "(Lcom/thedasagroup/suminative/data/repo/StockRepository;Lcom/thedasagroup/suminative/data/repo/MyGuavaRepository;Lcom/thedasagroup/suminative/data/database/LocalOrderRepository;Lcom/thedasagroup/suminative/data/prefs/Prefs;Lcom/instacart/truetime/time/TrueTimeImpl;)V", "invoke", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/order/OrderResponse2;", "order", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "(Lcom/thedasagroup/suminative/data/model/request/order/Order;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "convertToLocalOrder", "Lcom/thedasagroup/suminative/data/database/LocalOrder;", "orderId", "", "timestamp", "", "buildModifiersJson", "optionSets", "", "convertLocalOrderToOrderResponse", "localOrder", "localOrderItems", "Lcom/thedasagroup/suminative/data/database/LocalOrderItem;", "convertLocalOrderToStoreOrder", "Lcom/thedasagroup/suminative/data/model/response/store_orders/Order2;", "convertOrderTypeToDeliveryType", "", "orderType", "convertPaymentMethodToPaymentType", "paymentMethod", "convertStatusStringToInt", "status", "buildCartJsonFromOrderItems", "orderItems", "app_stagingGeneralDebug"})
public class OrderUseCase {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.repo.StockRepository stockRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.repo.MyGuavaRepository guavaRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.database.LocalOrderRepository localOrderRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.prefs.Prefs prefs = null;
    @org.jetbrains.annotations.NotNull()
    private final com.instacart.truetime.time.TrueTimeImpl trueTimeImpl = null;
    
    public OrderUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.StockRepository stockRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.MyGuavaRepository guavaRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.database.LocalOrderRepository localOrderRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs, @org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl trueTimeImpl) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object invoke(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.order.OrderResponse2>>> $completion) {
        return null;
    }
    
    private final com.thedasagroup.suminative.data.database.LocalOrder convertToLocalOrder(com.thedasagroup.suminative.data.model.request.order.Order order, java.lang.String orderId, long timestamp) {
        return null;
    }
    
    private final java.lang.String buildModifiersJson(java.util.List<? extends java.lang.Object> optionSets) {
        return null;
    }
    
    /**
     * Converts a LocalOrder from the local database to an OrderResponse
     * This is useful for displaying local orders in the same format as server responses
     */
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.model.response.order.OrderResponse2 convertLocalOrderToOrderResponse(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.database.LocalOrder localOrder, @org.jetbrains.annotations.NotNull()
    java.util.List<com.thedasagroup.suminative.data.database.LocalOrderItem> localOrderItems) {
        return null;
    }
    
    /**
     * Converts a LocalOrder to the Order model used in responses
     */
    private final com.thedasagroup.suminative.data.model.response.store_orders.Order2 convertLocalOrderToStoreOrder(com.thedasagroup.suminative.data.database.LocalOrder localOrder, java.util.List<com.thedasagroup.suminative.data.database.LocalOrderItem> localOrderItems) {
        return null;
    }
    
    /**
     * Converts order type string to delivery type integer
     */
    private final int convertOrderTypeToDeliveryType(java.lang.String orderType) {
        return 0;
    }
    
    /**
     * Converts payment method string to payment type integer
     */
    private final int convertPaymentMethodToPaymentType(java.lang.String paymentMethod) {
        return 0;
    }
    
    /**
     * Converts status string to status integer
     */
    private final int convertStatusStringToInt(java.lang.String status) {
        return 0;
    }
    
    /**
     * Builds a cart JSON string from order items
     * This is a simplified implementation - you may need to enhance based on your Cart model
     */
    private final java.lang.String buildCartJsonFromOrderItems(java.util.List<com.thedasagroup.suminative.data.database.LocalOrderItem> orderItems) {
        return null;
    }
}