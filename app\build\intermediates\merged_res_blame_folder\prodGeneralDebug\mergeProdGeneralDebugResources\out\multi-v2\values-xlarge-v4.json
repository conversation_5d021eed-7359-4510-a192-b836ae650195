{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-xlarge-v4/values-xlarge-v4.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-xlarge-v4\\values-xlarge-v4.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,126,197,267,337,405", "endColumns": "70,70,69,69,67,67", "endOffsets": "121,192,262,332,400,468"}, "to": {"startLines": "3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4", "startOffsets": "93,164,235,305,375,443", "endColumns": "70,70,69,69,67,67", "endOffsets": "159,230,300,370,438,506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-xlarge-v4\\values-xlarge-v4.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "37", "endOffsets": "88"}}]}]}