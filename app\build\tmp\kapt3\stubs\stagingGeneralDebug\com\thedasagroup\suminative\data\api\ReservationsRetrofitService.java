package com.thedasagroup.suminative.data.api;

/**
 * Retrofit service interface for Reservations API endpoints
 *
 * This service provides methods to interact with the reservations API using Retrofit.
 * The API returns direct arrays of reservations, not wrapped in response objects.
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J8\u0010\u0002\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u00032\b\b\u0001\u0010\u0006\u001a\u00020\u00072\b\b\u0001\u0010\b\u001a\u00020\t2\b\b\u0001\u0010\n\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\u000bJ.\u0010\f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u00032\b\b\u0001\u0010\u0006\u001a\u00020\u00072\b\b\u0001\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\rJ$\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u00040\u00032\b\b\u0001\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\u0010J$\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00120\u00040\u00032\b\b\u0001\u0010\u0013\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\u0010\u00a8\u0006\u0014"}, d2 = {"Lcom/thedasagroup/suminative/data/api/ReservationsRetrofitService;", "", "getActiveReservations", "Lretrofit2/Response;", "", "Lcom/thedasagroup/suminative/data/model/response/reservations/Reservation;", "storeId", "", "currentTime", "", "timezoneOffset", "(ILjava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllReservations", "(ILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getReservationAreas", "Lcom/thedasagroup/suminative/data/model/response/reservations/Area;", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getReservationTables", "Lcom/thedasagroup/suminative/data/model/response/reservations/Table;", "areaId", "app_stagingGeneralDebug"})
public abstract interface ReservationsRetrofitService {
    
    /**
     * Get currently active reservations (next 45 minutes)
     *
     * @param storeId The store ID to filter reservations
     * @param currentTime Current time in ISO format (e.g., "2025-07-15T18:00:00")
     * @return Response containing a list of active reservations
     */
    @retrofit2.http.GET(value = "BackendDASA-1.0.0/api/reservations/active")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getActiveReservations(@retrofit2.http.Query(value = "storeId")
    int storeId, @retrofit2.http.Query(value = "currentTime")
    @org.jetbrains.annotations.NotNull()
    java.lang.String currentTime, @retrofit2.http.Query(value = "timezoneOffset")
    int timezoneOffset, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<java.util.List<com.thedasagroup.suminative.data.model.response.reservations.Reservation>>> $completion);
    
    /**
     * Get entire reservation history
     *
     * @param storeId The store ID to filter reservations
     * @param currentTime Current time in ISO format (e.g., "2025-07-15T18:00:00")
     * @return Response containing a list of all reservations
     */
    @retrofit2.http.GET(value = "BackendDASA-1.0.0/api/reservations/all")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllReservations(@retrofit2.http.Query(value = "storeId")
    int storeId, @retrofit2.http.Query(value = "currentTime")
    @org.jetbrains.annotations.NotNull()
    java.lang.String currentTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<java.util.List<com.thedasagroup.suminative.data.model.response.reservations.Reservation>>> $completion);
    
    /**
     * Get reservation areas for a store
     *
     * @param storeId The store ID to filter areas
     * @return Response containing a list of areas
     */
    @retrofit2.http.GET(value = "BackendDASA-1.0.0/api/reservations/areas")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getReservationAreas(@retrofit2.http.Query(value = "storeId")
    int storeId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<java.util.List<com.thedasagroup.suminative.data.model.response.reservations.Area>>> $completion);
    
    /**
     * Get tables for a specific area
     *
     * @param areaId The area ID to filter tables
     * @return Response containing a list of tables
     */
    @retrofit2.http.GET(value = "BackendDASA-1.0.0/api/reservations/tables")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getReservationTables(@retrofit2.http.Query(value = "areaId")
    int areaId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<java.util.List<com.thedasagroup.suminative.data.model.response.reservations.Table>>> $completion);
}