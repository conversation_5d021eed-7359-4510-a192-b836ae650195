package com.thedasagroup.suminative.domain.categories;

import com.thedasagroup.suminative.data.database.CategoryRepository;
import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.StockRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class SyncCategoriesUseCase_Factory implements Factory<SyncCategoriesUseCase> {
  private final Provider<StockRepository> stockRepositoryProvider;

  private final Provider<CategoryRepository> categoryRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  public SyncCategoriesUseCase_Factory(Provider<StockRepository> stockRepositoryProvider,
      Provider<CategoryRepository> categoryRepositoryProvider, Provider<Prefs> prefsProvider) {
    this.stockRepositoryProvider = stockRepositoryProvider;
    this.categoryRepositoryProvider = categoryRepositoryProvider;
    this.prefsProvider = prefsProvider;
  }

  @Override
  public SyncCategoriesUseCase get() {
    return newInstance(stockRepositoryProvider.get(), categoryRepositoryProvider.get(), prefsProvider.get());
  }

  public static SyncCategoriesUseCase_Factory create(
      Provider<StockRepository> stockRepositoryProvider,
      Provider<CategoryRepository> categoryRepositoryProvider, Provider<Prefs> prefsProvider) {
    return new SyncCategoriesUseCase_Factory(stockRepositoryProvider, categoryRepositoryProvider, prefsProvider);
  }

  public static SyncCategoriesUseCase newInstance(StockRepository stockRepository,
      CategoryRepository categoryRepository, Prefs prefs) {
    return new SyncCategoriesUseCase(stockRepository, categoryRepository, prefs);
  }
}
