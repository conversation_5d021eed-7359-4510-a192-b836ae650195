package com.thedasagroup.suminative.ui.print;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000Z\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u0018\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\b\u0010\u0006\u001a\u00020\u0001H\u0007\u001a,\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\u000eH\u0007\u001a,\u0010\u000f\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\u000eH\u0007\u001a,\u0010\u0010\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\u000eH\u0007\u001a,\u0010\u0011\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\u000eH\u0007\u001a,\u0010\u0012\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\u000eH\u0007\u001a\f\u0010\u0013\u001a\u00020\t*\u00020\u0014H\u0007\u001aA\u0010\u0015\u001a\u00020\u0016*\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00182\b\b\u0002\u0010\u0019\u001a\u00020\u001a2\b\b\u0002\u0010\u001b\u001a\u00020\u001a2\b\b\u0002\u0010\u001c\u001a\u00020\u001d2\b\b\u0002\u0010\u001e\u001a\u00020\u001a\u00a2\u0006\u0004\b\u001f\u0010 \u001a#\u0010!\u001a\u00020\u00012\u0011\u0010\"\u001a\r\u0012\u0004\u0012\u00020\u00010#\u00a2\u0006\u0002\b$2\u0006\u0010%\u001a\u00020\u0016H\u0007\u00a8\u0006&"}, d2 = {"PrintingBill2", "", "orderItem", "Lcom/thedasagroup/suminative/data/model/request/pagination/OrderItem2;", "prefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "MyTextDivider", "Total", "title", "", "value", "style", "Landroidx/compose/ui/text/TextStyle;", "isBold", "", "Category", "TotalCart", "DateComposable", "OrderNotes", "formatOrderId", "", "verticalScrollbar", "Landroidx/compose/ui/Modifier;", "scrollState", "Landroidx/compose/foundation/ScrollState;", "scrollBarWidth", "Landroidx/compose/ui/unit/Dp;", "minScrollBarHeight", "scrollBarColor", "Landroidx/compose/ui/graphics/Color;", "cornerRadius", "verticalScrollbar-i1HVrS4", "(Landroidx/compose/ui/Modifier;Landroidx/compose/foundation/ScrollState;FFJF)Landroidx/compose/ui/Modifier;", "DrawScrollableView", "content", "Lkotlin/Function0;", "Landroidx/compose/runtime/Composable;", "modifier", "app_stagingGeneralDebug"})
public final class PrintingBillKt {
    
    @androidx.compose.runtime.Composable()
    public static final void PrintingBill2(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem2 orderItem, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MyTextDivider() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void Total(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String value, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.text.TextStyle style, boolean isBold) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void Category(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String value, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.text.TextStyle style, boolean isBold) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TotalCart(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String value, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.text.TextStyle style, boolean isBold) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DateComposable(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String value, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.text.TextStyle style, boolean isBold) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void OrderNotes(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String value, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.text.TextStyle style, boolean isBold) {
    }
    
    @android.annotation.SuppressLint(value = {"DefaultLocale"})
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String formatOrderId(int $this$formatOrderId) {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DrawScrollableView(@org.jetbrains.annotations.NotNull()
    androidx.compose.runtime.internal.ComposableFunction0<kotlin.Unit> content, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}