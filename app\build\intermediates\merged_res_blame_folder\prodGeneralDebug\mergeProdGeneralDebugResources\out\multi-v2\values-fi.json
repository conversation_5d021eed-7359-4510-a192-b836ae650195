{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,221,334,410,489,589,725,903,1041,1146,1256,1373,1433,1494,1585,1666,1733,1869,1968,2054,2192,2268,2365,2473,2620,2721,2836,2941,3013,3087,3206,3285,3489,3651,3758,3921,4020,4215,4319,4456,4553,4704,4810,4949,5046,5142,5291,5390,5534,5637,5801,5884,6013,6090,6216,6312,6388,6477,6552,6622,6697,6767,6887,6985,7129,7230,7350,7447,7586,7683,7779,7902,7993,8107,8212,8322,8431,8492,8545,8599,8651,8701,8752,8818,8872,8935,8987,9031,9095,9139,9196,9248,9308,9368,9416,9466,9542,9591,9643,9691,9756,9815,9878,10022,10129,10207,10268,10351,10420,10488,10599,10672,10746,10826,10904,10968,11067,11141,11235,11319,11445,11526,11618,11700,11797,11901,11978,12083,12183,12281,12385,12464,12566,12636,12716,12821,12943,13008,13085,13166,13251,15302,15417,15589,15635,15765,15916,15999,16119,16203,16349,16501,16635,16805,16885,16961,17031,17131,17241,17285,17368,17497,17601,17728,17860,17997,18107,18220,18287,18346,18403,18458,18525,18603,18700,18811,18879,18961,19206,19415,19536,19653,19780,19872,19977,20114,20258,20407,20499,20607,20692,20814,20912,21058,21151,21231,21286,21516,21601,21680,21747,21813,21918,22029,22124,22274,22437,22516,22584,22647,22819,22894,22986,23058,23147,23315,23413,23501,23608,23693,23817,23915,23970,24079,24152,24227,24305,24377,24425,24479,24556,24608,24676,24812,24917,25008,25073,25224,25359,25461,25574,25810,25900,26001,26104,26251,26476,26571,26757,26877,26998,27103,27190,27282,27360,27498,27653,27780,27828,27907,28042,28148,28229,28305,28406,28487,28559,28627,28690,28770,28844,28923,29020,29088,29158,29242,29312,29391,29559,29700,29807,29862,29940,30084,30151,30235,30316,30395,30476,30692,30759,30834,30920,30991,31087,31252,31334,31403,31493,31573,31641,31723,31808,31936,32023,32071,32148,32225,32299,32382,32492,32603,32726,32859,32940,32990,33040,33129,33225,33312,33412,33582,33677,33756,33868,33986,34122,34226,34331,34499,34624,34826,34987,35110,35266,35355,35449,35575,35696,35788,35908,35978,36340,36420,36506,36583", "endColumns": "165,112,75,78,99,135,177,137,104,109,116,59,60,90,80,66,135,98,85,137,75,96,107,146,100,114,104,71,73,118,78,203,161,106,162,98,194,103,136,96,150,105,138,96,95,148,98,143,102,163,82,128,76,125,95,75,88,74,69,74,69,119,97,143,100,119,96,138,96,95,122,90,113,104,109,108,60,52,53,51,49,50,65,53,62,51,43,63,43,56,51,59,59,47,49,75,48,51,47,64,58,62,143,106,77,60,82,68,67,110,72,73,79,77,63,98,73,93,83,125,80,91,81,96,103,76,104,99,97,103,78,101,69,79,104,121,64,76,80,84,2050,114,171,45,129,150,82,119,83,145,151,133,169,79,75,69,99,109,43,82,128,103,126,131,136,109,112,66,58,56,54,66,77,96,110,67,81,244,208,120,116,126,91,104,136,143,148,91,107,84,121,97,145,92,79,54,229,84,78,66,65,104,110,94,149,162,78,67,62,171,74,91,71,88,167,97,87,106,84,123,97,54,108,72,74,77,71,47,53,76,51,67,135,104,90,64,150,134,101,112,235,89,100,102,146,224,94,185,119,120,104,86,91,77,137,154,126,47,78,134,105,80,75,100,80,71,67,62,79,73,78,96,67,69,83,69,78,167,140,106,54,77,143,66,83,80,78,80,215,66,74,85,70,95,164,81,68,89,79,67,81,84,127,86,47,76,76,73,82,109,110,122,132,80,49,49,88,95,86,99,169,94,78,111,117,135,103,104,167,124,201,160,122,155,88,93,125,120,91,119,69,361,79,85,76,75", "endOffsets": "216,329,405,484,584,720,898,1036,1141,1251,1368,1428,1489,1580,1661,1728,1864,1963,2049,2187,2263,2360,2468,2615,2716,2831,2936,3008,3082,3201,3280,3484,3646,3753,3916,4015,4210,4314,4451,4548,4699,4805,4944,5041,5137,5286,5385,5529,5632,5796,5879,6008,6085,6211,6307,6383,6472,6547,6617,6692,6762,6882,6980,7124,7225,7345,7442,7581,7678,7774,7897,7988,8102,8207,8317,8426,8487,8540,8594,8646,8696,8747,8813,8867,8930,8982,9026,9090,9134,9191,9243,9303,9363,9411,9461,9537,9586,9638,9686,9751,9810,9873,10017,10124,10202,10263,10346,10415,10483,10594,10667,10741,10821,10899,10963,11062,11136,11230,11314,11440,11521,11613,11695,11792,11896,11973,12078,12178,12276,12380,12459,12561,12631,12711,12816,12938,13003,13080,13161,13246,15297,15412,15584,15630,15760,15911,15994,16114,16198,16344,16496,16630,16800,16880,16956,17026,17126,17236,17280,17363,17492,17596,17723,17855,17992,18102,18215,18282,18341,18398,18453,18520,18598,18695,18806,18874,18956,19201,19410,19531,19648,19775,19867,19972,20109,20253,20402,20494,20602,20687,20809,20907,21053,21146,21226,21281,21511,21596,21675,21742,21808,21913,22024,22119,22269,22432,22511,22579,22642,22814,22889,22981,23053,23142,23310,23408,23496,23603,23688,23812,23910,23965,24074,24147,24222,24300,24372,24420,24474,24551,24603,24671,24807,24912,25003,25068,25219,25354,25456,25569,25805,25895,25996,26099,26246,26471,26566,26752,26872,26993,27098,27185,27277,27355,27493,27648,27775,27823,27902,28037,28143,28224,28300,28401,28482,28554,28622,28685,28765,28839,28918,29015,29083,29153,29237,29307,29386,29554,29695,29802,29857,29935,30079,30146,30230,30311,30390,30471,30687,30754,30829,30915,30986,31082,31247,31329,31398,31488,31568,31636,31718,31803,31931,32018,32066,32143,32220,32294,32377,32487,32598,32721,32854,32935,32985,33035,33124,33220,33307,33407,33577,33672,33751,33863,33981,34117,34221,34326,34494,34619,34821,34982,35105,35261,35350,35444,35570,35691,35783,35903,35973,36335,36415,36501,36578,36654"}, "to": {"startLines": "193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17835,18001,18114,18190,18269,18369,18505,18683,18821,18926,19036,19153,19213,19274,19365,19446,19513,19649,19748,19834,19972,20048,20145,20253,20400,20501,20616,20721,20793,20867,20986,21065,21269,21431,21538,21701,21800,21995,22099,22236,22333,22484,22590,22729,22826,22922,23071,23170,23314,23417,23581,23664,23793,23870,23996,24092,24168,24257,24332,24402,24477,24547,24667,24765,24909,25010,25130,25227,25366,25463,25559,25682,25773,25887,25992,26102,26211,26272,26325,26379,26431,26481,26532,26598,26652,26715,26767,26811,26875,26919,26976,27028,27088,27148,27196,27246,27322,27371,27423,27471,27536,27595,27658,27802,27909,27987,28048,28131,28200,28268,28379,28452,28526,28606,28684,28748,28847,28921,29015,29099,29225,29306,29398,29480,29577,29681,29758,29863,29963,30061,30165,30244,30346,30416,30496,30601,30723,30788,30865,30946,31031,33082,33197,33369,33415,33545,33696,33779,33899,33983,34129,34281,34415,34585,34665,34741,34811,34911,35021,35065,35148,35277,35381,35508,35640,35777,35887,36000,36067,36126,36183,36238,36305,36383,36480,36591,36659,36741,36986,37195,37316,37433,37560,37652,37757,37894,38038,38187,38279,38387,38472,38594,38692,38838,38931,39011,39066,39296,39381,39460,39527,39593,39698,39809,39904,40054,40217,40296,40364,40427,40599,40674,40766,40838,40927,41095,41193,41281,41388,41473,41597,41695,41750,41859,41932,42007,42085,42157,42205,42259,42336,42388,42456,42592,42697,42788,42853,43004,43139,43241,43354,43590,43680,43781,43884,44031,44256,44351,44537,44657,44778,44883,44970,45062,45140,45278,45433,45560,45608,45687,45822,45928,46009,46085,46186,46267,46339,46407,46470,46550,46624,46703,46800,46868,46938,47022,47092,47171,47339,47480,47587,47642,47720,47864,47931,48015,48096,48175,48256,48472,48539,48614,48700,48771,48867,49032,49114,49183,49273,49353,49421,49503,49588,49716,49803,49851,49928,50005,50079,50162,50272,50383,50506,50639,50720,50770,50820,50909,51005,51092,51192,51362,51457,51536,51648,51766,51902,52006,52111,52279,52404,52606,52767,52890,53046,53135,53229,53355,53476,53568,53688,53758,54120,54200,54286,54363", "endColumns": "165,112,75,78,99,135,177,137,104,109,116,59,60,90,80,66,135,98,85,137,75,96,107,146,100,114,104,71,73,118,78,203,161,106,162,98,194,103,136,96,150,105,138,96,95,148,98,143,102,163,82,128,76,125,95,75,88,74,69,74,69,119,97,143,100,119,96,138,96,95,122,90,113,104,109,108,60,52,53,51,49,50,65,53,62,51,43,63,43,56,51,59,59,47,49,75,48,51,47,64,58,62,143,106,77,60,82,68,67,110,72,73,79,77,63,98,73,93,83,125,80,91,81,96,103,76,104,99,97,103,78,101,69,79,104,121,64,76,80,84,2050,114,171,45,129,150,82,119,83,145,151,133,169,79,75,69,99,109,43,82,128,103,126,131,136,109,112,66,58,56,54,66,77,96,110,67,81,244,208,120,116,126,91,104,136,143,148,91,107,84,121,97,145,92,79,54,229,84,78,66,65,104,110,94,149,162,78,67,62,171,74,91,71,88,167,97,87,106,84,123,97,54,108,72,74,77,71,47,53,76,51,67,135,104,90,64,150,134,101,112,235,89,100,102,146,224,94,185,119,120,104,86,91,77,137,154,126,47,78,134,105,80,75,100,80,71,67,62,79,73,78,96,67,69,83,69,78,167,140,106,54,77,143,66,83,80,78,80,215,66,74,85,70,95,164,81,68,89,79,67,81,84,127,86,47,76,76,73,82,109,110,122,132,80,49,49,88,95,86,99,169,94,78,111,117,135,103,104,167,124,201,160,122,155,88,93,125,120,91,119,69,361,79,85,76,75", "endOffsets": "17996,18109,18185,18264,18364,18500,18678,18816,18921,19031,19148,19208,19269,19360,19441,19508,19644,19743,19829,19967,20043,20140,20248,20395,20496,20611,20716,20788,20862,20981,21060,21264,21426,21533,21696,21795,21990,22094,22231,22328,22479,22585,22724,22821,22917,23066,23165,23309,23412,23576,23659,23788,23865,23991,24087,24163,24252,24327,24397,24472,24542,24662,24760,24904,25005,25125,25222,25361,25458,25554,25677,25768,25882,25987,26097,26206,26267,26320,26374,26426,26476,26527,26593,26647,26710,26762,26806,26870,26914,26971,27023,27083,27143,27191,27241,27317,27366,27418,27466,27531,27590,27653,27797,27904,27982,28043,28126,28195,28263,28374,28447,28521,28601,28679,28743,28842,28916,29010,29094,29220,29301,29393,29475,29572,29676,29753,29858,29958,30056,30160,30239,30341,30411,30491,30596,30718,30783,30860,30941,31026,33077,33192,33364,33410,33540,33691,33774,33894,33978,34124,34276,34410,34580,34660,34736,34806,34906,35016,35060,35143,35272,35376,35503,35635,35772,35882,35995,36062,36121,36178,36233,36300,36378,36475,36586,36654,36736,36981,37190,37311,37428,37555,37647,37752,37889,38033,38182,38274,38382,38467,38589,38687,38833,38926,39006,39061,39291,39376,39455,39522,39588,39693,39804,39899,40049,40212,40291,40359,40422,40594,40669,40761,40833,40922,41090,41188,41276,41383,41468,41592,41690,41745,41854,41927,42002,42080,42152,42200,42254,42331,42383,42451,42587,42692,42783,42848,42999,43134,43236,43349,43585,43675,43776,43879,44026,44251,44346,44532,44652,44773,44878,44965,45057,45135,45273,45428,45555,45603,45682,45817,45923,46004,46080,46181,46262,46334,46402,46465,46545,46619,46698,46795,46863,46933,47017,47087,47166,47334,47475,47582,47637,47715,47859,47926,48010,48091,48170,48251,48467,48534,48609,48695,48766,48862,49027,49109,49178,49268,49348,49416,49498,49583,49711,49798,49846,49923,50000,50074,50157,50267,50378,50501,50634,50715,50765,50815,50904,51000,51087,51187,51357,51452,51531,51643,51761,51897,52001,52106,52274,52399,52601,52762,52885,53041,53130,53224,53350,53471,53563,53683,53753,54115,54195,54281,54358,54434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,426,526,635,721,826,944,1030,1109,1200,1293,1388,1482,1576,1669,1765,1864,1955,2049,2129,2236,2337,2434,2540,2640,2738,2888,17111", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "421,521,630,716,821,939,1025,1104,1195,1288,1383,1477,1571,1664,1760,1859,1950,2044,2124,2231,2332,2429,2535,2635,2733,2883,2983,17187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,284,383,484,573,650,743,834,916,997,1079,1151,1227,1307,1381,1458,1530", "endColumns": "93,84,98,100,88,76,92,90,81,80,81,71,75,79,73,76,71,121", "endOffsets": "194,279,378,479,568,645,738,829,911,992,1074,1146,1222,1302,1376,1453,1525,1647"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,539,540,541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4414,4508,4846,4945,5046,5739,5816,16696,16787,16948,17029,17357,17504,17580,17660,54439,54516,54588", "endColumns": "93,84,98,100,88,76,92,90,81,80,81,71,75,79,73,76,71,121", "endOffsets": "4503,4588,4940,5041,5130,5811,5904,16782,16864,17024,17106,17424,17575,17655,17729,54511,54583,54705"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3392,3488,3590,3688,3793,3898,4010,17734", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3483,3585,3683,3788,3893,4005,4121,17830"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,89", "endOffsets": "140,230"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "54710,54800", "endColumns": "89,89", "endOffsets": "54795,54885"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,368", "endColumns": "102,100,108,98", "endOffsets": "153,254,363,462"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4743,5355,5456,5565", "endColumns": "102,100,108,98", "endOffsets": "4841,5451,5560,5659"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-fi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4593", "endColumns": "149", "endOffsets": "4738"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,393,507,604,705,823,960,1082,1234,1324,1420,1518,1620,1738,1861,1962,2094,2226,2355,2522,2644,2768,2895,3017,3116,3215,3336,3457,3560,3671,3779,3918,4062,4170,4276,4359,4457,4554,4638,4723,4823,4903,4988,5085,5188,5285,5390,5480,5588,5691,5801,5919,5999,6104", "endColumns": "115,112,108,113,96,100,117,136,121,151,89,95,97,101,117,122,100,131,131,128,166,121,123,126,121,98,98,120,120,102,110,107,138,143,107,105,82,97,96,83,84,99,79,84,96,102,96,104,89,107,102,109,117,79,104,98", "endOffsets": "166,279,388,502,599,700,818,955,1077,1229,1319,1415,1513,1615,1733,1856,1957,2089,2221,2350,2517,2639,2763,2890,3012,3111,3210,3331,3452,3555,3666,3774,3913,4057,4165,4271,4354,4452,4549,4633,4718,4818,4898,4983,5080,5183,5280,5385,5475,5583,5686,5796,5914,5994,6099,6198"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5974,6090,6203,6312,6426,6523,6624,6742,6879,7001,7153,7243,7339,7437,7539,7657,7780,7881,8013,8145,8274,8441,8563,8687,8814,8936,9035,9134,9255,9376,9479,9590,9698,9837,9981,10089,10195,10278,10376,10473,10557,10642,10742,10822,10907,11004,11107,11204,11309,11399,11507,11610,11720,11838,11918,12023", "endColumns": "115,112,108,113,96,100,117,136,121,151,89,95,97,101,117,122,100,131,131,128,166,121,123,126,121,98,98,120,120,102,110,107,138,143,107,105,82,97,96,83,84,99,79,84,96,102,96,104,89,107,102,109,117,79,104,98", "endOffsets": "6085,6198,6307,6421,6518,6619,6737,6874,6996,7148,7238,7334,7432,7534,7652,7775,7876,8008,8140,8269,8436,8558,8682,8809,8931,9030,9129,9250,9371,9474,9585,9693,9832,9976,10084,10190,10273,10371,10468,10552,10637,10737,10817,10902,10999,11102,11199,11304,11394,11502,11605,11715,11833,11913,12018,12117"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,344,418,501,590,672,768,876,960,1022,1087,1180,1255,1320,1408,1473,1539,1597,1668,1734,1788,1898,1958,2022,2076,2149,2265,2349,2425,2516,2597,2678,2811,2896,2981,3114,3204,3278,3330,3381,3447,3524,3606,3677,3751,3825,3904,3981,4053,4160,4249,4325,4416,4511,4585,4658,4752,4806,4880,4952,5038,5124,5186,5250,5313,5384,5485,5588,5683,5783,5839,5894,5973,6059,6138", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,73,82,88,81,95,107,83,61,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78,85,78,74", "endOffsets": "263,339,413,496,585,667,763,871,955,1017,1082,1175,1250,1315,1403,1468,1534,1592,1663,1729,1783,1893,1953,2017,2071,2144,2260,2344,2420,2511,2592,2673,2806,2891,2976,3109,3199,3273,3325,3376,3442,3519,3601,3672,3746,3820,3899,3976,4048,4155,4244,4320,4411,4506,4580,4653,4747,4801,4875,4947,5033,5119,5181,5245,5308,5379,5480,5583,5678,5778,5834,5889,5968,6054,6133,6208"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2988,3064,3138,3221,3310,4126,4222,4330,5135,5197,5262,5664,5909,12122,12210,12275,12341,12399,12470,12536,12590,12700,12760,12824,12878,12951,13067,13151,13227,13318,13399,13480,13613,13698,13783,13916,14006,14080,14132,14183,14249,14326,14408,14479,14553,14627,14706,14783,14855,14962,15051,15127,15218,15313,15387,15460,15554,15608,15682,15754,15840,15926,15988,16052,16115,16186,16287,16390,16485,16585,16641,16869,17192,17278,17429", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,75,73,82,88,81,95,107,83,61,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78,85,78,74", "endOffsets": "313,3059,3133,3216,3305,3387,4217,4325,4409,5192,5257,5350,5734,5969,12205,12270,12336,12394,12465,12531,12585,12695,12755,12819,12873,12946,13062,13146,13222,13313,13394,13475,13608,13693,13778,13911,14001,14075,14127,14178,14244,14321,14403,14474,14548,14622,14701,14778,14850,14957,15046,15122,15213,15308,15382,15455,15549,15603,15677,15749,15835,15921,15983,16047,16110,16181,16282,16385,16480,16580,16636,16691,16943,17273,17352,17499"}}]}]}