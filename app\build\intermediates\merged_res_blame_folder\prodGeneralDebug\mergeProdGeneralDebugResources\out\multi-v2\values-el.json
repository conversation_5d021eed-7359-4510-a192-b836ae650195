{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-el\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4768", "endColumns": "163", "endOffsets": "4927"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,239,393,469,553,664,810,988,1156,1294,1397,1538,1599,1660,1745,1844,1916,2074,2183,2269,2417,2493,2593,2694,2840,2950,3067,3170,3242,3313,3445,3531,3742,3925,4054,4209,4328,4518,4649,4798,4906,5070,5182,5311,5415,5512,5659,5755,5900,6002,6182,6268,6415,6491,6616,6726,6806,6889,6957,7030,7105,7178,7312,7427,7587,7692,7831,7935,8074,8178,8275,8413,8505,8615,8727,8845,8945,9007,9060,9114,9169,9218,9273,9351,9404,9465,9516,9561,9640,9684,9743,9798,9854,9909,9959,10011,10092,10145,10197,10243,10306,10365,10428,10587,10711,10791,10852,10935,11004,11072,11205,11285,11361,11441,11519,11587,11685,11759,11851,11934,12062,12141,12233,12317,12414,12506,12586,12690,12797,12914,13019,13097,13189,13263,13346,13480,13622,13689,13766,13860,13958,16009,16124,16331,16378,16509,16681,16771,16923,17017,17177,17359,17500,17694,17768,17849,17924,18024,18147,18194,18280,18419,18510,18645,18775,18906,19006,19112,19178,19237,19296,19351,19418,19498,19587,19721,19790,19866,20177,20427,20570,20701,20846,20949,21071,21202,21345,21520,21613,21723,21810,21930,22035,22192,22282,22360,22416,22678,22767,22855,22914,22972,23087,23221,23331,23498,23677,23749,23816,23888,24041,24122,24219,24297,24392,24599,24691,24769,24905,24988,25152,25308,25362,25482,25553,25625,25696,25771,25820,25890,25985,26037,26106,26280,26402,26513,26579,26716,26856,26981,27088,27355,27460,27558,27657,27839,28084,28169,28366,28494,28651,28769,28853,28939,29013,29158,29337,29480,29529,29621,29773,29893,29974,30059,30152,30232,30305,30385,30448,30531,30600,30678,30765,30832,30899,30989,31063,31147,31316,31476,31573,31630,31709,31857,31946,32022,32101,32179,32267,32526,32581,32661,32752,32822,32911,33099,33191,33272,33357,33437,33505,33586,33678,33838,33920,33973,34052,34129,34197,34279,34389,34495,34612,34742,34818,34866,34919,35016,35111,35197,35314,35501,35609,35691,35822,35960,36092,36216,36326,36499,36641,36842,37024,37152,37309,37402,37505,37627,37775,37875,38006,38077,38503,38592,38678,38766", "endColumns": "183,153,75,83,110,145,177,167,137,102,140,60,60,84,98,71,157,108,85,147,75,99,100,145,109,116,102,71,70,131,85,210,182,128,154,118,189,130,148,107,163,111,128,103,96,146,95,144,101,179,85,146,75,124,109,79,82,67,72,74,72,133,114,159,104,138,103,138,103,96,137,91,109,111,117,99,61,52,53,54,48,54,77,52,60,50,44,78,43,58,54,55,54,49,51,80,52,51,45,62,58,62,158,123,79,60,82,68,67,132,79,75,79,77,67,97,73,91,82,127,78,91,83,96,91,79,103,106,116,104,77,91,73,82,133,141,66,76,93,97,2050,114,206,46,130,171,89,151,93,159,181,140,193,73,80,74,99,122,46,85,138,90,134,129,130,99,105,65,58,58,54,66,79,88,133,68,75,310,249,142,130,144,102,121,130,142,174,92,109,86,119,104,156,89,77,55,261,88,87,58,57,114,133,109,166,178,71,66,71,152,80,96,77,94,206,91,77,135,82,163,155,53,119,70,71,70,74,48,69,94,51,68,173,121,110,65,136,139,124,106,266,104,97,98,181,244,84,196,127,156,117,83,85,73,144,178,142,48,91,151,119,80,84,92,79,72,79,62,82,68,77,86,66,66,89,73,83,168,159,96,56,78,147,88,75,78,77,87,258,54,79,90,69,88,187,91,80,84,79,67,80,91,159,81,52,78,76,67,81,109,105,116,129,75,47,52,96,94,85,116,186,107,81,130,137,131,123,109,172,141,200,181,127,156,92,102,121,147,99,130,70,425,88,85,87,74", "endOffsets": "234,388,464,548,659,805,983,1151,1289,1392,1533,1594,1655,1740,1839,1911,2069,2178,2264,2412,2488,2588,2689,2835,2945,3062,3165,3237,3308,3440,3526,3737,3920,4049,4204,4323,4513,4644,4793,4901,5065,5177,5306,5410,5507,5654,5750,5895,5997,6177,6263,6410,6486,6611,6721,6801,6884,6952,7025,7100,7173,7307,7422,7582,7687,7826,7930,8069,8173,8270,8408,8500,8610,8722,8840,8940,9002,9055,9109,9164,9213,9268,9346,9399,9460,9511,9556,9635,9679,9738,9793,9849,9904,9954,10006,10087,10140,10192,10238,10301,10360,10423,10582,10706,10786,10847,10930,10999,11067,11200,11280,11356,11436,11514,11582,11680,11754,11846,11929,12057,12136,12228,12312,12409,12501,12581,12685,12792,12909,13014,13092,13184,13258,13341,13475,13617,13684,13761,13855,13953,16004,16119,16326,16373,16504,16676,16766,16918,17012,17172,17354,17495,17689,17763,17844,17919,18019,18142,18189,18275,18414,18505,18640,18770,18901,19001,19107,19173,19232,19291,19346,19413,19493,19582,19716,19785,19861,20172,20422,20565,20696,20841,20944,21066,21197,21340,21515,21608,21718,21805,21925,22030,22187,22277,22355,22411,22673,22762,22850,22909,22967,23082,23216,23326,23493,23672,23744,23811,23883,24036,24117,24214,24292,24387,24594,24686,24764,24900,24983,25147,25303,25357,25477,25548,25620,25691,25766,25815,25885,25980,26032,26101,26275,26397,26508,26574,26711,26851,26976,27083,27350,27455,27553,27652,27834,28079,28164,28361,28489,28646,28764,28848,28934,29008,29153,29332,29475,29524,29616,29768,29888,29969,30054,30147,30227,30300,30380,30443,30526,30595,30673,30760,30827,30894,30984,31058,31142,31311,31471,31568,31625,31704,31852,31941,32017,32096,32174,32262,32521,32576,32656,32747,32817,32906,33094,33186,33267,33352,33432,33500,33581,33673,33833,33915,33968,34047,34124,34192,34274,34384,34490,34607,34737,34813,34861,34914,35011,35106,35192,35309,35496,35604,35686,35817,35955,36087,36211,36321,36494,36636,36837,37019,37147,37304,37397,37500,37622,37770,37870,38001,38072,38498,38587,38673,38761,38836"}, "to": {"startLines": "193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18518,18702,18856,18932,19016,19127,19273,19451,19619,19757,19860,20001,20062,20123,20208,20307,20379,20537,20646,20732,20880,20956,21056,21157,21303,21413,21530,21633,21705,21776,21908,21994,22205,22388,22517,22672,22791,22981,23112,23261,23369,23533,23645,23774,23878,23975,24122,24218,24363,24465,24645,24731,24878,24954,25079,25189,25269,25352,25420,25493,25568,25641,25775,25890,26050,26155,26294,26398,26537,26641,26738,26876,26968,27078,27190,27308,27408,27470,27523,27577,27632,27681,27736,27814,27867,27928,27979,28024,28103,28147,28206,28261,28317,28372,28422,28474,28555,28608,28660,28706,28769,28828,28891,29050,29174,29254,29315,29398,29467,29535,29668,29748,29824,29904,29982,30050,30148,30222,30314,30397,30525,30604,30696,30780,30877,30969,31049,31153,31260,31377,31482,31560,31652,31726,31809,31943,32085,32152,32229,32323,32421,34472,34587,34794,34841,34972,35144,35234,35386,35480,35640,35822,35963,36157,36231,36312,36387,36487,36610,36657,36743,36882,36973,37108,37238,37369,37469,37575,37641,37700,37759,37814,37881,37961,38050,38184,38253,38329,38640,38890,39033,39164,39309,39412,39534,39665,39808,39983,40076,40186,40273,40393,40498,40655,40745,40823,40879,41141,41230,41318,41377,41435,41550,41684,41794,41961,42140,42212,42279,42351,42504,42585,42682,42760,42855,43062,43154,43232,43368,43451,43615,43771,43825,43945,44016,44088,44159,44234,44283,44353,44448,44500,44569,44743,44865,44976,45042,45179,45319,45444,45551,45818,45923,46021,46120,46302,46547,46632,46829,46957,47114,47232,47316,47402,47476,47621,47800,47943,47992,48084,48236,48356,48437,48522,48615,48695,48768,48848,48911,48994,49063,49141,49228,49295,49362,49452,49526,49610,49779,49939,50036,50093,50172,50320,50409,50485,50564,50642,50730,50989,51044,51124,51215,51285,51374,51562,51654,51735,51820,51900,51968,52049,52141,52301,52383,52436,52515,52592,52660,52742,52852,52958,53075,53205,53281,53329,53382,53479,53574,53660,53777,53964,54072,54154,54285,54423,54555,54679,54789,54962,55104,55305,55487,55615,55772,55865,55968,56090,56238,56338,56469,56540,56966,57055,57141,57229", "endColumns": "183,153,75,83,110,145,177,167,137,102,140,60,60,84,98,71,157,108,85,147,75,99,100,145,109,116,102,71,70,131,85,210,182,128,154,118,189,130,148,107,163,111,128,103,96,146,95,144,101,179,85,146,75,124,109,79,82,67,72,74,72,133,114,159,104,138,103,138,103,96,137,91,109,111,117,99,61,52,53,54,48,54,77,52,60,50,44,78,43,58,54,55,54,49,51,80,52,51,45,62,58,62,158,123,79,60,82,68,67,132,79,75,79,77,67,97,73,91,82,127,78,91,83,96,91,79,103,106,116,104,77,91,73,82,133,141,66,76,93,97,2050,114,206,46,130,171,89,151,93,159,181,140,193,73,80,74,99,122,46,85,138,90,134,129,130,99,105,65,58,58,54,66,79,88,133,68,75,310,249,142,130,144,102,121,130,142,174,92,109,86,119,104,156,89,77,55,261,88,87,58,57,114,133,109,166,178,71,66,71,152,80,96,77,94,206,91,77,135,82,163,155,53,119,70,71,70,74,48,69,94,51,68,173,121,110,65,136,139,124,106,266,104,97,98,181,244,84,196,127,156,117,83,85,73,144,178,142,48,91,151,119,80,84,92,79,72,79,62,82,68,77,86,66,66,89,73,83,168,159,96,56,78,147,88,75,78,77,87,258,54,79,90,69,88,187,91,80,84,79,67,80,91,159,81,52,78,76,67,81,109,105,116,129,75,47,52,96,94,85,116,186,107,81,130,137,131,123,109,172,141,200,181,127,156,92,102,121,147,99,130,70,425,88,85,87,74", "endOffsets": "18697,18851,18927,19011,19122,19268,19446,19614,19752,19855,19996,20057,20118,20203,20302,20374,20532,20641,20727,20875,20951,21051,21152,21298,21408,21525,21628,21700,21771,21903,21989,22200,22383,22512,22667,22786,22976,23107,23256,23364,23528,23640,23769,23873,23970,24117,24213,24358,24460,24640,24726,24873,24949,25074,25184,25264,25347,25415,25488,25563,25636,25770,25885,26045,26150,26289,26393,26532,26636,26733,26871,26963,27073,27185,27303,27403,27465,27518,27572,27627,27676,27731,27809,27862,27923,27974,28019,28098,28142,28201,28256,28312,28367,28417,28469,28550,28603,28655,28701,28764,28823,28886,29045,29169,29249,29310,29393,29462,29530,29663,29743,29819,29899,29977,30045,30143,30217,30309,30392,30520,30599,30691,30775,30872,30964,31044,31148,31255,31372,31477,31555,31647,31721,31804,31938,32080,32147,32224,32318,32416,34467,34582,34789,34836,34967,35139,35229,35381,35475,35635,35817,35958,36152,36226,36307,36382,36482,36605,36652,36738,36877,36968,37103,37233,37364,37464,37570,37636,37695,37754,37809,37876,37956,38045,38179,38248,38324,38635,38885,39028,39159,39304,39407,39529,39660,39803,39978,40071,40181,40268,40388,40493,40650,40740,40818,40874,41136,41225,41313,41372,41430,41545,41679,41789,41956,42135,42207,42274,42346,42499,42580,42677,42755,42850,43057,43149,43227,43363,43446,43610,43766,43820,43940,44011,44083,44154,44229,44278,44348,44443,44495,44564,44738,44860,44971,45037,45174,45314,45439,45546,45813,45918,46016,46115,46297,46542,46627,46824,46952,47109,47227,47311,47397,47471,47616,47795,47938,47987,48079,48231,48351,48432,48517,48610,48690,48763,48843,48906,48989,49058,49136,49223,49290,49357,49447,49521,49605,49774,49934,50031,50088,50167,50315,50404,50480,50559,50637,50725,50984,51039,51119,51210,51280,51369,51557,51649,51730,51815,51895,51963,52044,52136,52296,52378,52431,52510,52587,52655,52737,52847,52953,53070,53200,53276,53324,53377,53474,53569,53655,53772,53959,54067,54149,54280,54418,54550,54674,54784,54957,55099,55300,55482,55610,55767,55860,55963,56085,56233,56333,56464,56535,56961,57050,57136,57224,57299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,358,444,529,625,712,814,931,1017,1080,1146,1246,1328,1391,1482,1545,1610,1672,1741,1803,1857,1995,2052,2113,2167,2240,2393,2478,2557,2653,2737,2821,2960,3041,3126,3267,3357,3443,3498,3549,3615,3693,3778,3849,3932,4004,4084,4164,4235,4342,4434,4506,4603,4700,4774,4848,4950,5006,5093,5165,5253,5345,5407,5471,5534,5604,5720,5829,5938,6043,6102,6157,6248,6336,6411", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,87,85,84,95,86,101,116,85,62,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,78,95,83,83,138,80,84,140,89,85,54,50,65,77,84,70,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,108,108,104,58,54,90,87,74,80", "endOffsets": "265,353,439,524,620,707,809,926,1012,1075,1141,1241,1323,1386,1477,1540,1605,1667,1736,1798,1852,1990,2047,2108,2162,2235,2388,2473,2552,2648,2732,2816,2955,3036,3121,3262,3352,3438,3493,3544,3610,3688,3773,3844,3927,3999,4079,4159,4230,4337,4429,4501,4598,4695,4769,4843,4945,5001,5088,5160,5248,5340,5402,5466,5529,5599,5715,5824,5933,6038,6097,6152,6243,6331,6406,6487"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3101,3189,3275,3360,3456,4278,4380,4497,5340,5403,5469,5911,6168,12585,12676,12739,12804,12866,12935,12997,13051,13189,13246,13307,13361,13434,13587,13672,13751,13847,13931,14015,14154,14235,14320,14461,14551,14637,14692,14743,14809,14887,14972,15043,15126,15198,15278,15358,15429,15536,15628,15700,15797,15894,15968,16042,16144,16200,16287,16359,16447,16539,16601,16665,16728,16798,16914,17023,17132,17237,17296,17530,17870,17958,18108", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,87,85,84,95,86,101,116,85,62,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,78,95,83,83,138,80,84,140,89,85,54,50,65,77,84,70,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,108,108,104,58,54,90,87,74,80", "endOffsets": "315,3184,3270,3355,3451,3538,4375,4492,4578,5398,5464,5564,5988,6226,12671,12734,12799,12861,12930,12992,13046,13184,13241,13302,13356,13429,13582,13667,13746,13842,13926,14010,14149,14230,14315,14456,14546,14632,14687,14738,14804,14882,14967,15038,15121,15193,15273,15353,15424,15531,15623,15695,15792,15889,15963,16037,16139,16195,16282,16354,16442,16534,16596,16660,16723,16793,16909,17018,17127,17232,17291,17346,17616,17953,18028,18184"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "320,438,549,666,751,857,980,1069,1154,1245,1338,1433,1527,1627,1720,1815,1912,2003,2094,2179,2290,2399,2501,2612,2722,2830,3001,17784", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "433,544,661,746,852,975,1064,1149,1240,1333,1428,1522,1622,1715,1810,1907,1998,2089,2174,2285,2394,2496,2607,2717,2825,2996,3096,17865"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,391,496,588,669,763,852,942,1023,1105,1180,1255,1333,1408,1487,1557", "endColumns": "98,85,100,104,91,80,93,88,89,80,81,74,74,77,74,78,69,122", "endOffsets": "199,285,386,491,583,664,758,847,937,1018,1100,1175,1250,1328,1403,1482,1552,1675"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,539,540,541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4583,4682,5042,5143,5248,5993,6074,17351,17440,17621,17702,18033,18189,18264,18342,57304,57383,57453", "endColumns": "98,85,100,104,91,80,93,88,89,80,81,74,74,77,74,78,69,122", "endOffsets": "4677,4763,5138,5243,5335,6069,6163,17435,17525,17697,17779,18103,18259,18337,18412,57378,57448,57571"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3543,3641,3744,3844,3947,4055,4161,18417", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "3636,3739,3839,3942,4050,4156,4273,18513"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,303,417,539,643,746,866,1017,1145,1303,1393,1493,1592,1697,1815,1941,2046,2188,2324,2468,2648,2786,2906,3033,3157,3257,3356,3492,3629,3735,3841,3951,4095,4248,4362,4468,4555,4653,4750,4840,4929,5032,5112,5195,5294,5396,5493,5591,5678,5784,5883,5985,6106,6186,6302", "endColumns": "123,123,113,121,103,102,119,150,127,157,89,99,98,104,117,125,104,141,135,143,179,137,119,126,123,99,98,135,136,105,105,109,143,152,113,105,86,97,96,89,88,102,79,82,98,101,96,97,86,105,98,101,120,79,115,106", "endOffsets": "174,298,412,534,638,741,861,1012,1140,1298,1388,1488,1587,1692,1810,1936,2041,2183,2319,2463,2643,2781,2901,3028,3152,3252,3351,3487,3624,3730,3836,3946,4090,4243,4357,4463,4550,4648,4745,4835,4924,5027,5107,5190,5289,5391,5488,5586,5673,5779,5878,5980,6101,6181,6297,6404"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6231,6355,6479,6593,6715,6819,6922,7042,7193,7321,7479,7569,7669,7768,7873,7991,8117,8222,8364,8500,8644,8824,8962,9082,9209,9333,9433,9532,9668,9805,9911,10017,10127,10271,10424,10538,10644,10731,10829,10926,11016,11105,11208,11288,11371,11470,11572,11669,11767,11854,11960,12059,12161,12282,12362,12478", "endColumns": "123,123,113,121,103,102,119,150,127,157,89,99,98,104,117,125,104,141,135,143,179,137,119,126,123,99,98,135,136,105,105,109,143,152,113,105,86,97,96,89,88,102,79,82,98,101,96,97,86,105,98,101,120,79,115,106", "endOffsets": "6350,6474,6588,6710,6814,6917,7037,7188,7316,7474,7564,7664,7763,7868,7986,8112,8217,8359,8495,8639,8819,8957,9077,9204,9328,9428,9527,9663,9800,9906,10012,10122,10266,10419,10533,10639,10726,10824,10921,11011,11100,11203,11283,11366,11465,11567,11664,11762,11849,11955,12054,12156,12277,12357,12473,12580"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,101", "endOffsets": "148,250"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "57576,57674", "endColumns": "97,101", "endOffsets": "57669,57771"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,397", "endColumns": "109,106,124,109", "endOffsets": "160,267,392,502"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4932,5569,5676,5801", "endColumns": "109,106,124,109", "endOffsets": "5037,5671,5796,5906"}}]}]}