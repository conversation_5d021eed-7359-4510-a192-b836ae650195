package com.thedasagroup.suminative.ui.refund;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000(\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0006\u001a\u001e\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a\u001e\u0010\u0006\u001a\u00020\u00012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\n\u001a\u00020\u000bH\u0007\u001a\u0010\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\tH\u0007\u001a\b\u0010\u000e\u001a\u00020\u0001H\u0007\u001a\u0016\u0010\u000f\u001a\u00020\u00012\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u00a8\u0006\u0011"}, d2 = {"RefundSumUpScreen", "", "viewModel", "Lcom/thedasagroup/suminative/ui/refund/RefundSumUpViewModel;", "onBackClick", "Lkotlin/Function0;", "OrdersList", "orders", "", "Lcom/thedasagroup/suminative/data/model/request/pagination/OrderItem;", "isRefreshing", "", "RefundOrderCard", "orderItem", "EmptyOrdersView", "ErrorView", "onRetry", "app_stagingGeneralDebug"})
public final class RefundSumUpScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void RefundSumUpScreen(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.refund.RefundSumUpViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void OrdersList(@org.jetbrains.annotations.NotNull()
    java.util.List<com.thedasagroup.suminative.data.model.request.pagination.OrderItem> orders, boolean isRefreshing) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void RefundOrderCard(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem orderItem) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EmptyOrdersView() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ErrorView(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRetry) {
    }
}