package com.thedasagroup.suminative.ui.reservations;

/**
 * Use case for fetching reservation areas for a store
 *
 * This use case handles the business logic for retrieving areas
 * where tables can be reserved in a restaurant/store.
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0019\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J,\u0010\b\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\n0\t2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000eH\u0086B\u00a2\u0006\u0002\u0010\u000fR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"Lcom/thedasagroup/suminative/ui/reservations/GetReservationAreasUseCase;", "", "reservationsRepository", "Lcom/thedasagroup/suminative/data/repo/ReservationsRepository;", "prefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "<init>", "(Lcom/thedasagroup/suminative/data/repo/ReservationsRepository;Lcom/thedasagroup/suminative/data/prefs/Prefs;)V", "invoke", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "", "Lcom/thedasagroup/suminative/data/model/response/reservations/Area;", "storeId", "", "(Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_stagingGeneralDebug"})
public final class GetReservationAreasUseCase {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.repo.ReservationsRepository reservationsRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.prefs.Prefs prefs = null;
    
    @javax.inject.Inject()
    public GetReservationAreasUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.ReservationsRepository reservationsRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        super();
    }
    
    /**
     * Fetch reservation areas for the current store
     *
     * @param storeId Optional store ID. If null, uses the current store from preferences
     * @return StateFlow containing the async result with list of areas
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object invoke(@org.jetbrains.annotations.Nullable()
    java.lang.Integer storeId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.data.model.response.reservations.Area>>>> $completion) {
        return null;
    }
}