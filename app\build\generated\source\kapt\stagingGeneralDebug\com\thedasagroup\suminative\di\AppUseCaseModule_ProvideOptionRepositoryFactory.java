package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.database.DatabaseManager;
import com.thedasagroup.suminative.data.repo.OptionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvideOptionRepositoryFactory implements Factory<OptionRepository> {
  private final Provider<DatabaseManager> databaseManagerProvider;

  public AppUseCaseModule_ProvideOptionRepositoryFactory(
      Provider<DatabaseManager> databaseManagerProvider) {
    this.databaseManagerProvider = databaseManagerProvider;
  }

  @Override
  public OptionRepository get() {
    return provideOptionRepository(databaseManagerProvider.get());
  }

  public static AppUseCaseModule_ProvideOptionRepositoryFactory create(
      Provider<DatabaseManager> databaseManagerProvider) {
    return new AppUseCaseModule_ProvideOptionRepositoryFactory(databaseManagerProvider);
  }

  public static OptionRepository provideOptionRepository(DatabaseManager databaseManager) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.provideOptionRepository(databaseManager));
  }
}
