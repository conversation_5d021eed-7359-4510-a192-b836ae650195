package com.thedasagroup.suminative.ui.common.customComposableViews;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u000b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\f"}, d2 = {"Lcom/thedasagroup/suminative/ui/common/customComposableViews/DateRange;", "", "<init>", "(Ljava/lang/String;I)V", "All", "Today", "Yesterday", "LastSevenDays", "LastThirtyDays", "ThisMonth", "LastMonth", "CustomRange", "app_stagingGeneralDebug"})
public enum DateRange {
    /*public static final*/ All /* = new All() */,
    /*public static final*/ Today /* = new Today() */,
    /*public static final*/ Yesterday /* = new Yesterday() */,
    /*public static final*/ LastSevenDays /* = new LastSevenDays() */,
    /*public static final*/ LastThirtyDays /* = new LastThirtyDays() */,
    /*public static final*/ ThisMonth /* = new ThisMonth() */,
    /*public static final*/ LastMonth /* = new LastMonth() */,
    /*public static final*/ CustomRange /* = new CustomRange() */;
    
    DateRange() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.thedasagroup.suminative.ui.common.customComposableViews.DateRange> getEntries() {
        return null;
    }
}