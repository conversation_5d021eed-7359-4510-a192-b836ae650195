package com.thedasagroup.suminative.data.model.response.reservations;

/**
 * Reservation data model matching the actual API response structure
 */
@kotlinx.serialization.Serializable()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b#\n\u0002\u0010\u000b\n\u0002\b\u0006\b\u0087\b\u0018\u0000 .2\u00020\u0001:\u0002-.B[\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u000b\u0010\fJ\u0010\u0010\u001f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0010J\u000b\u0010 \u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010!\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010\"\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0010J\u000b\u0010#\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010$\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010%\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0010Jb\u0010&\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001\u00a2\u0006\u0002\u0010\'J\u0013\u0010(\u001a\u00020)2\b\u0010*\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010+\u001a\u00020\u0003H\u00d6\u0001J\t\u0010,\u001a\u00020\u0005H\u00d6\u0001R \u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u0010\n\u0002\u0010\u0011\u0012\u0004\b\r\u0010\u000e\u001a\u0004\b\u000f\u0010\u0010R\u001e\u0010\u0004\u001a\u0004\u0018\u00010\u00058\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0012\u0010\u000e\u001a\u0004\b\u0013\u0010\u0014R\u001e\u0010\u0006\u001a\u0004\u0018\u00010\u00058\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0015\u0010\u000e\u001a\u0004\b\u0016\u0010\u0014R \u0010\u0007\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u0010\n\u0002\u0010\u0011\u0012\u0004\b\u0017\u0010\u000e\u001a\u0004\b\u0018\u0010\u0010R\u001e\u0010\b\u001a\u0004\u0018\u00010\u00058\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0019\u0010\u000e\u001a\u0004\b\u001a\u0010\u0014R\u001e\u0010\t\u001a\u0004\u0018\u00010\u00058\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u001b\u0010\u000e\u001a\u0004\b\u001c\u0010\u0014R \u0010\n\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u0010\n\u0002\u0010\u0011\u0012\u0004\b\u001d\u0010\u000e\u001a\u0004\b\u001e\u0010\u0010\u00a8\u0006/"}, d2 = {"Lcom/thedasagroup/suminative/data/model/response/reservations/Reservation;", "", "id", "", "tableName", "", "reservationTime", "numPeople", "customerName", "customerPhone", "reservationStatus", "<init>", "(Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)V", "getId$annotations", "()V", "getId", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getTableName$annotations", "getTableName", "()Ljava/lang/String;", "getReservationTime$annotations", "getReservationTime", "getNumPeople$annotations", "getNumPeople", "getCustomerName$annotations", "getCustomerName", "getCustomerPhone$annotations", "getCustomerPhone", "getReservationStatus$annotations", "getReservationStatus", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "(Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)Lcom/thedasagroup/suminative/data/model/response/reservations/Reservation;", "equals", "", "other", "hashCode", "toString", "$serializer", "Companion", "app_stagingGeneralDebug"})
public final class Reservation {
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer id = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String tableName = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String reservationTime = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer numPeople = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String customerName = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String customerPhone = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer reservationStatus = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.data.model.response.reservations.Reservation.Companion Companion = null;
    
    public Reservation(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String tableName, @org.jetbrains.annotations.Nullable()
    java.lang.String reservationTime, @org.jetbrains.annotations.Nullable()
    java.lang.Integer numPeople, @org.jetbrains.annotations.Nullable()
    java.lang.String customerName, @org.jetbrains.annotations.Nullable()
    java.lang.String customerPhone, @org.jetbrains.annotations.Nullable()
    java.lang.Integer reservationStatus) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getId() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "id")
    @java.lang.Deprecated()
    public static void getId$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getTableName() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "tableName")
    @java.lang.Deprecated()
    public static void getTableName$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getReservationTime() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "reservationTime")
    @java.lang.Deprecated()
    public static void getReservationTime$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getNumPeople() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "numPeople")
    @java.lang.Deprecated()
    public static void getNumPeople$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCustomerName() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "customerName")
    @java.lang.Deprecated()
    public static void getCustomerName$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCustomerPhone() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "customerPhone")
    @java.lang.Deprecated()
    public static void getCustomerPhone$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getReservationStatus() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "reservationStatus")
    @java.lang.Deprecated()
    public static void getReservationStatus$annotations() {
    }
    
    public Reservation() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.model.response.reservations.Reservation copy(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String tableName, @org.jetbrains.annotations.Nullable()
    java.lang.String reservationTime, @org.jetbrains.annotations.Nullable()
    java.lang.Integer numPeople, @org.jetbrains.annotations.Nullable()
    java.lang.String customerName, @org.jetbrains.annotations.Nullable()
    java.lang.String customerPhone, @org.jetbrains.annotations.Nullable()
    java.lang.Integer reservationStatus) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    /**
     * Reservation data model matching the actual API response structure
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0003\u0010\u0004J\u0015\u0010\u0005\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00070\u0006\u00a2\u0006\u0002\u0010\bJ\u000e\u0010\t\u001a\u00020\u00022\u0006\u0010\n\u001a\u00020\u000bJ\u0016\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0002R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014\u00a8\u0006\u0015"}, d2 = {"com/thedasagroup/suminative/data/model/response/reservations/Reservation.$serializer", "Lkotlinx/serialization/internal/GeneratedSerializer;", "Lcom/thedasagroup/suminative/data/model/response/reservations/Reservation;", "<init>", "()V", "childSerializers", "", "Lkotlinx/serialization/KSerializer;", "()[Lkotlinx/serialization/KSerializer;", "deserialize", "decoder", "Lkotlinx/serialization/encoding/Decoder;", "serialize", "", "encoder", "Lkotlinx/serialization/encoding/Encoder;", "value", "descriptor", "Lkotlinx/serialization/descriptors/SerialDescriptor;", "getDescriptor", "()Lkotlinx/serialization/descriptors/SerialDescriptor;", "app_stagingGeneralDebug"})
    @java.lang.Deprecated()
    public static final class $serializer implements kotlinx.serialization.internal.GeneratedSerializer<com.thedasagroup.suminative.data.model.response.reservations.Reservation> {
        @org.jetbrains.annotations.NotNull()
        public static final com.thedasagroup.suminative.data.model.response.reservations.Reservation.$serializer INSTANCE = null;
        @org.jetbrains.annotations.NotNull()
        private static final kotlinx.serialization.descriptors.SerialDescriptor descriptor = null;
        
        /**
         * Reservation data model matching the actual API response structure
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<?>[] childSerializers() {
            return null;
        }
        
        /**
         * Reservation data model matching the actual API response structure
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final com.thedasagroup.suminative.data.model.response.reservations.Reservation deserialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Decoder decoder) {
            return null;
        }
        
        /**
         * Reservation data model matching the actual API response structure
         */
        @java.lang.Override()
        public final void serialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Encoder encoder, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.data.model.response.reservations.Reservation value) {
        }
        
        private $serializer() {
            super();
        }
        
        /**
         * Reservation data model matching the actual API response structure
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.descriptors.SerialDescriptor getDescriptor() {
            return null;
        }
    }
    
    /**
     * Reservation data model matching the actual API response structure
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a8\u0006\u0007"}, d2 = {"Lcom/thedasagroup/suminative/data/model/response/reservations/Reservation$Companion;", "", "<init>", "()V", "serializer", "Lkotlinx/serialization/KSerializer;", "Lcom/thedasagroup/suminative/data/model/response/reservations/Reservation;", "app_stagingGeneralDebug"})
    public static final class Companion {
        
        /**
         * Reservation data model matching the actual API response structure
         */
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<com.thedasagroup.suminative.data.model.response.reservations.Reservation> serializer() {
            return null;
        }
        
        private Companion() {
            super();
        }
    }
}