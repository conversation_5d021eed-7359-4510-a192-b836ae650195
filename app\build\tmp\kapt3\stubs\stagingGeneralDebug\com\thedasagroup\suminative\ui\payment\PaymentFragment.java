package com.thedasagroup.suminative.ui.payment;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u0001\n\u0002\b\u0002\u0018\u0000 \'2\u00020\u00012\u00020\u0002:\u0001\'B\u0007\u00a2\u0006\u0004\b\u0003\u0010\u0004J\u0012\u0010\u0011\u001a\u00020\u00102\b\u0010\u0012\u001a\u0004\u0018\u00010\u0013H\u0016J\b\u0010\u0014\u001a\u00020\u0010H\u0016J$\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u001a2\b\u0010\u0012\u001a\u0004\u0018\u00010\u0013H\u0016J\b\u0010\u001b\u001a\u00020\u0010H\u0016J\"\u0010\u001c\u001a\u00020\u00102\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\u001e2\b\u0010 \u001a\u0004\u0018\u00010!H\u0016J\u0012\u0010\"\u001a\u00020\u00102\b\u0010 \u001a\u0004\u0018\u00010!H\u0002J\u0012\u0010#\u001a\u00020\u00102\b\u0010 \u001a\u0004\u0018\u00010!H\u0002J\u0014\u0010$\u001a\u000e\u0012\u0004\u0012\u00020\u0000\u0012\u0004\u0012\u00020&0%H\u0016R\u001b\u0010\u0005\u001a\u00020\u00068FX\u0086\u0084\u0002\u00a2\u0006\f\n\u0004\b\t\u0010\n\u001a\u0004\b\u0007\u0010\bR\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00100\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006("}, d2 = {"Lcom/thedasagroup/suminative/ui/payment/PaymentFragment;", "Landroidx/fragment/app/DialogFragment;", "Lcom/airbnb/mvrx/mocking/MockableMavericksView;", "<init>", "()V", "paymentViewModel", "Lcom/thedasagroup/suminative/ui/payment/PaymentViewModel;", "getPaymentViewModel", "()Lcom/thedasagroup/suminative/ui/payment/PaymentViewModel;", "paymentViewModel$delegate", "Lkotlin/Lazy;", "order", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "onPaymentSuccess", "Lkotlin/Function1;", "Lcom/thedasagroup/suminative/data/model/response/store_orders/Order2;", "", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onStart", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "invalidate", "onActivityResult", "requestCode", "", "resultCode", "data", "Landroid/content/Intent;", "handleSumUpLoginResult", "handleSumUpPaymentResult", "provideMocks", "Lcom/airbnb/mvrx/mocking/MockBuilder;", "", "Companion", "app_stagingGeneralDebug"})
public final class PaymentFragment extends androidx.fragment.app.DialogFragment implements com.airbnb.mvrx.mocking.MockableMavericksView {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy paymentViewModel$delegate = null;
    @org.jetbrains.annotations.Nullable()
    private com.thedasagroup.suminative.data.model.request.order.Order order;
    @org.jetbrains.annotations.NotNull()
    private kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.store_orders.Order2, kotlin.Unit> onPaymentSuccess;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TAG = "PaymentDialogFragment";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ARG_ORDER = "order";
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.payment.PaymentFragment.Companion Companion = null;
    
    public PaymentFragment() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.payment.PaymentViewModel getPaymentViewModel() {
        return null;
    }
    
    @java.lang.Override()
    public void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    public void onStart() {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void invalidate() {
    }
    
    @java.lang.Override()
    public void onActivityResult(int requestCode, int resultCode, @org.jetbrains.annotations.Nullable()
    android.content.Intent data) {
    }
    
    private final void handleSumUpLoginResult(android.content.Intent data) {
    }
    
    private final void handleSumUpPaymentResult(android.content.Intent data) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.airbnb.mvrx.mocking.MockBuilder provideMocks() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <T extends java.lang.Object>kotlinx.coroutines.Job collectLatest(@org.jetbrains.annotations.NotNull()
    kotlinx.coroutines.flow.Flow<? extends T> $this$collectLatest, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super T, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.airbnb.mvrx.MavericksViewInternalViewModel getMavericksViewInternalViewModel() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String getMvrxViewId() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public androidx.lifecycle.LifecycleOwner getSubscriptionLifecycleOwner() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, T extends java.lang.Object>kotlinx.coroutines.Job onAsync(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onAsync, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends com.airbnb.mvrx.Async<? extends T>> asyncProp, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super java.lang.Throwable, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> onFail, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super T, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> onSuccess) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super S, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super A, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super A, ? super B, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function4<? super A, ? super B, ? super C, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function5<? super A, ? super B, ? super C, ? super D, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object, E extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends E> prop5, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function6<? super A, ? super B, ? super C, ? super D, ? super E, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object, E extends java.lang.Object, F extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends E> prop5, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends F> prop6, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function7<? super A, ? super B, ? super C, ? super D, ? super E, ? super F, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object, E extends java.lang.Object, F extends java.lang.Object, G extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends E> prop5, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends F> prop6, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends G> prop7, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function8<? super A, ? super B, ? super C, ? super D, ? super E, ? super F, ? super G, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    public void postInvalidate() {
    }
    
    @java.lang.Override()
    public void registerMockPrinter() {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.airbnb.mvrx.UniqueOnly uniqueOnly(@org.jetbrains.annotations.Nullable()
    java.lang.String customId) {
        return null;
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J&\u0010\u0007\u001a\u00020\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u000e0\fR\u000e\u0010\u0004\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/thedasagroup/suminative/ui/payment/PaymentFragment$Companion;", "", "<init>", "()V", "TAG", "", "ARG_ORDER", "newInstance", "Lcom/thedasagroup/suminative/ui/payment/PaymentFragment;", "order", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "onPaymentSuccess", "Lkotlin/Function1;", "Lcom/thedasagroup/suminative/data/model/response/store_orders/Order2;", "", "app_stagingGeneralDebug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.thedasagroup.suminative.ui.payment.PaymentFragment newInstance(@org.jetbrains.annotations.Nullable()
        com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
        kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.store_orders.Order2, kotlin.Unit> onPaymentSuccess) {
            return null;
        }
    }
}