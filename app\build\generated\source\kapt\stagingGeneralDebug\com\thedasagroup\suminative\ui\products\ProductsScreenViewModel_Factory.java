package com.thedasagroup.suminative.ui.products;

import com.instacart.truetime.time.TrueTimeImpl;
import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.ProductRepository;
import com.thedasagroup.suminative.domain.cloud_print.CloudPrintUseCase;
import com.thedasagroup.suminative.domain.sales_report.GetSalesReportUseCase;
import com.thedasagroup.suminative.ui.sales.TotalSalesUseCase;
import com.thedasagroup.suminative.ui.stock.StockUseCase;
import com.thedasagroup.suminative.work.OrderSyncManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ProductsScreenViewModel_Factory {
  private final Provider<Prefs> prefsProvider;

  private final Provider<StockUseCase> stockUseCaseProvider;

  private final Provider<PlaceOnlineOrderUseCase> orderUseCaseProvider;

  private final Provider<OrderUseCase> offlineOrderUseCaseProvider;

  private final Provider<CloudPrintUseCase> cloudPrintUseCaseProvider;

  private final Provider<OptionDetailsUseCase> getOptionDetailsUseCaseProvider;

  private final Provider<TotalSalesUseCase> salesUseCaseProvider;

  private final Provider<GetSalesReportUseCase> salesReportUseCaseProvider;

  private final Provider<TrueTimeImpl> trueTimeImplProvider;

  private final Provider<ProductRepository> productsRepositoryProvider;

  private final Provider<DownloadProductsUseCase> downloadProductsUseCaseProvider;

  private final Provider<OrderSyncManager> orderSyncManagerProvider;

  public ProductsScreenViewModel_Factory(Provider<Prefs> prefsProvider,
      Provider<StockUseCase> stockUseCaseProvider,
      Provider<PlaceOnlineOrderUseCase> orderUseCaseProvider,
      Provider<OrderUseCase> offlineOrderUseCaseProvider,
      Provider<CloudPrintUseCase> cloudPrintUseCaseProvider,
      Provider<OptionDetailsUseCase> getOptionDetailsUseCaseProvider,
      Provider<TotalSalesUseCase> salesUseCaseProvider,
      Provider<GetSalesReportUseCase> salesReportUseCaseProvider,
      Provider<TrueTimeImpl> trueTimeImplProvider,
      Provider<ProductRepository> productsRepositoryProvider,
      Provider<DownloadProductsUseCase> downloadProductsUseCaseProvider,
      Provider<OrderSyncManager> orderSyncManagerProvider) {
    this.prefsProvider = prefsProvider;
    this.stockUseCaseProvider = stockUseCaseProvider;
    this.orderUseCaseProvider = orderUseCaseProvider;
    this.offlineOrderUseCaseProvider = offlineOrderUseCaseProvider;
    this.cloudPrintUseCaseProvider = cloudPrintUseCaseProvider;
    this.getOptionDetailsUseCaseProvider = getOptionDetailsUseCaseProvider;
    this.salesUseCaseProvider = salesUseCaseProvider;
    this.salesReportUseCaseProvider = salesReportUseCaseProvider;
    this.trueTimeImplProvider = trueTimeImplProvider;
    this.productsRepositoryProvider = productsRepositoryProvider;
    this.downloadProductsUseCaseProvider = downloadProductsUseCaseProvider;
    this.orderSyncManagerProvider = orderSyncManagerProvider;
  }

  public ProductsScreenViewModel get(ProductsScreenState state) {
    return newInstance(state, prefsProvider.get(), stockUseCaseProvider.get(), orderUseCaseProvider.get(), offlineOrderUseCaseProvider.get(), cloudPrintUseCaseProvider.get(), getOptionDetailsUseCaseProvider.get(), salesUseCaseProvider.get(), salesReportUseCaseProvider.get(), trueTimeImplProvider.get(), productsRepositoryProvider.get(), downloadProductsUseCaseProvider.get(), orderSyncManagerProvider.get());
  }

  public static ProductsScreenViewModel_Factory create(Provider<Prefs> prefsProvider,
      Provider<StockUseCase> stockUseCaseProvider,
      Provider<PlaceOnlineOrderUseCase> orderUseCaseProvider,
      Provider<OrderUseCase> offlineOrderUseCaseProvider,
      Provider<CloudPrintUseCase> cloudPrintUseCaseProvider,
      Provider<OptionDetailsUseCase> getOptionDetailsUseCaseProvider,
      Provider<TotalSalesUseCase> salesUseCaseProvider,
      Provider<GetSalesReportUseCase> salesReportUseCaseProvider,
      Provider<TrueTimeImpl> trueTimeImplProvider,
      Provider<ProductRepository> productsRepositoryProvider,
      Provider<DownloadProductsUseCase> downloadProductsUseCaseProvider,
      Provider<OrderSyncManager> orderSyncManagerProvider) {
    return new ProductsScreenViewModel_Factory(prefsProvider, stockUseCaseProvider, orderUseCaseProvider, offlineOrderUseCaseProvider, cloudPrintUseCaseProvider, getOptionDetailsUseCaseProvider, salesUseCaseProvider, salesReportUseCaseProvider, trueTimeImplProvider, productsRepositoryProvider, downloadProductsUseCaseProvider, orderSyncManagerProvider);
  }

  public static ProductsScreenViewModel newInstance(ProductsScreenState state, Prefs prefs,
      StockUseCase stockUseCase, PlaceOnlineOrderUseCase orderUseCase,
      OrderUseCase offlineOrderUseCase, CloudPrintUseCase cloudPrintUseCase,
      OptionDetailsUseCase getOptionDetailsUseCase, TotalSalesUseCase salesUseCase,
      GetSalesReportUseCase salesReportUseCase, TrueTimeImpl trueTimeImpl,
      ProductRepository productsRepository, DownloadProductsUseCase downloadProductsUseCase,
      OrderSyncManager orderSyncManager) {
    return new ProductsScreenViewModel(state, prefs, stockUseCase, orderUseCase, offlineOrderUseCase, cloudPrintUseCase, getOptionDetailsUseCase, salesUseCase, salesReportUseCase, trueTimeImpl, productsRepository, downloadProductsUseCase, orderSyncManager);
  }
}
