{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,355,437,514,612,706,803,925,1006,1066,1130,1219,1298,1361,1454,1516,1582,1640,1713,1777,1833,1955,2012,2074,2130,2206,2340,2425,2504,2602,2688,2774,2912,2993,3072,3196,3286,3363,3420,3471,3537,3615,3698,3769,3845,3920,3999,4072,4143,4252,4346,4424,4513,4603,4677,4758,4845,4898,4977,5044,5125,5209,5271,5335,5398,5469,5577,5689,5791,5902,5963,6018,6099,6182,6258", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80,82,75,71", "endOffsets": "264,350,432,509,607,701,798,920,1001,1061,1125,1214,1293,1356,1449,1511,1577,1635,1708,1772,1828,1950,2007,2069,2125,2201,2335,2420,2499,2597,2683,2769,2907,2988,3067,3191,3281,3358,3415,3466,3532,3610,3693,3764,3840,3915,3994,4067,4138,4247,4341,4419,4508,4598,4672,4753,4840,4893,4972,5039,5120,5204,5266,5330,5393,5464,5572,5684,5786,5897,5958,6013,6094,6177,6253,6325"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3041,3127,3209,3286,3384,4212,4309,4431,5214,5274,5338,5738,5989,12227,12320,12382,12448,12506,12579,12643,12699,12821,12878,12940,12996,13072,13206,13291,13370,13468,13554,13640,13778,13859,13938,14062,14152,14229,14286,14337,14403,14481,14564,14635,14711,14786,14865,14938,15009,15118,15212,15290,15379,15469,15543,15624,15711,15764,15843,15910,15991,16075,16137,16201,16264,16335,16443,16555,16657,16768,16829,17060,17388,17471,17624", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80,82,75,71", "endOffsets": "314,3122,3204,3281,3379,3473,4304,4426,4507,5269,5333,5422,5812,6047,12315,12377,12443,12501,12574,12638,12694,12816,12873,12935,12991,13067,13201,13286,13365,13463,13549,13635,13773,13854,13933,14057,14147,14224,14281,14332,14398,14476,14559,14630,14706,14781,14860,14933,15004,15113,15207,15285,15374,15464,15538,15619,15706,15759,15838,15905,15986,16070,16132,16196,16259,16330,16438,16550,16652,16763,16824,16879,17136,17466,17542,17691"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,280,377,476,561,637,733,820,909,990,1073,1150,1225,1297,1368,1452,1522", "endColumns": "91,82,96,98,84,75,95,86,88,80,82,76,74,71,70,83,69,119", "endOffsets": "192,275,372,471,556,632,728,815,904,985,1068,1145,1220,1292,1363,1447,1517,1637"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,539,540,541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4512,4604,4933,5030,5129,5817,5893,16884,16971,17141,17222,17547,17696,17771,17843,55867,55951,56021", "endColumns": "91,82,96,98,84,75,95,86,88,80,82,76,74,71,70,83,69,119", "endOffsets": "4599,4682,5025,5124,5209,5888,5984,16966,17055,17217,17300,17619,17766,17838,17909,55946,56016,56136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,294,415,533,633,731,846,998,1119,1261,1346,1445,1541,1644,1762,1883,1987,2118,2246,2382,2560,2691,2811,2932,3067,3164,3264,3384,3513,3613,3720,3823,3960,4100,4206,4310,4394,4494,4591,4678,4765,4870,4950,5033,5132,5236,5331,5430,5518,5628,5729,5834,5954,6034,6135", "endColumns": "121,116,120,117,99,97,114,151,120,141,84,98,95,102,117,120,103,130,127,135,177,130,119,120,134,96,99,119,128,99,106,102,136,139,105,103,83,99,96,86,86,104,79,82,98,103,94,98,87,109,100,104,119,79,100,94", "endOffsets": "172,289,410,528,628,726,841,993,1114,1256,1341,1440,1536,1639,1757,1878,1982,2113,2241,2377,2555,2686,2806,2927,3062,3159,3259,3379,3508,3608,3715,3818,3955,4095,4201,4305,4389,4489,4586,4673,4760,4865,4945,5028,5127,5231,5326,5425,5513,5623,5724,5829,5949,6029,6130,6225"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6052,6174,6291,6412,6530,6630,6728,6843,6995,7116,7258,7343,7442,7538,7641,7759,7880,7984,8115,8243,8379,8557,8688,8808,8929,9064,9161,9261,9381,9510,9610,9717,9820,9957,10097,10203,10307,10391,10491,10588,10675,10762,10867,10947,11030,11129,11233,11328,11427,11515,11625,11726,11831,11951,12031,12132", "endColumns": "121,116,120,117,99,97,114,151,120,141,84,98,95,102,117,120,103,130,127,135,177,130,119,120,134,96,99,119,128,99,106,102,136,139,105,103,83,99,96,86,86,104,79,82,98,103,94,98,87,109,100,104,119,79,100,94", "endOffsets": "6169,6286,6407,6525,6625,6723,6838,6990,7111,7253,7338,7437,7533,7636,7754,7875,7979,8110,8238,8374,8552,8683,8803,8924,9059,9156,9256,9376,9505,9605,9712,9815,9952,10092,10198,10302,10386,10486,10583,10670,10757,10862,10942,11025,11124,11228,11323,11422,11510,11620,11721,11826,11946,12026,12127,12222"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3478,3580,3682,3782,3882,3989,4093,17914", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "3575,3677,3777,3877,3984,4088,4207,18010"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,253,386,462,547,656,806,985,1128,1243,1358,1496,1555,1613,1693,1794,1858,2040,2140,2237,2386,2463,2562,2665,2815,2921,3034,3143,3225,3299,3432,3524,3718,3906,4021,4180,4277,4455,4572,4723,4836,4973,5078,5211,5315,5417,5567,5660,5810,5907,6073,6163,6305,6382,6494,6602,6677,6764,6834,6905,6980,7050,7175,7283,7449,7554,7679,7779,7941,8045,8146,8271,8361,8483,8590,8703,8801,8859,8914,8977,9033,9082,9132,9208,9258,9327,9379,9424,9505,9549,9603,9656,9714,9766,9817,9866,9941,9994,10046,10091,10162,10221,10276,10450,10564,10644,10705,10783,10852,10920,11033,11111,11183,11263,11341,11409,11506,11589,11693,11778,11923,12002,12094,12177,12269,12358,12435,12542,12649,12765,12880,12957,13046,13114,13195,13312,13433,13504,13581,13679,13761,15812,15927,16108,16160,16300,16481,16570,16722,16808,16954,17108,17237,17415,17487,17565,17647,17727,17859,17903,17986,18117,18210,18337,18455,18601,18704,18813,18881,18941,19001,19057,19122,19199,19290,19402,19472,19546,19806,20017,20134,20254,20382,20467,20567,20698,20848,20993,21084,21191,21276,21400,21505,21664,21754,21826,21882,22143,22232,22312,22372,22431,22542,22665,22756,22910,23106,23176,23239,23304,23476,23549,23641,23721,23818,23990,24086,24165,24291,24376,24536,24670,24727,24846,24921,24994,25057,25132,25183,25242,25333,25385,25451,25620,25739,25838,25902,26051,26197,26319,26424,26695,26803,26903,27004,27160,27367,27448,27628,27760,27908,28026,28113,28215,28293,28415,28572,28700,28748,28840,28979,29094,29175,29250,29345,29426,29499,29568,29632,29711,29785,29863,29947,30018,30086,30176,30251,30335,30528,30671,30779,30837,30920,31088,31153,31228,31306,31380,31456,31756,31811,31898,31992,32070,32169,32339,32423,32497,32582,32662,32730,32807,32902,33025,33110,33163,33245,33322,33394,33469,33579,33684,33791,33912,33990,34038,34085,34174,34281,34366,34481,34628,34738,34816,34960,35090,35231,35361,35458,35617,35747,35953,36140,36260,36416,36506,36595,36703,36843,36948,37089,37161,37591,37670,37756,37834", "endColumns": "197,132,75,84,108,149,178,142,114,114,137,58,57,79,100,63,181,99,96,148,76,98,102,149,105,112,108,81,73,132,91,193,187,114,158,96,177,116,150,112,136,104,132,103,101,149,92,149,96,165,89,141,76,111,107,74,86,69,70,74,69,124,107,165,104,124,99,161,103,100,124,89,121,106,112,97,57,54,62,55,48,49,75,49,68,51,44,80,43,53,52,57,51,50,48,74,52,51,44,70,58,54,173,113,79,60,77,68,67,112,77,71,79,77,67,96,82,103,84,144,78,91,82,91,88,76,106,106,115,114,76,88,67,80,116,120,70,76,97,81,2050,114,180,51,139,180,88,151,85,145,153,128,177,71,77,81,79,131,43,82,130,92,126,117,145,102,108,67,59,59,55,64,76,90,111,69,73,259,210,116,119,127,84,99,130,149,144,90,106,84,123,104,158,89,71,55,260,88,79,59,58,110,122,90,153,195,69,62,64,171,72,91,79,96,171,95,78,125,84,159,133,56,118,74,72,62,74,50,58,90,51,65,168,118,98,63,148,145,121,104,270,107,99,100,155,206,80,179,131,147,117,86,101,77,121,156,127,47,91,138,114,80,74,94,80,72,68,63,78,73,77,83,70,67,89,74,83,192,142,107,57,82,167,64,74,77,73,75,299,54,86,93,77,98,169,83,73,84,79,67,76,94,122,84,52,81,76,71,74,109,104,106,120,77,47,46,88,106,84,114,146,109,77,143,129,140,129,96,158,129,205,186,119,155,89,88,107,139,104,140,71,429,78,85,77,72", "endOffsets": "248,381,457,542,651,801,980,1123,1238,1353,1491,1550,1608,1688,1789,1853,2035,2135,2232,2381,2458,2557,2660,2810,2916,3029,3138,3220,3294,3427,3519,3713,3901,4016,4175,4272,4450,4567,4718,4831,4968,5073,5206,5310,5412,5562,5655,5805,5902,6068,6158,6300,6377,6489,6597,6672,6759,6829,6900,6975,7045,7170,7278,7444,7549,7674,7774,7936,8040,8141,8266,8356,8478,8585,8698,8796,8854,8909,8972,9028,9077,9127,9203,9253,9322,9374,9419,9500,9544,9598,9651,9709,9761,9812,9861,9936,9989,10041,10086,10157,10216,10271,10445,10559,10639,10700,10778,10847,10915,11028,11106,11178,11258,11336,11404,11501,11584,11688,11773,11918,11997,12089,12172,12264,12353,12430,12537,12644,12760,12875,12952,13041,13109,13190,13307,13428,13499,13576,13674,13756,15807,15922,16103,16155,16295,16476,16565,16717,16803,16949,17103,17232,17410,17482,17560,17642,17722,17854,17898,17981,18112,18205,18332,18450,18596,18699,18808,18876,18936,18996,19052,19117,19194,19285,19397,19467,19541,19801,20012,20129,20249,20377,20462,20562,20693,20843,20988,21079,21186,21271,21395,21500,21659,21749,21821,21877,22138,22227,22307,22367,22426,22537,22660,22751,22905,23101,23171,23234,23299,23471,23544,23636,23716,23813,23985,24081,24160,24286,24371,24531,24665,24722,24841,24916,24989,25052,25127,25178,25237,25328,25380,25446,25615,25734,25833,25897,26046,26192,26314,26419,26690,26798,26898,26999,27155,27362,27443,27623,27755,27903,28021,28108,28210,28288,28410,28567,28695,28743,28835,28974,29089,29170,29245,29340,29421,29494,29563,29627,29706,29780,29858,29942,30013,30081,30171,30246,30330,30523,30666,30774,30832,30915,31083,31148,31223,31301,31375,31451,31751,31806,31893,31987,32065,32164,32334,32418,32492,32577,32657,32725,32802,32897,33020,33105,33158,33240,33317,33389,33464,33574,33679,33786,33907,33985,34033,34080,34169,34276,34361,34476,34623,34733,34811,34955,35085,35226,35356,35453,35612,35742,35948,36135,36255,36411,36501,36590,36698,36838,36943,37084,37156,37586,37665,37751,37829,37902"}, "to": {"startLines": "193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18015,18213,18346,18422,18507,18616,18766,18945,19088,19203,19318,19456,19515,19573,19653,19754,19818,20000,20100,20197,20346,20423,20522,20625,20775,20881,20994,21103,21185,21259,21392,21484,21678,21866,21981,22140,22237,22415,22532,22683,22796,22933,23038,23171,23275,23377,23527,23620,23770,23867,24033,24123,24265,24342,24454,24562,24637,24724,24794,24865,24940,25010,25135,25243,25409,25514,25639,25739,25901,26005,26106,26231,26321,26443,26550,26663,26761,26819,26874,26937,26993,27042,27092,27168,27218,27287,27339,27384,27465,27509,27563,27616,27674,27726,27777,27826,27901,27954,28006,28051,28122,28181,28236,28410,28524,28604,28665,28743,28812,28880,28993,29071,29143,29223,29301,29369,29466,29549,29653,29738,29883,29962,30054,30137,30229,30318,30395,30502,30609,30725,30840,30917,31006,31074,31155,31272,31393,31464,31541,31639,31721,33772,33887,34068,34120,34260,34441,34530,34682,34768,34914,35068,35197,35375,35447,35525,35607,35687,35819,35863,35946,36077,36170,36297,36415,36561,36664,36773,36841,36901,36961,37017,37082,37159,37250,37362,37432,37506,37766,37977,38094,38214,38342,38427,38527,38658,38808,38953,39044,39151,39236,39360,39465,39624,39714,39786,39842,40103,40192,40272,40332,40391,40502,40625,40716,40870,41066,41136,41199,41264,41436,41509,41601,41681,41778,41950,42046,42125,42251,42336,42496,42630,42687,42806,42881,42954,43017,43092,43143,43202,43293,43345,43411,43580,43699,43798,43862,44011,44157,44279,44384,44655,44763,44863,44964,45120,45327,45408,45588,45720,45868,45986,46073,46175,46253,46375,46532,46660,46708,46800,46939,47054,47135,47210,47305,47386,47459,47528,47592,47671,47745,47823,47907,47978,48046,48136,48211,48295,48488,48631,48739,48797,48880,49048,49113,49188,49266,49340,49416,49716,49771,49858,49952,50030,50129,50299,50383,50457,50542,50622,50690,50767,50862,50985,51070,51123,51205,51282,51354,51429,51539,51644,51751,51872,51950,51998,52045,52134,52241,52326,52441,52588,52698,52776,52920,53050,53191,53321,53418,53577,53707,53913,54100,54220,54376,54466,54555,54663,54803,54908,55049,55121,55551,55630,55716,55794", "endColumns": "197,132,75,84,108,149,178,142,114,114,137,58,57,79,100,63,181,99,96,148,76,98,102,149,105,112,108,81,73,132,91,193,187,114,158,96,177,116,150,112,136,104,132,103,101,149,92,149,96,165,89,141,76,111,107,74,86,69,70,74,69,124,107,165,104,124,99,161,103,100,124,89,121,106,112,97,57,54,62,55,48,49,75,49,68,51,44,80,43,53,52,57,51,50,48,74,52,51,44,70,58,54,173,113,79,60,77,68,67,112,77,71,79,77,67,96,82,103,84,144,78,91,82,91,88,76,106,106,115,114,76,88,67,80,116,120,70,76,97,81,2050,114,180,51,139,180,88,151,85,145,153,128,177,71,77,81,79,131,43,82,130,92,126,117,145,102,108,67,59,59,55,64,76,90,111,69,73,259,210,116,119,127,84,99,130,149,144,90,106,84,123,104,158,89,71,55,260,88,79,59,58,110,122,90,153,195,69,62,64,171,72,91,79,96,171,95,78,125,84,159,133,56,118,74,72,62,74,50,58,90,51,65,168,118,98,63,148,145,121,104,270,107,99,100,155,206,80,179,131,147,117,86,101,77,121,156,127,47,91,138,114,80,74,94,80,72,68,63,78,73,77,83,70,67,89,74,83,192,142,107,57,82,167,64,74,77,73,75,299,54,86,93,77,98,169,83,73,84,79,67,76,94,122,84,52,81,76,71,74,109,104,106,120,77,47,46,88,106,84,114,146,109,77,143,129,140,129,96,158,129,205,186,119,155,89,88,107,139,104,140,71,429,78,85,77,72", "endOffsets": "18208,18341,18417,18502,18611,18761,18940,19083,19198,19313,19451,19510,19568,19648,19749,19813,19995,20095,20192,20341,20418,20517,20620,20770,20876,20989,21098,21180,21254,21387,21479,21673,21861,21976,22135,22232,22410,22527,22678,22791,22928,23033,23166,23270,23372,23522,23615,23765,23862,24028,24118,24260,24337,24449,24557,24632,24719,24789,24860,24935,25005,25130,25238,25404,25509,25634,25734,25896,26000,26101,26226,26316,26438,26545,26658,26756,26814,26869,26932,26988,27037,27087,27163,27213,27282,27334,27379,27460,27504,27558,27611,27669,27721,27772,27821,27896,27949,28001,28046,28117,28176,28231,28405,28519,28599,28660,28738,28807,28875,28988,29066,29138,29218,29296,29364,29461,29544,29648,29733,29878,29957,30049,30132,30224,30313,30390,30497,30604,30720,30835,30912,31001,31069,31150,31267,31388,31459,31536,31634,31716,33767,33882,34063,34115,34255,34436,34525,34677,34763,34909,35063,35192,35370,35442,35520,35602,35682,35814,35858,35941,36072,36165,36292,36410,36556,36659,36768,36836,36896,36956,37012,37077,37154,37245,37357,37427,37501,37761,37972,38089,38209,38337,38422,38522,38653,38803,38948,39039,39146,39231,39355,39460,39619,39709,39781,39837,40098,40187,40267,40327,40386,40497,40620,40711,40865,41061,41131,41194,41259,41431,41504,41596,41676,41773,41945,42041,42120,42246,42331,42491,42625,42682,42801,42876,42949,43012,43087,43138,43197,43288,43340,43406,43575,43694,43793,43857,44006,44152,44274,44379,44650,44758,44858,44959,45115,45322,45403,45583,45715,45863,45981,46068,46170,46248,46370,46527,46655,46703,46795,46934,47049,47130,47205,47300,47381,47454,47523,47587,47666,47740,47818,47902,47973,48041,48131,48206,48290,48483,48626,48734,48792,48875,49043,49108,49183,49261,49335,49411,49711,49766,49853,49947,50025,50124,50294,50378,50452,50537,50617,50685,50762,50857,50980,51065,51118,51200,51277,51349,51424,51534,51639,51746,51867,51945,51993,52040,52129,52236,52321,52436,52583,52693,52771,52915,53045,53186,53316,53413,53572,53702,53908,54095,54215,54371,54461,54550,54658,54798,54903,55044,55116,55546,55625,55711,55789,55862"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,85", "endOffsets": "136,222"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "56141,56227", "endColumns": "85,85", "endOffsets": "56222,56308"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,437,542,649,734,838,958,1036,1112,1204,1298,1393,1487,1587,1681,1777,1872,1964,2056,2138,2249,2352,2451,2566,2680,2783,2938,17305", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "432,537,644,729,833,953,1031,1107,1199,1293,1388,1482,1582,1676,1772,1867,1959,2051,2133,2244,2347,2446,2561,2675,2778,2933,3036,17383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-nl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4687", "endColumns": "142", "endOffsets": "4825"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4830,5427,5528,5639", "endColumns": "102,100,110,98", "endOffsets": "4928,5523,5634,5733"}}]}]}