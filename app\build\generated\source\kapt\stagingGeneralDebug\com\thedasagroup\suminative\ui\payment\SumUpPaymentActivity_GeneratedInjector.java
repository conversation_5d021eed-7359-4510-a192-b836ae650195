package com.thedasagroup.suminative.ui.payment;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = SumUpPaymentActivity.class
)
@GeneratedEntryPoint
@InstallIn(ActivityComponent.class)
public interface SumUpPaymentActivity_GeneratedInjector {
  void injectSumUpPaymentActivity(SumUpPaymentActivity sumUpPaymentActivity);
}
