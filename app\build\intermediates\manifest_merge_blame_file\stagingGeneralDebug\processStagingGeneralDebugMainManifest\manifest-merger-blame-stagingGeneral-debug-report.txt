1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.dasadirect.dasapos"
4    android:versionCode="78"
5    android:versionName="2.22" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
11-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:5:5-76
11-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:5:22-74
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:6:5-66
12-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:7:5-68
13-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:7:22-65
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:8:5-79
14-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:8:22-76
15    <!-- <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> -->
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
16-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:10:5-88
16-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:10:22-86
17    <uses-permission
17-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:11:5-107
18        android:name="android.permission.READ_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:11:22-77
19        android:maxSdkVersion="32" />
19-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:11:78-104
20    <uses-permission
20-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:12:5-108
21        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
21-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:12:22-78
22        android:maxSdkVersion="32" />
22-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:12:79-105
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
23-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:13:5-76
23-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:13:22-73
24    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
24-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:14:5-75
24-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:14:22-72
25    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
25-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:15:5-75
25-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:15:22-72
26
27    <queries>
27-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:5-25:15
28        <package android:name="woyou.aidlservice.jiuiv5" />
28-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:24:9-59
28-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:24:18-57
29
30        <intent>
30-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:168:9-172:18
31            <action android:name="android.intent.action.VIEW" />
31-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:169:13-65
31-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:169:21-62
32
33            <category android:name="android.intent.category.BROWSABLE" />
33-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:170:13-74
33-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:170:23-71
34
35            <data android:scheme="https" />
35-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:171:13-44
35-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:171:19-41
36        </intent>
37    </queries>
38    <queries>
38-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:5-25:15
39        <intent>
39-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:168:9-172:18
40            <action android:name="android.intent.action.VIEW" />
40-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:169:13-65
40-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:169:21-62
41
42            <category android:name="android.intent.category.BROWSABLE" />
42-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:170:13-74
42-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:170:23-71
43
44            <data android:scheme="https" />
44-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:171:13-44
44-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:171:19-41
45        </intent>
46    </queries>
47
48    <!--
49    <uses-permission android:name="com.google.android.gms.permission.AD_ID" tools:node="remove"/>
50    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
51    <uses-permission android:name="android.permission.BLUETOOTH" />
52    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
53    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
54    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
55    -->
56
57
58    <!-- SumUp SDK Permissions -->
59    <uses-permission
59-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:18:5-68
60        android:name="android.permission.BLUETOOTH"
60-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:18:22-65
61        android:maxSdkVersion="30" />
61-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:16:9-35
62    <uses-permission
62-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:19:5-74
63        android:name="android.permission.BLUETOOTH_ADMIN"
63-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:19:22-71
64        android:maxSdkVersion="30" />
64-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:19:9-35
65    <uses-permission
65-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:20:5-76
66        android:name="android.permission.BLUETOOTH_CONNECT"
66-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:20:22-73
67        android:usesPermissionFlags="neverForLocation" />
67-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:27:9-55
68    <uses-permission
68-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:21:5-73
69        android:name="android.permission.BLUETOOTH_SCAN"
69-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:21:22-70
70        android:usesPermissionFlags="neverForLocation" />
70-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:24:9-55
71    <uses-permission android:name="android.permission.VIBRATE" />
71-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:7:5-66
71-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:7:22-63
72    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
72-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:8:5-78
72-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:8:22-75
73    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
73-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:9:5-77
73-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:9:22-74
74    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
74-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:12:5-79
74-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:12:22-76
75    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
75-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:13:5-81
75-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:13:22-78
76
77    <uses-feature
77-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:29:5-31:35
78        android:glEsVersion="0x00020000"
78-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:30:9-41
79        android:required="true" />
79-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:31:9-32
80    <uses-feature
80-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:32:5-34:36
81        android:name="android.hardware.location.gps"
81-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:33:9-53
82        android:required="false" />
82-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:34:9-33
83    <uses-feature
83-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:35:5-37:36
84        android:name="android.hardware.location.network"
84-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:36:9-57
85        android:required="false" /> <!-- Also implied, but also really needed -->
85-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:37:9-33
86    <uses-feature
86-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:39:5-41:35
87        android:name="android.hardware.touchscreen"
87-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:40:9-52
88        android:required="true" />
88-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:41:9-32
89    <uses-feature
89-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:42:5-44:35
90        android:name="android.hardware.screen.portrait"
90-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:43:9-56
91        android:required="true" /> <!-- any location is good enough for us -->
91-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:44:9-32
92    <uses-feature
92-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:46:5-48:35
93        android:name="android.hardware.location"
93-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:47:9-49
94        android:required="true" /> <!-- Only necessary because of missing checks. See: APPS-801 -->
94-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:48:9-32
95    <uses-feature
95-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:50:5-52:35
96        android:name="android.hardware.bluetooth"
96-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:51:9-50
97        android:required="true" />
97-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:52:9-32
98
99    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
99-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
99-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
100    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
100-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:25:5-79
100-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:25:22-76
101    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
101-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:26:5-88
101-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:26:22-85
102    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
102-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:27:5-82
102-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:27:22-79
103    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
103-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:26:5-110
103-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:26:22-107
104    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
104-->[com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:12:5-76
104-->[com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:12:22-73
105
106    <permission
106-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
107        android:name="com.dasadirect.dasapos.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
107-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
108        android:protectionLevel="signature" />
108-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
109
110    <uses-permission android:name="com.dasadirect.dasapos.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
110-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
110-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
111
112    <application
112-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:5-43:19
113        android:name="com.thedasagroup.suminative.App"
113-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:345-364
114        android:allowBackup="false"
114-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:18-45
115        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
115-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
116        android:dataExtractionRules="@xml/data_extraction_rules"
116-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:46-102
117        android:debuggable="true"
118        android:extractNativeLibs="false"
119        android:fullBackupContent="@xml/backup_rules"
119-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:103-148
120        android:icon="@mipmap/ic_launcher"
120-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:149-183
121        android:label="@string/app_name"
121-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:184-216
122        android:largeHeap="true"
122-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:401-425
123        android:roundIcon="@mipmap/ic_launcher"
123-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:217-256
124        android:supportsRtl="true"
124-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:257-283
125        android:testOnly="true"
126        android:theme="@style/Theme.SumiNative"
126-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:284-323
127        android:usesCleartextTraffic="true" >
127-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:365-400
128        <receiver
128-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:29:9-33:20
129            android:name="com.thedasagroup.suminative.ui.service.StartReceiver"
129-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:29:42-82
130            android:enabled="true"
130-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:29:19-41
131            android:exported="true" >
131-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:29:83-106
132            <intent-filter>
132-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:30:13-32:29
133                <action android:name="android.intent.action.BOOT_COMPLETED" />
133-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:17-78
133-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:25-76
134            </intent-filter>
135        </receiver>
136
137        <service
137-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:35:9-36:19
138            android:name="com.thedasagroup.suminative.ui.service.EndlessSocketService"
138-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:35:18-65
139            android:enabled="true"
139-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:35:66-88
140            android:exported="false"
140-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:35:89-113
141            android:foregroundServiceType="specialUse" >
141-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:35:114-156
142        </service>
143
144        <activity
144-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:51:9-64:20
145            android:name="com.thedasagroup.suminative.ui.stores.SelectStoreActivity"
145-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:52:13-58
146            android:exported="true"
146-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:53:13-36
147            android:label="@string/app_name"
147-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:54:13-45
148            android:screenOrientation="landscape"
148-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:56:13-50
149            android:theme="@style/Theme.SumiNative" >
149-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:55:13-52
150            <intent-filter>
150-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:59:13-63:29
151                <action android:name="android.intent.action.MAIN" />
151-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:60:17-69
151-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:60:25-66
152
153                <category android:name="android.intent.category.LAUNCHER" />
153-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:62:17-77
153-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:62:27-74
154            </intent-filter>
155        </activity>
156        <activity
156-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:66:9-69:15
157            android:name="com.thedasagroup.suminative.ui.login.LoginActivity"
157-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:66:19-57
158            android:screenOrientation="landscape" />
158-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:67:13-50
159        <activity
159-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:70:9-73:15
160            android:name="com.thedasagroup.suminative.ui.user_profile.SelectUserProfileActivity"
160-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:70:19-76
161            android:screenOrientation="landscape" />
161-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:71:13-50
162        <activity
162-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:74:9-77:15
163            android:name="com.thedasagroup.suminative.ui.MainActivity"
163-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:74:19-50
164            android:screenOrientation="landscape" />
164-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:75:13-50
165        <activity
165-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:78:9-81:15
166            android:name="com.thedasagroup.suminative.ui.tracking.TrackingActivity"
166-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:78:19-63
167            android:screenOrientation="landscape" />
167-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:79:13-50
168        <activity
168-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:82:9-85:15
169            android:name="com.thedasagroup.suminative.ui.stores.ClosedStoreActivity"
169-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:82:19-64
170            android:screenOrientation="landscape" />
170-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:83:13-50
171        <activity
171-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:86:9-89:15
172            android:name="com.thedasagroup.suminative.ui.stock.StockActivity"
172-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:86:19-57
173            android:screenOrientation="landscape" />
173-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:87:13-50
174        <activity
174-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:91:9-94:15
175            android:name="com.thedasagroup.suminative.ui.sales.SalesActivity"
175-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:91:19-57
176            android:screenOrientation="landscape" />
176-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:92:13-50
177        <activity
177-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:96:9-99:15
178            android:name="com.thedasagroup.suminative.LCDActivity"
178-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:96:19-46
179            android:screenOrientation="landscape" />
179-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:97:13-50
180        <activity
180-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:101:9-104:15
181            android:name="com.thedasagroup.suminative.ui.payment.PaymentActivity"
181-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:101:19-61
182            android:screenOrientation="landscape" />
182-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:102:13-50
183        <activity
183-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:106:9-111:15
184            android:name="com.thedasagroup.suminative.ui.payment.CashPaymentActivity"
184-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:106:19-65
185            android:launchMode="singleTop"
185-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:109:13-43
186            android:screenOrientation="landscape"
186-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:107:13-50
187            android:theme="@style/Theme.SumiNative" />
187-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:108:13-52
188        <activity
188-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:113:9-118:15
189            android:name="com.thedasagroup.suminative.ui.splitbill.SplitBillActivity"
189-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:113:19-65
190            android:launchMode="singleTop"
190-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:116:13-43
191            android:screenOrientation="landscape"
191-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:114:13-50
192            android:theme="@style/Theme.SumiNative" />
192-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:115:13-52
193        <activity
193-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:120:9-123:15
194            android:name="com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersActivity"
194-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:120:19-70
195            android:screenOrientation="landscape" />
195-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:121:13-50
196        <activity
196-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:125:9-128:15
197            android:name="com.thedasagroup.suminative.ui.refund.RefundSumUpActivity"
197-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:125:19-64
198            android:screenOrientation="landscape" />
198-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:126:13-50
199        <activity
199-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:130:9-133:15
200            android:name="com.thedasagroup.suminative.ui.local_orders.LocalOrdersActivity"
200-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:130:19-70
201            android:screenOrientation="landscape" />
201-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:131:13-50
202        <activity
202-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:135:9-138:15
203            android:name="com.thedasagroup.suminative.ui.reservations.ReservationsActivity"
203-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:135:19-71
204            android:screenOrientation="landscape" />
204-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:136:13-50
205        <activity
205-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:140:9-143:15
206            android:name="com.thedasagroup.suminative.ui.reservations.AreaTableSelectionActivity"
206-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:140:19-77
207            android:screenOrientation="landscape" />
207-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:141:13-50
208        <activity
208-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:145:9-148:15
209            android:name="com.thedasagroup.suminative.ui.reservations.AreaTableSelectionUsageExample"
209-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:145:19-81
210            android:screenOrientation="landscape" />
210-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:146:13-50
211        <activity
211-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:150:9-158:20
212            android:name="com.thedasagroup.suminative.ui.settings.SettingsActivity"
212-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:151:13-57
213            android:exported="false"
213-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:152:13-37
214            android:label="Settings"
214-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:153:13-37
215            android:parentActivityName="com.thedasagroup.suminative.ui.MainActivity" >
215-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:154:13-58
216            <meta-data
216-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:155:13-157:52
217                android:name="android.support.PARENT_ACTIVITY"
217-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:156:17-63
218                android:value=".ui.MainActivity" />
218-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:157:17-49
219        </activity>
220
221        <!-- SumUp Payment Activity -->
222        <activity
222-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:41:9-134
223            android:name="com.thedasagroup.suminative.ui.payment.SumUpPaymentActivity"
223-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:41:19-66
224            android:exported="false"
224-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:41:67-91
225            android:theme="@style/Theme.SumiNative" />
225-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:41:92-131
226        <activity
226-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:12:9-16:58
227            android:name="com.pluto.ui.selector.SelectorActivity"
227-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:13:13-66
228            android:exported="false"
228-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:14:13-37
229            android:launchMode="singleTask"
229-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:15:13-44
230            android:theme="@style/PlutoTheme.Selector" />
230-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:16:13-55
231        <activity
231-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:17:9-22:75
232            android:name="com.pluto.ui.container.PlutoActivity"
232-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:18:13-64
233            android:exported="false"
233-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:19:13-37
234            android:launchMode="singleTask"
234-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:20:13-44
235            android:theme="@style/PlutoContainerTheme"
235-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:21:13-55
236            android:windowSoftInputMode="stateUnspecified|adjustResize" />
236-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:22:13-72
237        <activity
237-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:23:9-27:58
238            android:name="com.pluto.tool.modules.ruler.RulerActivity"
238-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:24:13-70
239            android:exported="false"
239-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:25:13-37
240            android:launchMode="singleTask"
240-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:26:13-44
241            android:theme="@style/PlutoContainerTheme" />
241-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:27:13-55
242
243        <provider
243-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:29:9-37:20
244            android:name="com.pluto.core.PlutoFileProvider"
244-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:30:13-60
245            android:authorities="pluto___com.dasadirect.dasapos.provider"
245-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:31:13-68
246            android:exported="false"
246-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:32:13-37
247            android:grantUriPermissions="true" >
247-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:33:13-47
248            <meta-data
248-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:34:13-36:71
249                android:name="android.support.FILE_PROVIDER_PATHS"
249-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:35:17-67
250                android:resource="@xml/pluto___file_provider_paths" />
250-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:36:17-68
251        </provider>
252
253        <activity
253-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:55:9-63:20
254            android:name="com.sumup.merchant.reader.identitylib.ui.activities.LoginActivity"
254-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:56:13-93
255            android:configChanges="orientation|keyboardHidden|screenSize"
255-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:57:13-74
256            android:exported="false"
256-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:58:13-37
257            android:launchMode="singleTop"
257-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:59:13-43
258            android:screenOrientation="locked"
258-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:60:13-47
259            android:theme="@style/SumUpTheme.ActionBarNoShadow"
259-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:61:13-64
260            android:windowSoftInputMode="adjustResize" >
260-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:62:13-55
261        </activity>
262        <activity
262-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:64:9-72:20
263            android:name="com.sumup.merchant.reader.identitylib.ui.activities.ssologin.SSOLoginActivity"
263-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:65:13-105
264            android:configChanges="orientation|keyboardHidden|screenSize"
264-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:66:13-74
265            android:exported="false"
265-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:67:13-37
266            android:launchMode="singleTop"
266-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:68:13-43
267            android:screenOrientation="locked"
267-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:69:13-47
268            android:theme="@style/SumUpTheme.ActionBarNoShadow"
268-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:70:13-64
269            android:windowSoftInputMode="adjustResize" >
269-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:71:13-55
270        </activity>
271        <activity
271-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:73:9-78:57
272            android:name="com.sumup.merchant.reader.ui.activities.CardReaderPaymentAPIDrivenPageActivity"
272-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:74:13-106
273            android:configChanges="orientation|keyboardHidden|screenSize"
273-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:75:13-74
274            android:screenOrientation="locked"
274-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:76:13-47
275            android:theme="@style/SumUpTheme.NoActionBar"
275-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:77:13-58
276            android:windowSoftInputMode="stateHidden" />
276-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:78:13-54
277        <activity
277-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:79:9-83:49
278            android:name="com.sumup.merchant.reader.ui.activities.PaymentSettingsActivity"
278-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:80:13-91
279            android:label="@string/sumup_payment_setting_card_reader"
279-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:81:13-70
280            android:screenOrientation="locked"
280-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:82:13-47
281            android:theme="@style/SumUpTheme" />
281-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:83:13-46
282        <activity
282-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:84:9-88:67
283            android:name="com.sumup.merchant.reader.ui.activities.CardReaderSetupActivity"
283-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:85:13-91
284            android:configChanges="orientation|keyboardHidden|screenSize"
284-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:86:13-74
285            android:screenOrientation="locked"
285-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:87:13-47
286            android:theme="@style/SumUpTheme.ActionBarNoShadow" />
286-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:88:13-64
287        <activity
287-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:89:9-93:67
288            android:name="com.sumup.merchant.reader.troubleshooting.ui.BtTroubleshootingActivity"
288-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:90:13-98
289            android:configChanges="orientation|keyboardHidden|screenSize"
289-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:91:13-74
290            android:screenOrientation="locked"
290-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:92:13-47
291            android:theme="@style/SumUpTheme.ActionBarNoShadow" />
291-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:93:13-64
292        <activity
292-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:94:9-97:61
293            android:name="com.sumup.merchant.reader.troubleshooting.ReaderTroubleshootingActivity"
293-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:95:13-99
294            android:screenOrientation="locked"
294-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:96:13-47
295            android:theme="@style/SumUpTheme.NoActionBar" />
295-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:97:13-58
296        <activity
296-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:98:9-101:61
297            android:name="com.sumup.merchant.reader.webview.ReaderWebViewActivity"
297-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:99:13-83
298            android:screenOrientation="locked"
298-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:100:13-47
299            android:theme="@style/SumUpTheme.NoActionBar" />
299-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:101:13-58
300        <activity
300-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:102:9-107:57
301            android:name="com.sumup.merchant.reader.autoreceipt.AutoReceiptSettingsActivity"
301-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:103:13-93
302            android:configChanges="orientation|keyboardHidden|screenSize"
302-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:104:13-74
303            android:screenOrientation="locked"
303-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:105:13-47
304            android:theme="@style/SumUpTheme.ActionBarNoShadow"
304-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:106:13-64
305            android:windowSoftInputMode="stateHidden" />
305-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:107:13-54
306        <activity
306-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:108:9-112:67
307            android:name="com.sumup.merchant.reader.ui.activities.CardReaderPageActivity"
307-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:109:13-90
308            android:label="@string/sumup_payment_setting_card_reader"
308-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:110:13-70
309            android:screenOrientation="locked"
309-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:111:13-47
310            android:theme="@style/SumUpTheme.ActionBarNoShadow" />
310-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:112:13-64
311
312        <receiver
312-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:114:9-116:40
313            android:name="com.sumup.merchant.reader.receiver.ShareReceiptReceiver"
313-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:115:13-83
314            android:exported="false" /> <!-- This is exported so users can launch it from the command line. It should only be included in debug builds. -->
314-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:116:13-37
315        <activity
315-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:10:9-12:39
316            android:name="com.airbnb.mvrx.launcher.MavericksLauncherActivity"
316-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:11:13-78
317            android:exported="true" />
317-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:12:13-36
318        <activity android:name="com.airbnb.mvrx.launcher.MavericksLauncherMockActivity" />
318-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:13:9-91
318-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:13:19-88
319        <activity android:name="com.airbnb.mvrx.launcher.MavericksLauncherTestMocksActivity" />
319-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:14:9-96
319-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:14:19-93
320        <activity
320-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:35:9-40:77
321            android:name="net.openid.appauth.AuthorizationManagementActivity"
321-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:36:13-78
322            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation|keyboard|keyboardHidden"
322-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:37:13-115
323            android:exported="false"
323-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:38:13-37
324            android:launchMode="singleTask"
324-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:39:13-44
325            android:theme="@style/Theme.AppCompat.Translucent.NoTitleBar" />
325-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:40:13-74
326        <activity
326-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:41:9-52:20
327            android:name="net.openid.appauth.RedirectUriReceiverActivity"
327-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:42:13-74
328            android:exported="true" >
328-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:43:13-36
329            <intent-filter>
329-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:44:13-51:29
330                <action android:name="android.intent.action.VIEW" />
330-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:169:13-65
330-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:169:21-62
331
332                <category android:name="android.intent.category.DEFAULT" />
332-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:47:17-76
332-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:47:27-73
333                <category android:name="android.intent.category.BROWSABLE" />
333-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:170:13-74
333-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:170:23-71
334
335                <data android:scheme="com.dasadirect.dasapos2" />
335-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:171:13-44
335-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:171:19-41
336            </intent-filter>
337        </activity>
338
339        <service
339-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:11:9-20:19
340            android:name="com.google.firebase.components.ComponentDiscoveryService"
340-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:12:13-84
341            android:directBootAware="true"
341-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
342            android:exported="false" >
342-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:13:13-37
343            <meta-data
343-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:14:13-16:85
344                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfKtxRegistrar"
344-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:15:17-112
345                android:value="com.google.firebase.components.ComponentRegistrar" />
345-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:16:17-82
346            <meta-data
346-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:17:13-19:85
347                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfRegistrar"
347-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:18:17-109
348                android:value="com.google.firebase.components.ComponentRegistrar" />
348-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:19:17-82
349            <meta-data
349-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:29:13-31:85
350                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
350-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:30:17-128
351                android:value="com.google.firebase.components.ComponentRegistrar" />
351-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:31:17-82
352            <meta-data
352-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:32:13-34:85
353                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
353-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:33:17-117
354                android:value="com.google.firebase.components.ComponentRegistrar" />
354-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:34:17-82
355            <meta-data
355-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:15:13-17:85
356                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
356-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:16:17-126
357                android:value="com.google.firebase.components.ComponentRegistrar" />
357-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:17:17-82
358            <meta-data
358-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:18:13-20:85
359                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
359-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:19:17-115
360                android:value="com.google.firebase.components.ComponentRegistrar" />
360-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:20:17-82
361            <meta-data
361-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:37:13-39:85
362                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
362-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:38:17-139
363                android:value="com.google.firebase.components.ComponentRegistrar" />
363-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:39:17-82
364            <meta-data
364-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:29:13-31:85
365                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
365-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:30:17-117
366                android:value="com.google.firebase.components.ComponentRegistrar" />
366-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:31:17-82
367            <meta-data
367-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
368                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
368-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
369                android:value="com.google.firebase.components.ComponentRegistrar" />
369-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
370            <meta-data
370-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
371                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
371-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
372                android:value="com.google.firebase.components.ComponentRegistrar" />
372-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
373            <meta-data
373-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
374                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
374-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
375                android:value="com.google.firebase.components.ComponentRegistrar" />
375-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
376            <meta-data
376-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
377                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
377-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
378                android:value="com.google.firebase.components.ComponentRegistrar" />
378-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
379            <meta-data
379-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
380                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
380-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
381                android:value="com.google.firebase.components.ComponentRegistrar" />
381-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
382            <meta-data
382-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:27:13-29:85
383                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
383-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:28:17-115
384                android:value="com.google.firebase.components.ComponentRegistrar" />
384-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:29:17-82
385        </service>
386
387        <activity
387-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:23:9-25:39
388            android:name="androidx.activity.ComponentActivity"
388-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:24:13-63
389            android:exported="true" />
389-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:25:13-36
390        <activity
390-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
391            android:name="androidx.compose.ui.tooling.PreviewActivity"
391-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
392            android:exported="true" />
392-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
393
394        <service
394-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
395            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
395-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
396            android:directBootAware="false"
396-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
397            android:enabled="@bool/enable_system_alarm_service_default"
397-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
398            android:exported="false" />
398-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
399        <service
399-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
400            android:name="androidx.work.impl.background.systemjob.SystemJobService"
400-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
401            android:directBootAware="false"
401-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
402            android:enabled="@bool/enable_system_job_service_default"
402-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
403            android:exported="true"
403-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
404            android:permission="android.permission.BIND_JOB_SERVICE" />
404-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
405        <service
405-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
406            android:name="androidx.work.impl.foreground.SystemForegroundService"
406-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
407            android:directBootAware="false"
407-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
408            android:enabled="@bool/enable_system_foreground_service_default"
408-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
409            android:exported="false" />
409-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
410
411        <receiver
411-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
412            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
412-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
413            android:directBootAware="false"
413-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
414            android:enabled="true"
414-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
415            android:exported="false" />
415-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
416        <receiver
416-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
417            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
417-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
418            android:directBootAware="false"
418-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
419            android:enabled="false"
419-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
420            android:exported="false" >
420-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
421            <intent-filter>
421-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
422                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
422-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
422-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
423                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
423-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
423-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
424            </intent-filter>
425        </receiver>
426        <receiver
426-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
427            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
427-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
428            android:directBootAware="false"
428-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
429            android:enabled="false"
429-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
430            android:exported="false" >
430-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
431            <intent-filter>
431-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
432                <action android:name="android.intent.action.BATTERY_OKAY" />
432-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
432-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
433                <action android:name="android.intent.action.BATTERY_LOW" />
433-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
433-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
434            </intent-filter>
435        </receiver>
436        <receiver
436-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
437            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
437-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
438            android:directBootAware="false"
438-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
439            android:enabled="false"
439-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
440            android:exported="false" >
440-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
441            <intent-filter>
441-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
442                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
442-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
442-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
443                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
443-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
443-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
444            </intent-filter>
445        </receiver>
446        <receiver
446-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
447            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
447-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
448            android:directBootAware="false"
448-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
449            android:enabled="false"
449-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
450            android:exported="false" >
450-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
451            <intent-filter>
451-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
452                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
452-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
452-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
453            </intent-filter>
454        </receiver>
455        <receiver
455-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
456            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
456-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
457            android:directBootAware="false"
457-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
458            android:enabled="false"
458-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
459            android:exported="false" >
459-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
460            <intent-filter>
460-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
461                <action android:name="android.intent.action.BOOT_COMPLETED" />
461-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:17-78
461-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:25-76
462                <action android:name="android.intent.action.TIME_SET" />
462-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
462-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
463                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
463-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
463-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
464            </intent-filter>
465        </receiver>
466        <receiver
466-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
467            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
467-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
468            android:directBootAware="false"
468-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
469            android:enabled="@bool/enable_system_alarm_service_default"
469-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
470            android:exported="false" >
470-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
471            <intent-filter>
471-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
472                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
472-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
472-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
473            </intent-filter>
474        </receiver>
475        <receiver
475-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
476            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
476-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
477            android:directBootAware="false"
477-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
478            android:enabled="true"
478-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
479            android:exported="true"
479-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
480            android:permission="android.permission.DUMP" >
480-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
481            <intent-filter>
481-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
482                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
482-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
482-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
483            </intent-filter>
484        </receiver>
485
486        <property
486-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:30:9-32:61
487            android:name="android.adservices.AD_SERVICES_CONFIG"
487-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:31:13-65
488            android:resource="@xml/ga_ad_services_config" />
488-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:32:13-58
489
490        <service
490-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:22:9-25:40
491            android:name="com.google.firebase.sessions.SessionLifecycleService"
491-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:23:13-80
492            android:enabled="true"
492-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:24:13-35
493            android:exported="false" />
493-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:25:13-37
494
495        <provider
495-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
496            android:name="com.google.firebase.provider.FirebaseInitProvider"
496-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
497            android:authorities="com.dasadirect.dasapos.firebaseinitprovider"
497-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
498            android:directBootAware="true"
498-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
499            android:exported="false"
499-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
500            android:initOrder="100" />
500-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
501
502        <receiver
502-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:29:9-33:20
503            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
503-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:30:13-85
504            android:enabled="true"
504-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:31:13-35
505            android:exported="false" >
505-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:32:13-37
506        </receiver>
507
508        <service
508-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:35:9-38:40
509            android:name="com.google.android.gms.measurement.AppMeasurementService"
509-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:36:13-84
510            android:enabled="true"
510-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:37:13-35
511            android:exported="false" />
511-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:38:13-37
512        <service
512-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:39:9-43:72
513            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
513-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:40:13-87
514            android:enabled="true"
514-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:41:13-35
515            android:exported="false"
515-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:42:13-37
516            android:permission="android.permission.BIND_JOB_SERVICE" />
516-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:43:13-69
517
518        <uses-library
518-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
519            android:name="android.ext.adservices"
519-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
520            android:required="false" />
520-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
521        <uses-library
521-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
522            android:name="androidx.window.extensions"
522-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
523            android:required="false" />
523-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
524        <uses-library
524-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
525            android:name="androidx.window.sidecar"
525-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
526            android:required="false" />
526-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
527
528        <meta-data
528-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
529            android:name="com.google.android.gms.version"
529-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
530            android:value="@integer/google_play_services_version" />
530-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
531
532        <service
532-->[androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:24:9-28:63
533            android:name="androidx.room.MultiInstanceInvalidationService"
533-->[androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:25:13-74
534            android:directBootAware="true"
534-->[androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:26:13-43
535            android:exported="false" />
535-->[androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:27:13-37
536        <service
536-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
537            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
537-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
538            android:exported="false" >
538-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
539            <meta-data
539-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
540                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
540-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
541                android:value="cct" />
541-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
542        </service>
543
544        <receiver
544-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
545            android:name="androidx.profileinstaller.ProfileInstallReceiver"
545-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
546            android:directBootAware="false"
546-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
547            android:enabled="true"
547-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
548            android:exported="true"
548-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
549            android:permission="android.permission.DUMP" >
549-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
550            <intent-filter>
550-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
551                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
551-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
551-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
552            </intent-filter>
553            <intent-filter>
553-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
554                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
554-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
554-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
555            </intent-filter>
556            <intent-filter>
556-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
557                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
557-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
557-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
558            </intent-filter>
559            <intent-filter>
559-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
560                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
560-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
560-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
561            </intent-filter>
562        </receiver>
563
564        <service
564-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
565            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
565-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
566            android:exported="false"
566-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
567            android:permission="android.permission.BIND_JOB_SERVICE" >
567-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
568        </service>
569
570        <receiver
570-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
571            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
571-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
572            android:exported="false" />
572-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
573    </application>
574
575</manifest>
