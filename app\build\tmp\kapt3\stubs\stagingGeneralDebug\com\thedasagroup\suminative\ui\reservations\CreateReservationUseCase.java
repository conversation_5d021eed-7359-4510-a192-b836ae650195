package com.thedasagroup.suminative.ui.reservations;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0011\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J.\u0010\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\u00072\u0006\u0010\n\u001a\u00020\u000b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\rH\u0086B\u00a2\u0006\u0002\u0010\u000eJr\u0010\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\u00072\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\r2\u0006\u0010\u0010\u001a\u00020\r2\u0006\u0010\u0011\u001a\u00020\r2\u0006\u0010\u0012\u001a\u00020\r2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0016\u001a\u00020\r2\u0006\u0010\u0017\u001a\u00020\r2\u0006\u0010\u0018\u001a\u00020\u00142\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\rH\u0086B\u00a2\u0006\u0002\u0010\u0019J\b\u0010\u001a\u001a\u00020\rH\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001b"}, d2 = {"Lcom/thedasagroup/suminative/ui/reservations/CreateReservationUseCase;", "", "reservationsRepository", "Lcom/thedasagroup/suminative/data/repo/ReservationsRepository;", "<init>", "(Lcom/thedasagroup/suminative/data/repo/ReservationsRepository;)V", "invoke", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/reservations/CreateReservationResponse;", "request", "Lcom/thedasagroup/suminative/data/model/request/reservations/CreateReservationRequest;", "timezoneOffset", "", "(Lcom/thedasagroup/suminative/data/model/request/reservations/CreateReservationRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "id", "storeId", "tableId", "customerId", "guestName", "", "guestPhone", "numPeople", "reservationStatus", "reservationTime", "(Ljava/lang/Integer;IIILjava/lang/String;Ljava/lang/String;IILjava/lang/String;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getDeviceTimezoneOffset", "app_stagingGeneralDebug"})
public final class CreateReservationUseCase {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.repo.ReservationsRepository reservationsRepository = null;
    
    @javax.inject.Inject()
    public CreateReservationUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.ReservationsRepository reservationsRepository) {
        super();
    }
    
    /**
     * Creates or updates a reservation with automatic timezone offset calculation
     * @param request The reservation request data
     * @param timezoneOffset Optional timezone offset in minutes east of UTC. If null, uses device timezone
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object invoke(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.reservations.CreateReservationRequest request, @org.jetbrains.annotations.Nullable()
    java.lang.Integer timezoneOffset, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse>>> $completion) {
        return null;
    }
    
    /**
     * Alternative method that accepts individual parameters and builds the request
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object invoke(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, int storeId, int tableId, int customerId, @org.jetbrains.annotations.NotNull()
    java.lang.String guestName, @org.jetbrains.annotations.NotNull()
    java.lang.String guestPhone, int numPeople, int reservationStatus, @org.jetbrains.annotations.NotNull()
    java.lang.String reservationTime, @org.jetbrains.annotations.Nullable()
    java.lang.Integer timezoneOffset, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse>>> $completion) {
        return null;
    }
    
    /**
     * Gets the device's current timezone offset in minutes east of UTC
     * @return Timezone offset in minutes (e.g., +5h = 300, -3h = -180)
     */
    private final int getDeviceTimezoneOffset() {
        return 0;
    }
}