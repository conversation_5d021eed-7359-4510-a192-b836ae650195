package com.thedasagroup.suminative.ui.service;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\u001a\u000e\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003\u00a8\u0006\u0004"}, d2 = {"log", "", "message", "", "app_stagingGeneralDebug"})
public final class EndlessSocketServiceKt {
    
    public static final void log(@org.jetbrains.annotations.NotNull()
    java.lang.String message) {
    }
}