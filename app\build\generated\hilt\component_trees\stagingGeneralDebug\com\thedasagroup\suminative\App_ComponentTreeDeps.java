package com.thedasagroup.suminative;

import dagger.hilt.internal.aggregatedroot.codegen._com_thedasagroup_suminative_App;
import dagger.hilt.internal.componenttreedeps.ComponentTreeDeps;
import dagger.hilt.processor.internal.definecomponent.codegen._com_airbnb_mvrx_hilt_MavericksViewModelComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._com_airbnb_mvrx_hilt_MavericksViewModelComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ActivityComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ActivityRetainedComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_FragmentComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ServiceComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ViewComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ViewModelComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ViewWithFragmentComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ActivityComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ActivityRetainedComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_FragmentComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ServiceComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ViewComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ViewModelComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ViewWithFragmentComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_components_SingletonComponent;
import hilt_aggregated_deps._androidx_hilt_work_HiltWrapper_WorkerFactoryModule;
import hilt_aggregated_deps._com_airbnb_mvrx_hilt_CreateMavericksViewModelComponent;
import hilt_aggregated_deps._com_airbnb_mvrx_hilt_HiltMavericksEntryPoint;
import hilt_aggregated_deps._com_sumup_identity_auth_implementation_di_HiltAuthRequestProviderModule;
import hilt_aggregated_deps._com_sumup_identity_auth_implementation_di_HiltWrapper_HiltAuthModule;
import hilt_aggregated_deps._com_sumup_identity_auth_implementation_di_HiltWrapper_HiltInternalAuthRequestProviderModule;
import hilt_aggregated_deps._com_thedasagroup_suminative_App_GeneratedInjector;
import hilt_aggregated_deps._com_thedasagroup_suminative_di_AppViewModelModule;
import hilt_aggregated_deps._com_thedasagroup_suminative_di_CategoryModule;
import hilt_aggregated_deps._com_thedasagroup_suminative_di_HiltWrapper_AppUseCaseModule;
import hilt_aggregated_deps._com_thedasagroup_suminative_di_HiltWrapper_OrderUseCaseModule;
import hilt_aggregated_deps._com_thedasagroup_suminative_di_HiltWrapper_RepoModule;
import hilt_aggregated_deps._com_thedasagroup_suminative_di_PaymentModule;
import hilt_aggregated_deps._com_thedasagroup_suminative_ui_guava_orders_GuavaOrdersActivity_GeneratedInjector;
import hilt_aggregated_deps._com_thedasagroup_suminative_ui_payment_CashPaymentActivity_GeneratedInjector;
import hilt_aggregated_deps._com_thedasagroup_suminative_ui_payment_PaymentActivity_GeneratedInjector;
import hilt_aggregated_deps._com_thedasagroup_suminative_ui_payment_SumUpPaymentActivity_GeneratedInjector;
import hilt_aggregated_deps._com_thedasagroup_suminative_ui_refund_RefundSumUpActivity_GeneratedInjector;
import hilt_aggregated_deps._com_thedasagroup_suminative_ui_reservations_AreaTableSelectionActivity_GeneratedInjector;
import hilt_aggregated_deps._com_thedasagroup_suminative_ui_service_EndlessSocketService_GeneratedInjector;
import hilt_aggregated_deps._com_thedasagroup_suminative_ui_service_MySocketJobService_GeneratedInjector;
import hilt_aggregated_deps._com_thedasagroup_suminative_ui_settings_SettingsActivity_GeneratedInjector;
import hilt_aggregated_deps._com_thedasagroup_suminative_work_SyncOrdersWorker_HiltModule;
import hilt_aggregated_deps._com_thedasagroup_suminative_work_UploadLogsWorker_HiltModule;
import hilt_aggregated_deps._dagger_hilt_android_flags_FragmentGetContextFix_FragmentGetContextFixEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_flags_HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_ActivityEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_FragmentEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltViewModelFactory_ViewModelFactoriesEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltWrapper_DefaultViewModelFactories_ActivityModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ActivityCreatorEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ViewModelModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ActivityComponentManager_ActivityComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_FragmentComponentManager_FragmentComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedLifecycleEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_LifecycleModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_HiltWrapper_SavedStateHandleModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ServiceComponentManager_ServiceComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ViewComponentManager_ViewComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ViewComponentManager_ViewWithFragmentComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_modules_ApplicationContextModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_modules_HiltWrapper_ActivityModule;

@ComponentTreeDeps(
    rootDeps = _com_thedasagroup_suminative_App.class,
    defineComponentDeps = {
        _com_airbnb_mvrx_hilt_MavericksViewModelComponent.class,
        _com_airbnb_mvrx_hilt_MavericksViewModelComponentBuilder.class,
        _dagger_hilt_android_components_ActivityComponent.class,
        _dagger_hilt_android_components_ActivityRetainedComponent.class,
        _dagger_hilt_android_components_FragmentComponent.class,
        _dagger_hilt_android_components_ServiceComponent.class,
        _dagger_hilt_android_components_ViewComponent.class,
        _dagger_hilt_android_components_ViewModelComponent.class,
        _dagger_hilt_android_components_ViewWithFragmentComponent.class,
        _dagger_hilt_android_internal_builders_ActivityComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ActivityRetainedComponentBuilder.class,
        _dagger_hilt_android_internal_builders_FragmentComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ServiceComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ViewComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ViewModelComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ViewWithFragmentComponentBuilder.class,
        _dagger_hilt_components_SingletonComponent.class
    },
    aggregatedDeps = {
        _androidx_hilt_work_HiltWrapper_WorkerFactoryModule.class,
        _com_airbnb_mvrx_hilt_CreateMavericksViewModelComponent.class,
        _com_airbnb_mvrx_hilt_HiltMavericksEntryPoint.class,
        _com_sumup_identity_auth_implementation_di_HiltAuthRequestProviderModule.class,
        _com_sumup_identity_auth_implementation_di_HiltWrapper_HiltAuthModule.class,
        _com_sumup_identity_auth_implementation_di_HiltWrapper_HiltInternalAuthRequestProviderModule.class,
        _com_thedasagroup_suminative_App_GeneratedInjector.class,
        _com_thedasagroup_suminative_di_AppViewModelModule.class,
        _com_thedasagroup_suminative_di_CategoryModule.class,
        _com_thedasagroup_suminative_di_HiltWrapper_AppUseCaseModule.class,
        _com_thedasagroup_suminative_di_HiltWrapper_OrderUseCaseModule.class,
        _com_thedasagroup_suminative_di_HiltWrapper_RepoModule.class,
        _com_thedasagroup_suminative_di_PaymentModule.class,
        _com_thedasagroup_suminative_ui_guava_orders_GuavaOrdersActivity_GeneratedInjector.class,
        _com_thedasagroup_suminative_ui_payment_CashPaymentActivity_GeneratedInjector.class,
        _com_thedasagroup_suminative_ui_payment_PaymentActivity_GeneratedInjector.class,
        _com_thedasagroup_suminative_ui_payment_SumUpPaymentActivity_GeneratedInjector.class,
        _com_thedasagroup_suminative_ui_refund_RefundSumUpActivity_GeneratedInjector.class,
        _com_thedasagroup_suminative_ui_reservations_AreaTableSelectionActivity_GeneratedInjector.class,
        _com_thedasagroup_suminative_ui_service_EndlessSocketService_GeneratedInjector.class,
        _com_thedasagroup_suminative_ui_service_MySocketJobService_GeneratedInjector.class,
        _com_thedasagroup_suminative_ui_settings_SettingsActivity_GeneratedInjector.class,
        _com_thedasagroup_suminative_work_SyncOrdersWorker_HiltModule.class,
        _com_thedasagroup_suminative_work_UploadLogsWorker_HiltModule.class,
        _dagger_hilt_android_flags_FragmentGetContextFix_FragmentGetContextFixEntryPoint.class,
        _dagger_hilt_android_flags_HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule.class,
        _dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_ActivityEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_FragmentEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_HiltViewModelFactory_ViewModelFactoriesEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_HiltWrapper_DefaultViewModelFactories_ActivityModule.class,
        _dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ActivityCreatorEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ViewModelModule.class,
        _dagger_hilt_android_internal_managers_ActivityComponentManager_ActivityComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_FragmentComponentManager_FragmentComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedLifecycleEntryPoint.class,
        _dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_LifecycleModule.class,
        _dagger_hilt_android_internal_managers_HiltWrapper_SavedStateHandleModule.class,
        _dagger_hilt_android_internal_managers_ServiceComponentManager_ServiceComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_ViewComponentManager_ViewComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_ViewComponentManager_ViewWithFragmentComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_modules_ApplicationContextModule.class,
        _dagger_hilt_android_internal_modules_HiltWrapper_ActivityModule.class
    }
)
public final class App_ComponentTreeDeps {
}
