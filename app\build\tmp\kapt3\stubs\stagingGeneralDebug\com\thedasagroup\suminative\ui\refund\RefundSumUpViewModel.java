package com.thedasagroup.suminative.ui.refund;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0005\u0018\u0000 \r2\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0002\f\rB\u001b\b\u0007\u0012\b\b\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u000e\u0010\b\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\nJ\u0006\u0010\u000b\u001a\u00020\tR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/thedasagroup/suminative/ui/refund/RefundSumUpViewModel;", "Lcom/airbnb/mvrx/MavericksViewModel;", "Lcom/thedasagroup/suminative/ui/refund/RefundSumUpState;", "state", "getPendingOrdersPagedUseCase", "Lcom/thedasagroup/suminative/ui/orders/GetPendingOrdersPagedUseCase;", "<init>", "(Lcom/thedasagroup/suminative/ui/refund/RefundSumUpState;Lcom/thedasagroup/suminative/ui/orders/GetPendingOrdersPagedUseCase;)V", "loadOrders", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "refreshOrders", "Factory", "Companion", "app_stagingGeneralDebug"})
public final class RefundSumUpViewModel extends com.airbnb.mvrx.MavericksViewModel<com.thedasagroup.suminative.ui.refund.RefundSumUpState> {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.orders.GetPendingOrdersPagedUseCase getPendingOrdersPagedUseCase = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.refund.RefundSumUpViewModel.Companion Companion = null;
    
    @dagger.assisted.AssistedInject()
    public RefundSumUpViewModel(@dagger.assisted.Assisted()
    @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.refund.RefundSumUpState state, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.GetPendingOrdersPagedUseCase getPendingOrdersPagedUseCase) {
        super(null, null);
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object loadOrders(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    public final void refreshOrders() {
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u001b\u0010\u0006\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0003H\u0096\u0001J\u0013\u0010\n\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0007\u001a\u00020\bH\u0096\u0001\u00a8\u0006\u000b"}, d2 = {"Lcom/thedasagroup/suminative/ui/refund/RefundSumUpViewModel$Companion;", "Lcom/airbnb/mvrx/MavericksViewModelFactory;", "Lcom/thedasagroup/suminative/ui/refund/RefundSumUpViewModel;", "Lcom/thedasagroup/suminative/ui/refund/RefundSumUpState;", "<init>", "()V", "create", "viewModelContext", "Lcom/airbnb/mvrx/ViewModelContext;", "state", "initialState", "app_stagingGeneralDebug"})
    public static final class Companion implements com.airbnb.mvrx.MavericksViewModelFactory<com.thedasagroup.suminative.ui.refund.RefundSumUpViewModel, com.thedasagroup.suminative.ui.refund.RefundSumUpState> {
        
        private Companion() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.refund.RefundSumUpViewModel create(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.refund.RefundSumUpState state) {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.refund.RefundSumUpState initialState(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext) {
            return null;
        }
    }
    
    @dagger.assisted.AssistedFactory()
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\bg\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001\u00a8\u0006\u0004"}, d2 = {"Lcom/thedasagroup/suminative/ui/refund/RefundSumUpViewModel$Factory;", "Lcom/airbnb/mvrx/hilt/AssistedViewModelFactory;", "Lcom/thedasagroup/suminative/ui/refund/RefundSumUpViewModel;", "Lcom/thedasagroup/suminative/ui/refund/RefundSumUpState;", "app_stagingGeneralDebug"})
    public static abstract interface Factory extends com.airbnb.mvrx.hilt.AssistedViewModelFactory<com.thedasagroup.suminative.ui.refund.RefundSumUpViewModel, com.thedasagroup.suminative.ui.refund.RefundSumUpState> {
    }
}