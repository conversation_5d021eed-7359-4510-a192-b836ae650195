package com.thedasagroup.suminative.ui.stock;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u00008\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\u001a0\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0007\u001a:\u0010\n\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00052\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\f2\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\u0006\u0010\u0006\u001a\u00020\u0007H\u0007\u001a\u000e\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011\u00a8\u0006\u0012"}, d2 = {"StockScreen", "", "modifier", "Landroidx/compose/ui/Modifier;", "viewModel", "Lcom/thedasagroup/suminative/ui/stock/StockScreenViewModel;", "isBackVisible", "", "onBackClick", "Lkotlin/Function0;", "tabRow", "onClickUpdateStock", "Lkotlin/Function1;", "Lcom/thedasagroup/suminative/data/model/response/stock/StockItem;", "getStockString", "", "stock", "", "app_stagingGeneralDebug"})
public final class StockScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.foundation.ExperimentalFoundationApi.class, androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void StockScreen(@org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.stock.StockScreenViewModel viewModel, boolean isBackVisible, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.foundation.ExperimentalFoundationApi()
    @androidx.compose.runtime.Composable()
    public static final void tabRow(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.stock.StockScreenViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.stock.StockItem, kotlin.Unit> onClickUpdateStock, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick, boolean isBackVisible) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getStockString(int stock) {
        return null;
    }
}