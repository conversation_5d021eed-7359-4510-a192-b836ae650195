package com.thedasagroup.suminative.data.model.request.reservations;

/**
 * Request model for creating or updating a reservation via POST API
 * Matches the new API structure from the curl example
 */
@kotlinx.serialization.Serializable()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b-\n\u0002\u0010\u000b\n\u0002\b\u0006\b\u0087\b\u0018\u0000 ;2\u00020\u0001:\u0002:;B[\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\b\u0012\u0006\u0010\n\u001a\u00020\u0003\u0012\u0006\u0010\u000b\u001a\u00020\u0003\u0012\u0006\u0010\f\u001a\u00020\b\u0012\u0006\u0010\r\u001a\u00020\u0003\u00a2\u0006\u0004\b\u000e\u0010\u000fJ\u0010\u0010)\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0013J\t\u0010*\u001a\u00020\u0003H\u00c6\u0003J\t\u0010+\u001a\u00020\u0003H\u00c6\u0003J\t\u0010,\u001a\u00020\u0003H\u00c6\u0003J\t\u0010-\u001a\u00020\bH\u00c6\u0003J\t\u0010.\u001a\u00020\bH\u00c6\u0003J\t\u0010/\u001a\u00020\u0003H\u00c6\u0003J\t\u00100\u001a\u00020\u0003H\u00c6\u0003J\t\u00101\u001a\u00020\bH\u00c6\u0003J\t\u00102\u001a\u00020\u0003H\u00c6\u0003Jt\u00103\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\b2\b\b\u0002\u0010\n\u001a\u00020\u00032\b\b\u0002\u0010\u000b\u001a\u00020\u00032\b\b\u0002\u0010\f\u001a\u00020\b2\b\b\u0002\u0010\r\u001a\u00020\u0003H\u00c6\u0001\u00a2\u0006\u0002\u00104J\u0013\u00105\u001a\u0002062\b\u00107\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00108\u001a\u00020\u0003H\u00d6\u0001J\t\u00109\u001a\u00020\bH\u00d6\u0001R \u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u0010\n\u0002\u0010\u0014\u0012\u0004\b\u0010\u0010\u0011\u001a\u0004\b\u0012\u0010\u0013R\u001c\u0010\u0004\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0015\u0010\u0011\u001a\u0004\b\u0016\u0010\u0017R\u001c\u0010\u0005\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0018\u0010\u0011\u001a\u0004\b\u0019\u0010\u0017R\u001c\u0010\u0006\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u001a\u0010\u0011\u001a\u0004\b\u001b\u0010\u0017R\u001c\u0010\u0007\u001a\u00020\b8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u001c\u0010\u0011\u001a\u0004\b\u001d\u0010\u001eR\u001c\u0010\t\u001a\u00020\b8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u001f\u0010\u0011\u001a\u0004\b \u0010\u001eR\u001c\u0010\n\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b!\u0010\u0011\u001a\u0004\b\"\u0010\u0017R\u001c\u0010\u000b\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b#\u0010\u0011\u001a\u0004\b$\u0010\u0017R\u001c\u0010\f\u001a\u00020\b8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b%\u0010\u0011\u001a\u0004\b&\u0010\u001eR\u001c\u0010\r\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\'\u0010\u0011\u001a\u0004\b(\u0010\u0017\u00a8\u0006<"}, d2 = {"Lcom/thedasagroup/suminative/data/model/request/reservations/CreateReservationRequest;", "", "id", "", "storeId", "tableId", "customerId", "guestName", "", "guestPhone", "numPeople", "reservationStatus", "reservationTime", "timezoneOffset", "<init>", "(Ljava/lang/Integer;IIILjava/lang/String;Ljava/lang/String;IILjava/lang/String;I)V", "getId$annotations", "()V", "getId", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getStoreId$annotations", "getStoreId", "()I", "getTableId$annotations", "getTableId", "getCustomerId$annotations", "getCustomerId", "getGuestName$annotations", "getGuestName", "()Ljava/lang/String;", "getGuestPhone$annotations", "getGuestPhone", "getNumPeople$annotations", "getNumPeople", "getReservationStatus$annotations", "getReservationStatus", "getReservationTime$annotations", "getReservationTime", "getTimezoneOffset$annotations", "getTimezoneOffset", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "copy", "(Ljava/lang/Integer;IIILjava/lang/String;Ljava/lang/String;IILjava/lang/String;I)Lcom/thedasagroup/suminative/data/model/request/reservations/CreateReservationRequest;", "equals", "", "other", "hashCode", "toString", "$serializer", "Companion", "app_stagingGeneralDebug"})
public final class CreateReservationRequest {
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer id = null;
    private final int storeId = 0;
    private final int tableId = 0;
    private final int customerId = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String guestName = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String guestPhone = null;
    private final int numPeople = 0;
    private final int reservationStatus = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String reservationTime = null;
    private final int timezoneOffset = 0;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.data.model.request.reservations.CreateReservationRequest.Companion Companion = null;
    
    public CreateReservationRequest(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, int storeId, int tableId, int customerId, @org.jetbrains.annotations.NotNull()
    java.lang.String guestName, @org.jetbrains.annotations.NotNull()
    java.lang.String guestPhone, int numPeople, int reservationStatus, @org.jetbrains.annotations.NotNull()
    java.lang.String reservationTime, int timezoneOffset) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getId() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "id")
    @java.lang.Deprecated()
    public static void getId$annotations() {
    }
    
    public final int getStoreId() {
        return 0;
    }
    
    @kotlinx.serialization.SerialName(value = "storeId")
    @java.lang.Deprecated()
    public static void getStoreId$annotations() {
    }
    
    public final int getTableId() {
        return 0;
    }
    
    @kotlinx.serialization.SerialName(value = "tableId")
    @java.lang.Deprecated()
    public static void getTableId$annotations() {
    }
    
    public final int getCustomerId() {
        return 0;
    }
    
    @kotlinx.serialization.SerialName(value = "customerId")
    @java.lang.Deprecated()
    public static void getCustomerId$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getGuestName() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "guestName")
    @java.lang.Deprecated()
    public static void getGuestName$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getGuestPhone() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "guestPhone")
    @java.lang.Deprecated()
    public static void getGuestPhone$annotations() {
    }
    
    public final int getNumPeople() {
        return 0;
    }
    
    @kotlinx.serialization.SerialName(value = "numPeople")
    @java.lang.Deprecated()
    public static void getNumPeople$annotations() {
    }
    
    public final int getReservationStatus() {
        return 0;
    }
    
    @kotlinx.serialization.SerialName(value = "reservationStatus")
    @java.lang.Deprecated()
    public static void getReservationStatus$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getReservationTime() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "reservationTime")
    @java.lang.Deprecated()
    public static void getReservationTime$annotations() {
    }
    
    public final int getTimezoneOffset() {
        return 0;
    }
    
    @kotlinx.serialization.SerialName(value = "timezoneOffset")
    @java.lang.Deprecated()
    public static void getTimezoneOffset$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component1() {
        return null;
    }
    
    public final int component10() {
        return 0;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final int component3() {
        return 0;
    }
    
    public final int component4() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component6() {
        return null;
    }
    
    public final int component7() {
        return 0;
    }
    
    public final int component8() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.model.request.reservations.CreateReservationRequest copy(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, int storeId, int tableId, int customerId, @org.jetbrains.annotations.NotNull()
    java.lang.String guestName, @org.jetbrains.annotations.NotNull()
    java.lang.String guestPhone, int numPeople, int reservationStatus, @org.jetbrains.annotations.NotNull()
    java.lang.String reservationTime, int timezoneOffset) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    /**
     * Request model for creating or updating a reservation via POST API
     * Matches the new API structure from the curl example
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0003\u0010\u0004J\u0015\u0010\u0005\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00070\u0006\u00a2\u0006\u0002\u0010\bJ\u000e\u0010\t\u001a\u00020\u00022\u0006\u0010\n\u001a\u00020\u000bJ\u0016\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0002R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014\u00a8\u0006\u0015"}, d2 = {"com/thedasagroup/suminative/data/model/request/reservations/CreateReservationRequest.$serializer", "Lkotlinx/serialization/internal/GeneratedSerializer;", "Lcom/thedasagroup/suminative/data/model/request/reservations/CreateReservationRequest;", "<init>", "()V", "childSerializers", "", "Lkotlinx/serialization/KSerializer;", "()[Lkotlinx/serialization/KSerializer;", "deserialize", "decoder", "Lkotlinx/serialization/encoding/Decoder;", "serialize", "", "encoder", "Lkotlinx/serialization/encoding/Encoder;", "value", "descriptor", "Lkotlinx/serialization/descriptors/SerialDescriptor;", "getDescriptor", "()Lkotlinx/serialization/descriptors/SerialDescriptor;", "app_stagingGeneralDebug"})
    @java.lang.Deprecated()
    public static final class $serializer implements kotlinx.serialization.internal.GeneratedSerializer<com.thedasagroup.suminative.data.model.request.reservations.CreateReservationRequest> {
        @org.jetbrains.annotations.NotNull()
        public static final com.thedasagroup.suminative.data.model.request.reservations.CreateReservationRequest.$serializer INSTANCE = null;
        @org.jetbrains.annotations.NotNull()
        private static final kotlinx.serialization.descriptors.SerialDescriptor descriptor = null;
        
        /**
         * Request model for creating or updating a reservation via POST API
         * Matches the new API structure from the curl example
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<?>[] childSerializers() {
            return null;
        }
        
        /**
         * Request model for creating or updating a reservation via POST API
         * Matches the new API structure from the curl example
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final com.thedasagroup.suminative.data.model.request.reservations.CreateReservationRequest deserialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Decoder decoder) {
            return null;
        }
        
        /**
         * Request model for creating or updating a reservation via POST API
         * Matches the new API structure from the curl example
         */
        @java.lang.Override()
        public final void serialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Encoder encoder, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.data.model.request.reservations.CreateReservationRequest value) {
        }
        
        private $serializer() {
            super();
        }
        
        /**
         * Request model for creating or updating a reservation via POST API
         * Matches the new API structure from the curl example
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.descriptors.SerialDescriptor getDescriptor() {
            return null;
        }
    }
    
    /**
     * Request model for creating or updating a reservation via POST API
     * Matches the new API structure from the curl example
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a8\u0006\u0007"}, d2 = {"Lcom/thedasagroup/suminative/data/model/request/reservations/CreateReservationRequest$Companion;", "", "<init>", "()V", "serializer", "Lkotlinx/serialization/KSerializer;", "Lcom/thedasagroup/suminative/data/model/request/reservations/CreateReservationRequest;", "app_stagingGeneralDebug"})
    public static final class Companion {
        
        /**
         * Request model for creating or updating a reservation via POST API
         * Matches the new API structure from the curl example
         */
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<com.thedasagroup.suminative.data.model.request.reservations.CreateReservationRequest> serializer() {
            return null;
        }
        
        private Companion() {
            super();
        }
    }
}