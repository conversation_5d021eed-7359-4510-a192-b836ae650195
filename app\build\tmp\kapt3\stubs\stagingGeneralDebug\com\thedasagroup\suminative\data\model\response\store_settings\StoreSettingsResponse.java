package com.thedasagroup.suminative.data.model.response.store_settings;

@kotlinx.serialization.Serializable()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b3\b\u0087\b\u0018\u0000 F2\u00020\u0001:\u0002EFB\u008b\u0001\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0003\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0010\u0012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0012\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0014\u00a2\u0006\u0004\b\u0015\u0010\u0016J\u0011\u00105\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u00106\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u0011\u00107\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0003H\u00c6\u0003J\u000b\u00108\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u0010\u00109\u001a\u0004\u0018\u00010\fH\u00c6\u0003\u00a2\u0006\u0002\u0010%J\u000b\u0010:\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010;\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u0010\u0010<\u001a\u0004\u0018\u00010\u0010H\u00c6\u0003\u00a2\u0006\u0002\u0010-J\u000b\u0010=\u001a\u0004\u0018\u00010\u0012H\u00c6\u0003J\u000b\u0010>\u001a\u0004\u0018\u00010\u0014H\u00c6\u0003J\u0092\u0001\u0010?\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u00102\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u00122\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0014H\u00c6\u0001\u00a2\u0006\u0002\u0010@J\u0013\u0010A\u001a\u00020\u00102\b\u0010B\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010C\u001a\u00020\fH\u00d6\u0001J\t\u0010D\u001a\u00020\u0006H\u00d6\u0001R$\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0017\u0010\u0018\u001a\u0004\b\u0019\u0010\u001aR\u001e\u0010\u0005\u001a\u0004\u0018\u00010\u00068\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u001b\u0010\u0018\u001a\u0004\b\u001c\u0010\u001dR$\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u001e\u0010\u0018\u001a\u0004\b\u001f\u0010\u001aR\u001e\u0010\t\u001a\u0004\u0018\u00010\n8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b \u0010\u0018\u001a\u0004\b!\u0010\"R \u0010\u000b\u001a\u0004\u0018\u00010\f8\u0006X\u0087\u0004\u00a2\u0006\u0010\n\u0002\u0010&\u0012\u0004\b#\u0010\u0018\u001a\u0004\b$\u0010%R\u001e\u0010\r\u001a\u0004\u0018\u00010\u00068\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\'\u0010\u0018\u001a\u0004\b(\u0010\u001dR\u001e\u0010\u000e\u001a\u0004\u0018\u00010\u00068\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b)\u0010\u0018\u001a\u0004\b*\u0010\u001dR \u0010\u000f\u001a\u0004\u0018\u00010\u00108\u0006X\u0087\u0004\u00a2\u0006\u0010\n\u0002\u0010.\u0012\u0004\b+\u0010\u0018\u001a\u0004\b,\u0010-R\u001e\u0010\u0011\u001a\u0004\u0018\u00010\u00128\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b/\u0010\u0018\u001a\u0004\b0\u00101R\u001e\u0010\u0013\u001a\u0004\u0018\u00010\u00148\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b2\u0010\u0018\u001a\u0004\b3\u00104\u00a8\u0006G"}, d2 = {"Lcom/thedasagroup/suminative/data/model/response/store_settings/StoreSettingsResponse;", "", "businesses", "", "Lcom/thedasagroup/suminative/data/model/response/login/Businesse;", "command", "", "countries", "Lcom/thedasagroup/suminative/data/model/response/login/Country;", "myStoreSettings", "Lcom/thedasagroup/suminative/data/model/response/login/MyStoreSettings;", "paymentId", "", "paymentResponseJson", "socketId", "success", "", "user", "Lcom/thedasagroup/suminative/data/model/response/login/User;", "store", "Lcom/thedasagroup/suminative/data/model/response/store_settings/Store2;", "<init>", "(Ljava/util/List;Ljava/lang/String;Ljava/util/List;Lcom/thedasagroup/suminative/data/model/response/login/MyStoreSettings;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;Lcom/thedasagroup/suminative/data/model/response/login/User;Lcom/thedasagroup/suminative/data/model/response/store_settings/Store2;)V", "getBusinesses$annotations", "()V", "getBusinesses", "()Ljava/util/List;", "getCommand$annotations", "getCommand", "()Ljava/lang/String;", "getCountries$annotations", "getCountries", "getMyStoreSettings$annotations", "getMyStoreSettings", "()Lcom/thedasagroup/suminative/data/model/response/login/MyStoreSettings;", "getPaymentId$annotations", "getPaymentId", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getPaymentResponseJson$annotations", "getPaymentResponseJson", "getSocketId$annotations", "getSocketId", "getSuccess$annotations", "getSuccess", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getUser$annotations", "getUser", "()Lcom/thedasagroup/suminative/data/model/response/login/User;", "getStore$annotations", "getStore", "()Lcom/thedasagroup/suminative/data/model/response/store_settings/Store2;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "copy", "(Ljava/util/List;Ljava/lang/String;Ljava/util/List;Lcom/thedasagroup/suminative/data/model/response/login/MyStoreSettings;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;Lcom/thedasagroup/suminative/data/model/response/login/User;Lcom/thedasagroup/suminative/data/model/response/store_settings/Store2;)Lcom/thedasagroup/suminative/data/model/response/store_settings/StoreSettingsResponse;", "equals", "other", "hashCode", "toString", "$serializer", "Companion", "app_stagingGeneralDebug"})
public final class StoreSettingsResponse {
    @org.jetbrains.annotations.Nullable()
    private final java.util.List<com.thedasagroup.suminative.data.model.response.login.Businesse> businesses = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String command = null;
    @org.jetbrains.annotations.Nullable()
    private final java.util.List<com.thedasagroup.suminative.data.model.response.login.Country> countries = null;
    @org.jetbrains.annotations.Nullable()
    private final com.thedasagroup.suminative.data.model.response.login.MyStoreSettings myStoreSettings = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer paymentId = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String paymentResponseJson = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String socketId = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Boolean success = null;
    @org.jetbrains.annotations.Nullable()
    private final com.thedasagroup.suminative.data.model.response.login.User user = null;
    @org.jetbrains.annotations.Nullable()
    private final com.thedasagroup.suminative.data.model.response.store_settings.Store2 store = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.data.model.response.store_settings.StoreSettingsResponse.Companion Companion = null;
    
    public StoreSettingsResponse(@org.jetbrains.annotations.Nullable()
    java.util.List<com.thedasagroup.suminative.data.model.response.login.Businesse> businesses, @org.jetbrains.annotations.Nullable()
    java.lang.String command, @org.jetbrains.annotations.Nullable()
    java.util.List<com.thedasagroup.suminative.data.model.response.login.Country> countries, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.login.MyStoreSettings myStoreSettings, @org.jetbrains.annotations.Nullable()
    java.lang.Integer paymentId, @org.jetbrains.annotations.Nullable()
    java.lang.String paymentResponseJson, @org.jetbrains.annotations.Nullable()
    java.lang.String socketId, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean success, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.login.User user, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.store_settings.Store2 store) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.thedasagroup.suminative.data.model.response.login.Businesse> getBusinesses() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "businesses")
    @java.lang.Deprecated()
    public static void getBusinesses$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCommand() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "command")
    @java.lang.Deprecated()
    public static void getCommand$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.thedasagroup.suminative.data.model.response.login.Country> getCountries() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "countries")
    @java.lang.Deprecated()
    public static void getCountries$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.response.login.MyStoreSettings getMyStoreSettings() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "myStoreSettings")
    @java.lang.Deprecated()
    public static void getMyStoreSettings$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getPaymentId() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "paymentId")
    @java.lang.Deprecated()
    public static void getPaymentId$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getPaymentResponseJson() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "paymentResponseJson")
    @java.lang.Deprecated()
    public static void getPaymentResponseJson$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSocketId() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "socketId")
    @java.lang.Deprecated()
    public static void getSocketId$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean getSuccess() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "success")
    @java.lang.Deprecated()
    public static void getSuccess$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.response.login.User getUser() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "user")
    @java.lang.Deprecated()
    public static void getUser$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.response.store_settings.Store2 getStore() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "store")
    @java.lang.Deprecated()
    public static void getStore$annotations() {
    }
    
    public StoreSettingsResponse() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.thedasagroup.suminative.data.model.response.login.Businesse> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.response.store_settings.Store2 component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.thedasagroup.suminative.data.model.response.login.Country> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.response.login.MyStoreSettings component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.response.login.User component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.model.response.store_settings.StoreSettingsResponse copy(@org.jetbrains.annotations.Nullable()
    java.util.List<com.thedasagroup.suminative.data.model.response.login.Businesse> businesses, @org.jetbrains.annotations.Nullable()
    java.lang.String command, @org.jetbrains.annotations.Nullable()
    java.util.List<com.thedasagroup.suminative.data.model.response.login.Country> countries, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.login.MyStoreSettings myStoreSettings, @org.jetbrains.annotations.Nullable()
    java.lang.Integer paymentId, @org.jetbrains.annotations.Nullable()
    java.lang.String paymentResponseJson, @org.jetbrains.annotations.Nullable()
    java.lang.String socketId, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean success, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.login.User user, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.store_settings.Store2 store) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0003\u0010\u0004J\u0015\u0010\u0005\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00070\u0006\u00a2\u0006\u0002\u0010\bJ\u000e\u0010\t\u001a\u00020\u00022\u0006\u0010\n\u001a\u00020\u000bJ\u0016\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0002R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014\u00a8\u0006\u0015"}, d2 = {"com/thedasagroup/suminative/data/model/response/store_settings/StoreSettingsResponse.$serializer", "Lkotlinx/serialization/internal/GeneratedSerializer;", "Lcom/thedasagroup/suminative/data/model/response/store_settings/StoreSettingsResponse;", "<init>", "()V", "childSerializers", "", "Lkotlinx/serialization/KSerializer;", "()[Lkotlinx/serialization/KSerializer;", "deserialize", "decoder", "Lkotlinx/serialization/encoding/Decoder;", "serialize", "", "encoder", "Lkotlinx/serialization/encoding/Encoder;", "value", "descriptor", "Lkotlinx/serialization/descriptors/SerialDescriptor;", "getDescriptor", "()Lkotlinx/serialization/descriptors/SerialDescriptor;", "app_stagingGeneralDebug"})
    @java.lang.Deprecated()
    public static final class $serializer implements kotlinx.serialization.internal.GeneratedSerializer<com.thedasagroup.suminative.data.model.response.store_settings.StoreSettingsResponse> {
        @org.jetbrains.annotations.NotNull()
        public static final com.thedasagroup.suminative.data.model.response.store_settings.StoreSettingsResponse.$serializer INSTANCE = null;
        @org.jetbrains.annotations.NotNull()
        private static final kotlinx.serialization.descriptors.SerialDescriptor descriptor = null;
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<?>[] childSerializers() {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final com.thedasagroup.suminative.data.model.response.store_settings.StoreSettingsResponse deserialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Decoder decoder) {
            return null;
        }
        
        @java.lang.Override()
        public final void serialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Encoder encoder, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.data.model.response.store_settings.StoreSettingsResponse value) {
        }
        
        private $serializer() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.descriptors.SerialDescriptor getDescriptor() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a8\u0006\u0007"}, d2 = {"Lcom/thedasagroup/suminative/data/model/response/store_settings/StoreSettingsResponse$Companion;", "", "<init>", "()V", "serializer", "Lkotlinx/serialization/KSerializer;", "Lcom/thedasagroup/suminative/data/model/response/store_settings/StoreSettingsResponse;", "app_stagingGeneralDebug"})
    public static final class Companion {
        
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<com.thedasagroup.suminative.data.model.response.store_settings.StoreSettingsResponse> serializer() {
            return null;
        }
        
        private Companion() {
            super();
        }
    }
}