package com.thedasagroup.suminative.ui.payment;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000H\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0002\u001ad\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0014\b\u0002\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00050\t2\u0014\b\u0002\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00050\t2\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00050\u000e2\u0016\b\u0002\u0010\u000f\u001a\u0010\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u0005\u0018\u00010\tH\u0007\u001a\u0016\u0010\u0011\u001a\u00020\u00052\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00050\u000eH\u0007\u001a\u001e\u0010\u0013\u001a\u00020\u00052\u0006\u0010\u0014\u001a\u00020\u00012\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00050\u000eH\u0007\u001a4\u0010\u0015\u001a\u00020\u00052\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00180\u00172\b\u0010\u0019\u001a\u0004\u0018\u00010\u00182\u0012\u0010\u001a\u001a\u000e\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020\u00050\tH\u0007\u001a&\u0010\u001b\u001a\u00020\u00052\u0006\u0010\u001c\u001a\u00020\u00182\u0006\u0010\u001d\u001a\u00020\u001e2\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00050\u000eH\u0007\"\u0014\u0010\u0000\u001a\u00020\u0001X\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0003\u00a8\u0006 "}, d2 = {"TAG", "", "getTAG", "()Ljava/lang/String;", "PaymentScreen", "", "paymentViewModel", "Lcom/thedasagroup/suminative/ui/payment/PaymentViewModel;", "onMakePaymentClickSuccess", "Lkotlin/Function1;", "Lcom/thedasagroup/suminative/data/model/response/store_orders/Order2;", "onMakePaymentClickFail", "Lcom/thedasagroup/suminative/data/model/response/my_guava/failure/GuavaFailResponse;", "onPaymentCancelled", "Lkotlin/Function0;", "onSumUpPaymentClick", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "PaymentSessionTryAgain", "onClick", "PaymentMethodButton", "text", "TerminalSelectionCard", "terminals", "", "Lcom/thedasagroup/suminative/data/model/response/my_guava/terminals/Terminal;", "selectedTerminal", "onTerminalSelected", "TerminalItem", "terminal", "isSelected", "", "onSelected", "app_stagingGeneralDebug"})
public final class PaymentScreenKt {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "PaymentScreen";
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getTAG() {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PaymentScreen(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.payment.PaymentViewModel paymentViewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.store_orders.Order2, kotlin.Unit> onMakePaymentClickSuccess, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.my_guava.failure.GuavaFailResponse, kotlin.Unit> onMakePaymentClickFail, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPaymentCancelled, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.order.Order, kotlin.Unit> onSumUpPaymentClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PaymentSessionTryAgain(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PaymentMethodButton(@org.jetbrains.annotations.NotNull()
    java.lang.String text, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TerminalSelectionCard(@org.jetbrains.annotations.NotNull()
    java.util.List<com.thedasagroup.suminative.data.model.response.my_guava.terminals.Terminal> terminals, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.my_guava.terminals.Terminal selectedTerminal, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.my_guava.terminals.Terminal, kotlin.Unit> onTerminalSelected) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TerminalItem(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.my_guava.terminals.Terminal terminal, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSelected) {
    }
}