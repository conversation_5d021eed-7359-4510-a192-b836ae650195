package com.thedasagroup.suminative.ui.reservations;

import com.instacart.truetime.time.TrueTimeImpl;
import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.ReservationsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class GetActiveReservationsUseCase_Factory implements Factory<GetActiveReservationsUseCase> {
  private final Provider<ReservationsRepository> reservationsRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  private final Provider<TrueTimeImpl> trueTimeImplProvider;

  public GetActiveReservationsUseCase_Factory(
      Provider<ReservationsRepository> reservationsRepositoryProvider,
      Provider<Prefs> prefsProvider, Provider<TrueTimeImpl> trueTimeImplProvider) {
    this.reservationsRepositoryProvider = reservationsRepositoryProvider;
    this.prefsProvider = prefsProvider;
    this.trueTimeImplProvider = trueTimeImplProvider;
  }

  @Override
  public GetActiveReservationsUseCase get() {
    return newInstance(reservationsRepositoryProvider.get(), prefsProvider.get(), trueTimeImplProvider.get());
  }

  public static GetActiveReservationsUseCase_Factory create(
      Provider<ReservationsRepository> reservationsRepositoryProvider,
      Provider<Prefs> prefsProvider, Provider<TrueTimeImpl> trueTimeImplProvider) {
    return new GetActiveReservationsUseCase_Factory(reservationsRepositoryProvider, prefsProvider, trueTimeImplProvider);
  }

  public static GetActiveReservationsUseCase newInstance(
      ReservationsRepository reservationsRepository, Prefs prefs, TrueTimeImpl trueTimeImpl) {
    return new GetActiveReservationsUseCase(reservationsRepository, prefs, trueTimeImpl);
  }
}
