{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,269,377", "endColumns": "107,105,107,105", "endOffsets": "158,264,372,478"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4832,5432,5538,5646", "endColumns": "107,105,107,105", "endOffsets": "4935,5533,5641,5747"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,781,904,983,1043,1108,1197,1262,1321,1407,1471,1535,1598,1668,1732,1786,1891,1949,2011,2065,2137,2254,2341,2417,2509,2591,2674,2814,2891,2972,3099,3190,3267,3321,3372,3438,3508,3585,3656,3731,3802,3879,3948,4017,4124,4215,4287,4376,4465,4539,4611,4697,4747,4826,4892,4972,5056,5118,5182,5245,5314,5414,5509,5601,5693,5751,5806,5887,5968,6043", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,77,76,85,83,101,122,78,59,64,88,64,58,85,63,63,62,69,63,53,104,57,61,53,71,116,86,75,91,81,82,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,80,80,74,74", "endOffsets": "267,349,427,504,590,674,776,899,978,1038,1103,1192,1257,1316,1402,1466,1530,1593,1663,1727,1781,1886,1944,2006,2060,2132,2249,2336,2412,2504,2586,2669,2809,2886,2967,3094,3185,3262,3316,3367,3433,3503,3580,3651,3726,3797,3874,3943,4012,4119,4210,4282,4371,4460,4534,4606,4692,4742,4821,4887,4967,5051,5113,5177,5240,5309,5409,5504,5596,5688,5746,5801,5882,5963,6038,6113"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3065,3147,3225,3302,3388,4222,4324,4447,5218,5278,5343,5752,6013,12222,12308,12372,12436,12499,12569,12633,12687,12792,12850,12912,12966,13038,13155,13242,13318,13410,13492,13575,13715,13792,13873,14000,14091,14168,14222,14273,14339,14409,14486,14557,14632,14703,14780,14849,14918,15025,15116,15188,15277,15366,15440,15512,15598,15648,15727,15793,15873,15957,16019,16083,16146,16215,16315,16410,16502,16594,16652,16886,17218,17299,17453", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,81,77,76,85,83,101,122,78,59,64,88,64,58,85,63,63,62,69,63,53,104,57,61,53,71,116,86,75,91,81,82,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,80,80,74,74", "endOffsets": "317,3142,3220,3297,3383,3467,4319,4442,4521,5273,5338,5427,5812,6067,12303,12367,12431,12494,12564,12628,12682,12787,12845,12907,12961,13033,13150,13237,13313,13405,13487,13570,13710,13787,13868,13995,14086,14163,14217,14268,14334,14404,14481,14552,14627,14698,14775,14844,14913,15020,15111,15183,15272,15361,15435,15507,15593,15643,15722,15788,15868,15952,16014,16078,16141,16210,16310,16405,16497,16589,16647,16702,16962,17294,17369,17523"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,378,476,563,660,759,848,938,1021,1106,1185,1260,1335,1409,1484,1550", "endColumns": "94,84,92,97,86,96,98,88,89,82,84,78,74,74,73,74,65,117", "endOffsets": "195,280,373,471,558,655,754,843,933,1016,1101,1180,1255,1330,1404,1479,1545,1663"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,193,194,195", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4526,4621,4940,5033,5131,5817,5914,16707,16796,16967,17050,17374,17528,17603,17678,17853,17928,17994", "endColumns": "94,84,92,97,86,96,98,88,89,82,84,78,74,74,73,74,65,117", "endOffsets": "4616,4701,5028,5126,5213,5909,6008,16791,16881,17045,17130,17448,17598,17673,17747,17923,17989,18107"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3472,3573,3676,3784,3889,3993,4093,17752", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "3568,3671,3779,3884,3988,4088,4217,17848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,87", "endOffsets": "135,223"}, "to": {"startLines": "196,197", "startColumns": "4,4", "startOffsets": "18112,18197", "endColumns": "84,87", "endOffsets": "18192,18280"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,615,735,812,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1910,2023,2131,2234,2343,2459,2579,2746,2848", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,610,730,807,882,973,1066,1161,1255,1355,1448,1543,1637,1728,1819,1905,2018,2126,2229,2338,2454,2574,2741,2843,2926"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,430,529,636,727,832,952,1029,1104,1195,1288,1383,1477,1577,1670,1765,1859,1950,2041,2127,2240,2348,2451,2560,2676,2796,2963,17135", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "425,524,631,722,827,947,1024,1099,1190,1283,1378,1472,1572,1665,1760,1854,1945,2036,2122,2235,2343,2446,2555,2671,2791,2958,3060,17213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-as\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "121", "endOffsets": "316"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4706", "endColumns": "125", "endOffsets": "4827"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,407,525,621,715,826,970,1091,1233,1318,1416,1511,1610,1726,1854,1957,2088,2218,2347,2527,2647,2765,2889,3022,3118,3214,3335,3461,3558,3668,3776,3912,4056,4166,4268,4345,4446,4547,4638,4730,4839,4919,5004,5105,5210,5308,5410,5497,5604,5703,5807,5928,6008,6111", "endColumns": "118,119,112,117,95,93,110,143,120,141,84,97,94,98,115,127,102,130,129,128,179,119,117,123,132,95,95,120,125,96,109,107,135,143,109,101,76,100,100,90,91,108,79,84,100,104,97,101,86,106,98,103,120,79,102,93", "endOffsets": "169,289,402,520,616,710,821,965,1086,1228,1313,1411,1506,1605,1721,1849,1952,2083,2213,2342,2522,2642,2760,2884,3017,3113,3209,3330,3456,3553,3663,3771,3907,4051,4161,4263,4340,4441,4542,4633,4725,4834,4914,4999,5100,5205,5303,5405,5492,5599,5698,5802,5923,6003,6106,6200"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6072,6191,6311,6424,6542,6638,6732,6843,6987,7108,7250,7335,7433,7528,7627,7743,7871,7974,8105,8235,8364,8544,8664,8782,8906,9039,9135,9231,9352,9478,9575,9685,9793,9929,10073,10183,10285,10362,10463,10564,10655,10747,10856,10936,11021,11122,11227,11325,11427,11514,11621,11720,11824,11945,12025,12128", "endColumns": "118,119,112,117,95,93,110,143,120,141,84,97,94,98,115,127,102,130,129,128,179,119,117,123,132,95,95,120,125,96,109,107,135,143,109,101,76,100,100,90,91,108,79,84,100,104,97,101,86,106,98,103,120,79,102,93", "endOffsets": "6186,6306,6419,6537,6633,6727,6838,6982,7103,7245,7330,7428,7523,7622,7738,7866,7969,8100,8230,8359,8539,8659,8777,8901,9034,9130,9226,9347,9473,9570,9680,9788,9924,10068,10178,10280,10357,10458,10559,10650,10742,10851,10931,11016,11117,11222,11320,11422,11509,11616,11715,11819,11940,12020,12123,12217"}}]}]}