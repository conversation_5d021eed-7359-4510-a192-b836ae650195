package com.thedasagroup.suminative.ui.stock;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u0000 \'2\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0002&\'B3\b\u0007\u0012\b\b\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0004\b\f\u0010\rJ\u001a\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190\u00180\u0017H\u0086@\u00a2\u0006\u0002\u0010\u001aJ\"\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190\u00180\u00172\u0006\u0010\u001c\u001a\u00020\u001dH\u0086@\u00a2\u0006\u0002\u0010\u001eJ\u000e\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\"J\u0010\u0010#\u001a\u00020 2\b\u0010$\u001a\u0004\u0018\u00010%R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015\u00a8\u0006("}, d2 = {"Lcom/thedasagroup/suminative/ui/stock/StockScreenViewModel;", "Lcom/airbnb/mvrx/MavericksViewModel;", "Lcom/thedasagroup/suminative/ui/stock/StockScreenState;", "state", "prefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "stockUseCase", "Lcom/thedasagroup/suminative/ui/stock/StockUseCase;", "changeStockUseCase", "Lcom/thedasagroup/suminative/ui/stock/ChangeStockUseCase;", "stockRepository", "Lcom/thedasagroup/suminative/data/repo/StockRepository;", "<init>", "(Lcom/thedasagroup/suminative/ui/stock/StockScreenState;Lcom/thedasagroup/suminative/data/prefs/Prefs;Lcom/thedasagroup/suminative/ui/stock/StockUseCase;Lcom/thedasagroup/suminative/ui/stock/ChangeStockUseCase;Lcom/thedasagroup/suminative/data/repo/StockRepository;)V", "getPrefs", "()Lcom/thedasagroup/suminative/data/prefs/Prefs;", "getStockUseCase", "()Lcom/thedasagroup/suminative/ui/stock/StockUseCase;", "getChangeStockUseCase", "()Lcom/thedasagroup/suminative/ui/stock/ChangeStockUseCase;", "getStockRepository", "()Lcom/thedasagroup/suminative/data/repo/StockRepository;", "getStockItems", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/stock/StockItemsResponse;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "changeStock", "request", "Lcom/thedasagroup/suminative/data/model/request/stock/ChangeStockRequest;", "(Lcom/thedasagroup/suminative/data/model/request/stock/ChangeStockRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateStock", "", "stock", "", "showUpdateStockDialog", "stockItem", "Lcom/thedasagroup/suminative/data/model/response/stock/StockItem;", "Factory", "Companion", "app_stagingGeneralDebug"})
public final class StockScreenViewModel extends com.airbnb.mvrx.MavericksViewModel<com.thedasagroup.suminative.ui.stock.StockScreenState> {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.prefs.Prefs prefs = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.stock.StockUseCase stockUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.stock.ChangeStockUseCase changeStockUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.repo.StockRepository stockRepository = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.stock.StockScreenViewModel.Companion Companion = null;
    
    @dagger.assisted.AssistedInject()
    public StockScreenViewModel(@dagger.assisted.Assisted()
    @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.stock.StockScreenState state, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.stock.StockUseCase stockUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.stock.ChangeStockUseCase changeStockUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.StockRepository stockRepository) {
        super(null, null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.prefs.Prefs getPrefs() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.stock.StockUseCase getStockUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.stock.ChangeStockUseCase getChangeStockUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.repo.StockRepository getStockRepository() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getStockItems(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object changeStock(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.stock.ChangeStockRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse>>> $completion) {
        return null;
    }
    
    public final void updateStock(int stock) {
    }
    
    public final void showUpdateStockDialog(@org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.stock.StockItem stockItem) {
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u001b\u0010\u0006\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0003H\u0096\u0001J\u0013\u0010\n\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0007\u001a\u00020\bH\u0096\u0001\u00a8\u0006\u000b"}, d2 = {"Lcom/thedasagroup/suminative/ui/stock/StockScreenViewModel$Companion;", "Lcom/airbnb/mvrx/MavericksViewModelFactory;", "Lcom/thedasagroup/suminative/ui/stock/StockScreenViewModel;", "Lcom/thedasagroup/suminative/ui/stock/StockScreenState;", "<init>", "()V", "create", "viewModelContext", "Lcom/airbnb/mvrx/ViewModelContext;", "state", "initialState", "app_stagingGeneralDebug"})
    public static final class Companion implements com.airbnb.mvrx.MavericksViewModelFactory<com.thedasagroup.suminative.ui.stock.StockScreenViewModel, com.thedasagroup.suminative.ui.stock.StockScreenState> {
        
        private Companion() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.stock.StockScreenViewModel create(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.stock.StockScreenState state) {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.stock.StockScreenState initialState(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext) {
            return null;
        }
    }
    
    @dagger.assisted.AssistedFactory()
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\bg\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001J\u0010\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0003H&\u00a8\u0006\u0006"}, d2 = {"Lcom/thedasagroup/suminative/ui/stock/StockScreenViewModel$Factory;", "Lcom/airbnb/mvrx/hilt/AssistedViewModelFactory;", "Lcom/thedasagroup/suminative/ui/stock/StockScreenViewModel;", "Lcom/thedasagroup/suminative/ui/stock/StockScreenState;", "create", "state", "app_stagingGeneralDebug"})
    public static abstract interface Factory extends com.airbnb.mvrx.hilt.AssistedViewModelFactory<com.thedasagroup.suminative.ui.stock.StockScreenViewModel, com.thedasagroup.suminative.ui.stock.StockScreenState> {
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public abstract com.thedasagroup.suminative.ui.stock.StockScreenViewModel create(@org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.stock.StockScreenState state);
    }
}