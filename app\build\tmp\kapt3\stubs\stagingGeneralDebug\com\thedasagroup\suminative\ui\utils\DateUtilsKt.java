package com.thedasagroup.suminative.ui.utils;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000(\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0010\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\t\n\u0002\b\b\n\u0002\u0010\b\n\u0002\b\u0006\u001a\u0012\u0010\u000e\u001a\u00020\u0001*\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u0001\u001a\u0012\u0010\u0010\u001a\u00020\u0001*\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u0001\u001a\u0012\u0010\u0011\u001a\u00020\u0012*\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u0001\u001a\n\u0010\u0013\u001a\u00020\u0012*\u00020\u0012\u001a\n\u0010\u0014\u001a\u00020\u0001*\u00020\u0015\u001a\u0012\u0010\u000e\u001a\u00020\u0001*\u00020\u00122\u0006\u0010\u000f\u001a\u00020\u0001\u001a&\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u00012\u0006\u0010\u0019\u001a\u00020\u00012\u0006\u0010\u001a\u001a\u00020\u00012\u0006\u0010\u001b\u001a\u00020\u0001\u001a\u0016\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u001c\u001a\u00020\u00122\u0006\u0010\u001d\u001a\u00020\u0012\u001a\u001e\u0010\u001e\u001a\u00020\u00122\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020 2\u0006\u0010\"\u001a\u00020\u0012\u001a\n\u0010#\u001a\u00020\u0001*\u00020\u0012\u001a\u000e\u0010$\u001a\u00020\u00012\u0006\u0010%\u001a\u00020\u0012\"\u0014\u0010\u0000\u001a\u00020\u0001X\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0003\"\u0014\u0010\u0004\u001a\u00020\u0001X\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0003\"\u0014\u0010\u0006\u001a\u00020\u0001X\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\u0003\"\u0014\u0010\b\u001a\u00020\u0001X\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0003\"\u0014\u0010\n\u001a\u00020\u0001X\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\u0003\"\u0014\u0010\f\u001a\u00020\u0001X\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u0003\u00a8\u0006&"}, d2 = {"DATE_FORMAT_BACK_END", "", "getDATE_FORMAT_BACK_END", "()Ljava/lang/String;", "DATE_FORMAT_BACK_END2", "getDATE_FORMAT_BACK_END2", "DATE_FORMAT_APP", "getDATE_FORMAT_APP", "DATE_FORMAT_DATE_ONLY", "getDATE_FORMAT_DATE_ONLY", "DATE_FORMAT_RESERVATIONS", "getDATE_FORMAT_RESERVATIONS", "DATE_FORMAT_GUAVA", "getDATE_FORMAT_GUAVA", "formatDate", "format", "formatDatePrint", "toDate", "Ljava/util/Date;", "toGMT", "transformDecimal", "", "getMinutesBetweenTwoDates", "", "startDateString", "endDateString", "formatStart", "formatEnd", "startDate", "endDate", "getDateFromHourAndMinute", "hour", "", "minute", "nowDate", "getDayOfWeek", "formatDateToHourAndMinute", "date", "app_stagingGeneralDebug"})
public final class DateUtilsKt {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String DATE_FORMAT_BACK_END = "yyyy-MM-dd\'T\'HH:mm:ss\'Z\'";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String DATE_FORMAT_BACK_END2 = "yyyy-MM-dd\'T\'HH:mm:ss.S\'Z\'";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String DATE_FORMAT_APP = "yyyy-MM-dd HH:mm:ss";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String DATE_FORMAT_DATE_ONLY = "yyyy-MM-dd";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String DATE_FORMAT_RESERVATIONS = "yyyy-MM-dd\'T\'HH:mm:ss";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String DATE_FORMAT_GUAVA = "yyyy-MM-dd\'T\'HH:mm:ss.SSS\'Z\'";
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getDATE_FORMAT_BACK_END() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getDATE_FORMAT_BACK_END2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getDATE_FORMAT_APP() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getDATE_FORMAT_DATE_ONLY() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getDATE_FORMAT_RESERVATIONS() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getDATE_FORMAT_GUAVA() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String formatDate(@org.jetbrains.annotations.NotNull()
    java.lang.String $this$formatDate, @org.jetbrains.annotations.NotNull()
    java.lang.String format) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String formatDatePrint(@org.jetbrains.annotations.NotNull()
    java.lang.String $this$formatDatePrint, @org.jetbrains.annotations.NotNull()
    java.lang.String format) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.Date toDate(@org.jetbrains.annotations.NotNull()
    java.lang.String $this$toDate, @org.jetbrains.annotations.NotNull()
    java.lang.String format) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.Date toGMT(@org.jetbrains.annotations.NotNull()
    java.util.Date $this$toGMT) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String transformDecimal(double $this$transformDecimal) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String formatDate(@org.jetbrains.annotations.NotNull()
    java.util.Date $this$formatDate, @org.jetbrains.annotations.NotNull()
    java.lang.String format) {
        return null;
    }
    
    public static final long getMinutesBetweenTwoDates(@org.jetbrains.annotations.NotNull()
    java.lang.String startDateString, @org.jetbrains.annotations.NotNull()
    java.lang.String endDateString, @org.jetbrains.annotations.NotNull()
    java.lang.String formatStart, @org.jetbrains.annotations.NotNull()
    java.lang.String formatEnd) {
        return 0L;
    }
    
    public static final long getMinutesBetweenTwoDates(@org.jetbrains.annotations.NotNull()
    java.util.Date startDate, @org.jetbrains.annotations.NotNull()
    java.util.Date endDate) {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.Date getDateFromHourAndMinute(int hour, int minute, @org.jetbrains.annotations.NotNull()
    java.util.Date nowDate) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getDayOfWeek(@org.jetbrains.annotations.NotNull()
    java.util.Date $this$getDayOfWeek) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String formatDateToHourAndMinute(@org.jetbrains.annotations.NotNull()
    java.util.Date date) {
        return null;
    }
}