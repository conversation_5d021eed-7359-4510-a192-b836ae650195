{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,89", "endOffsets": "140,230"}, "to": {"startLines": "543,544", "startColumns": "4,4", "startOffsets": "56334,56424", "endColumns": "89,89", "endOffsets": "56419,56509"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-lv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "161", "endOffsets": "356"}, "to": {"startLines": "51", "startColumns": "4", "startOffsets": "4940", "endColumns": "165", "endOffsets": "5101"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,215,349,425,506,621,762,939,1068,1186,1283,1405,1466,1526,1620,1725,1792,1923,2013,2093,2225,2300,2397,2489,2623,2723,2834,2928,3005,3078,3199,3291,3496,3685,3796,3974,4089,4281,4386,4531,4641,4779,4887,5036,5141,5241,5387,5479,5625,5724,5876,5962,6087,6164,6286,6392,6463,6552,6621,6694,6772,6848,6983,7085,7237,7346,7466,7562,7725,7830,7930,8051,8145,8255,8366,8477,8582,8640,8692,8747,8800,8850,8899,8977,9027,9094,9143,9187,9259,9305,9360,9415,9475,9536,9586,9637,9714,9765,9817,9862,9925,9983,10047,10176,10279,10358,10419,10502,10571,10639,10767,10843,10919,10999,11077,11148,11250,11325,11421,11505,11636,11714,11806,11907,12004,12097,12177,12296,12411,12527,12637,12719,12817,12886,12972,13105,13267,13344,13421,13513,13613,15664,15779,15962,16008,16143,16294,16389,16535,16626,16760,16924,17057,17236,17309,17395,17464,17569,17688,17737,17816,17931,18028,18154,18286,18433,18544,18653,18717,18776,18833,18891,18955,19034,19128,19262,19335,19409,19671,19886,20011,20131,20258,20352,20463,20586,20738,20896,20991,21103,21191,21317,21414,21560,21652,21723,21790,22016,22110,22191,22254,22316,22431,22547,22648,22800,22965,23041,23107,23168,23342,23420,23504,23581,23675,23828,23924,23999,24119,24213,24361,24489,24554,24678,24751,24828,24889,24964,25013,25076,25162,25214,25288,25458,25585,25694,25758,25909,26055,26184,26302,26556,26665,26765,26867,27049,27258,27346,27554,27682,27831,27949,28025,28112,28197,28318,28493,28632,28679,28760,28885,28994,29085,29154,29244,29320,29395,29465,29526,29604,29674,29751,29839,29906,29974,30070,30148,30231,30434,30573,30657,30716,30798,30965,31033,31112,31199,31276,31360,31590,31645,31717,31807,31877,31985,32174,32263,32335,32420,32511,32581,32661,32749,32889,32974,33029,33114,33191,33270,33354,33464,33564,33679,33795,33871,33926,33980,34080,34185,34271,34369,34540,34646,34731,34851,34964,35110,35213,35309,35470,35592,35771,35936,36064,36187,36278,36363,36476,36595,36690,36832,36907,37352,37435,37521,37608", "endColumns": "159,133,75,80,114,140,176,128,117,96,121,60,59,93,104,66,130,89,79,131,74,96,91,133,99,110,93,76,72,120,91,204,188,110,177,114,191,104,144,109,137,107,148,104,99,145,91,145,98,151,85,124,76,121,105,70,88,68,72,77,75,134,101,151,108,119,95,162,104,99,120,93,109,110,110,104,57,51,54,52,49,48,77,49,66,48,43,71,45,54,54,59,60,49,50,76,50,51,44,62,57,63,128,102,78,60,82,68,67,127,75,75,79,77,70,101,74,95,83,130,77,91,100,96,92,79,118,114,115,109,81,97,68,85,132,161,76,76,91,99,2050,114,182,45,134,150,94,145,90,133,163,132,178,72,85,68,104,118,48,78,114,96,125,131,146,110,108,63,58,56,57,63,78,93,133,72,73,261,214,124,119,126,93,110,122,151,157,94,111,87,125,96,145,91,70,66,225,93,80,62,61,114,115,100,151,164,75,65,60,173,77,83,76,93,152,95,74,119,93,147,127,64,123,72,76,60,74,48,62,85,51,73,169,126,108,63,150,145,128,117,253,108,99,101,181,208,87,207,127,148,117,75,86,84,120,174,138,46,80,124,108,90,68,89,75,74,69,60,77,69,76,87,66,67,95,77,82,202,138,83,58,81,166,67,78,86,76,83,229,54,71,89,69,107,188,88,71,84,90,69,79,87,139,84,54,84,76,78,83,109,99,114,115,75,54,53,99,104,85,97,170,105,84,119,112,145,102,95,160,121,178,164,127,122,90,84,112,118,94,141,74,444,82,85,86,76", "endOffsets": "210,344,420,501,616,757,934,1063,1181,1278,1400,1461,1521,1615,1720,1787,1918,2008,2088,2220,2295,2392,2484,2618,2718,2829,2923,3000,3073,3194,3286,3491,3680,3791,3969,4084,4276,4381,4526,4636,4774,4882,5031,5136,5236,5382,5474,5620,5719,5871,5957,6082,6159,6281,6387,6458,6547,6616,6689,6767,6843,6978,7080,7232,7341,7461,7557,7720,7825,7925,8046,8140,8250,8361,8472,8577,8635,8687,8742,8795,8845,8894,8972,9022,9089,9138,9182,9254,9300,9355,9410,9470,9531,9581,9632,9709,9760,9812,9857,9920,9978,10042,10171,10274,10353,10414,10497,10566,10634,10762,10838,10914,10994,11072,11143,11245,11320,11416,11500,11631,11709,11801,11902,11999,12092,12172,12291,12406,12522,12632,12714,12812,12881,12967,13100,13262,13339,13416,13508,13608,15659,15774,15957,16003,16138,16289,16384,16530,16621,16755,16919,17052,17231,17304,17390,17459,17564,17683,17732,17811,17926,18023,18149,18281,18428,18539,18648,18712,18771,18828,18886,18950,19029,19123,19257,19330,19404,19666,19881,20006,20126,20253,20347,20458,20581,20733,20891,20986,21098,21186,21312,21409,21555,21647,21718,21785,22011,22105,22186,22249,22311,22426,22542,22643,22795,22960,23036,23102,23163,23337,23415,23499,23576,23670,23823,23919,23994,24114,24208,24356,24484,24549,24673,24746,24823,24884,24959,25008,25071,25157,25209,25283,25453,25580,25689,25753,25904,26050,26179,26297,26551,26660,26760,26862,27044,27253,27341,27549,27677,27826,27944,28020,28107,28192,28313,28488,28627,28674,28755,28880,28989,29080,29149,29239,29315,29390,29460,29521,29599,29669,29746,29834,29901,29969,30065,30143,30226,30429,30568,30652,30711,30793,30960,31028,31107,31194,31271,31355,31585,31640,31712,31802,31872,31980,32169,32258,32330,32415,32506,32576,32656,32744,32884,32969,33024,33109,33186,33265,33349,33459,33559,33674,33790,33866,33921,33975,34075,34180,34266,34364,34535,34641,34726,34846,34959,35105,35208,35304,35465,35587,35766,35931,36059,36182,36273,36358,36471,36590,36685,36827,36902,37347,37430,37516,37603,37680"}, "to": {"startLines": "194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18436,18596,18730,18806,18887,19002,19143,19320,19449,19567,19664,19786,19847,19907,20001,20106,20173,20304,20394,20474,20606,20681,20778,20870,21004,21104,21215,21309,21386,21459,21580,21672,21877,22066,22177,22355,22470,22662,22767,22912,23022,23160,23268,23417,23522,23622,23768,23860,24006,24105,24257,24343,24468,24545,24667,24773,24844,24933,25002,25075,25153,25229,25364,25466,25618,25727,25847,25943,26106,26211,26311,26432,26526,26636,26747,26858,26963,27021,27073,27128,27181,27231,27280,27358,27408,27475,27524,27568,27640,27686,27741,27796,27856,27917,27967,28018,28095,28146,28198,28243,28306,28364,28428,28557,28660,28739,28800,28883,28952,29020,29148,29224,29300,29380,29458,29529,29631,29706,29802,29886,30017,30095,30187,30288,30385,30478,30558,30677,30792,30908,31018,31100,31198,31267,31353,31486,31648,31725,31802,31894,31994,34045,34160,34343,34389,34524,34675,34770,34916,35007,35141,35305,35438,35617,35690,35776,35845,35950,36069,36118,36197,36312,36409,36535,36667,36814,36925,37034,37098,37157,37214,37272,37336,37415,37509,37643,37716,37790,38052,38267,38392,38512,38639,38733,38844,38967,39119,39277,39372,39484,39572,39698,39795,39941,40033,40104,40171,40397,40491,40572,40635,40697,40812,40928,41029,41181,41346,41422,41488,41549,41723,41801,41885,41962,42056,42209,42305,42380,42500,42594,42742,42870,42935,43059,43132,43209,43270,43345,43394,43457,43543,43595,43669,43839,43966,44075,44139,44290,44436,44565,44683,44937,45046,45146,45248,45430,45639,45727,45935,46063,46212,46330,46406,46493,46578,46699,46874,47013,47060,47141,47266,47375,47466,47535,47625,47701,47776,47846,47907,47985,48055,48132,48220,48287,48355,48451,48529,48612,48815,48954,49038,49097,49179,49346,49414,49493,49580,49657,49741,49971,50026,50098,50188,50258,50366,50555,50644,50716,50801,50892,50962,51042,51130,51270,51355,51410,51495,51572,51651,51735,51845,51945,52060,52176,52252,52307,52361,52461,52566,52652,52750,52921,53027,53112,53232,53345,53491,53594,53690,53851,53973,54152,54317,54445,54568,54659,54744,54857,54976,55071,55213,55288,55733,55816,55902,55989", "endColumns": "159,133,75,80,114,140,176,128,117,96,121,60,59,93,104,66,130,89,79,131,74,96,91,133,99,110,93,76,72,120,91,204,188,110,177,114,191,104,144,109,137,107,148,104,99,145,91,145,98,151,85,124,76,121,105,70,88,68,72,77,75,134,101,151,108,119,95,162,104,99,120,93,109,110,110,104,57,51,54,52,49,48,77,49,66,48,43,71,45,54,54,59,60,49,50,76,50,51,44,62,57,63,128,102,78,60,82,68,67,127,75,75,79,77,70,101,74,95,83,130,77,91,100,96,92,79,118,114,115,109,81,97,68,85,132,161,76,76,91,99,2050,114,182,45,134,150,94,145,90,133,163,132,178,72,85,68,104,118,48,78,114,96,125,131,146,110,108,63,58,56,57,63,78,93,133,72,73,261,214,124,119,126,93,110,122,151,157,94,111,87,125,96,145,91,70,66,225,93,80,62,61,114,115,100,151,164,75,65,60,173,77,83,76,93,152,95,74,119,93,147,127,64,123,72,76,60,74,48,62,85,51,73,169,126,108,63,150,145,128,117,253,108,99,101,181,208,87,207,127,148,117,75,86,84,120,174,138,46,80,124,108,90,68,89,75,74,69,60,77,69,76,87,66,67,95,77,82,202,138,83,58,81,166,67,78,86,76,83,229,54,71,89,69,107,188,88,71,84,90,69,79,87,139,84,54,84,76,78,83,109,99,114,115,75,54,53,99,104,85,97,170,105,84,119,112,145,102,95,160,121,178,164,127,122,90,84,112,118,94,141,74,444,82,85,86,76", "endOffsets": "18591,18725,18801,18882,18997,19138,19315,19444,19562,19659,19781,19842,19902,19996,20101,20168,20299,20389,20469,20601,20676,20773,20865,20999,21099,21210,21304,21381,21454,21575,21667,21872,22061,22172,22350,22465,22657,22762,22907,23017,23155,23263,23412,23517,23617,23763,23855,24001,24100,24252,24338,24463,24540,24662,24768,24839,24928,24997,25070,25148,25224,25359,25461,25613,25722,25842,25938,26101,26206,26306,26427,26521,26631,26742,26853,26958,27016,27068,27123,27176,27226,27275,27353,27403,27470,27519,27563,27635,27681,27736,27791,27851,27912,27962,28013,28090,28141,28193,28238,28301,28359,28423,28552,28655,28734,28795,28878,28947,29015,29143,29219,29295,29375,29453,29524,29626,29701,29797,29881,30012,30090,30182,30283,30380,30473,30553,30672,30787,30903,31013,31095,31193,31262,31348,31481,31643,31720,31797,31889,31989,34040,34155,34338,34384,34519,34670,34765,34911,35002,35136,35300,35433,35612,35685,35771,35840,35945,36064,36113,36192,36307,36404,36530,36662,36809,36920,37029,37093,37152,37209,37267,37331,37410,37504,37638,37711,37785,38047,38262,38387,38507,38634,38728,38839,38962,39114,39272,39367,39479,39567,39693,39790,39936,40028,40099,40166,40392,40486,40567,40630,40692,40807,40923,41024,41176,41341,41417,41483,41544,41718,41796,41880,41957,42051,42204,42300,42375,42495,42589,42737,42865,42930,43054,43127,43204,43265,43340,43389,43452,43538,43590,43664,43834,43961,44070,44134,44285,44431,44560,44678,44932,45041,45141,45243,45425,45634,45722,45930,46058,46207,46325,46401,46488,46573,46694,46869,47008,47055,47136,47261,47370,47461,47530,47620,47696,47771,47841,47902,47980,48050,48127,48215,48282,48350,48446,48524,48607,48810,48949,49033,49092,49174,49341,49409,49488,49575,49652,49736,49966,50021,50093,50183,50253,50361,50550,50639,50711,50796,50887,50957,51037,51125,51265,51350,51405,51490,51567,51646,51730,51840,51940,52055,52171,52247,52302,52356,52456,52561,52647,52745,52916,53022,53107,53227,53340,53486,53589,53685,53846,53968,54147,54312,54440,54563,54654,54739,54852,54971,55066,55208,55283,55728,55811,55897,55984,56061"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,411,496,577,682,770,871,1005,1088,1149,1214,1308,1381,1442,1567,1633,1701,1762,1834,1894,1948,2068,2128,2190,2244,2321,2451,2538,2615,2705,2788,2870,3011,3091,3176,3303,3394,3470,3524,3577,3643,3717,3798,3869,3949,4022,4099,4176,4250,4360,4453,4528,4618,4709,4781,4859,4950,5004,5087,5155,5239,5326,5388,5452,5515,5587,5697,5810,5913,6022,6080,6137,6214,6299,6377", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,86,84,80,104,87,100,133,82,60,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,76,89,82,81,140,79,84,126,90,75,53,52,65,73,80,70,79,72,76,76,73,109,92,74,89,90,71,77,90,53,82,67,83,86,61,63,62,71,109,112,102,108,57,56,76,84,77,73", "endOffsets": "319,406,491,572,677,765,866,1000,1083,1144,1209,1303,1376,1437,1562,1628,1696,1757,1829,1889,1943,2063,2123,2185,2239,2316,2446,2533,2610,2700,2783,2865,3006,3086,3171,3298,3389,3465,3519,3572,3638,3712,3793,3864,3944,4017,4094,4171,4245,4355,4448,4523,4613,4704,4776,4854,4945,4999,5082,5150,5234,5321,5383,5447,5510,5582,5692,5805,5908,6017,6075,6132,6209,6294,6372,6446"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,56,57,58,62,65,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,182,186,187,189", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3258,3345,3430,3511,3616,4435,4536,4670,5498,5559,5624,6035,6282,12592,12717,12783,12851,12912,12984,13044,13098,13218,13278,13340,13394,13471,13601,13688,13765,13855,13938,14020,14161,14241,14326,14453,14544,14620,14674,14727,14793,14867,14948,15019,15099,15172,15249,15326,15400,15510,15603,15678,15768,15859,15931,16009,16100,16154,16237,16305,16389,16476,16538,16602,16665,16737,16847,16960,17063,17172,17230,17464,17796,17881,18032", "endLines": "6,34,35,36,37,38,46,47,48,56,57,58,62,65,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,182,186,187,189", "endColumns": "12,86,84,80,104,87,100,133,82,60,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,76,89,82,81,140,79,84,126,90,75,53,52,65,73,80,70,79,72,76,76,73,109,92,74,89,90,71,77,90,53,82,67,83,86,61,63,62,71,109,112,102,108,57,56,76,84,77,73", "endOffsets": "369,3340,3425,3506,3611,3699,4531,4665,4748,5554,5619,5713,6103,6338,12712,12778,12846,12907,12979,13039,13093,13213,13273,13335,13389,13466,13596,13683,13760,13850,13933,14015,14156,14236,14321,14448,14539,14615,14669,14722,14788,14862,14943,15014,15094,15167,15244,15321,15395,15505,15598,15673,15763,15854,15926,16004,16095,16149,16232,16300,16384,16471,16533,16597,16660,16732,16842,16955,17058,17167,17225,17282,17536,17876,17954,18101"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "374,494,604,713,799,903,1025,1107,1187,1297,1405,1511,1620,1731,1834,1946,2053,2158,2258,2343,2452,2563,2662,2773,2880,2985,3159,17713", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "489,599,708,794,898,1020,1102,1182,1292,1400,1506,1615,1726,1829,1941,2048,2153,2253,2338,2447,2558,2657,2768,2875,2980,3154,3253,17791"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,309,424,549,658,758,875,1013,1131,1278,1364,1462,1556,1657,1776,1900,2003,2141,2272,2410,2593,2725,2844,2971,3091,3186,3285,3406,3541,3643,3757,3863,3998,4143,4252,4355,4438,4533,4627,4717,4804,4915,4995,5081,5176,5280,5371,5469,5558,5665,5767,5867,6020,6100,6205", "endColumns": "126,126,114,124,108,99,116,137,117,146,85,97,93,100,118,123,102,137,130,137,182,131,118,126,119,94,98,120,134,101,113,105,134,144,108,102,82,94,93,89,86,110,79,85,94,103,90,97,88,106,101,99,152,79,104,98", "endOffsets": "177,304,419,544,653,753,870,1008,1126,1273,1359,1457,1551,1652,1771,1895,1998,2136,2267,2405,2588,2720,2839,2966,3086,3181,3280,3401,3536,3638,3752,3858,3993,4138,4247,4350,4433,4528,4622,4712,4799,4910,4990,5076,5171,5275,5366,5464,5553,5660,5762,5862,6015,6095,6200,6299"}, "to": {"startLines": "66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6343,6470,6597,6712,6837,6946,7046,7163,7301,7419,7566,7652,7750,7844,7945,8064,8188,8291,8429,8560,8698,8881,9013,9132,9259,9379,9474,9573,9694,9829,9931,10045,10151,10286,10431,10540,10643,10726,10821,10915,11005,11092,11203,11283,11369,11464,11568,11659,11757,11846,11953,12055,12155,12308,12388,12493", "endColumns": "126,126,114,124,108,99,116,137,117,146,85,97,93,100,118,123,102,137,130,137,182,131,118,126,119,94,98,120,134,101,113,105,134,144,108,102,82,94,93,89,86,110,79,85,94,103,90,97,88,106,101,99,152,79,104,98", "endOffsets": "6465,6592,6707,6832,6941,7041,7158,7296,7414,7561,7647,7745,7839,7940,8059,8183,8286,8424,8555,8693,8876,9008,9127,9254,9374,9469,9568,9689,9824,9926,10040,10146,10281,10426,10535,10638,10721,10816,10910,11000,11087,11198,11278,11364,11459,11563,11654,11752,11841,11948,12050,12150,12303,12383,12488,12587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "39,40,41,42,43,44,45,193", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3704,3802,3904,4004,4105,4212,4320,18335", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "3797,3899,3999,4100,4207,4315,4430,18431"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,373", "endColumns": "102,98,115,101", "endOffsets": "153,252,368,470"}, "to": {"startLines": "52,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "5106,5718,5817,5933", "endColumns": "102,98,115,101", "endOffsets": "5204,5812,5928,6030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,292,388,491,581,667,755,848,932,1017,1104,1177,1253,1330,1406,1484,1552", "endColumns": "97,88,95,102,89,85,87,92,83,84,86,72,75,76,75,77,67,121", "endOffsets": "198,287,383,486,576,662,750,843,927,1012,1099,1172,1248,1325,1401,1479,1547,1669"}, "to": {"startLines": "49,50,53,54,55,63,64,180,181,183,184,188,190,191,192,540,541,542", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4753,4851,5209,5305,5408,6108,6194,17287,17380,17541,17626,17959,18106,18182,18259,56066,56144,56212", "endColumns": "97,88,95,102,89,85,87,92,83,84,86,72,75,76,75,77,67,121", "endOffsets": "4846,4935,5300,5403,5493,6189,6277,17375,17459,17621,17708,18027,18177,18254,18330,56139,56207,56329"}}]}]}