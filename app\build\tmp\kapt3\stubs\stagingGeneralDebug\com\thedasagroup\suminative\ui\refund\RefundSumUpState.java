package com.thedasagroup.suminative.ui.refund;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\f\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\b\b\u0010\tJ\u000f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u000e\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0006H\u00c6\u0003J-\u0010\u0010\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u0006H\u00c6\u0001J\u0013\u0010\u0011\u001a\u00020\u00062\b\u0010\u0012\u001a\u0004\u0018\u00010\u0013H\u00d6\u0003J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\fR\u0011\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\f\u00a8\u0006\u0018"}, d2 = {"Lcom/thedasagroup/suminative/ui/refund/RefundSumUpState;", "Lcom/airbnb/mvrx/MavericksState;", "ordersResponse", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/request/pagination/OrderResponse;", "isLoading", "", "isRefreshing", "<init>", "(Lcom/airbnb/mvrx/Async;ZZ)V", "getOrdersResponse", "()Lcom/airbnb/mvrx/Async;", "()Z", "component1", "component2", "component3", "copy", "equals", "other", "", "hashCode", "", "toString", "", "app_stagingGeneralDebug"})
public final class RefundSumUpState implements com.airbnb.mvrx.MavericksState {
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> ordersResponse = null;
    private final boolean isLoading = false;
    private final boolean isRefreshing = false;
    
    public RefundSumUpState(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> ordersResponse, boolean isLoading, boolean isRefreshing) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> getOrdersResponse() {
        return null;
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    public final boolean isRefreshing() {
        return false;
    }
    
    public RefundSumUpState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> component1() {
        return null;
    }
    
    public final boolean component2() {
        return false;
    }
    
    public final boolean component3() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.refund.RefundSumUpState copy(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> ordersResponse, boolean isLoading, boolean isRefreshing) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}