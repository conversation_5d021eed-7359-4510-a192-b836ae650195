package com.thedasagroup.suminative.ui.common;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000\"\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\u001a6\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001a6\u0010\n\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001a6\u0010\u000b\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u00a8\u0006\f"}, d2 = {"SuccessDialog", "", "message", "", "isVisible", "", "onDismiss", "Lkotlin/Function0;", "autoDismissDelayMs", "", "FailureDialog", "CancelledDialog", "app_stagingGeneralDebug"})
public final class SuccessDialogKt {
    
    @androidx.compose.runtime.Composable()
    public static final void SuccessDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String message, boolean isVisible, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, long autoDismissDelayMs) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void FailureDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String message, boolean isVisible, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, long autoDismissDelayMs) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CancelledDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String message, boolean isVisible, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, long autoDismissDelayMs) {
    }
}