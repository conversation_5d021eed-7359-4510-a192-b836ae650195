package com.thedasagroup.suminative.ui.stock;

import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.StockRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class StockScreenViewModel_Factory {
  private final Provider<Prefs> prefsProvider;

  private final Provider<StockUseCase> stockUseCaseProvider;

  private final Provider<ChangeStockUseCase> changeStockUseCaseProvider;

  private final Provider<StockRepository> stockRepositoryProvider;

  public StockScreenViewModel_Factory(Provider<Prefs> prefsProvider,
      Provider<StockUseCase> stockUseCaseProvider,
      Provider<ChangeStockUseCase> changeStockUseCaseProvider,
      Provider<StockRepository> stockRepositoryProvider) {
    this.prefsProvider = prefsProvider;
    this.stockUseCaseProvider = stockUseCaseProvider;
    this.changeStockUseCaseProvider = changeStockUseCaseProvider;
    this.stockRepositoryProvider = stockRepositoryProvider;
  }

  public StockScreenViewModel get(StockScreenState state) {
    return newInstance(state, prefsProvider.get(), stockUseCaseProvider.get(), changeStockUseCaseProvider.get(), stockRepositoryProvider.get());
  }

  public static StockScreenViewModel_Factory create(Provider<Prefs> prefsProvider,
      Provider<StockUseCase> stockUseCaseProvider,
      Provider<ChangeStockUseCase> changeStockUseCaseProvider,
      Provider<StockRepository> stockRepositoryProvider) {
    return new StockScreenViewModel_Factory(prefsProvider, stockUseCaseProvider, changeStockUseCaseProvider, stockRepositoryProvider);
  }

  public static StockScreenViewModel newInstance(StockScreenState state, Prefs prefs,
      StockUseCase stockUseCase, ChangeStockUseCase changeStockUseCase,
      StockRepository stockRepository) {
    return new StockScreenViewModel(state, prefs, stockUseCase, changeStockUseCase, stockRepository);
  }
}
