package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.database.CategoryRepository;
import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.StockRepository;
import com.thedasagroup.suminative.ui.stock.CategorySortingHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class CategoryModule_ProvidesCategoriesHelperFactory implements Factory<CategorySortingHelper> {
  private final Provider<StockRepository> stockRepositoryProvider;

  private final Provider<CategoryRepository> categoryRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  public CategoryModule_ProvidesCategoriesHelperFactory(
      Provider<StockRepository> stockRepositoryProvider,
      Provider<CategoryRepository> categoryRepositoryProvider, Provider<Prefs> prefsProvider) {
    this.stockRepositoryProvider = stockRepositoryProvider;
    this.categoryRepositoryProvider = categoryRepositoryProvider;
    this.prefsProvider = prefsProvider;
  }

  @Override
  public CategorySortingHelper get() {
    return providesCategoriesHelper(stockRepositoryProvider.get(), categoryRepositoryProvider.get(), prefsProvider.get());
  }

  public static CategoryModule_ProvidesCategoriesHelperFactory create(
      Provider<StockRepository> stockRepositoryProvider,
      Provider<CategoryRepository> categoryRepositoryProvider, Provider<Prefs> prefsProvider) {
    return new CategoryModule_ProvidesCategoriesHelperFactory(stockRepositoryProvider, categoryRepositoryProvider, prefsProvider);
  }

  public static CategorySortingHelper providesCategoriesHelper(StockRepository stockRepository,
      CategoryRepository categoryRepository, Prefs prefs) {
    return Preconditions.checkNotNullFromProvides(CategoryModule.INSTANCE.providesCategoriesHelper(stockRepository, categoryRepository, prefs));
  }
}
