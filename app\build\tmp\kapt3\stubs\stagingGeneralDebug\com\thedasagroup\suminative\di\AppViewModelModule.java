package com.thedasagroup.suminative.di;

@dagger.Module()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\bg\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\n\u0012\u0002\b\u0003\u0012\u0002\b\u00030\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u0018\u0010\u0006\u001a\n\u0012\u0002\b\u0003\u0012\u0002\b\u00030\u00032\u0006\u0010\u0004\u001a\u00020\u0007H\'J\u0018\u0010\b\u001a\n\u0012\u0002\b\u0003\u0012\u0002\b\u00030\u00032\u0006\u0010\u0004\u001a\u00020\tH\'J\u0018\u0010\n\u001a\n\u0012\u0002\b\u0003\u0012\u0002\b\u00030\u00032\u0006\u0010\u0004\u001a\u00020\u000bH\'J\u0018\u0010\f\u001a\n\u0012\u0002\b\u0003\u0012\u0002\b\u00030\u00032\u0006\u0010\u0004\u001a\u00020\rH\'J\u0018\u0010\u000e\u001a\n\u0012\u0002\b\u0003\u0012\u0002\b\u00030\u00032\u0006\u0010\u0004\u001a\u00020\u000fH\'J\u0018\u0010\u0010\u001a\n\u0012\u0002\b\u0003\u0012\u0002\b\u00030\u00032\u0006\u0010\u0004\u001a\u00020\u0011H\'J\u0018\u0010\u0012\u001a\n\u0012\u0002\b\u0003\u0012\u0002\b\u00030\u00032\u0006\u0010\u0004\u001a\u00020\u0013H\'J\u0018\u0010\u0014\u001a\n\u0012\u0002\b\u0003\u0012\u0002\b\u00030\u00032\u0006\u0010\u0004\u001a\u00020\u0015H\'J\u0018\u0010\u0016\u001a\n\u0012\u0002\b\u0003\u0012\u0002\b\u00030\u00032\u0006\u0010\u0004\u001a\u00020\u0017H\'J\u0018\u0010\u0018\u001a\n\u0012\u0002\b\u0003\u0012\u0002\b\u00030\u00032\u0006\u0010\u0004\u001a\u00020\u0019H\'\u00a8\u0006\u001a"}, d2 = {"Lcom/thedasagroup/suminative/di/AppViewModelModule;", "", "loginViewModelFactory", "Lcom/airbnb/mvrx/hilt/AssistedViewModelFactory;", "factory", "Lcom/thedasagroup/suminative/ui/login/LoginScreenViewModel$Factory;", "orderScreenViewModelFactory", "Lcom/thedasagroup/suminative/ui/orders/OrderScreenViewModel$Factory;", "stockScreenViewModelFactory", "Lcom/thedasagroup/suminative/ui/stock/StockScreenViewModel$Factory;", "productsScreenViewModelFactory", "Lcom/thedasagroup/suminative/ui/products/ProductsScreenViewModel$Factory;", "downloadProductsViewModelFactory", "Lcom/thedasagroup/suminative/ui/products/DownloadProductsViewModel$Factory;", "guavaOrdersScreenViewModelFactory", "Lcom/thedasagroup/suminative/ui/guava_orders/GuavaOrdersViewModel$Factory;", "selectUserProfileViewModelFactory", "Lcom/thedasagroup/suminative/ui/user_profile/SelectUserProfileViewModel$Factory;", "localOrdersViewModelFactory", "Lcom/thedasagroup/suminative/ui/local_orders/LocalOrdersViewModel$Factory;", "commonViewModelFactory", "Lcom/thedasagroup/suminative/ui/common/CommonViewModel$Factory;", "reservationsViewModelFactory", "Lcom/thedasagroup/suminative/ui/reservations/ReservationsViewModel$Factory;", "refundSumUpViewModelFactory", "Lcom/thedasagroup/suminative/ui/refund/RefundSumUpViewModel$Factory;", "app_stagingGeneralDebug"})
@dagger.hilt.InstallIn(value = {com.airbnb.mvrx.hilt.MavericksViewModelComponent.class})
public abstract interface AppViewModelModule {
    
    @dagger.Binds()
    @dagger.multibindings.IntoMap()
    @com.airbnb.mvrx.hilt.ViewModelKey(value = com.thedasagroup.suminative.ui.login.LoginScreenViewModel.class)
    @org.jetbrains.annotations.NotNull()
    public abstract com.airbnb.mvrx.hilt.AssistedViewModelFactory<?, ?> loginViewModelFactory(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.login.LoginScreenViewModel.Factory factory);
    
    @dagger.Binds()
    @dagger.multibindings.IntoMap()
    @com.airbnb.mvrx.hilt.ViewModelKey(value = com.thedasagroup.suminative.ui.orders.OrderScreenViewModel.class)
    @org.jetbrains.annotations.NotNull()
    public abstract com.airbnb.mvrx.hilt.AssistedViewModelFactory<?, ?> orderScreenViewModelFactory(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.OrderScreenViewModel.Factory factory);
    
    @dagger.Binds()
    @dagger.multibindings.IntoMap()
    @com.airbnb.mvrx.hilt.ViewModelKey(value = com.thedasagroup.suminative.ui.stock.StockScreenViewModel.class)
    @org.jetbrains.annotations.NotNull()
    public abstract com.airbnb.mvrx.hilt.AssistedViewModelFactory<?, ?> stockScreenViewModelFactory(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.stock.StockScreenViewModel.Factory factory);
    
    @dagger.Binds()
    @dagger.multibindings.IntoMap()
    @com.airbnb.mvrx.hilt.ViewModelKey(value = com.thedasagroup.suminative.ui.products.ProductsScreenViewModel.class)
    @org.jetbrains.annotations.NotNull()
    public abstract com.airbnb.mvrx.hilt.AssistedViewModelFactory<?, ?> productsScreenViewModelFactory(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.ProductsScreenViewModel.Factory factory);
    
    @dagger.Binds()
    @dagger.multibindings.IntoMap()
    @com.airbnb.mvrx.hilt.ViewModelKey(value = com.thedasagroup.suminative.ui.products.DownloadProductsViewModel.class)
    @org.jetbrains.annotations.NotNull()
    public abstract com.airbnb.mvrx.hilt.AssistedViewModelFactory<?, ?> downloadProductsViewModelFactory(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.DownloadProductsViewModel.Factory factory);
    
    @dagger.Binds()
    @dagger.multibindings.IntoMap()
    @com.airbnb.mvrx.hilt.ViewModelKey(value = com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersViewModel.class)
    @org.jetbrains.annotations.NotNull()
    public abstract com.airbnb.mvrx.hilt.AssistedViewModelFactory<?, ?> guavaOrdersScreenViewModelFactory(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersViewModel.Factory factory);
    
    @dagger.Binds()
    @dagger.multibindings.IntoMap()
    @com.airbnb.mvrx.hilt.ViewModelKey(value = com.thedasagroup.suminative.ui.user_profile.SelectUserProfileViewModel.class)
    @org.jetbrains.annotations.NotNull()
    public abstract com.airbnb.mvrx.hilt.AssistedViewModelFactory<?, ?> selectUserProfileViewModelFactory(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.user_profile.SelectUserProfileViewModel.Factory factory);
    
    @dagger.Binds()
    @dagger.multibindings.IntoMap()
    @com.airbnb.mvrx.hilt.ViewModelKey(value = com.thedasagroup.suminative.ui.local_orders.LocalOrdersViewModel.class)
    @org.jetbrains.annotations.NotNull()
    public abstract com.airbnb.mvrx.hilt.AssistedViewModelFactory<?, ?> localOrdersViewModelFactory(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.local_orders.LocalOrdersViewModel.Factory factory);
    
    @dagger.Binds()
    @dagger.multibindings.IntoMap()
    @com.airbnb.mvrx.hilt.ViewModelKey(value = com.thedasagroup.suminative.ui.common.CommonViewModel.class)
    @org.jetbrains.annotations.NotNull()
    public abstract com.airbnb.mvrx.hilt.AssistedViewModelFactory<?, ?> commonViewModelFactory(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.common.CommonViewModel.Factory factory);
    
    @dagger.Binds()
    @dagger.multibindings.IntoMap()
    @com.airbnb.mvrx.hilt.ViewModelKey(value = com.thedasagroup.suminative.ui.reservations.ReservationsViewModel.class)
    @org.jetbrains.annotations.NotNull()
    public abstract com.airbnb.mvrx.hilt.AssistedViewModelFactory<?, ?> reservationsViewModelFactory(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.reservations.ReservationsViewModel.Factory factory);
    
    @dagger.Binds()
    @dagger.multibindings.IntoMap()
    @com.airbnb.mvrx.hilt.ViewModelKey(value = com.thedasagroup.suminative.ui.refund.RefundSumUpViewModel.class)
    @org.jetbrains.annotations.NotNull()
    public abstract com.airbnb.mvrx.hilt.AssistedViewModelFactory<?, ?> refundSumUpViewModelFactory(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.refund.RefundSumUpViewModel.Factory factory);
}