{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,365,457,538,640,720,818,940,1019,1078,1141,1233,1297,1357,1449,1514,1577,1639,1706,1770,1824,1929,1988,2049,2103,2172,2291,2374,2451,2541,2625,2709,2845,2924,3008,3130,3216,3294,3348,3399,3465,3534,3608,3679,3755,3827,3904,3975,4049,4160,4251,4330,4417,4505,4577,4651,4736,4787,4866,4933,5014,5098,5160,5224,5287,5355,5462,5561,5660,5755,5813,5868,5946,6027,6106", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,98,91,80,101,79,97,121,78,58,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,76,89,83,83,135,78,83,121,85,77,53,50,65,68,73,70,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77,80,78,87", "endOffsets": "261,360,452,533,635,715,813,935,1014,1073,1136,1228,1292,1352,1444,1509,1572,1634,1701,1765,1819,1924,1983,2044,2098,2167,2286,2369,2446,2536,2620,2704,2840,2919,3003,3125,3211,3289,3343,3394,3460,3529,3603,3674,3750,3822,3899,3970,4044,4155,4246,4325,4412,4500,4572,4646,4731,4782,4861,4928,5009,5093,5155,5219,5282,5350,5457,5556,5655,5750,5808,5863,5941,6022,6101,6189"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2987,3086,3178,3259,3361,4169,4267,4389,5180,5239,5302,5705,5933,12151,12243,12308,12371,12433,12500,12564,12618,12723,12782,12843,12897,12966,13085,13168,13245,13335,13419,13503,13639,13718,13802,13924,14010,14088,14142,14193,14259,14328,14402,14473,14549,14621,14698,14769,14843,14954,15045,15124,15211,15299,15371,15445,15530,15581,15660,15727,15808,15892,15954,16018,16081,16149,16256,16355,16454,16549,16607,16832,17166,17247,17395", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,98,91,80,101,79,97,121,78,58,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,76,89,83,83,135,78,83,121,85,77,53,50,65,68,73,70,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77,80,78,87", "endOffsets": "311,3081,3173,3254,3356,3436,4262,4384,4463,5234,5297,5389,5764,5988,12238,12303,12366,12428,12495,12559,12613,12718,12777,12838,12892,12961,13080,13163,13240,13330,13414,13498,13634,13713,13797,13919,14005,14083,14137,14188,14254,14323,14397,14468,14544,14616,14693,14764,14838,14949,15040,15119,15206,15294,15366,15440,15525,15576,15655,15722,15803,15887,15949,16013,16076,16144,16251,16350,16449,16544,16602,16657,16905,17242,17321,17478"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,327,434,566,682,778,891,1035,1159,1314,1399,1498,1588,1682,1796,1918,2022,2155,2282,2417,2589,2717,2835,2961,3081,3172,3270,3388,3527,3623,3731,3834,3967,4110,4216,4313,4393,4491,4583,4667,4752,4853,4933,5018,5117,5217,5312,5412,5499,5603,5704,5808,5930,6010,6114", "endColumns": "138,132,106,131,115,95,112,143,123,154,84,98,89,93,113,121,103,132,126,134,171,127,117,125,119,90,97,117,138,95,107,102,132,142,105,96,79,97,91,83,84,100,79,84,98,99,94,99,86,103,100,103,121,79,103,98", "endOffsets": "189,322,429,561,677,773,886,1030,1154,1309,1394,1493,1583,1677,1791,1913,2017,2150,2277,2412,2584,2712,2830,2956,3076,3167,3265,3383,3522,3618,3726,3829,3962,4105,4211,4308,4388,4486,4578,4662,4747,4848,4928,5013,5112,5212,5307,5407,5494,5598,5699,5803,5925,6005,6109,6208"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5993,6132,6265,6372,6504,6620,6716,6829,6973,7097,7252,7337,7436,7526,7620,7734,7856,7960,8093,8220,8355,8527,8655,8773,8899,9019,9110,9208,9326,9465,9561,9669,9772,9905,10048,10154,10251,10331,10429,10521,10605,10690,10791,10871,10956,11055,11155,11250,11350,11437,11541,11642,11746,11868,11948,12052", "endColumns": "138,132,106,131,115,95,112,143,123,154,84,98,89,93,113,121,103,132,126,134,171,127,117,125,119,90,97,117,138,95,107,102,132,142,105,96,79,97,91,83,84,100,79,84,98,99,94,99,86,103,100,103,121,79,103,98", "endOffsets": "6127,6260,6367,6499,6615,6711,6824,6968,7092,7247,7332,7431,7521,7615,7729,7851,7955,8088,8215,8350,8522,8650,8768,8894,9014,9105,9203,9321,9460,9556,9664,9767,9900,10043,10149,10246,10326,10424,10516,10600,10685,10786,10866,10951,11050,11150,11245,11345,11432,11536,11637,11741,11863,11943,12047,12146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,606,719,796,871,964,1059,1154,1248,1350,1445,1542,1640,1736,1829,1909,2015,2114,2210,2315,2418,2520,2674,2776", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,601,714,791,866,959,1054,1149,1243,1345,1440,1537,1635,1731,1824,1904,2010,2109,2205,2310,2413,2515,2669,2771,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "316,419,522,633,717,817,930,1007,1082,1175,1270,1365,1459,1561,1656,1753,1851,1947,2040,2120,2226,2325,2421,2526,2629,2731,2885,17086", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "414,517,628,712,812,925,1002,1077,1170,1265,1360,1454,1556,1651,1748,1846,1942,2035,2115,2221,2320,2416,2521,2624,2726,2880,2982,17161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-sv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "143", "endOffsets": "338"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4649", "endColumns": "147", "endOffsets": "4792"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,352,428,516,617,753,916,1043,1154,1254,1382,1440,1497,1581,1667,1731,1870,1949,2032,2176,2251,2345,2442,2575,2677,2784,2881,2959,3031,3150,3238,3426,3604,3723,3864,3976,4150,4260,4402,4503,4635,4733,4862,4969,5068,5213,5303,5448,5541,5693,5775,5909,5985,6095,6192,6270,6355,6426,6495,6570,6645,6763,6858,7011,7116,7237,7330,7479,7586,7689,7812,7906,8020,8122,8236,8333,8389,8441,8494,8546,8596,8648,8718,8769,8835,8887,8932,9004,9048,9106,9161,9215,9271,9321,9371,9442,9491,9543,9588,9649,9707,9766,9901,10018,10100,10161,10240,10309,10377,10487,10562,10637,10717,10795,10862,10962,11035,11127,11207,11322,11399,11491,11571,11663,11753,11830,11925,12022,12126,12229,12309,12403,12470,12547,12673,12803,12877,12954,13047,13142,15193,15308,15480,15536,15661,15807,15895,16015,16107,16237,16381,16493,16692,16768,16849,16919,17005,17126,17171,17254,17363,17451,17566,17685,17824,17922,18027,18092,18151,18214,18269,18334,18410,18500,18625,18695,18775,19021,19228,19342,19463,19589,19679,19782,19906,20068,20223,20316,20425,20512,20636,20737,20883,20979,21051,21108,21353,21435,21510,21570,21629,21731,21846,21934,22073,22232,22311,22375,22438,22589,22663,22755,22834,22930,23073,23166,23244,23354,23434,23568,23698,23755,23881,23951,24024,24087,24162,24215,24277,24369,24421,24490,24660,24772,24861,24924,25050,25187,25294,25408,25694,25784,25883,25981,26158,26352,26437,26613,26707,26835,26953,27034,27121,27197,27319,27609,27746,27795,27877,28002,28108,28189,28262,28352,28433,28502,28573,28637,28708,28780,28855,28943,29014,29081,29164,29231,29314,29489,29633,29718,29777,29856,30009,30069,30147,30228,30303,30377,30603,30658,30733,30814,30884,30972,31130,31220,31297,31386,31464,31532,31610,31688,31821,31903,31960,32038,32115,32185,32264,32374,32481,32585,32709,32785,32837,32886,32977,33078,33164,33266,33410,33515,33603,33715,33845,33986,34104,34203,34354,34478,34659,34831,34939,35073,35160,35246,35341,35455,35549,35689,35763,36163,36242,36328,36406", "endColumns": "171,124,75,87,100,135,162,126,110,99,127,57,56,83,85,63,138,78,82,143,74,93,96,132,101,106,96,77,71,118,87,187,177,118,140,111,173,109,141,100,131,97,128,106,98,144,89,144,92,151,81,133,75,109,96,77,84,70,68,74,74,117,94,152,104,120,92,148,106,102,122,93,113,101,113,96,55,51,52,51,49,51,69,50,65,51,44,71,43,57,54,53,55,49,49,70,48,51,44,60,57,58,134,116,81,60,78,68,67,109,74,74,79,77,66,99,72,91,79,114,76,91,79,91,89,76,94,96,103,102,79,93,66,76,125,129,73,76,92,94,2050,114,171,55,124,145,87,119,91,129,143,111,198,75,80,69,85,120,44,82,108,87,114,118,138,97,104,64,58,62,54,64,75,89,124,69,79,245,206,113,120,125,89,102,123,161,154,92,108,86,123,100,145,95,71,56,244,81,74,59,58,101,114,87,138,158,78,63,62,150,73,91,78,95,142,92,77,109,79,133,129,56,125,69,72,62,74,52,61,91,51,68,169,111,88,62,125,136,106,113,285,89,98,97,176,193,84,175,93,127,117,80,86,75,121,289,136,48,81,124,105,80,72,89,80,68,70,63,70,71,74,87,70,66,82,66,82,174,143,84,58,78,152,59,77,80,74,73,225,54,74,80,69,87,157,89,76,88,77,67,77,77,132,81,56,77,76,69,78,109,106,103,123,75,51,48,90,100,85,101,143,104,87,111,129,140,117,98,150,123,180,171,107,133,86,85,94,113,93,139,73,399,78,85,77,72", "endOffsets": "222,347,423,511,612,748,911,1038,1149,1249,1377,1435,1492,1576,1662,1726,1865,1944,2027,2171,2246,2340,2437,2570,2672,2779,2876,2954,3026,3145,3233,3421,3599,3718,3859,3971,4145,4255,4397,4498,4630,4728,4857,4964,5063,5208,5298,5443,5536,5688,5770,5904,5980,6090,6187,6265,6350,6421,6490,6565,6640,6758,6853,7006,7111,7232,7325,7474,7581,7684,7807,7901,8015,8117,8231,8328,8384,8436,8489,8541,8591,8643,8713,8764,8830,8882,8927,8999,9043,9101,9156,9210,9266,9316,9366,9437,9486,9538,9583,9644,9702,9761,9896,10013,10095,10156,10235,10304,10372,10482,10557,10632,10712,10790,10857,10957,11030,11122,11202,11317,11394,11486,11566,11658,11748,11825,11920,12017,12121,12224,12304,12398,12465,12542,12668,12798,12872,12949,13042,13137,15188,15303,15475,15531,15656,15802,15890,16010,16102,16232,16376,16488,16687,16763,16844,16914,17000,17121,17166,17249,17358,17446,17561,17680,17819,17917,18022,18087,18146,18209,18264,18329,18405,18495,18620,18690,18770,19016,19223,19337,19458,19584,19674,19777,19901,20063,20218,20311,20420,20507,20631,20732,20878,20974,21046,21103,21348,21430,21505,21565,21624,21726,21841,21929,22068,22227,22306,22370,22433,22584,22658,22750,22829,22925,23068,23161,23239,23349,23429,23563,23693,23750,23876,23946,24019,24082,24157,24210,24272,24364,24416,24485,24655,24767,24856,24919,25045,25182,25289,25403,25689,25779,25878,25976,26153,26347,26432,26608,26702,26830,26948,27029,27116,27192,27314,27604,27741,27790,27872,27997,28103,28184,28257,28347,28428,28497,28568,28632,28703,28775,28850,28938,29009,29076,29159,29226,29309,29484,29628,29713,29772,29851,30004,30064,30142,30223,30298,30372,30598,30653,30728,30809,30879,30967,31125,31215,31292,31381,31459,31527,31605,31683,31816,31898,31955,32033,32110,32180,32259,32369,32476,32580,32704,32780,32832,32881,32972,33073,33159,33261,33405,33510,33598,33710,33840,33981,34099,34198,34349,34473,34654,34826,34934,35068,35155,35241,35336,35450,35544,35684,35758,36158,36237,36323,36401,36474"}, "to": {"startLines": "193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17799,17971,18096,18172,18260,18361,18497,18660,18787,18898,18998,19126,19184,19241,19325,19411,19475,19614,19693,19776,19920,19995,20089,20186,20319,20421,20528,20625,20703,20775,20894,20982,21170,21348,21467,21608,21720,21894,22004,22146,22247,22379,22477,22606,22713,22812,22957,23047,23192,23285,23437,23519,23653,23729,23839,23936,24014,24099,24170,24239,24314,24389,24507,24602,24755,24860,24981,25074,25223,25330,25433,25556,25650,25764,25866,25980,26077,26133,26185,26238,26290,26340,26392,26462,26513,26579,26631,26676,26748,26792,26850,26905,26959,27015,27065,27115,27186,27235,27287,27332,27393,27451,27510,27645,27762,27844,27905,27984,28053,28121,28231,28306,28381,28461,28539,28606,28706,28779,28871,28951,29066,29143,29235,29315,29407,29497,29574,29669,29766,29870,29973,30053,30147,30214,30291,30417,30547,30621,30698,30791,30886,32937,33052,33224,33280,33405,33551,33639,33759,33851,33981,34125,34237,34436,34512,34593,34663,34749,34870,34915,34998,35107,35195,35310,35429,35568,35666,35771,35836,35895,35958,36013,36078,36154,36244,36369,36439,36519,36765,36972,37086,37207,37333,37423,37526,37650,37812,37967,38060,38169,38256,38380,38481,38627,38723,38795,38852,39097,39179,39254,39314,39373,39475,39590,39678,39817,39976,40055,40119,40182,40333,40407,40499,40578,40674,40817,40910,40988,41098,41178,41312,41442,41499,41625,41695,41768,41831,41906,41959,42021,42113,42165,42234,42404,42516,42605,42668,42794,42931,43038,43152,43438,43528,43627,43725,43902,44096,44181,44357,44451,44579,44697,44778,44865,44941,45063,45353,45490,45539,45621,45746,45852,45933,46006,46096,46177,46246,46317,46381,46452,46524,46599,46687,46758,46825,46908,46975,47058,47233,47377,47462,47521,47600,47753,47813,47891,47972,48047,48121,48347,48402,48477,48558,48628,48716,48874,48964,49041,49130,49208,49276,49354,49432,49565,49647,49704,49782,49859,49929,50008,50118,50225,50329,50453,50529,50581,50630,50721,50822,50908,51010,51154,51259,51347,51459,51589,51730,51848,51947,52098,52222,52403,52575,52683,52817,52904,52990,53085,53199,53293,53433,53507,53907,53986,54072,54150", "endColumns": "171,124,75,87,100,135,162,126,110,99,127,57,56,83,85,63,138,78,82,143,74,93,96,132,101,106,96,77,71,118,87,187,177,118,140,111,173,109,141,100,131,97,128,106,98,144,89,144,92,151,81,133,75,109,96,77,84,70,68,74,74,117,94,152,104,120,92,148,106,102,122,93,113,101,113,96,55,51,52,51,49,51,69,50,65,51,44,71,43,57,54,53,55,49,49,70,48,51,44,60,57,58,134,116,81,60,78,68,67,109,74,74,79,77,66,99,72,91,79,114,76,91,79,91,89,76,94,96,103,102,79,93,66,76,125,129,73,76,92,94,2050,114,171,55,124,145,87,119,91,129,143,111,198,75,80,69,85,120,44,82,108,87,114,118,138,97,104,64,58,62,54,64,75,89,124,69,79,245,206,113,120,125,89,102,123,161,154,92,108,86,123,100,145,95,71,56,244,81,74,59,58,101,114,87,138,158,78,63,62,150,73,91,78,95,142,92,77,109,79,133,129,56,125,69,72,62,74,52,61,91,51,68,169,111,88,62,125,136,106,113,285,89,98,97,176,193,84,175,93,127,117,80,86,75,121,289,136,48,81,124,105,80,72,89,80,68,70,63,70,71,74,87,70,66,82,66,82,174,143,84,58,78,152,59,77,80,74,73,225,54,74,80,69,87,157,89,76,88,77,67,77,77,132,81,56,77,76,69,78,109,106,103,123,75,51,48,90,100,85,101,143,104,87,111,129,140,117,98,150,123,180,171,107,133,86,85,94,113,93,139,73,399,78,85,77,72", "endOffsets": "17966,18091,18167,18255,18356,18492,18655,18782,18893,18993,19121,19179,19236,19320,19406,19470,19609,19688,19771,19915,19990,20084,20181,20314,20416,20523,20620,20698,20770,20889,20977,21165,21343,21462,21603,21715,21889,21999,22141,22242,22374,22472,22601,22708,22807,22952,23042,23187,23280,23432,23514,23648,23724,23834,23931,24009,24094,24165,24234,24309,24384,24502,24597,24750,24855,24976,25069,25218,25325,25428,25551,25645,25759,25861,25975,26072,26128,26180,26233,26285,26335,26387,26457,26508,26574,26626,26671,26743,26787,26845,26900,26954,27010,27060,27110,27181,27230,27282,27327,27388,27446,27505,27640,27757,27839,27900,27979,28048,28116,28226,28301,28376,28456,28534,28601,28701,28774,28866,28946,29061,29138,29230,29310,29402,29492,29569,29664,29761,29865,29968,30048,30142,30209,30286,30412,30542,30616,30693,30786,30881,32932,33047,33219,33275,33400,33546,33634,33754,33846,33976,34120,34232,34431,34507,34588,34658,34744,34865,34910,34993,35102,35190,35305,35424,35563,35661,35766,35831,35890,35953,36008,36073,36149,36239,36364,36434,36514,36760,36967,37081,37202,37328,37418,37521,37645,37807,37962,38055,38164,38251,38375,38476,38622,38718,38790,38847,39092,39174,39249,39309,39368,39470,39585,39673,39812,39971,40050,40114,40177,40328,40402,40494,40573,40669,40812,40905,40983,41093,41173,41307,41437,41494,41620,41690,41763,41826,41901,41954,42016,42108,42160,42229,42399,42511,42600,42663,42789,42926,43033,43147,43433,43523,43622,43720,43897,44091,44176,44352,44446,44574,44692,44773,44860,44936,45058,45348,45485,45534,45616,45741,45847,45928,46001,46091,46172,46241,46312,46376,46447,46519,46594,46682,46753,46820,46903,46970,47053,47228,47372,47457,47516,47595,47748,47808,47886,47967,48042,48116,48342,48397,48472,48553,48623,48711,48869,48959,49036,49125,49203,49271,49349,49427,49560,49642,49699,49777,49854,49924,50003,50113,50220,50324,50448,50524,50576,50625,50716,50817,50903,51005,51149,51254,51342,51454,51584,51725,51843,51942,52093,52217,52398,52570,52678,52812,52899,52985,53080,53194,53288,53428,53502,53902,53981,54067,54145,54218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,286,382,481,569,645,733,822,903,989,1079,1148,1222,1293,1363,1441,1508", "endColumns": "92,87,95,98,87,75,87,88,80,85,89,68,73,70,69,77,66,119", "endOffsets": "193,281,377,476,564,640,728,817,898,984,1074,1143,1217,1288,1358,1436,1503,1623"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,539,540,541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4468,4561,4897,4993,5092,5769,5845,16662,16751,16910,16996,17326,17483,17557,17628,54223,54301,54368", "endColumns": "92,87,95,98,87,75,87,88,80,85,89,68,73,70,69,77,66,119", "endOffsets": "4556,4644,4988,5087,5175,5840,5928,16746,16827,16991,17081,17390,17552,17623,17693,54296,54363,54483"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,368", "endColumns": "99,99,112,97", "endOffsets": "150,250,363,461"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4797,5394,5494,5607", "endColumns": "99,99,112,97", "endOffsets": "4892,5489,5602,5700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3441,3536,3638,3736,3835,3943,4048,17698", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "3531,3633,3731,3830,3938,4043,4164,17794"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,88", "endOffsets": "140,229"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "54488,54578", "endColumns": "89,88", "endOffsets": "54573,54662"}}]}]}