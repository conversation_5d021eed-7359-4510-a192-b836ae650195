{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4706", "endColumns": "144", "endOffsets": "4846"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,411,527,629,726,840,974,1092,1244,1328,1429,1524,1624,1739,1869,1975,2114,2250,2381,2547,2674,2794,2918,3038,3134,3231,3351,3467,3567,3678,3787,3927,4072,4182,4285,4371,4465,4557,4647,4736,4837,4917,5001,5102,5208,5300,5399,5487,5599,5700,5804,5923,6003,6103", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "169,290,406,522,624,721,835,969,1087,1239,1323,1424,1519,1619,1734,1864,1970,2109,2245,2376,2542,2669,2789,2913,3033,3129,3226,3346,3462,3562,3673,3782,3922,4067,4177,4280,4366,4460,4552,4642,4731,4832,4912,4996,5097,5203,5295,5394,5482,5594,5695,5799,5918,5998,6098,6190"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6100,6219,6340,6456,6572,6674,6771,6885,7019,7137,7289,7373,7474,7569,7669,7784,7914,8020,8159,8295,8426,8592,8719,8839,8963,9083,9179,9276,9396,9512,9612,9723,9832,9972,10117,10227,10330,10416,10510,10602,10692,10781,10882,10962,11046,11147,11253,11345,11444,11532,11644,11745,11849,11968,12048,12148", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "6214,6335,6451,6567,6669,6766,6880,7014,7132,7284,7368,7469,7564,7664,7779,7909,8015,8154,8290,8421,8587,8714,8834,8958,9078,9174,9271,9391,9507,9607,9718,9827,9967,10112,10222,10325,10411,10505,10597,10687,10776,10877,10957,11041,11142,11248,11340,11439,11527,11639,11740,11844,11963,12043,12143,12235"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,443,549,656,745,846,965,1050,1130,1221,1314,1409,1503,1603,1696,1791,1886,1977,2068,2153,2260,2371,2473,2581,2689,2799,2961,17369", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "438,544,651,740,841,960,1045,1125,1216,1309,1404,1498,1598,1691,1786,1881,1972,2063,2148,2255,2366,2468,2576,2684,2794,2956,3056,17450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,432,516,611,700,801,921,1002,1062,1126,1218,1297,1357,1447,1511,1582,1645,1720,1784,1838,1965,2023,2085,2139,2218,2359,2446,2522,2617,2698,2780,2919,3002,3086,3225,3312,3392,3448,3499,3565,3639,3719,3790,3873,3946,4023,4092,4166,4268,4356,4433,4526,4622,4696,4776,4873,4925,5009,5075,5162,5250,5312,5376,5439,5507,5616,5727,5831,5941,6001,6056,6133,6216,6293", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,77,83,94,88,100,119,80,59,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,110,103,109,59,54,76,82,76,78", "endOffsets": "268,349,427,511,606,695,796,916,997,1057,1121,1213,1292,1352,1442,1506,1577,1640,1715,1779,1833,1960,2018,2080,2134,2213,2354,2441,2517,2612,2693,2775,2914,2997,3081,3220,3307,3387,3443,3494,3560,3634,3714,3785,3868,3941,4018,4087,4161,4263,4351,4428,4521,4617,4691,4771,4868,4920,5004,5070,5157,5245,5307,5371,5434,5502,5611,5722,5826,5936,5996,6051,6128,6211,6288,6367"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3061,3142,3220,3304,3399,4223,4324,4444,5248,5308,5372,5781,6040,12240,12330,12394,12465,12528,12603,12667,12721,12848,12906,12968,13022,13101,13242,13329,13405,13500,13581,13663,13802,13885,13969,14108,14195,14275,14331,14382,14448,14522,14602,14673,14756,14829,14906,14975,15049,15151,15239,15316,15409,15505,15579,15659,15756,15808,15892,15958,16045,16133,16195,16259,16322,16390,16499,16610,16714,16824,16884,17117,17455,17538,17691", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,80,77,83,94,88,100,119,80,59,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,110,103,109,59,54,76,82,76,78", "endOffsets": "318,3137,3215,3299,3394,3483,4319,4439,4520,5303,5367,5459,5855,6095,12325,12389,12460,12523,12598,12662,12716,12843,12901,12963,13017,13096,13237,13324,13400,13495,13576,13658,13797,13880,13964,14103,14190,14270,14326,14377,14443,14517,14597,14668,14751,14824,14901,14970,15044,15146,15234,15311,15404,15500,15574,15654,15751,15803,15887,15953,16040,16128,16190,16254,16317,16385,16494,16605,16709,16819,16879,16934,17189,17533,17610,17765"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3488,3585,3687,3786,3886,3993,4103,18000", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3580,3682,3781,3881,3988,4098,4218,18096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,229,375,451,537,650,777,963,1117,1243,1347,1487,1550,1611,1701,1792,1861,2012,2115,2203,2345,2422,2514,2618,2761,2870,2981,3083,3159,3231,3353,3451,3658,3829,3944,4104,4213,4390,4488,4625,4728,4869,4973,5103,5201,5302,5436,5528,5661,5760,5919,6001,6115,6195,6313,6417,6494,6580,6651,6722,6797,6868,6994,7092,7237,7333,7444,7538,7681,7779,7880,7998,8090,8200,8307,8426,8532,8595,8649,8704,8758,8811,8861,8929,8980,9047,9098,9143,9218,9262,9318,9374,9429,9489,9541,9591,9666,9717,9772,9818,9882,9943,10007,10160,10276,10358,10419,10494,10563,10631,10735,10817,10895,10978,11056,11123,11223,11298,11390,11475,11597,11676,11769,11857,11958,12051,12132,12227,12333,12456,12566,12643,12737,12816,12890,12995,13104,13177,13254,13357,13443,15494,15609,15823,15868,15991,16154,16247,16369,16463,16593,16778,16882,17075,17143,17224,17299,17388,17499,17544,17633,17753,17842,17960,18088,18230,18335,18441,18507,18563,18618,18670,18737,18819,18913,19051,19118,19198,19448,19687,19818,19947,20073,20164,20282,20401,20550,20711,20806,20915,21002,21119,21226,21371,21462,21541,21601,21817,21907,21987,22045,22110,22230,22339,22436,22624,22801,22869,22948,23008,23176,23254,23341,23421,23519,23691,23795,23874,23990,24068,24160,24294,24348,24452,24522,24602,24663,24739,24787,24855,24946,24998,25069,25240,25356,25447,25511,25650,25787,25908,26022,26253,26345,26445,26544,26701,26913,27005,27175,27294,27422,27540,27628,27720,27796,27934,28086,28221,28270,28353,28484,28602,28690,28772,28879,28966,29042,29117,29180,29251,29320,29397,29489,29556,29628,29719,29788,29875,30061,30198,30289,30348,30427,30567,30627,30707,30789,30869,30949,31181,31236,31316,31401,31473,31563,31743,31828,31897,31981,32061,32129,32201,32295,32431,32512,32564,32643,32718,32784,32867,32977,33080,33193,33319,33400,33448,33499,33591,33717,33801,33909,34133,34236,34314,34446,34579,34757,34887,34985,35125,35252,35446,35619,35736,35870,35956,36041,36144,36262,36367,36490,36561,36955,37036,37122,37204", "endColumns": "173,145,75,85,112,126,185,153,125,103,139,62,60,89,90,68,150,102,87,141,76,91,103,142,108,110,101,75,71,121,97,206,170,114,159,108,176,97,136,102,140,103,129,97,100,133,91,132,98,158,81,113,79,117,103,76,85,70,70,74,70,125,97,144,95,110,93,142,97,100,117,91,109,106,118,105,62,53,54,53,52,49,67,50,66,50,44,74,43,55,55,54,59,51,49,74,50,54,45,63,60,63,152,115,81,60,74,68,67,103,81,77,82,77,66,99,74,91,84,121,78,92,87,100,92,80,94,105,122,109,76,93,78,73,104,108,72,76,102,85,2050,114,213,44,122,162,92,121,93,129,184,103,192,67,80,74,88,110,44,88,119,88,117,127,141,104,105,65,55,54,51,66,81,93,137,66,79,249,238,130,128,125,90,117,118,148,160,94,108,86,116,106,144,90,78,59,215,89,79,57,64,119,108,96,187,176,67,78,59,167,77,86,79,97,171,103,78,115,77,91,133,53,103,69,79,60,75,47,67,90,51,70,170,115,90,63,138,136,120,113,230,91,99,98,156,211,91,169,118,127,117,87,91,75,137,151,134,48,82,130,117,87,81,106,86,75,74,62,70,68,76,91,66,71,90,68,86,185,136,90,58,78,139,59,79,81,79,79,231,54,79,84,71,89,179,84,68,83,79,67,71,93,135,80,51,78,74,65,82,109,102,112,125,80,47,50,91,125,83,107,223,102,77,131,132,177,129,97,139,126,193,172,116,133,85,84,102,117,104,122,70,393,80,85,81,76", "endOffsets": "224,370,446,532,645,772,958,1112,1238,1342,1482,1545,1606,1696,1787,1856,2007,2110,2198,2340,2417,2509,2613,2756,2865,2976,3078,3154,3226,3348,3446,3653,3824,3939,4099,4208,4385,4483,4620,4723,4864,4968,5098,5196,5297,5431,5523,5656,5755,5914,5996,6110,6190,6308,6412,6489,6575,6646,6717,6792,6863,6989,7087,7232,7328,7439,7533,7676,7774,7875,7993,8085,8195,8302,8421,8527,8590,8644,8699,8753,8806,8856,8924,8975,9042,9093,9138,9213,9257,9313,9369,9424,9484,9536,9586,9661,9712,9767,9813,9877,9938,10002,10155,10271,10353,10414,10489,10558,10626,10730,10812,10890,10973,11051,11118,11218,11293,11385,11470,11592,11671,11764,11852,11953,12046,12127,12222,12328,12451,12561,12638,12732,12811,12885,12990,13099,13172,13249,13352,13438,15489,15604,15818,15863,15986,16149,16242,16364,16458,16588,16773,16877,17070,17138,17219,17294,17383,17494,17539,17628,17748,17837,17955,18083,18225,18330,18436,18502,18558,18613,18665,18732,18814,18908,19046,19113,19193,19443,19682,19813,19942,20068,20159,20277,20396,20545,20706,20801,20910,20997,21114,21221,21366,21457,21536,21596,21812,21902,21982,22040,22105,22225,22334,22431,22619,22796,22864,22943,23003,23171,23249,23336,23416,23514,23686,23790,23869,23985,24063,24155,24289,24343,24447,24517,24597,24658,24734,24782,24850,24941,24993,25064,25235,25351,25442,25506,25645,25782,25903,26017,26248,26340,26440,26539,26696,26908,27000,27170,27289,27417,27535,27623,27715,27791,27929,28081,28216,28265,28348,28479,28597,28685,28767,28874,28961,29037,29112,29175,29246,29315,29392,29484,29551,29623,29714,29783,29870,30056,30193,30284,30343,30422,30562,30622,30702,30784,30864,30944,31176,31231,31311,31396,31468,31558,31738,31823,31892,31976,32056,32124,32196,32290,32426,32507,32559,32638,32713,32779,32862,32972,33075,33188,33314,33395,33443,33494,33586,33712,33796,33904,34128,34231,34309,34441,34574,34752,34882,34980,35120,35247,35441,35614,35731,35865,35951,36036,36139,36257,36362,36485,36556,36950,37031,37117,37199,37276"}, "to": {"startLines": "193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18101,18275,18421,18497,18583,18696,18823,19009,19163,19289,19393,19533,19596,19657,19747,19838,19907,20058,20161,20249,20391,20468,20560,20664,20807,20916,21027,21129,21205,21277,21399,21497,21704,21875,21990,22150,22259,22436,22534,22671,22774,22915,23019,23149,23247,23348,23482,23574,23707,23806,23965,24047,24161,24241,24359,24463,24540,24626,24697,24768,24843,24914,25040,25138,25283,25379,25490,25584,25727,25825,25926,26044,26136,26246,26353,26472,26578,26641,26695,26750,26804,26857,26907,26975,27026,27093,27144,27189,27264,27308,27364,27420,27475,27535,27587,27637,27712,27763,27818,27864,27928,27989,28053,28206,28322,28404,28465,28540,28609,28677,28781,28863,28941,29024,29102,29169,29269,29344,29436,29521,29643,29722,29815,29903,30004,30097,30178,30273,30379,30502,30612,30689,30783,30862,30936,31041,31150,31223,31300,31403,31489,33540,33655,33869,33914,34037,34200,34293,34415,34509,34639,34824,34928,35121,35189,35270,35345,35434,35545,35590,35679,35799,35888,36006,36134,36276,36381,36487,36553,36609,36664,36716,36783,36865,36959,37097,37164,37244,37494,37733,37864,37993,38119,38210,38328,38447,38596,38757,38852,38961,39048,39165,39272,39417,39508,39587,39647,39863,39953,40033,40091,40156,40276,40385,40482,40670,40847,40915,40994,41054,41222,41300,41387,41467,41565,41737,41841,41920,42036,42114,42206,42340,42394,42498,42568,42648,42709,42785,42833,42901,42992,43044,43115,43286,43402,43493,43557,43696,43833,43954,44068,44299,44391,44491,44590,44747,44959,45051,45221,45340,45468,45586,45674,45766,45842,45980,46132,46267,46316,46399,46530,46648,46736,46818,46925,47012,47088,47163,47226,47297,47366,47443,47535,47602,47674,47765,47834,47921,48107,48244,48335,48394,48473,48613,48673,48753,48835,48915,48995,49227,49282,49362,49447,49519,49609,49789,49874,49943,50027,50107,50175,50247,50341,50477,50558,50610,50689,50764,50830,50913,51023,51126,51239,51365,51446,51494,51545,51637,51763,51847,51955,52179,52282,52360,52492,52625,52803,52933,53031,53171,53298,53492,53665,53782,53916,54002,54087,54190,54308,54413,54536,54607,55001,55082,55168,55250", "endColumns": "173,145,75,85,112,126,185,153,125,103,139,62,60,89,90,68,150,102,87,141,76,91,103,142,108,110,101,75,71,121,97,206,170,114,159,108,176,97,136,102,140,103,129,97,100,133,91,132,98,158,81,113,79,117,103,76,85,70,70,74,70,125,97,144,95,110,93,142,97,100,117,91,109,106,118,105,62,53,54,53,52,49,67,50,66,50,44,74,43,55,55,54,59,51,49,74,50,54,45,63,60,63,152,115,81,60,74,68,67,103,81,77,82,77,66,99,74,91,84,121,78,92,87,100,92,80,94,105,122,109,76,93,78,73,104,108,72,76,102,85,2050,114,213,44,122,162,92,121,93,129,184,103,192,67,80,74,88,110,44,88,119,88,117,127,141,104,105,65,55,54,51,66,81,93,137,66,79,249,238,130,128,125,90,117,118,148,160,94,108,86,116,106,144,90,78,59,215,89,79,57,64,119,108,96,187,176,67,78,59,167,77,86,79,97,171,103,78,115,77,91,133,53,103,69,79,60,75,47,67,90,51,70,170,115,90,63,138,136,120,113,230,91,99,98,156,211,91,169,118,127,117,87,91,75,137,151,134,48,82,130,117,87,81,106,86,75,74,62,70,68,76,91,66,71,90,68,86,185,136,90,58,78,139,59,79,81,79,79,231,54,79,84,71,89,179,84,68,83,79,67,71,93,135,80,51,78,74,65,82,109,102,112,125,80,47,50,91,125,83,107,223,102,77,131,132,177,129,97,139,126,193,172,116,133,85,84,102,117,104,122,70,393,80,85,81,76", "endOffsets": "18270,18416,18492,18578,18691,18818,19004,19158,19284,19388,19528,19591,19652,19742,19833,19902,20053,20156,20244,20386,20463,20555,20659,20802,20911,21022,21124,21200,21272,21394,21492,21699,21870,21985,22145,22254,22431,22529,22666,22769,22910,23014,23144,23242,23343,23477,23569,23702,23801,23960,24042,24156,24236,24354,24458,24535,24621,24692,24763,24838,24909,25035,25133,25278,25374,25485,25579,25722,25820,25921,26039,26131,26241,26348,26467,26573,26636,26690,26745,26799,26852,26902,26970,27021,27088,27139,27184,27259,27303,27359,27415,27470,27530,27582,27632,27707,27758,27813,27859,27923,27984,28048,28201,28317,28399,28460,28535,28604,28672,28776,28858,28936,29019,29097,29164,29264,29339,29431,29516,29638,29717,29810,29898,29999,30092,30173,30268,30374,30497,30607,30684,30778,30857,30931,31036,31145,31218,31295,31398,31484,33535,33650,33864,33909,34032,34195,34288,34410,34504,34634,34819,34923,35116,35184,35265,35340,35429,35540,35585,35674,35794,35883,36001,36129,36271,36376,36482,36548,36604,36659,36711,36778,36860,36954,37092,37159,37239,37489,37728,37859,37988,38114,38205,38323,38442,38591,38752,38847,38956,39043,39160,39267,39412,39503,39582,39642,39858,39948,40028,40086,40151,40271,40380,40477,40665,40842,40910,40989,41049,41217,41295,41382,41462,41560,41732,41836,41915,42031,42109,42201,42335,42389,42493,42563,42643,42704,42780,42828,42896,42987,43039,43110,43281,43397,43488,43552,43691,43828,43949,44063,44294,44386,44486,44585,44742,44954,45046,45216,45335,45463,45581,45669,45761,45837,45975,46127,46262,46311,46394,46525,46643,46731,46813,46920,47007,47083,47158,47221,47292,47361,47438,47530,47597,47669,47760,47829,47916,48102,48239,48330,48389,48468,48608,48668,48748,48830,48910,48990,49222,49277,49357,49442,49514,49604,49784,49869,49938,50022,50102,50170,50242,50336,50472,50553,50605,50684,50759,50825,50908,51018,51121,51234,51360,51441,51489,51540,51632,51758,51842,51950,52174,52277,52355,52487,52620,52798,52928,53026,53166,53293,53487,53660,53777,53911,53997,54082,54185,54303,54408,54531,54602,54996,55077,55163,55245,55322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,1011,1101,1177,1253,1332,1407,1483,1550", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,75,78,74,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,1006,1096,1172,1248,1327,1402,1478,1545,1658"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,539,540,541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4525,4620,4966,5063,5162,5860,5943,16939,17030,17194,17279,17615,17770,17846,17925,55327,55403,55470", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,75,78,74,75,66,112", "endOffsets": "4615,4701,5058,5157,5243,5938,6035,17025,17112,17274,17364,17686,17841,17920,17995,55398,55465,55578"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4851,5464,5563,5675", "endColumns": "114,98,111,105", "endOffsets": "4961,5558,5670,5776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "55583,55666", "endColumns": "82,84", "endOffsets": "55661,55746"}}]}]}