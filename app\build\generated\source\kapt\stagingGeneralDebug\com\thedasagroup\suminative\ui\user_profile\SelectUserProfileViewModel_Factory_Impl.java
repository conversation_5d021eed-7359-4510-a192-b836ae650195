package com.thedasagroup.suminative.ui.user_profile;

import dagger.internal.DaggerGenerated;
import dagger.internal.InstanceFactory;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class SelectUserProfileViewModel_Factory_Impl implements SelectUserProfileViewModel.Factory {
  private final SelectUserProfileViewModel_Factory delegateFactory;

  SelectUserProfileViewModel_Factory_Impl(SelectUserProfileViewModel_Factory delegateFactory) {
    this.delegateFactory = delegateFactory;
  }

  @Override
  public SelectUserProfileViewModel create(SelectUserProfileState state) {
    return delegateFactory.get(state);
  }

  public static Provider<SelectUserProfileViewModel.Factory> create(
      SelectUserProfileViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new SelectUserProfileViewModel_Factory_Impl(delegateFactory));
  }

  public static dagger.internal.Provider<SelectUserProfileViewModel.Factory> createFactoryProvider(
      SelectUserProfileViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new SelectUserProfileViewModel_Factory_Impl(delegateFactory));
  }
}
