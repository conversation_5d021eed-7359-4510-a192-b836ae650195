package com.thedasagroup.suminative.ui.orders;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000\u00a8\u0001\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\u001ab\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00060\f2\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00060\u000e2\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u00060\u000e2\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00060\u000eH\u0007\u001a\u0084\u0001\u0010\u0014\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00060\f2\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00060\u000e2\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u00060\u000e2\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00060\u000e2\u000e\u0010\u0015\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00170\u00162\u0006\u0010\u0018\u001a\u00020\u00132\b\b\u0002\u0010\u0019\u001a\u00020\u0013H\u0007\u001a&\u0010\u001a\u001a\u00020\u00062\u0006\u0010\u001b\u001a\u00020\u00112\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0018\u001a\u00020\u0013H\u0086@\u00a2\u0006\u0002\u0010\u001c\u001a$\u0010\u001d\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0012\u0010\u001e\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00060\u000eH\u0007\u001a\u00ac\u0001\u0010\u001f\u001a\u00020\u00062\u0006\u0010 \u001a\u00020!2\u0006\u0010\"\u001a\u00020\u00132\f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00060\f2\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020\u00060\u000e2\u0006\u0010\u0007\u001a\u00020\b2\u0012\u0010$\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020\u00060\u000e2\u0012\u0010%\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020\u00060\u000e2\u001e\u0010&\u001a\u001a\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020(\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00060\'2\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u00060\u000e2\f\u0010)\u001a\b\u0012\u0004\u0012\u00020\u00060\fH\u0007\u001aD\u0010*\u001a\u00020\u00062\u0006\u0010+\u001a\u00020,2\b\b\u0002\u0010\t\u001a\u00020\n2\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00060\u000e2\f\u0010-\u001a\b\u0012\u0004\u0012\u00020\u00060\f2\u0006\u0010.\u001a\u00020/H\u0007\u001aD\u00100\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\u0012\u0010%\u001a\u000e\u0012\u0004\u0012\u00020(\u0012\u0004\u0012\u00020\u00060\u000e2\f\u0010-\u001a\b\u0012\u0004\u0012\u00020\u00060\f2\u0006\u0010 \u001a\u00020!H\u0007\u001aJ\u00101\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\u0018\u00102\u001a\u0014\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020(\u0012\u0004\u0012\u00020\u0006032\f\u0010-\u001a\b\u0012\u0004\u0012\u00020\u00060\f2\u0006\u0010 \u001a\u00020!H\u0007\u001a!\u00104\u001a\b\u0012\u0004\u0012\u00020\u000f0\f2\u0011\u00105\u001a\r\u0012\u0004\u0012\u00020\u00060\f\u00a2\u0006\u0002\b6H\u0007\u001a\u00d0\u0001\u00107\u001a\u00020\u00062\u0006\u0010\u0015\u001a\u00020\u00172\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u0007\u001a\u00020\b2\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020\u00060\u000e2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00060\f2\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00060\u000e2\u0012\u0010%\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020\u00060\u000e2\u001e\u0010&\u001a\u001a\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020(\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00060\'2\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u00060\u000e2\u0012\u0010$\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020\u00060\u000e2\u0012\u0010)\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020\u00060\u000e2\b\b\u0002\u0010\u0019\u001a\u00020\u0013H\u0007\u001aH\u00108\u001a\u00020\u00062\u0006\u0010+\u001a\u0002092\u001e\u0010&\u001a\u001a\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020(\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00060\'2\u0006\u0010 \u001a\u00020!2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010:\u001a\u00020\bH\u0003\u001a\u00ae\u0001\u0010;\u001a\u00020\u0006*\u00020<2\u0006\u0010 \u001a\u00020!2\u0006\u0010\"\u001a\u00020\u00132\f\u0010=\u001a\b\u0012\u0004\u0012\u00020\u00060\f2\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020\u00060\u000e2\u0006\u0010\u0007\u001a\u00020\b2\u0012\u0010$\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020\u00060\u000e2\u0012\u0010%\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020\u00060\u000e2\u001e\u0010&\u001a\u001a\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020(\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00060\'2\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u00060\u000e2\f\u0010)\u001a\b\u0012\u0004\u0012\u00020\u00060\f\u001a\u001f\u0010>\u001a\u00020\u00062\u0006\u0010?\u001a\u00020@2\u0006\u0010A\u001a\u00020BH\u0007\u00a2\u0006\u0004\bC\u0010D\u001a\u0016\u0010E\u001a\u00020\u00112\u0006\u0010F\u001a\u00020(2\u0006\u0010G\u001a\u00020(\u001a\u000e\u0010H\u001a\u00020\u00132\u0006\u0010I\u001a\u00020J\u001a$\u0010K\u001a\u00020\u00062\u0006\u0010L\u001a\u00020\u00112\b\b\u0002\u0010M\u001a\u00020N2\b\b\u0002\u0010O\u001a\u00020\u0013H\u0007\u001a\u0018\u0010P\u001a\u00020\u00062\u0006\u0010 \u001a\u00020!2\u0006\u0010:\u001a\u00020\bH\u0007\"\u0013\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0002\u0010\u0003\u00a8\u0006Q"}, d2 = {"buttonWidth", "Landroidx/compose/ui/unit/Dp;", "getButtonWidth", "()F", "F", "OrderScreenTopFunction", "", "viewModel", "Lcom/thedasagroup/suminative/ui/orders/OrderScreenViewModel;", "modifier", "Landroidx/compose/ui/Modifier;", "callOrders", "Lkotlin/Function0;", "onPrintBill", "Lkotlin/Function1;", "Landroid/graphics/Bitmap;", "onTrackingUrlClick", "", "onUpdateShowAllOrders", "", "ExpandableList", "ordersResponse", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/request/pagination/OrderResponse;", "isShowAllOrders", "shouldShowAllOrders", "getCurrentRouteOrders", "currentRouteId", "(Ljava/lang/String;Lcom/thedasagroup/suminative/ui/orders/OrderScreenViewModel;ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "CloseStoreComposable", "onCloseStoreCheckChanged", "SectionHeader", "orderItem", "Lcom/thedasagroup/suminative/data/model/request/pagination/OrderItem;", "isExpanded", "onHeaderClicked", "onAcceptOrderWithDelayDialog", "onChangeStatus", "onUpdateStatusSilent", "Lkotlin/Function3;", "", "updateOrderMinutes", "PrintingPreviewDialog2", "order", "Lcom/thedasagroup/suminative/data/model/request/pagination/OrderItem2;", "onCancel", "productsScreenViewModel", "Lcom/thedasagroup/suminative/ui/products/ProductsScreenViewModel;", "ChangeStatusDialog", "AcceptOrderWithDelayDialog", "onAcceptOrderDelay", "Lkotlin/Function2;", "screenshotableComposable", "content", "Landroidx/compose/runtime/Composable;", "OrderScreen", "OrderButtonFun", "Lcom/thedasagroup/suminative/data/model/response/store_orders/Order;", "orderScreenViewModel", "Section", "Landroidx/compose/foundation/lazy/LazyListScope;", "onHeaderClick", "CartHeader", "cart", "Lcom/thedasagroup/suminative/data/model/response/store_orders/Cart;", "bgColor", "Landroidx/compose/ui/graphics/Color;", "CartHeader-4WTKRHQ", "(Lcom/thedasagroup/suminative/data/model/response/store_orders/Cart;J)V", "getOrderStatusString", "status", "deliveryType", "isNetworkConnected", "context", "Landroid/content/Context;", "TotalCartFigma", "title", "style", "Landroidx/compose/ui/text/TextStyle;", "isBold", "OrderSummery", "app_stagingGeneralDebug"})
public final class OrdersScreenKt {
    private static final float buttonWidth = 0.0F;
    
    public static final float getButtonWidth() {
        return 0.0F;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void OrderScreenTopFunction(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.OrderScreenViewModel viewModel, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> callOrders, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super android.graphics.Bitmap, kotlin.Unit> onPrintBill, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onTrackingUrlClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onUpdateShowAllOrders) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ExpandableList(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.OrderScreenViewModel viewModel, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> callOrders, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super android.graphics.Bitmap, kotlin.Unit> onPrintBill, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onTrackingUrlClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onUpdateShowAllOrders, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> ordersResponse, boolean isShowAllOrders, boolean shouldShowAllOrders) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public static final java.lang.Object getCurrentRouteOrders(@org.jetbrains.annotations.NotNull()
    java.lang.String currentRouteId, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.OrderScreenViewModel viewModel, boolean isShowAllOrders, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CloseStoreComposable(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.OrderScreenViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onCloseStoreCheckChanged) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SectionHeader(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem orderItem, boolean isExpanded, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onHeaderClicked, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.pagination.OrderItem, kotlin.Unit> onPrintBill, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.OrderScreenViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.pagination.OrderItem, kotlin.Unit> onAcceptOrderWithDelayDialog, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.pagination.OrderItem, kotlin.Unit> onChangeStatus, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super com.thedasagroup.suminative.data.model.request.pagination.OrderItem, ? super java.lang.Integer, ? super java.lang.Boolean, kotlin.Unit> onUpdateStatusSilent, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onTrackingUrlClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> updateOrderMinutes) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PrintingPreviewDialog2(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem2 order, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super android.graphics.Bitmap, kotlin.Unit> onPrintBill, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCancel, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.ProductsScreenViewModel productsScreenViewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ChangeStatusDialog(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.OrderScreenViewModel viewModel, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onChangeStatus, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCancel, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem orderItem) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AcceptOrderWithDelayDialog(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.OrderScreenViewModel viewModel, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super com.thedasagroup.suminative.data.model.request.pagination.OrderItem, ? super java.lang.Integer, kotlin.Unit> onAcceptOrderDelay, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCancel, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem orderItem) {
    }
    
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final kotlin.jvm.functions.Function0<android.graphics.Bitmap> screenshotableComposable(@org.jetbrains.annotations.NotNull()
    androidx.compose.runtime.internal.ComposableFunction0<kotlin.Unit> content) {
        return null;
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.foundation.ExperimentalFoundationApi.class})
    @androidx.compose.runtime.Composable()
    public static final void OrderScreen(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.pagination.OrderResponse ordersResponse, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.OrderScreenViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.pagination.OrderItem, kotlin.Unit> onPrintBill, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> callOrders, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onUpdateShowAllOrders, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.pagination.OrderItem, kotlin.Unit> onChangeStatus, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super com.thedasagroup.suminative.data.model.request.pagination.OrderItem, ? super java.lang.Integer, ? super java.lang.Boolean, kotlin.Unit> onUpdateStatusSilent, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onTrackingUrlClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.pagination.OrderItem, kotlin.Unit> onAcceptOrderWithDelayDialog, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.pagination.OrderItem, kotlin.Unit> updateOrderMinutes, boolean shouldShowAllOrders) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void OrderButtonFun(com.thedasagroup.suminative.data.model.response.store_orders.Order order, kotlin.jvm.functions.Function3<? super com.thedasagroup.suminative.data.model.request.pagination.OrderItem, ? super java.lang.Integer, ? super java.lang.Boolean, kotlin.Unit> onUpdateStatusSilent, com.thedasagroup.suminative.data.model.request.pagination.OrderItem orderItem, androidx.compose.ui.Modifier modifier, com.thedasagroup.suminative.ui.orders.OrderScreenViewModel orderScreenViewModel) {
    }
    
    public static final void Section(@org.jetbrains.annotations.NotNull()
    androidx.compose.foundation.lazy.LazyListScope $this$Section, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem orderItem, boolean isExpanded, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onHeaderClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.pagination.OrderItem, kotlin.Unit> onPrintBill, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.OrderScreenViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.pagination.OrderItem, kotlin.Unit> onAcceptOrderWithDelayDialog, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.pagination.OrderItem, kotlin.Unit> onChangeStatus, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super com.thedasagroup.suminative.data.model.request.pagination.OrderItem, ? super java.lang.Integer, ? super java.lang.Boolean, kotlin.Unit> onUpdateStatusSilent, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onTrackingUrlClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> updateOrderMinutes) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getOrderStatusString(int status, int deliveryType) {
        return null;
    }
    
    public static final boolean isNetworkConnected(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return false;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TotalCartFigma(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.text.TextStyle style, boolean isBold) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void OrderSummery(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem orderItem, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.OrderScreenViewModel orderScreenViewModel) {
    }
}