package com.thedasagroup.suminative.ui.payment;

/**
 * Helper object to show the Payment dialog
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J.\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t2\u0012\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\r0\u000bJ\u000e\u0010\u000e\u001a\u00020\r2\u0006\u0010\u0006\u001a\u00020\u0007\u00a8\u0006\u000f"}, d2 = {"Lcom/thedasagroup/suminative/ui/payment/PaymentDialogHelper;", "", "<init>", "()V", "showPaymentDialog", "Lcom/thedasagroup/suminative/ui/payment/PaymentFragment;", "activity", "Landroidx/fragment/app/FragmentActivity;", "order", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "onPaymentSuccess", "Lkotlin/Function1;", "Lcom/thedasagroup/suminative/data/model/response/store_orders/Order2;", "", "dismissPaymentDialog", "app_stagingGeneralDebug"})
public final class PaymentDialogHelper {
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.payment.PaymentDialogHelper INSTANCE = null;
    
    private PaymentDialogHelper() {
        super();
    }
    
    /**
     * Shows the payment dialog for a given order
     *
     * @param activity The FragmentActivity where the dialog should be shown
     * @param order The order to process payment for, or null for a new order
     * @return The PaymentFragment instance that was shown
     */
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.payment.PaymentFragment showPaymentDialog(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.FragmentActivity activity, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.store_orders.Order2, kotlin.Unit> onPaymentSuccess) {
        return null;
    }
    
    /**
     * Dismisses any currently showing payment dialog
     *
     * @param activity The FragmentActivity where the dialog was shown
     */
    public final void dismissPaymentDialog(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.FragmentActivity activity) {
    }
}