package com.thedasagroup.suminative.ui.payment;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u00b0\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0017\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\u0007\u0018\u0000 a2\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0002`aBc\b\u0007\u0012\b\b\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0006\u0010\u000e\u001a\u00020\u000f\u0012\u0006\u0010\u0010\u001a\u00020\u0011\u0012\u0006\u0010\u0012\u001a\u00020\u0013\u0012\u0006\u0010\u0014\u001a\u00020\u0015\u0012\u0006\u0010\u0016\u001a\u00020\u0017\u00a2\u0006\u0004\b\u0018\u0010\u0019J\u001a\u00104\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002070605H\u0086@\u00a2\u0006\u0002\u00108J*\u00109\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020:06052\u0006\u0010;\u001a\u00020<2\u0006\u0010=\u001a\u00020>H\u0086@\u00a2\u0006\u0002\u0010?J*\u0010@\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020:06052\u0006\u0010A\u001a\u00020B2\u0006\u0010;\u001a\u00020<H\u0086@\u00a2\u0006\u0002\u0010CJ*\u0010D\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020:06052\u0006\u0010;\u001a\u00020<2\u0006\u0010=\u001a\u00020>H\u0086@\u00a2\u0006\u0002\u0010?J\"\u0010E\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020:06052\u0006\u0010F\u001a\u00020GH\u0086@\u00a2\u0006\u0002\u0010HJ\u000e\u0010I\u001a\u00020J2\u0006\u0010A\u001a\u00020BJ0\u0010K\u001a\u00020J2\u0006\u0010F\u001a\u00020G2\f\u0010L\u001a\b\u0012\u0004\u0012\u00020J0M2\u0012\u0010N\u001a\u000e\u0012\u0004\u0012\u00020G\u0012\u0004\u0012\u00020J0OJ\u0010\u0010P\u001a\u00020J2\b\u0010F\u001a\u0004\u0018\u00010GJ\u000e\u0010Q\u001a\u00020J2\u0006\u0010;\u001a\u00020<J\u000e\u0010R\u001a\u00020J2\u0006\u0010R\u001a\u00020SJ\u0006\u0010T\u001a\u00020JJ\u000e\u0010U\u001a\u00020J2\u0006\u0010V\u001a\u00020GJ\u000e\u0010W\u001a\u00020J2\u0006\u0010X\u001a\u00020SJ\u0016\u0010Y\u001a\u00020J2\u0006\u0010Z\u001a\u00020[2\u0006\u0010\\\u001a\u00020SJ\u0006\u0010]\u001a\u00020SJ\u0006\u0010^\u001a\u00020SJ\b\u0010_\u001a\u00020JH\u0016R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010!R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010#R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010%R\u0011\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\'R\u0011\u0010\u0012\u001a\u00020\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010)R\u0011\u0010\u0014\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010+R\u0011\u0010\u0016\u001a\u00020\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010-R\u001c\u0010.\u001a\u0004\u0018\u00010/X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b0\u00101\"\u0004\b2\u00103\u00a8\u0006b"}, d2 = {"Lcom/thedasagroup/suminative/ui/payment/PaymentViewModel;", "Lcom/airbnb/mvrx/MavericksViewModel;", "Lcom/thedasagroup/suminative/ui/payment/PaymentState;", "initialState", "myGuavaCreateOrderUseCase", "Lcom/thedasagroup/suminative/domain/myguava/MyGuavaCreateOrderUseCase;", "getTerminalsUseCase", "Lcom/thedasagroup/suminative/domain/myguava/MyGuavaGetTerminalsUseCase;", "createSessionUseCase", "Lcom/thedasagroup/suminative/domain/myguava/MyGuavaCreateSessionUseCase;", "makePaymentUseCase", "Lcom/thedasagroup/suminative/domain/myguava/MyGuavaMakePaymentUseCase;", "makeRefundUseCase", "Lcom/thedasagroup/suminative/domain/myguava/MyGuavaMakeRefundUseCase;", "checkStatusUseCase", "Lcom/thedasagroup/suminative/domain/myguava/MyGuavaCheckStatusUseCase;", "orderUseCase", "Lcom/thedasagroup/suminative/ui/products/OrderUseCase;", "onlineOrderUseCase", "Lcom/thedasagroup/suminative/ui/products/PlaceOnlineOrderUseCase;", "guavaRepository", "Lcom/thedasagroup/suminative/data/repo/MyGuavaRepository;", "prefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "<init>", "(Lcom/thedasagroup/suminative/ui/payment/PaymentState;Lcom/thedasagroup/suminative/domain/myguava/MyGuavaCreateOrderUseCase;Lcom/thedasagroup/suminative/domain/myguava/MyGuavaGetTerminalsUseCase;Lcom/thedasagroup/suminative/domain/myguava/MyGuavaCreateSessionUseCase;Lcom/thedasagroup/suminative/domain/myguava/MyGuavaMakePaymentUseCase;Lcom/thedasagroup/suminative/domain/myguava/MyGuavaMakeRefundUseCase;Lcom/thedasagroup/suminative/domain/myguava/MyGuavaCheckStatusUseCase;Lcom/thedasagroup/suminative/ui/products/OrderUseCase;Lcom/thedasagroup/suminative/ui/products/PlaceOnlineOrderUseCase;Lcom/thedasagroup/suminative/data/repo/MyGuavaRepository;Lcom/thedasagroup/suminative/data/prefs/Prefs;)V", "getMyGuavaCreateOrderUseCase", "()Lcom/thedasagroup/suminative/domain/myguava/MyGuavaCreateOrderUseCase;", "getGetTerminalsUseCase", "()Lcom/thedasagroup/suminative/domain/myguava/MyGuavaGetTerminalsUseCase;", "getCreateSessionUseCase", "()Lcom/thedasagroup/suminative/domain/myguava/MyGuavaCreateSessionUseCase;", "getMakePaymentUseCase", "()Lcom/thedasagroup/suminative/domain/myguava/MyGuavaMakePaymentUseCase;", "getMakeRefundUseCase", "()Lcom/thedasagroup/suminative/domain/myguava/MyGuavaMakeRefundUseCase;", "getCheckStatusUseCase", "()Lcom/thedasagroup/suminative/domain/myguava/MyGuavaCheckStatusUseCase;", "getOrderUseCase", "()Lcom/thedasagroup/suminative/ui/products/OrderUseCase;", "getOnlineOrderUseCase", "()Lcom/thedasagroup/suminative/ui/products/PlaceOnlineOrderUseCase;", "getGuavaRepository", "()Lcom/thedasagroup/suminative/data/repo/MyGuavaRepository;", "getPrefs", "()Lcom/thedasagroup/suminative/data/prefs/Prefs;", "loopJob", "Lkotlinx/coroutines/Job;", "getLoopJob", "()Lkotlinx/coroutines/Job;", "setLoopJob", "(Lkotlinx/coroutines/Job;)V", "getTerminalList", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/my_guava/terminals/GetTerminalListResponse;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createSession", "Lcom/thedasagroup/suminative/data/model/response/my_guava/sessions/Session;", "terminal", "Lcom/thedasagroup/suminative/data/model/response/my_guava/terminals/Terminal;", "guavaOrder", "Lcom/thedasagroup/suminative/data/model/response/my_guava/orders/list_of_orders/GuavaOrder;", "(Lcom/thedasagroup/suminative/data/model/response/my_guava/terminals/Terminal;Lcom/thedasagroup/suminative/data/model/response/my_guava/orders/list_of_orders/GuavaOrder;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "makePayment", "order", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "(Lcom/thedasagroup/suminative/data/model/request/order/Order;Lcom/thedasagroup/suminative/data/model/response/my_guava/terminals/Terminal;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "makeRefund", "checkStatus", "sessionId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateOrder", "", "startCheckingPaymentStatus", "onSuccess", "Lkotlin/Function0;", "onFailure", "Lkotlin/Function1;", "cancelPayment", "selectTerminal", "showChangeDialog", "", "hideChangeDialog", "updateAmountGivenText", "amount", "showChangeCalculation", "show", "updatePaymentsMap", "personNumber", "", "isPaid", "isMyGuava", "isSumUp", "onCleared", "Factory", "Companion", "app_stagingGeneralDebug"})
public final class PaymentViewModel extends com.airbnb.mvrx.MavericksViewModel<com.thedasagroup.suminative.ui.payment.PaymentState> {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.domain.myguava.MyGuavaCreateOrderUseCase myGuavaCreateOrderUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.domain.myguava.MyGuavaGetTerminalsUseCase getTerminalsUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.domain.myguava.MyGuavaCreateSessionUseCase createSessionUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.domain.myguava.MyGuavaMakePaymentUseCase makePaymentUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.domain.myguava.MyGuavaMakeRefundUseCase makeRefundUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.domain.myguava.MyGuavaCheckStatusUseCase checkStatusUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.products.OrderUseCase orderUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.products.PlaceOnlineOrderUseCase onlineOrderUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.repo.MyGuavaRepository guavaRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.prefs.Prefs prefs = null;
    @org.jetbrains.annotations.Nullable()
    private kotlinx.coroutines.Job loopJob;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.payment.PaymentViewModel.Companion Companion = null;
    
    @dagger.assisted.AssistedInject()
    public PaymentViewModel(@dagger.assisted.Assisted()
    @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.payment.PaymentState initialState, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.myguava.MyGuavaCreateOrderUseCase myGuavaCreateOrderUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.myguava.MyGuavaGetTerminalsUseCase getTerminalsUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.myguava.MyGuavaCreateSessionUseCase createSessionUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.myguava.MyGuavaMakePaymentUseCase makePaymentUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.myguava.MyGuavaMakeRefundUseCase makeRefundUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.myguava.MyGuavaCheckStatusUseCase checkStatusUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.OrderUseCase orderUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.PlaceOnlineOrderUseCase onlineOrderUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.MyGuavaRepository guavaRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        super(null, null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.myguava.MyGuavaCreateOrderUseCase getMyGuavaCreateOrderUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.myguava.MyGuavaGetTerminalsUseCase getGetTerminalsUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.myguava.MyGuavaCreateSessionUseCase getCreateSessionUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.myguava.MyGuavaMakePaymentUseCase getMakePaymentUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.myguava.MyGuavaMakeRefundUseCase getMakeRefundUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.myguava.MyGuavaCheckStatusUseCase getCheckStatusUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.products.OrderUseCase getOrderUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.products.PlaceOnlineOrderUseCase getOnlineOrderUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.repo.MyGuavaRepository getGuavaRepository() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.prefs.Prefs getPrefs() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.coroutines.Job getLoopJob() {
        return null;
    }
    
    public final void setLoopJob(@org.jetbrains.annotations.Nullable()
    kotlinx.coroutines.Job p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTerminalList(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.terminals.GetTerminalListResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object createSession(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.my_guava.terminals.Terminal terminal, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GuavaOrder guavaOrder, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object makePayment(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.my_guava.terminals.Terminal terminal, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object makeRefund(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.my_guava.terminals.Terminal terminal, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GuavaOrder guavaOrder, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object checkStatus(@org.jetbrains.annotations.NotNull()
    java.lang.String sessionId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session>>> $completion) {
        return null;
    }
    
    public final void updateOrder(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order) {
    }
    
    public final void startCheckingPaymentStatus(@org.jetbrains.annotations.NotNull()
    java.lang.String sessionId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSuccess, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onFailure) {
    }
    
    public final void cancelPayment(@org.jetbrains.annotations.Nullable()
    java.lang.String sessionId) {
    }
    
    public final void selectTerminal(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.my_guava.terminals.Terminal terminal) {
    }
    
    public final void showChangeDialog(boolean showChangeDialog) {
    }
    
    public final void hideChangeDialog() {
    }
    
    public final void updateAmountGivenText(@org.jetbrains.annotations.NotNull()
    java.lang.String amount) {
    }
    
    public final void showChangeCalculation(boolean show) {
    }
    
    public final void updatePaymentsMap(int personNumber, boolean isPaid) {
    }
    
    public final boolean isMyGuava() {
        return false;
    }
    
    public final boolean isSumUp() {
        return false;
    }
    
    @java.lang.Override()
    public void onCleared() {
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u001b\u0010\u0006\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0003H\u0096\u0001J\u0013\u0010\n\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0007\u001a\u00020\bH\u0096\u0001\u00a8\u0006\u000b"}, d2 = {"Lcom/thedasagroup/suminative/ui/payment/PaymentViewModel$Companion;", "Lcom/airbnb/mvrx/MavericksViewModelFactory;", "Lcom/thedasagroup/suminative/ui/payment/PaymentViewModel;", "Lcom/thedasagroup/suminative/ui/payment/PaymentState;", "<init>", "()V", "create", "viewModelContext", "Lcom/airbnb/mvrx/ViewModelContext;", "state", "initialState", "app_stagingGeneralDebug"})
    public static final class Companion implements com.airbnb.mvrx.MavericksViewModelFactory<com.thedasagroup.suminative.ui.payment.PaymentViewModel, com.thedasagroup.suminative.ui.payment.PaymentState> {
        
        private Companion() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.payment.PaymentViewModel create(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.payment.PaymentState state) {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.payment.PaymentState initialState(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext) {
            return null;
        }
    }
    
    @dagger.assisted.AssistedFactory()
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\bg\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001J\u0010\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0003H&\u00a8\u0006\u0006"}, d2 = {"Lcom/thedasagroup/suminative/ui/payment/PaymentViewModel$Factory;", "Lcom/airbnb/mvrx/hilt/AssistedViewModelFactory;", "Lcom/thedasagroup/suminative/ui/payment/PaymentViewModel;", "Lcom/thedasagroup/suminative/ui/payment/PaymentState;", "create", "state", "app_stagingGeneralDebug"})
    public static abstract interface Factory extends com.airbnb.mvrx.hilt.AssistedViewModelFactory<com.thedasagroup.suminative.ui.payment.PaymentViewModel, com.thedasagroup.suminative.ui.payment.PaymentState> {
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public abstract com.thedasagroup.suminative.ui.payment.PaymentViewModel create(@org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.payment.PaymentState state);
    }
}