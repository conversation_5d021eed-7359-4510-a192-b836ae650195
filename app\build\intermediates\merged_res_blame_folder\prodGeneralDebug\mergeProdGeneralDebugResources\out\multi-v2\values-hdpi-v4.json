{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-hdpi-v4/values-hdpi-v4.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-hdpi-v4\\values-hdpi-v4.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,155,216,275,336,388,447,518,574,623,684", "endColumns": "55,43,60,58,60,51,58,70,55,48,60,59", "endOffsets": "106,150,211,270,331,383,442,513,569,618,679,739"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-hdpi-v4\\values-hdpi-v4.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "6", "endColumns": "13", "endOffsets": "327"}, "to": {"startLines": "14", "startColumns": "4", "startOffsets": "744", "endLines": "18", "endColumns": "13", "endOffsets": "1016"}}]}]}