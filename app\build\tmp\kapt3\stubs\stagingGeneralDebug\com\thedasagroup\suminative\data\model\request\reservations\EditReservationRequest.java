package com.thedasagroup.suminative.data.model.request.reservations;

/**
 * Request model for editing a reservation
 * This maintains the original API structure for the PUT request
 */
@kotlinx.serialization.Serializable()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u001c\n\u0002\u0010\u000b\n\u0002\b\u0006\b\u0087\b\u0018\u0000 (2\u00020\u0001:\u0002\'(B7\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\b\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0003\u00a2\u0006\u0004\b\n\u0010\u000bJ\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0006H\u00c6\u0003J\t\u0010 \u001a\u00020\u0003H\u00c6\u0003JE\u0010!\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\u00062\b\b\u0002\u0010\t\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\"\u001a\u00020#2\b\u0010$\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010%\u001a\u00020\u0003H\u00d6\u0001J\t\u0010&\u001a\u00020\u0006H\u00d6\u0001R\u001c\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\f\u0010\r\u001a\u0004\b\u000e\u0010\u000fR\u001c\u0010\u0004\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0010\u0010\r\u001a\u0004\b\u0011\u0010\u000fR\u001c\u0010\u0005\u001a\u00020\u00068\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0012\u0010\r\u001a\u0004\b\u0013\u0010\u0014R\u001c\u0010\u0007\u001a\u00020\u00068\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0015\u0010\r\u001a\u0004\b\u0016\u0010\u0014R\u001c\u0010\b\u001a\u00020\u00068\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0017\u0010\r\u001a\u0004\b\u0018\u0010\u0014R\u001c\u0010\t\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0019\u0010\r\u001a\u0004\b\u001a\u0010\u000f\u00a8\u0006)"}, d2 = {"Lcom/thedasagroup/suminative/data/model/request/reservations/EditReservationRequest;", "", "customerId", "", "tableId", "reservationTime", "", "guestName", "guestPhone", "partySize", "<init>", "(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V", "getCustomerId$annotations", "()V", "getCustomerId", "()I", "getTableId$annotations", "getTableId", "getReservationTime$annotations", "getReservationTime", "()Ljava/lang/String;", "getGuestName$annotations", "getGuestName", "getGuestPhone$annotations", "getGuestPhone", "getPartySize$annotations", "getPartySize", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "toString", "$serializer", "Companion", "app_stagingGeneralDebug"})
public final class EditReservationRequest {
    private final int customerId = 0;
    private final int tableId = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String reservationTime = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String guestName = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String guestPhone = null;
    private final int partySize = 0;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.data.model.request.reservations.EditReservationRequest.Companion Companion = null;
    
    public EditReservationRequest(int customerId, int tableId, @org.jetbrains.annotations.NotNull()
    java.lang.String reservationTime, @org.jetbrains.annotations.NotNull()
    java.lang.String guestName, @org.jetbrains.annotations.NotNull()
    java.lang.String guestPhone, int partySize) {
        super();
    }
    
    public final int getCustomerId() {
        return 0;
    }
    
    @kotlinx.serialization.SerialName(value = "customerId")
    @java.lang.Deprecated()
    public static void getCustomerId$annotations() {
    }
    
    public final int getTableId() {
        return 0;
    }
    
    @kotlinx.serialization.SerialName(value = "tableId")
    @java.lang.Deprecated()
    public static void getTableId$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getReservationTime() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "reservationTime")
    @java.lang.Deprecated()
    public static void getReservationTime$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getGuestName() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "guestName")
    @java.lang.Deprecated()
    public static void getGuestName$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getGuestPhone() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "guestPhone")
    @java.lang.Deprecated()
    public static void getGuestPhone$annotations() {
    }
    
    public final int getPartySize() {
        return 0;
    }
    
    @kotlinx.serialization.SerialName(value = "partySize")
    @java.lang.Deprecated()
    public static void getPartySize$annotations() {
    }
    
    public final int component1() {
        return 0;
    }
    
    public final int component2() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    public final int component6() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.model.request.reservations.EditReservationRequest copy(int customerId, int tableId, @org.jetbrains.annotations.NotNull()
    java.lang.String reservationTime, @org.jetbrains.annotations.NotNull()
    java.lang.String guestName, @org.jetbrains.annotations.NotNull()
    java.lang.String guestPhone, int partySize) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    /**
     * Request model for editing a reservation
     * This maintains the original API structure for the PUT request
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0003\u0010\u0004J\u0015\u0010\u0005\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00070\u0006\u00a2\u0006\u0002\u0010\bJ\u000e\u0010\t\u001a\u00020\u00022\u0006\u0010\n\u001a\u00020\u000bJ\u0016\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0002R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014\u00a8\u0006\u0015"}, d2 = {"com/thedasagroup/suminative/data/model/request/reservations/EditReservationRequest.$serializer", "Lkotlinx/serialization/internal/GeneratedSerializer;", "Lcom/thedasagroup/suminative/data/model/request/reservations/EditReservationRequest;", "<init>", "()V", "childSerializers", "", "Lkotlinx/serialization/KSerializer;", "()[Lkotlinx/serialization/KSerializer;", "deserialize", "decoder", "Lkotlinx/serialization/encoding/Decoder;", "serialize", "", "encoder", "Lkotlinx/serialization/encoding/Encoder;", "value", "descriptor", "Lkotlinx/serialization/descriptors/SerialDescriptor;", "getDescriptor", "()Lkotlinx/serialization/descriptors/SerialDescriptor;", "app_stagingGeneralDebug"})
    @java.lang.Deprecated()
    public static final class $serializer implements kotlinx.serialization.internal.GeneratedSerializer<com.thedasagroup.suminative.data.model.request.reservations.EditReservationRequest> {
        @org.jetbrains.annotations.NotNull()
        public static final com.thedasagroup.suminative.data.model.request.reservations.EditReservationRequest.$serializer INSTANCE = null;
        @org.jetbrains.annotations.NotNull()
        private static final kotlinx.serialization.descriptors.SerialDescriptor descriptor = null;
        
        /**
         * Request model for editing a reservation
         * This maintains the original API structure for the PUT request
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<?>[] childSerializers() {
            return null;
        }
        
        /**
         * Request model for editing a reservation
         * This maintains the original API structure for the PUT request
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final com.thedasagroup.suminative.data.model.request.reservations.EditReservationRequest deserialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Decoder decoder) {
            return null;
        }
        
        /**
         * Request model for editing a reservation
         * This maintains the original API structure for the PUT request
         */
        @java.lang.Override()
        public final void serialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Encoder encoder, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.data.model.request.reservations.EditReservationRequest value) {
        }
        
        private $serializer() {
            super();
        }
        
        /**
         * Request model for editing a reservation
         * This maintains the original API structure for the PUT request
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.descriptors.SerialDescriptor getDescriptor() {
            return null;
        }
    }
    
    /**
     * Request model for editing a reservation
     * This maintains the original API structure for the PUT request
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a8\u0006\u0007"}, d2 = {"Lcom/thedasagroup/suminative/data/model/request/reservations/EditReservationRequest$Companion;", "", "<init>", "()V", "serializer", "Lkotlinx/serialization/KSerializer;", "Lcom/thedasagroup/suminative/data/model/request/reservations/EditReservationRequest;", "app_stagingGeneralDebug"})
    public static final class Companion {
        
        /**
         * Request model for editing a reservation
         * This maintains the original API structure for the PUT request
         */
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<com.thedasagroup.suminative.data.model.request.reservations.EditReservationRequest> serializer() {
            return null;
        }
        
        private Companion() {
            super();
        }
    }
}