<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res"><file name="buy_some_items" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\buy_some_items.xml" qualifiers="" type="drawable"/><file name="cart_minus" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\cart_minus.xml" qualifiers="" type="drawable"/><file name="cart_plus" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\cart_plus.xml" qualifiers="" type="drawable"/><file name="collapse" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\collapse.xml" qualifiers="" type="drawable"/><file name="dasa_logo" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\dasa_logo.png" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_note" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\ic_note.xml" qualifiers="" type="drawable"/><file name="profile_icon" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\profile_icon.xml" qualifiers="" type="drawable"/><file name="store_closed" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\store_closed.png" qualifiers="" type="drawable"/><file name="trash" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\trash.xml" qualifiers="" type="drawable"/><file name="nunito" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\font\nunito.xml" qualifiers="" type="font"/><file name="nunito_bold" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\font\nunito_bold.ttf" qualifiers="" type="font"/><file name="nunito_regular" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\font\nunito_regular.ttf" qualifiers="" type="font"/><file name="popins" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\font\popins.xml" qualifiers="" type="font"/><file name="poppins_bold" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\font\poppins_bold.ttf" qualifiers="" type="font"/><file name="poppins_regular" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\font\poppins_regular.ttf" qualifiers="" type="font"/><file name="activity_lcd" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\layout\activity_lcd.xml" qualifiers="" type="layout"/><file name="activity_tracking" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\layout\activity_tracking.xml" qualifiers="" type="layout"/><file name="dialog_cancel_order" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\layout\dialog_cancel_order.xml" qualifiers="" type="layout"/><file name="dialog_new_order" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\layout\dialog_new_order.xml" qualifiers="" type="layout"/><file name="dialog_update_order" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\layout\dialog_update_order.xml" qualifiers="" type="layout"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="cancelled_order" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\raw\cancelled_order.mp3" qualifiers="" type="raw"/><file name="courier_arriving" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\raw\courier_arriving.mp3" qualifiers="" type="raw"/><file name="courier_assigned" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\raw\courier_assigned.mp3" qualifiers="" type="raw"/><file name="delivered" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\raw\delivered.mp3" qualifiers="" type="raw"/><file name="delivery_imminent" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\raw\delivery_imminent.mp3" qualifiers="" type="raw"/><file name="notification" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\raw\notification.mp3" qualifiers="" type="raw"/><file name="out_for_delivery" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\raw\out_for_delivery.mp3" qualifiers="" type="raw"/><file name="schedule_order" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\raw\schedule_order.mp3" qualifiers="" type="raw"/><file path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FF2E7D32</color><color name="purple_500">#FF2E7D32</color><color name="purple_700">#FF2E7D32</color><color name="teal_200">#FF2E7D32</color><color name="teal_700">#FF2E7D32</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\values\strings.xml" qualifiers=""><string name="login_heading_text">Login</string><string name="login_email_id_or_phone_label">Email ID or Mobile Number</string><string name="login_password_label">Password</string><string name="login_button_text">Login</string><string name="forgot_password">Forgot Password?</string><string name="icon_password_visible">Password Visible</string><string name="icon_password_hidden">Password Hidden</string><string name="empty_string"/><string name="login_error_msg_empty_email_mobile">Please enter your email or mobile number</string><string name="login_error_msg_empty_password">Please enter your password</string><string name="do_not_have_account">Don\'t have an account?</string><string name="register">Register</string><string name="registration_heading_text">Registration</string><string name="registration_email_label">Email ID</string><string name="registration_mobile_label">Mobile Number</string><string name="registration_password_label">Password</string><string name="registration_confirm_password_label">Confirm Password</string><string name="registration_button_text">Register</string><string name="back_to_login">Back to Login</string><string name="registration_error_msg_empty_email">Please enter your email id</string><string name="registration_error_msg_empty_mobile">Please enter your mobile number</string><string name="registration_error_msg_empty_password">Please enter your password</string><string name="registration_error_msg_empty_confirm_password">Please confirm your password</string><string name="registration_error_msg_password_mismatch">Please enter the same password here as above</string><string name="dashboard_title_welcome_user">Welcome User</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\values\strings_app.xml" qualifiers=""><string name="app_name">DasaPOS</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\values-v21\themes.xml" qualifiers="v21"><style name="Theme.SumiNative" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryDark">@color/purple_700</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorAccent">@color/purple_200</item>
        <item name="android:statusBarColor">@color/purple_700</item>
        <item name="android:windowLightStatusBar">true</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="stagingGeneral$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\stagingGeneral\res"/><source path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res"/><source path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="stagingGeneral" generated-set="stagingGeneral$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\stagingGeneral\res"/><source path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res"><file name="buy_some_items" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\buy_some_items.xml" qualifiers="" type="drawable"/><file name="cart_minus" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\cart_minus.xml" qualifiers="" type="drawable"/><file name="cart_plus" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\cart_plus.xml" qualifiers="" type="drawable"/><file name="collapse" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\collapse.xml" qualifiers="" type="drawable"/><file name="dasa_logo" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\dasa_logo.png" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_note" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\ic_note.xml" qualifiers="" type="drawable"/><file name="profile_icon" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\profile_icon.xml" qualifiers="" type="drawable"/><file name="store_closed" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\store_closed.png" qualifiers="" type="drawable"/><file name="trash" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\drawable\trash.xml" qualifiers="" type="drawable"/><file name="nunito" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\font\nunito.xml" qualifiers="" type="font"/><file name="nunito_bold" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\font\nunito_bold.ttf" qualifiers="" type="font"/><file name="nunito_regular" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\font\nunito_regular.ttf" qualifiers="" type="font"/><file name="popins" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\font\popins.xml" qualifiers="" type="font"/><file name="poppins_bold" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\font\poppins_bold.ttf" qualifiers="" type="font"/><file name="poppins_regular" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\font\poppins_regular.ttf" qualifiers="" type="font"/><file name="activity_lcd" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\layout\activity_lcd.xml" qualifiers="" type="layout"/><file name="activity_tracking" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\layout\activity_tracking.xml" qualifiers="" type="layout"/><file name="dialog_cancel_order" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\layout\dialog_cancel_order.xml" qualifiers="" type="layout"/><file name="dialog_new_order" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\layout\dialog_new_order.xml" qualifiers="" type="layout"/><file name="dialog_update_order" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\layout\dialog_update_order.xml" qualifiers="" type="layout"/><file name="main_menu" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\menu\main_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="cancelled_order" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\raw\cancelled_order.mp3" qualifiers="" type="raw"/><file name="courier_arriving" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\raw\courier_arriving.mp3" qualifiers="" type="raw"/><file name="courier_assigned" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\raw\courier_assigned.mp3" qualifiers="" type="raw"/><file name="delivered" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\raw\delivered.mp3" qualifiers="" type="raw"/><file name="delivery_imminent" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\raw\delivery_imminent.mp3" qualifiers="" type="raw"/><file name="notification" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\raw\notification.mp3" qualifiers="" type="raw"/><file name="out_for_delivery" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\raw\out_for_delivery.mp3" qualifiers="" type="raw"/><file name="schedule_order" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\raw\schedule_order.mp3" qualifiers="" type="raw"/><file path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FF2E7D32</color><color name="purple_500">#FF2E7D32</color><color name="purple_700">#FF2E7D32</color><color name="teal_200">#FF2E7D32</color><color name="teal_700">#FF2E7D32</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\values\strings.xml" qualifiers=""><string name="login_heading_text">Login</string><string name="login_email_id_or_phone_label">Email ID or Mobile Number</string><string name="login_password_label">Password</string><string name="login_button_text">Login</string><string name="forgot_password">Forgot Password?</string><string name="icon_password_visible">Password Visible</string><string name="icon_password_hidden">Password Hidden</string><string name="empty_string"/><string name="login_error_msg_empty_email_mobile">Please enter your email or mobile number</string><string name="login_error_msg_empty_password">Please enter your password</string><string name="do_not_have_account">Don\'t have an account?</string><string name="register">Register</string><string name="registration_heading_text">Registration</string><string name="registration_email_label">Email ID</string><string name="registration_mobile_label">Mobile Number</string><string name="registration_password_label">Password</string><string name="registration_confirm_password_label">Confirm Password</string><string name="registration_button_text">Register</string><string name="back_to_login">Back to Login</string><string name="registration_error_msg_empty_email">Please enter your email id</string><string name="registration_error_msg_empty_mobile">Please enter your mobile number</string><string name="registration_error_msg_empty_password">Please enter your password</string><string name="registration_error_msg_empty_confirm_password">Please confirm your password</string><string name="registration_error_msg_password_mismatch">Please enter the same password here as above</string><string name="dashboard_title_welcome_user">Welcome User</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\values\strings_app.xml" qualifiers=""><string name="app_name">DasaPOS</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.SumiNative" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryDark">@color/purple_700</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorAccent">@color/purple_200</item>
        <item name="android:statusBarColor">@color/purple_700</item>
        <item name="android:windowLightStatusBar">true</item>
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\values-v21\themes.xml" qualifiers="v21"><style name="Theme.SumiNative" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryDark">@color/purple_700</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorAccent">@color/purple_200</item>
        <item name="android:statusBarColor">@color/purple_700</item>
        <item name="android:windowLightStatusBar">true</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source><source path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\build\generated\res\resValues\stagingGeneral\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\build\generated\res\processStagingGeneralDebugGoogleServices"/><source path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\build\generated\res\injectCrashlyticsMappingFileIdStagingGeneralDebug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\build\generated\res\resValues\stagingGeneral\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\build\generated\res\processStagingGeneralDebugGoogleServices"><file path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\build\generated\res\processStagingGeneralDebugGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">552125958429</string><string name="google_api_key" translatable="false">AIzaSyBn8bybiTibHkeRacv0uSoJUEX19kblmBI</string><string name="google_app_id" translatable="false">1:552125958429:android:62da8feb4d68fe94d6b9f4</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyBn8bybiTibHkeRacv0uSoJUEX19kblmBI</string><string name="google_storage_bucket" translatable="false">dasadirect.firebasestorage.app</string><string name="project_id" translatable="false">dasadirect</string></file></source><source path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\build\generated\res\injectCrashlyticsMappingFileIdStagingGeneralDebug"><file path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\build\generated\res\injectCrashlyticsMappingFileIdStagingGeneralDebug\values\com_google_firebase_crashlytics_mappingfileid.xml" qualifiers=""><string name="com.google.firebase.crashlytics.mapping_file_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">00000000000000000000000000000000</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="variant$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\stagingGeneralDebug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="variant" generated-set="variant$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\stagingGeneralDebug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processStagingGeneralDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processStagingGeneralDebugGoogleServices" generated-set="res-processStagingGeneralDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-injectCrashlyticsMappingFileIdStagingGeneralDebug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-injectCrashlyticsMappingFileIdStagingGeneralDebug" generated-set="res-injectCrashlyticsMappingFileIdStagingGeneralDebug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>