package com.thedasagroup.suminative.ui.reservations;

import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.ReservationsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class GetReservationAreasUseCase_Factory implements Factory<GetReservationAreasUseCase> {
  private final Provider<ReservationsRepository> reservationsRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  public GetReservationAreasUseCase_Factory(
      Provider<ReservationsRepository> reservationsRepositoryProvider,
      Provider<Prefs> prefsProvider) {
    this.reservationsRepositoryProvider = reservationsRepositoryProvider;
    this.prefsProvider = prefsProvider;
  }

  @Override
  public GetReservationAreasUseCase get() {
    return newInstance(reservationsRepositoryProvider.get(), prefsProvider.get());
  }

  public static GetReservationAreasUseCase_Factory create(
      Provider<ReservationsRepository> reservationsRepositoryProvider,
      Provider<Prefs> prefsProvider) {
    return new GetReservationAreasUseCase_Factory(reservationsRepositoryProvider, prefsProvider);
  }

  public static GetReservationAreasUseCase newInstance(
      ReservationsRepository reservationsRepository, Prefs prefs) {
    return new GetReservationAreasUseCase(reservationsRepository, prefs);
  }
}
