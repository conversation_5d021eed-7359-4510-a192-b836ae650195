package com.thedasagroup.suminative.di;

@dagger.Module()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\bg\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\n\u0012\u0002\b\u0003\u0012\u0002\b\u00030\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\'\u00a8\u0006\u0006"}, d2 = {"Lcom/thedasagroup/suminative/di/PaymentModule;", "", "paymentViewModelFactory", "Lcom/airbnb/mvrx/hilt/AssistedViewModelFactory;", "factory", "Lcom/thedasagroup/suminative/ui/payment/PaymentViewModel$Factory;", "app_stagingGeneralDebug"})
@dagger.hilt.InstallIn(value = {com.airbnb.mvrx.hilt.MavericksViewModelComponent.class})
public abstract interface PaymentModule {
    
    @dagger.Binds()
    @dagger.multibindings.IntoMap()
    @com.airbnb.mvrx.hilt.ViewModelKey(value = com.thedasagroup.suminative.ui.payment.PaymentViewModel.class)
    @org.jetbrains.annotations.NotNull()
    public abstract com.airbnb.mvrx.hilt.AssistedViewModelFactory<?, ?> paymentViewModelFactory(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.payment.PaymentViewModel.Factory factory);
}