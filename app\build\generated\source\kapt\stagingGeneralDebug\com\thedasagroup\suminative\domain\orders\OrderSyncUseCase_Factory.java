package com.thedasagroup.suminative.domain.orders;

import com.thedasagroup.suminative.data.database.LocalOrderRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class OrderSyncUseCase_Factory implements Factory<OrderSyncUseCase> {
  private final Provider<LocalOrderRepository> orderRepositoryProvider;

  public OrderSyncUseCase_Factory(Provider<LocalOrderRepository> orderRepositoryProvider) {
    this.orderRepositoryProvider = orderRepositoryProvider;
  }

  @Override
  public OrderSyncUseCase get() {
    return newInstance(orderRepositoryProvider.get());
  }

  public static OrderSyncUseCase_Factory create(
      Provider<LocalOrderRepository> orderRepositoryProvider) {
    return new OrderSyncUseCase_Factory(orderRepositoryProvider);
  }

  public static OrderSyncUseCase newInstance(LocalOrderRepository orderRepository) {
    return new OrderSyncUseCase(orderRepository);
  }
}
