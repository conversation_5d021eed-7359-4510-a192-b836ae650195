package com.thedasagroup.suminative.ui.reservations

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.mvrx.compose.collectAsState
import com.airbnb.mvrx.compose.mavericksActivityViewModel
import com.thedasagroup.suminative.data.model.response.reservations.Area
import com.thedasagroup.suminative.data.model.response.reservations.Table
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlin.math.max

@AndroidEntryPoint
class AreaTableSelectionActivity : ComponentActivity() {

    companion object {
        const val EXTRA_SELECTED_AREA_ID = "selected_area_id"
        const val EXTRA_SELECTED_AREA_NAME = "selected_area_name"
        const val EXTRA_SELECTED_TABLE_ID = "selected_table_id"
        const val EXTRA_SELECTED_TABLE_NAME = "selected_table_name"
        const val EXTRA_SELECTED_TABLE_CAPACITY = "selected_table_capacity"
        const val EXTRA_EXCLUDED_TABLE_IDS = "excluded_table_ids"

        fun createIntent(context: Context, excludedTableIds: List<Int> = emptyList()): Intent {
            return Intent(context, AreaTableSelectionActivity::class.java).apply {
                putIntegerArrayListExtra(EXTRA_EXCLUDED_TABLE_IDS, ArrayList(excludedTableIds))
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Get excluded table IDs from intent
        val excludedTableIds = intent.getIntegerArrayListExtra(EXTRA_EXCLUDED_TABLE_IDS) ?: arrayListOf()

        setContent {
            SumiNativeTheme {
                AreaTableSelectionScreen(
                    excludedTableIds = excludedTableIds,
                    onAreaTableSelected = { area, table ->
                        val resultIntent = Intent().apply {
                            putExtra(EXTRA_SELECTED_AREA_ID, area.id)
                            putExtra(EXTRA_SELECTED_AREA_NAME, area.description)
                            putExtra(EXTRA_SELECTED_TABLE_ID, table.id)
                            putExtra(EXTRA_SELECTED_TABLE_NAME, table.tableName)
                            putExtra(EXTRA_SELECTED_TABLE_CAPACITY, table.seatingCapacity)
                        }
                        setResult(RESULT_OK, resultIntent)
                        finish()
                    },
                    onBackPressed = {
                        setResult(RESULT_CANCELED)
                        finish()
                    }
                )
            }
        }
    }
}

@Composable
fun AreaTableSelectionScreen(
    excludedTableIds: List<Int> = emptyList(),
    onAreaTableSelected: (Area, Table) -> Unit,
    onBackPressed: () -> Unit
) {
    val viewModel: ReservationsViewModel = mavericksActivityViewModel()
    val state by viewModel.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.loadReservationAreas()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(24.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Button(
                onClick = onBackPressed,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.Gray.copy(alpha = 0.2f),
                    contentColor = Color.Black
                )
            ) {
                Text("Back")
            }
            
            Text(
                text = if (state.selectedAreaId == null) "Select a dining section:" else "Select a table:",
                fontSize = 24.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Black
            )
            
            Spacer(modifier = Modifier.width(80.dp)) // Balance the back button
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Content based on selection state
        if (state.selectedAreaId == null) {
            // Show areas
            AreaSelectionContent(
                state = state,
                onAreaSelected = { area ->
                    viewModel.loadReservationTables(area.id)
                }
            )
        } else {
            // Show tables for selected area
            TableSelectionContent(
                state = state,
                excludedTableIds = excludedTableIds,
                onTableSelected = { table ->
                    state.areasResponse.invoke()?.let { areas ->
                        val selectedArea = areas.find { it.id == state.selectedAreaId }
                        if (selectedArea != null) {
                            onAreaTableSelected(selectedArea, table)
                        }
                    }
                },
                onBackToAreas = {
                    viewModel.clearAreaSelection()
                }
            )
        }
    }
}

@Composable
fun AreaSelectionContent(
    state: ReservationsState,
    onAreaSelected: (Area) -> Unit
) {
    when (val areasResponse = state.areasResponse) {
        is com.airbnb.mvrx.Loading -> {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    color = Color(0xFF2E7D32) // Green color matching POS theme
                )
            }
        }
        is com.airbnb.mvrx.Success -> {
            val areas = areasResponse.invoke()

            // Auto-select area if there's only one
            LaunchedEffect(areas) {
                if (areas.size == 1) {
                    onAreaSelected(areas.first())
                }
            }

            if (areas.isEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No dining sections available",
                        fontSize = 18.sp,
                        color = Color.Gray
                    )
                }
            } else if (areas.size == 1) {
                // Show loading indicator while auto-selecting single area
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        CircularProgressIndicator(
                            color = Color(0xFF2E7D32)
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "Loading ${areas.first().description}...",
                            fontSize = 16.sp,
                            color = Color(0xFF2E7D32),
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            } else {
                // Show area selection grid when multiple areas available
                LazyVerticalGrid(
                    columns = GridCells.Fixed(3),
                    horizontalArrangement = Arrangement.spacedBy(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    items(areas) { area ->
                        AreaCard(
                            area = area,
                            onClick = { onAreaSelected(area) }
                        )
                    }
                }
            }
        }
        is com.airbnb.mvrx.Fail -> {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Failed to load dining sections",
                        fontSize = 18.sp,
                        color = Color.Red
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Please try again",
                        fontSize = 14.sp,
                        color = Color.Gray
                    )
                }
            }
        }
        else -> {
            // Uninitialized state
        }
    }
}

@Composable
fun AreaCard(
    area: Area,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(80.dp)
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF2E7D32) // Green background matching POS theme
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = area.description,
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color.White,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun TableSelectionContent(
    state: ReservationsState,
    excludedTableIds: List<Int> = emptyList(),
    onTableSelected: (Table) -> Unit,
    onBackToAreas: () -> Unit
) {
    Column {
        // Back to areas button
        Button(
            onClick = onBackToAreas,
            colors = ButtonDefaults.buttonColors(
                containerColor = Color.Gray.copy(alpha = 0.2f),
                contentColor = Color.Black
            ),
            modifier = Modifier.padding(bottom = 16.dp)
        ) {
            Text("← Back to Sections")
        }

        when (val tablesResponse = state.tablesResponse) {
            is com.airbnb.mvrx.Loading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = Color(0xFF2E7D32)
                    )
                }
            }
            is com.airbnb.mvrx.Success -> {
                val allTables = tablesResponse.invoke()
                // Filter out already selected tables
                val availableTables = allTables.filter { table ->
                    table.id !in excludedTableIds
                }

                // Auto-select table if there's only one available (optional enhancement)
                // Commented out by default as users may want to see table details first
                /*
                LaunchedEffect(availableTables) {
                    if (availableTables.size == 1) {
                        onTableSelected(availableTables.first())
                    }
                }
                */

                if (availableTables.isEmpty()) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = if (allTables.isEmpty()) {
                                    "No tables available in this section"
                                } else {
                                    "All tables in this section are already selected"
                                },
                                fontSize = 18.sp,
                                color = Color.Gray,
                                textAlign = TextAlign.Center
                            )
                            if (allTables.isNotEmpty() && availableTables.isEmpty()) {
                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = "Try selecting a different section",
                                    fontSize = 14.sp,
                                    color = Color.Gray,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                    }
                } else {
                    // Use box layout with positioning based on tableDetailsJson
                    TableBoxLayout(
                        tables = availableTables,
                        onTableSelected = onTableSelected
                    )
                }
            }
            is com.airbnb.mvrx.Fail -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "Failed to load tables",
                            fontSize = 18.sp,
                            color = Color.Red
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Please try again",
                            fontSize = 14.sp,
                            color = Color.Gray
                        )
                    }
                }
            }
            else -> {
                // Uninitialized state
            }
        }
    }
}

@Composable
fun TableCard(
    table: Table,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val isOccupied = table.occupied
    val isReserved = table.reserved
    val tableDetails = table.getTableDetails()

    // Parse color from tableDetailsJson or use default
    val customColor = try {
        tableDetails?.color?.let { colorString ->
            if (colorString.startsWith("#")) {
                Color(android.graphics.Color.parseColor(colorString))
            } else {
                Color(0xFF2E7D32) // Default green
            }
        } ?: Color(0xFF2E7D32)
    } catch (e: Exception) {
        Color(0xFF2E7D32) // Default green if parsing fails
    }

    val backgroundColor = when {
        isOccupied -> Color.Red.copy(alpha = 0.7f)
        isReserved -> Color.Yellow.copy(alpha = 0.7f)
        else -> customColor.copy(alpha = 0.1f) // Light version of custom color
    }
    val textColor = when {
        isOccupied || isReserved -> Color.White
        else -> Color.Black
    }
    val borderColor = customColor

    // Determine shape from tableDetailsJson
    val shape = when (tableDetails?.shape?.uppercase()) {
        "CIRCLE" -> CircleShape
        "ROUND" -> CircleShape
        "SQUARE" -> RoundedCornerShape(8.dp)
        "RECTANGLE" -> RoundedCornerShape(12.dp)
        else -> RoundedCornerShape(12.dp) // Default to rectangle
    }

    Card(
        modifier = modifier
            .clickable(enabled = !isOccupied && !isReserved) { onClick() }
            .border(
                width = 2.dp,
                color = borderColor,
                shape = shape
            ),
        shape = shape,
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = table.tableName,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = textColor,
                    textAlign = TextAlign.Center,
                    maxLines = 1
                )
                Spacer(modifier = Modifier.height(2.dp))
                Text(
                    text = "Seats ${table.seatingCapacity}",
                    fontSize = 12.sp,
                    color = textColor.copy(alpha = 0.8f),
                    textAlign = TextAlign.Center,
                    maxLines = 1
                )

                // Status indicator
                if (isOccupied) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "Occupied",
                        fontSize = 12.sp,
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                } else if (isReserved) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "Reserved",
                        fontSize = 12.sp,
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
    }
}

@Composable
fun TableBoxLayout(
    tables: List<Table>,
    onTableSelected: (Table) -> Unit
) {
    // Separate tables with and without position data
    val tablesWithPosition = tables.filter { it.getTableDetails()?.position != null }
    val tablesWithoutPosition = tables.filter { it.getTableDetails()?.position == null }

    if (tablesWithPosition.isEmpty()) {
        // Fallback to grid layout if no tables have position data
//        LazyVerticalGrid(
//            columns = GridCells.Fixed(3),
//            horizontalArrangement = Arrangement.spacedBy(16.dp),
//            verticalArrangement = Arrangement.spacedBy(16.dp),
//            modifier = Modifier.fillMaxWidth()
//        ) {
//            items(tables) { table ->
//                TableCard(
//                    table = table,
//                    onClick = { onTableSelected(table) }
//                )
//            }
//        }
//        return
    }

    // Use fixed 8x8 grid dimensions (can be overridden by tableDetailsJson)
    val firstTableDetails = tablesWithPosition.firstOrNull()?.getTableDetails()
    val gridRows = firstTableDetails?.totalRows ?: 8
    val gridCols = firstTableDetails?.totalColumns ?: 8

    // Create a map of position to table for quick lookup
    val tablePositionMap = tablesWithPosition.associateBy { table ->
        val details = table.getTableDetails()
        Pair(details?.position?.row ?: 1, details?.position?.col ?: 1)
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Create rows
            for (row in 1..gridRows) {
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // Create columns
                    for (col in 1..gridCols) {
                        val table = tablePositionMap[Pair(row, col)]
                        if (table != null) {
                            TableCard(
                                table = table,
                                onClick = { onTableSelected(table) },
                                modifier = Modifier
                                    .weight(1f)
                                    .aspectRatio(1f) // Keep tables square
                            )
                        } else {
                            // Empty space to maintain grid structure - no visible grid lines
                            Spacer(modifier = Modifier.weight(1f))
                        }
                    }
                }
            }
        }

        // Add tables without position data at the bottom
//        if (tablesWithoutPosition.isNotEmpty()) {
//            Spacer(modifier = Modifier.height(24.dp))
//            Text(
//                text = "Additional Tables",
//                fontSize = 18.sp,
//                fontWeight = FontWeight.Medium,
//                color = Color.Black,
//                modifier = Modifier.padding(bottom = 16.dp)
//            )
//            LazyVerticalGrid(
//                columns = GridCells.Fixed(3),
//                horizontalArrangement = Arrangement.spacedBy(16.dp),
//                verticalArrangement = Arrangement.spacedBy(16.dp),
//                modifier = Modifier.fillMaxWidth()
//            ) {
//                items(tablesWithoutPosition) { table ->
//                    TableCard(
//                        table = table,
//                        onClick = { onTableSelected(table) }
//                    )
//                }
//            }
//        }
    }
}
