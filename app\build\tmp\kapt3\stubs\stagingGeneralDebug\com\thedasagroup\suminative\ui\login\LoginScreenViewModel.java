package com.thedasagroup.suminative.ui.login;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\b\u0018\u0000  2\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0002\u001f B#\b\u0007\u0012\b\b\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\u000e\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011J\u000e\u0010\u0012\u001a\u00020\u000f2\u0006\u0010\u0013\u001a\u00020\u0011J4\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00170\u00160\u00152\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0013\u001a\u00020\u00112\b\b\u0002\u0010\u0018\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0002\u0010\u001aJ\u000e\u0010\u001b\u001a\u00020\u000f2\u0006\u0010\u001c\u001a\u00020\u0011J\u000e\u0010\u001d\u001a\u00020\u000f2\u0006\u0010\u001c\u001a\u00020\u0011J\u000e\u0010\u001e\u001a\u00020\u000f2\u0006\u0010\u001c\u001a\u00020\u0011R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u0006!"}, d2 = {"Lcom/thedasagroup/suminative/ui/login/LoginScreenViewModel;", "Lcom/airbnb/mvrx/MavericksViewModel;", "Lcom/thedasagroup/suminative/ui/login/LoginScreenState;", "state", "loginUseCase", "Lcom/thedasagroup/suminative/ui/login/LoginUseCase;", "prefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "<init>", "(Lcom/thedasagroup/suminative/ui/login/LoginScreenState;Lcom/thedasagroup/suminative/ui/login/LoginUseCase;Lcom/thedasagroup/suminative/data/prefs/Prefs;)V", "getLoginUseCase", "()Lcom/thedasagroup/suminative/ui/login/LoginUseCase;", "getPrefs", "()Lcom/thedasagroup/suminative/data/prefs/Prefs;", "updateEmail", "", "email", "", "updatePassword", "password", "login", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/login/LoginResponse;", "shouldEncrypt", "", "(Ljava/lang/String;Ljava/lang/String;ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateLoginError", "error", "updateEmailError", "updatePasswordError", "Factory", "Companion", "app_stagingGeneralDebug"})
public final class LoginScreenViewModel extends com.airbnb.mvrx.MavericksViewModel<com.thedasagroup.suminative.ui.login.LoginScreenState> {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.login.LoginUseCase loginUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.prefs.Prefs prefs = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.login.LoginScreenViewModel.Companion Companion = null;
    
    @dagger.assisted.AssistedInject()
    public LoginScreenViewModel(@dagger.assisted.Assisted()
    @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.login.LoginScreenState state, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.login.LoginUseCase loginUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        super(null, null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.login.LoginUseCase getLoginUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.prefs.Prefs getPrefs() {
        return null;
    }
    
    public final void updateEmail(@org.jetbrains.annotations.NotNull()
    java.lang.String email) {
    }
    
    public final void updatePassword(@org.jetbrains.annotations.NotNull()
    java.lang.String password) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object login(@org.jetbrains.annotations.NotNull()
    java.lang.String email, @org.jetbrains.annotations.NotNull()
    java.lang.String password, boolean shouldEncrypt, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse>>> $completion) {
        return null;
    }
    
    public final void updateLoginError(@org.jetbrains.annotations.NotNull()
    java.lang.String error) {
    }
    
    public final void updateEmailError(@org.jetbrains.annotations.NotNull()
    java.lang.String error) {
    }
    
    public final void updatePasswordError(@org.jetbrains.annotations.NotNull()
    java.lang.String error) {
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u001b\u0010\u0006\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0003H\u0096\u0001J\u0013\u0010\n\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0007\u001a\u00020\bH\u0096\u0001\u00a8\u0006\u000b"}, d2 = {"Lcom/thedasagroup/suminative/ui/login/LoginScreenViewModel$Companion;", "Lcom/airbnb/mvrx/MavericksViewModelFactory;", "Lcom/thedasagroup/suminative/ui/login/LoginScreenViewModel;", "Lcom/thedasagroup/suminative/ui/login/LoginScreenState;", "<init>", "()V", "create", "viewModelContext", "Lcom/airbnb/mvrx/ViewModelContext;", "state", "initialState", "app_stagingGeneralDebug"})
    public static final class Companion implements com.airbnb.mvrx.MavericksViewModelFactory<com.thedasagroup.suminative.ui.login.LoginScreenViewModel, com.thedasagroup.suminative.ui.login.LoginScreenState> {
        
        private Companion() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.login.LoginScreenViewModel create(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.login.LoginScreenState state) {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.login.LoginScreenState initialState(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext) {
            return null;
        }
    }
    
    @dagger.assisted.AssistedFactory()
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\bg\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001J\u0010\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0003H&\u00a8\u0006\u0006"}, d2 = {"Lcom/thedasagroup/suminative/ui/login/LoginScreenViewModel$Factory;", "Lcom/airbnb/mvrx/hilt/AssistedViewModelFactory;", "Lcom/thedasagroup/suminative/ui/login/LoginScreenViewModel;", "Lcom/thedasagroup/suminative/ui/login/LoginScreenState;", "create", "state", "app_stagingGeneralDebug"})
    public static abstract interface Factory extends com.airbnb.mvrx.hilt.AssistedViewModelFactory<com.thedasagroup.suminative.ui.login.LoginScreenViewModel, com.thedasagroup.suminative.ui.login.LoginScreenState> {
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public abstract com.thedasagroup.suminative.ui.login.LoginScreenViewModel create(@org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.login.LoginScreenState state);
    }
}