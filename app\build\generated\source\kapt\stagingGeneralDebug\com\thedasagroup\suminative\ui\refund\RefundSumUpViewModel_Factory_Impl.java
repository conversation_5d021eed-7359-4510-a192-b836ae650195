package com.thedasagroup.suminative.ui.refund;

import dagger.internal.DaggerGenerated;
import dagger.internal.InstanceFactory;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RefundSumUpViewModel_Factory_Impl implements RefundSumUpViewModel.Factory {
  private final RefundSumUpViewModel_Factory delegateFactory;

  RefundSumUpViewModel_Factory_Impl(RefundSumUpViewModel_Factory delegateFactory) {
    this.delegateFactory = delegateFactory;
  }

  @Override
  public RefundSumUpViewModel create(RefundSumUpState state) {
    return delegateFactory.get(state);
  }

  public static Provider<RefundSumUpViewModel.Factory> create(
      RefundSumUpViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new RefundSumUpViewModel_Factory_Impl(delegateFactory));
  }

  public static dagger.internal.Provider<RefundSumUpViewModel.Factory> createFactoryProvider(
      RefundSumUpViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new RefundSumUpViewModel_Factory_Impl(delegateFactory));
  }
}
