{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,252,367", "endColumns": "95,100,114,103", "endOffsets": "146,247,362,466"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4855,5460,5561,5676", "endColumns": "95,100,114,103", "endOffsets": "4946,5556,5671,5775"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,96", "endOffsets": "139,236"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "56614,56703", "endColumns": "88,96", "endOffsets": "56698,56795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,292,400,516,611,708,822,962,1085,1232,1317,1417,1515,1617,1739,1876,1981,2121,2259,2385,2581,2704,2826,2948,3074,3173,3268,3387,3524,3626,3737,3841,3986,4133,4240,4347,4431,4529,4623,4711,4798,4899,4980,5063,5162,5268,5363,5466,5552,5661,5759,5865,5986,6067,6179", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "171,287,395,511,606,703,817,957,1080,1227,1312,1412,1510,1612,1734,1871,1976,2116,2254,2380,2576,2699,2821,2943,3069,3168,3263,3382,3519,3621,3732,3836,3981,4128,4235,4342,4426,4524,4618,4706,4793,4894,4975,5058,5157,5263,5358,5461,5547,5656,5754,5860,5981,6062,6174,6272"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6092,6213,6329,6437,6553,6648,6745,6859,6999,7122,7269,7354,7454,7552,7654,7776,7913,8018,8158,8296,8422,8618,8741,8863,8985,9111,9210,9305,9424,9561,9663,9774,9878,10023,10170,10277,10384,10468,10566,10660,10748,10835,10936,11017,11100,11199,11305,11400,11503,11589,11698,11796,11902,12023,12104,12216", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "6208,6324,6432,6548,6643,6740,6854,6994,7117,7264,7349,7449,7547,7649,7771,7908,8013,8153,8291,8417,8613,8736,8858,8980,9106,9205,9300,9419,9556,9658,9769,9873,10018,10165,10272,10379,10463,10561,10655,10743,10830,10931,11012,11095,11194,11300,11395,11498,11584,11693,11791,11897,12018,12099,12211,12309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,353,429,515,620,755,925,1066,1188,1290,1424,1482,1539,1628,1720,1789,1940,2060,2144,2289,2363,2455,2554,2685,2790,2896,2988,3059,3132,3275,3369,3603,3787,3900,4071,4187,4393,4507,4660,4783,4933,5046,5187,5301,5414,5573,5667,5826,5924,6086,6175,6313,6388,6507,6618,6694,6785,6864,6933,7008,7078,7206,7324,7491,7604,7753,7862,8016,8130,8243,8384,8490,8602,8714,8827,8944,9006,9057,9115,9169,9217,9272,9364,9415,9483,9536,9581,9657,9701,9754,9810,9872,9921,9972,10022,10090,10142,10194,10241,10309,10370,10422,10560,10667,10752,10813,10894,10963,11031,11167,11244,11323,11403,11481,11552,11649,11727,11819,11901,12025,12106,12198,12288,12389,12499,12576,12682,12800,12918,13024,13101,13188,13258,13348,13475,13627,13700,13777,13862,13946,15997,16112,16328,16373,16502,16676,16766,16909,16996,17143,17301,17428,17595,17670,17751,17822,17913,18034,18078,18165,18293,18385,18505,18626,18767,18872,18981,19049,19107,19172,19228,19295,19371,19466,19580,19646,19723,19988,20228,20353,20478,20607,20698,20812,20935,21080,21277,21369,21491,21578,21698,21814,21971,22064,22135,22200,22437,22522,22601,22666,22730,22838,22950,23045,23193,23363,23441,23502,23563,23740,23814,23905,23981,24074,24259,24349,24421,24543,24646,24799,24916,24973,25091,25160,25235,25295,25371,25419,25480,25565,25617,25690,25861,25990,26106,26175,26316,26461,26572,26671,26939,27048,27145,27244,27430,27664,27754,27959,28088,28249,28369,28449,28533,28609,28762,28914,29040,29090,29182,29317,29427,29508,29584,29675,29754,29831,29903,29964,30044,30113,30192,30291,30367,30437,30524,30594,30677,30876,31027,31118,31175,31260,31418,31478,31560,31643,31716,31795,32014,32069,32145,32223,32308,32430,32621,32716,32806,32890,32980,33048,33124,33207,33330,33419,33474,33559,33636,33703,33775,33885,33999,34109,34240,34316,34364,34416,34507,34604,34691,34792,34967,35075,35158,35280,35409,35551,35668,35768,35913,36055,36263,36437,36578,36732,36834,36930,37047,37172,37280,37419,37492,37946,38029,38115,38198", "endColumns": "182,114,75,85,104,134,169,140,121,101,133,57,56,88,91,68,150,119,83,144,73,91,98,130,104,105,91,70,72,142,93,233,183,112,170,115,205,113,152,122,149,112,140,113,112,158,93,158,97,161,88,137,74,118,110,75,90,78,68,74,69,127,117,166,112,148,108,153,113,112,140,105,111,111,112,116,61,50,57,53,47,54,91,50,67,52,44,75,43,52,55,61,48,50,49,67,51,51,46,67,60,51,137,106,84,60,80,68,67,135,76,78,79,77,70,96,77,91,81,123,80,91,89,100,109,76,105,117,117,105,76,86,69,89,126,151,72,76,84,83,2050,114,215,44,128,173,89,142,86,146,157,126,166,74,80,70,90,120,43,86,127,91,119,120,140,104,108,67,57,64,55,66,75,94,113,65,76,264,239,124,124,128,90,113,122,144,196,91,121,86,119,115,156,92,70,64,236,84,78,64,63,107,111,94,147,169,77,60,60,176,73,90,75,92,184,89,71,121,102,152,116,56,117,68,74,59,75,47,60,84,51,72,170,128,115,68,140,144,110,98,267,108,96,98,185,233,89,204,128,160,119,79,83,75,152,151,125,49,91,134,109,80,75,90,78,76,71,60,79,68,78,98,75,69,86,69,82,198,150,90,56,84,157,59,81,82,72,78,218,54,75,77,84,121,190,94,89,83,89,67,75,82,122,88,54,84,76,66,71,109,113,109,130,75,47,51,90,96,86,100,174,107,82,121,128,141,116,99,144,141,207,173,140,153,101,95,116,124,107,138,72,453,82,85,82,77", "endOffsets": "233,348,424,510,615,750,920,1061,1183,1285,1419,1477,1534,1623,1715,1784,1935,2055,2139,2284,2358,2450,2549,2680,2785,2891,2983,3054,3127,3270,3364,3598,3782,3895,4066,4182,4388,4502,4655,4778,4928,5041,5182,5296,5409,5568,5662,5821,5919,6081,6170,6308,6383,6502,6613,6689,6780,6859,6928,7003,7073,7201,7319,7486,7599,7748,7857,8011,8125,8238,8379,8485,8597,8709,8822,8939,9001,9052,9110,9164,9212,9267,9359,9410,9478,9531,9576,9652,9696,9749,9805,9867,9916,9967,10017,10085,10137,10189,10236,10304,10365,10417,10555,10662,10747,10808,10889,10958,11026,11162,11239,11318,11398,11476,11547,11644,11722,11814,11896,12020,12101,12193,12283,12384,12494,12571,12677,12795,12913,13019,13096,13183,13253,13343,13470,13622,13695,13772,13857,13941,15992,16107,16323,16368,16497,16671,16761,16904,16991,17138,17296,17423,17590,17665,17746,17817,17908,18029,18073,18160,18288,18380,18500,18621,18762,18867,18976,19044,19102,19167,19223,19290,19366,19461,19575,19641,19718,19983,20223,20348,20473,20602,20693,20807,20930,21075,21272,21364,21486,21573,21693,21809,21966,22059,22130,22195,22432,22517,22596,22661,22725,22833,22945,23040,23188,23358,23436,23497,23558,23735,23809,23900,23976,24069,24254,24344,24416,24538,24641,24794,24911,24968,25086,25155,25230,25290,25366,25414,25475,25560,25612,25685,25856,25985,26101,26170,26311,26456,26567,26666,26934,27043,27140,27239,27425,27659,27749,27954,28083,28244,28364,28444,28528,28604,28757,28909,29035,29085,29177,29312,29422,29503,29579,29670,29749,29826,29898,29959,30039,30108,30187,30286,30362,30432,30519,30589,30672,30871,31022,31113,31170,31255,31413,31473,31555,31638,31711,31790,32009,32064,32140,32218,32303,32425,32616,32711,32801,32885,32975,33043,33119,33202,33325,33414,33469,33554,33631,33698,33770,33880,33994,34104,34235,34311,34359,34411,34502,34599,34686,34787,34962,35070,35153,35275,35404,35546,35663,35763,35908,36050,36258,36432,36573,36727,36829,36925,37042,37167,37275,37414,37487,37941,38024,38110,38193,38271"}, "to": {"startLines": "193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18127,18310,18425,18501,18587,18692,18827,18997,19138,19260,19362,19496,19554,19611,19700,19792,19861,20012,20132,20216,20361,20435,20527,20626,20757,20862,20968,21060,21131,21204,21347,21441,21675,21859,21972,22143,22259,22465,22579,22732,22855,23005,23118,23259,23373,23486,23645,23739,23898,23996,24158,24247,24385,24460,24579,24690,24766,24857,24936,25005,25080,25150,25278,25396,25563,25676,25825,25934,26088,26202,26315,26456,26562,26674,26786,26899,27016,27078,27129,27187,27241,27289,27344,27436,27487,27555,27608,27653,27729,27773,27826,27882,27944,27993,28044,28094,28162,28214,28266,28313,28381,28442,28494,28632,28739,28824,28885,28966,29035,29103,29239,29316,29395,29475,29553,29624,29721,29799,29891,29973,30097,30178,30270,30360,30461,30571,30648,30754,30872,30990,31096,31173,31260,31330,31420,31547,31699,31772,31849,31934,32018,34069,34184,34400,34445,34574,34748,34838,34981,35068,35215,35373,35500,35667,35742,35823,35894,35985,36106,36150,36237,36365,36457,36577,36698,36839,36944,37053,37121,37179,37244,37300,37367,37443,37538,37652,37718,37795,38060,38300,38425,38550,38679,38770,38884,39007,39152,39349,39441,39563,39650,39770,39886,40043,40136,40207,40272,40509,40594,40673,40738,40802,40910,41022,41117,41265,41435,41513,41574,41635,41812,41886,41977,42053,42146,42331,42421,42493,42615,42718,42871,42988,43045,43163,43232,43307,43367,43443,43491,43552,43637,43689,43762,43933,44062,44178,44247,44388,44533,44644,44743,45011,45120,45217,45316,45502,45736,45826,46031,46160,46321,46441,46521,46605,46681,46834,46986,47112,47162,47254,47389,47499,47580,47656,47747,47826,47903,47975,48036,48116,48185,48264,48363,48439,48509,48596,48666,48749,48948,49099,49190,49247,49332,49490,49550,49632,49715,49788,49867,50086,50141,50217,50295,50380,50502,50693,50788,50878,50962,51052,51120,51196,51279,51402,51491,51546,51631,51708,51775,51847,51957,52071,52181,52312,52388,52436,52488,52579,52676,52763,52864,53039,53147,53230,53352,53481,53623,53740,53840,53985,54127,54335,54509,54650,54804,54906,55002,55119,55244,55352,55491,55564,56018,56101,56187,56270", "endColumns": "182,114,75,85,104,134,169,140,121,101,133,57,56,88,91,68,150,119,83,144,73,91,98,130,104,105,91,70,72,142,93,233,183,112,170,115,205,113,152,122,149,112,140,113,112,158,93,158,97,161,88,137,74,118,110,75,90,78,68,74,69,127,117,166,112,148,108,153,113,112,140,105,111,111,112,116,61,50,57,53,47,54,91,50,67,52,44,75,43,52,55,61,48,50,49,67,51,51,46,67,60,51,137,106,84,60,80,68,67,135,76,78,79,77,70,96,77,91,81,123,80,91,89,100,109,76,105,117,117,105,76,86,69,89,126,151,72,76,84,83,2050,114,215,44,128,173,89,142,86,146,157,126,166,74,80,70,90,120,43,86,127,91,119,120,140,104,108,67,57,64,55,66,75,94,113,65,76,264,239,124,124,128,90,113,122,144,196,91,121,86,119,115,156,92,70,64,236,84,78,64,63,107,111,94,147,169,77,60,60,176,73,90,75,92,184,89,71,121,102,152,116,56,117,68,74,59,75,47,60,84,51,72,170,128,115,68,140,144,110,98,267,108,96,98,185,233,89,204,128,160,119,79,83,75,152,151,125,49,91,134,109,80,75,90,78,76,71,60,79,68,78,98,75,69,86,69,82,198,150,90,56,84,157,59,81,82,72,78,218,54,75,77,84,121,190,94,89,83,89,67,75,82,122,88,54,84,76,66,71,109,113,109,130,75,47,51,90,96,86,100,174,107,82,121,128,141,116,99,144,141,207,173,140,153,101,95,116,124,107,138,72,453,82,85,82,77", "endOffsets": "18305,18420,18496,18582,18687,18822,18992,19133,19255,19357,19491,19549,19606,19695,19787,19856,20007,20127,20211,20356,20430,20522,20621,20752,20857,20963,21055,21126,21199,21342,21436,21670,21854,21967,22138,22254,22460,22574,22727,22850,23000,23113,23254,23368,23481,23640,23734,23893,23991,24153,24242,24380,24455,24574,24685,24761,24852,24931,25000,25075,25145,25273,25391,25558,25671,25820,25929,26083,26197,26310,26451,26557,26669,26781,26894,27011,27073,27124,27182,27236,27284,27339,27431,27482,27550,27603,27648,27724,27768,27821,27877,27939,27988,28039,28089,28157,28209,28261,28308,28376,28437,28489,28627,28734,28819,28880,28961,29030,29098,29234,29311,29390,29470,29548,29619,29716,29794,29886,29968,30092,30173,30265,30355,30456,30566,30643,30749,30867,30985,31091,31168,31255,31325,31415,31542,31694,31767,31844,31929,32013,34064,34179,34395,34440,34569,34743,34833,34976,35063,35210,35368,35495,35662,35737,35818,35889,35980,36101,36145,36232,36360,36452,36572,36693,36834,36939,37048,37116,37174,37239,37295,37362,37438,37533,37647,37713,37790,38055,38295,38420,38545,38674,38765,38879,39002,39147,39344,39436,39558,39645,39765,39881,40038,40131,40202,40267,40504,40589,40668,40733,40797,40905,41017,41112,41260,41430,41508,41569,41630,41807,41881,41972,42048,42141,42326,42416,42488,42610,42713,42866,42983,43040,43158,43227,43302,43362,43438,43486,43547,43632,43684,43757,43928,44057,44173,44242,44383,44528,44639,44738,45006,45115,45212,45311,45497,45731,45821,46026,46155,46316,46436,46516,46600,46676,46829,46981,47107,47157,47249,47384,47494,47575,47651,47742,47821,47898,47970,48031,48111,48180,48259,48358,48434,48504,48591,48661,48744,48943,49094,49185,49242,49327,49485,49545,49627,49710,49783,49862,50081,50136,50212,50290,50375,50497,50688,50783,50873,50957,51047,51115,51191,51274,51397,51486,51541,51626,51703,51770,51842,51952,52066,52176,52307,52383,52431,52483,52574,52671,52758,52859,53034,53142,53225,53347,53476,53618,53735,53835,53980,54122,54330,54504,54645,54799,54901,54997,55114,55239,55347,55486,55559,56013,56096,56182,56265,56343"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,421,513,628,712,827,950,1027,1102,1193,1286,1381,1475,1575,1668,1763,1858,1949,2040,2123,2233,2343,2443,2554,2663,2782,2964,17417", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "416,508,623,707,822,945,1022,1097,1188,1281,1376,1470,1570,1663,1758,1853,1944,2035,2118,2228,2338,2438,2549,2658,2777,2959,3062,17496"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,288,385,484,571,653,749,838,925,1008,1096,1170,1245,1316,1386,1465,1531", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,74,70,69,78,65,120", "endOffsets": "195,283,380,479,566,648,744,833,920,1003,1091,1165,1240,1311,1381,1460,1526,1647"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,539,540,541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4495,4590,4951,5048,5147,5855,5937,16985,17074,17246,17329,17663,17810,17885,17956,56348,56427,56493", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,74,70,69,78,65,120", "endOffsets": "4585,4673,5043,5142,5229,5932,6028,17069,17156,17324,17412,17732,17880,17951,18021,56422,56488,56609"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3471,3568,3670,3772,3873,3976,4083,18026", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "3563,3665,3767,3868,3971,4078,4188,18122"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,344,420,497,587,667,766,886,969,1032,1096,1195,1270,1329,1439,1501,1570,1628,1700,1761,1816,1919,1976,2036,2091,2172,2292,2375,2453,2549,2635,2723,2858,2941,3021,3161,3255,3337,3390,3441,3507,3583,3665,3736,3820,3897,3972,4051,4128,4233,4329,4406,4498,4595,4669,4754,4851,4903,4986,5053,5141,5228,5290,5354,5417,5483,5581,5687,5781,5888,5945,6000,6085,6170,6247", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "258,339,415,492,582,662,761,881,964,1027,1091,1190,1265,1324,1434,1496,1565,1623,1695,1756,1811,1914,1971,2031,2086,2167,2287,2370,2448,2544,2630,2718,2853,2936,3016,3156,3250,3332,3385,3436,3502,3578,3660,3731,3815,3892,3967,4046,4123,4228,4324,4401,4493,4590,4664,4749,4846,4898,4981,5048,5136,5223,5285,5349,5412,5478,5576,5682,5776,5883,5940,5995,6080,6165,6242,6315"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3067,3148,3224,3301,3391,4193,4292,4412,5234,5297,5361,5780,6033,12314,12424,12486,12555,12613,12685,12746,12801,12904,12961,13021,13076,13157,13277,13360,13438,13534,13620,13708,13843,13926,14006,14146,14240,14322,14375,14426,14492,14568,14650,14721,14805,14882,14957,15036,15113,15218,15314,15391,15483,15580,15654,15739,15836,15888,15971,16038,16126,16213,16275,16339,16402,16468,16566,16672,16766,16873,16930,17161,17501,17586,17737", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "308,3143,3219,3296,3386,3466,4287,4407,4490,5292,5356,5455,5850,6087,12419,12481,12550,12608,12680,12741,12796,12899,12956,13016,13071,13152,13272,13355,13433,13529,13615,13703,13838,13921,14001,14141,14235,14317,14370,14421,14487,14563,14645,14716,14800,14877,14952,15031,15108,15213,15309,15386,15478,15575,15649,15734,15831,15883,15966,16033,16121,16208,16270,16334,16397,16463,16561,16667,16761,16868,16925,16980,17241,17581,17658,17805"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-hu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "172", "endOffsets": "367"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4678", "endColumns": "176", "endOffsets": "4850"}}]}]}