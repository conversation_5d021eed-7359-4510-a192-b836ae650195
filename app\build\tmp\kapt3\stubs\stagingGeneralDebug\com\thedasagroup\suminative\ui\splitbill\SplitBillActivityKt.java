package com.thedasagroup.suminative.ui.splitbill;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u00002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0000\u001aH\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\u0018\u0010\b\u001a\u0014\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00010\t2\u0006\u0010\u000b\u001a\u00020\fH\u0007\u001a.\u0010\r\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u00052\u0006\u0010\u000f\u001a\u00020\n2\u0006\u0010\u0010\u001a\u00020\u00112\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\u0007H\u0007\u00a8\u0006\u0012"}, d2 = {"SplitBillScreen", "", "order", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "numberOfPersons", "", "onBackClick", "Lkotlin/Function0;", "onPaymentClick", "Lkotlin/Function2;", "", "paymentViewModel", "Lcom/thedasagroup/suminative/ui/payment/PaymentViewModel;", "PersonPaymentCard", "personNumber", "amount", "isPaid", "", "app_stagingGeneralDebug"})
public final class SplitBillActivityKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void SplitBillScreen(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, int numberOfPersons, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.Double, kotlin.Unit> onPaymentClick, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.payment.PaymentViewModel paymentViewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PersonPaymentCard(int personNumber, double amount, boolean isPaid, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPaymentClick) {
    }
}