package com.thedasagroup.suminative.ui.payment;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = PaymentActivity.class
)
@GeneratedEntryPoint
@InstallIn(ActivityComponent.class)
public interface PaymentActivity_GeneratedInjector {
  void injectPaymentActivity(PaymentActivity paymentActivity);
}
