package com.thedasagroup.suminative.ui.products;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000\u0016\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a.\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u00a8\u0006\u0007"}, d2 = {"DownloadProductsScreen", "", "viewModel", "Lcom/thedasagroup/suminative/ui/products/DownloadProductsViewModel;", "onDownloadComplete", "Lkotlin/Function0;", "onSkipDownload", "app_stagingGeneralDebug"})
public final class DownloadProductsScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void DownloadProductsScreen(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.DownloadProductsViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDownloadComplete, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSkipDownload) {
    }
}