package com.thedasagroup.suminative.ui.reservations;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000 \n\u0000\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\b\n\u0000\u001a(\u0010\u0000\u001a\u00020\u0001*\u00020\u00022\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u00042\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u00a8\u0006\t"}, d2 = {"launchAreaTableSelection", "", "Landroid/app/Activity;", "launcher", "Landroidx/activity/result/ActivityResultLauncher;", "Landroid/content/Intent;", "excludedTableIds", "", "", "app_stagingGeneralDebug"})
public final class AreaTableSelectionHelperKt {
    
    /**
     * Extension function for Activity to easily launch area table selection
     */
    public static final void launchAreaTableSelection(@org.jetbrains.annotations.NotNull()
    android.app.Activity $this$launchAreaTableSelection, @org.jetbrains.annotations.NotNull()
    androidx.activity.result.ActivityResultLauncher<android.content.Intent> launcher, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Integer> excludedTableIds) {
    }
}