package com.thedasagroup.suminative.data.repo;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\"\u0010\u0004\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00060\u00052\u0006\u0010\b\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\nJ\"\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u00060\u00052\u0006\u0010\b\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010\u000eJ\"\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u00060\u00052\u0006\u0010\b\u001a\u00020\u0011H\u0086@\u00a2\u0006\u0002\u0010\u0012J\"\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u00060\u00052\u0006\u0010\b\u001a\u00020\u0014H\u0086@\u00a2\u0006\u0002\u0010\u0015J\"\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00170\u00060\u00052\u0006\u0010\b\u001a\u00020\u0018H\u0086@\u00a2\u0006\u0002\u0010\u0019J\"\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001b0\u00060\u00052\u0006\u0010\b\u001a\u00020\u001cH\u0086@\u00a2\u0006\u0002\u0010\u001dJ\"\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001f0\u00060\u00052\u0006\u0010\b\u001a\u00020 H\u0086@\u00a2\u0006\u0002\u0010!\u00a8\u0006\""}, d2 = {"Lcom/thedasagroup/suminative/data/repo/StockRepository;", "Lcom/thedasagroup/suminative/data/repo/BaseRepository;", "<init>", "()V", "getPagedStockItems", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/stock/StockItemsResponse;", "request", "Lcom/thedasagroup/suminative/data/model/request/stock/GetPagedStockItemsRequest;", "(Lcom/thedasagroup/suminative/data/model/request/stock/GetPagedStockItemsRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "changeStock", "Lcom/thedasagroup/suminative/data/model/response/stock/ChangeStockResponse;", "Lcom/thedasagroup/suminative/data/model/request/stock/ChangeStockRequest;", "(Lcom/thedasagroup/suminative/data/model/request/stock/ChangeStockRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "placeOrder", "Lcom/thedasagroup/suminative/data/model/response/order/OrderResponse2;", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "(Lcom/thedasagroup/suminative/data/model/request/order/Order;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cloudPrint", "Lcom/thedasagroup/suminative/data/model/request/cloud_print/CloudPrintRequest;", "(Lcom/thedasagroup/suminative/data/model/request/cloud_print/CloudPrintRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getOptionDetails", "Lcom/thedasagroup/suminative/data/model/response/options_details/OptionDetails;", "Lcom/thedasagroup/suminative/data/model/request/option_details/GetOptionDetailsRequest;", "(Lcom/thedasagroup/suminative/data/model/request/option_details/GetOptionDetailsRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPaymentSecret", "Lcom/thedasagroup/suminative/data/model/response/payments/PaymentSecretResponse;", "Lcom/thedasagroup/suminative/data/model/request/payment/GetPaymentSecretRequest;", "(Lcom/thedasagroup/suminative/data/model/request/payment/GetPaymentSecretRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCategorySorting", "Lcom/thedasagroup/suminative/data/model/response/category_sorting/CategorySortingResponse;", "Lcom/thedasagroup/suminative/data/model/request/category_sorting/CategorySortingRequest;", "(Lcom/thedasagroup/suminative/data/model/request/category_sorting/CategorySortingRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_stagingGeneralDebug"})
public final class StockRepository extends com.thedasagroup.suminative.data.repo.BaseRepository {
    
    public StockRepository() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getPagedStockItems(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.stock.GetPagedStockItemsRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object changeStock(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.stock.ChangeStockRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.ChangeStockResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object placeOrder(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.order.OrderResponse2>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object cloudPrint(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.cloud_print.CloudPrintRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.order.OrderResponse2>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getOptionDetails(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.option_details.GetOptionDetailsRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.options_details.OptionDetails>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getPaymentSecret(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.payment.GetPaymentSecretRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.payments.PaymentSecretResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCategorySorting(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.category_sorting.CategorySortingRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.category_sorting.CategorySortingResponse>>> $completion) {
        return null;
    }
}