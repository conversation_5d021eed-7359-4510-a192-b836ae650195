package com.thedasagroup.suminative.ui.reservations;

/**
 * Helper class for launching the Area and Table Selection Activity
 * and handling the results.
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001:\u0001\u0013B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J,\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000b2\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00050\u000eJ\u001a\u0010\u000f\u001a\u0004\u0018\u00010\u00102\u0006\u0010\u0011\u001a\u00020\u00052\b\u0010\u0012\u001a\u0004\u0018\u00010\fR\u000e\u0010\u0004\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/thedasagroup/suminative/ui/reservations/AreaTableSelectionHelper;", "", "<init>", "()V", "REQUEST_CODE_AREA_TABLE_SELECTION", "", "launchAreaTableSelection", "", "activity", "Landroid/app/Activity;", "launcher", "Landroidx/activity/result/ActivityResultLauncher;", "Landroid/content/Intent;", "excludedTableIds", "", "parseResult", "Lcom/thedasagroup/suminative/ui/reservations/AreaTableSelectionHelper$AreaTableSelection;", "resultCode", "data", "AreaTableSelection", "app_stagingGeneralDebug"})
public final class AreaTableSelectionHelper {
    public static final int REQUEST_CODE_AREA_TABLE_SELECTION = 1001;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper INSTANCE = null;
    
    private AreaTableSelectionHelper() {
        super();
    }
    
    /**
     * Launch the Area and Table Selection Activity
     *
     * @param activity The calling activity
     * @param launcher The activity result launcher
     * @param excludedTableIds List of table IDs to exclude from selection (already selected tables)
     */
    public final void launchAreaTableSelection(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity, @org.jetbrains.annotations.NotNull()
    androidx.activity.result.ActivityResultLauncher<android.content.Intent> launcher, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Integer> excludedTableIds) {
    }
    
    /**
     * Parse the result from the Area and Table Selection Activity
     *
     * @param resultCode The result code from the activity
     * @param data The intent data containing the selection
     * @return AreaTableSelection object if successful, null otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection parseResult(int resultCode, @org.jetbrains.annotations.Nullable()
    android.content.Intent data) {
        return null;
    }
    
    /**
     * Data class to hold the selected area and table information
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0013\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B/\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\u0006\u0010\u0007\u001a\u00020\u0005\u0012\u0006\u0010\b\u001a\u00020\u0003\u00a2\u0006\u0004\b\t\u0010\nJ\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J;\u0010\u0017\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0018\u001a\u00020\u00192\b\u0010\u001a\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001b\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u001c\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\fR\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000eR\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\f\u00a8\u0006\u001d"}, d2 = {"Lcom/thedasagroup/suminative/ui/reservations/AreaTableSelectionHelper$AreaTableSelection;", "", "areaId", "", "areaName", "", "tableId", "tableName", "tableCapacity", "<init>", "(ILjava/lang/String;ILjava/lang/String;I)V", "getAreaId", "()I", "getAreaName", "()Ljava/lang/String;", "getTableId", "getTableName", "getTableCapacity", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "toString", "app_stagingGeneralDebug"})
    public static final class AreaTableSelection {
        private final int areaId = 0;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String areaName = null;
        private final int tableId = 0;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String tableName = null;
        private final int tableCapacity = 0;
        
        public AreaTableSelection(int areaId, @org.jetbrains.annotations.NotNull()
        java.lang.String areaName, int tableId, @org.jetbrains.annotations.NotNull()
        java.lang.String tableName, int tableCapacity) {
            super();
        }
        
        public final int getAreaId() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getAreaName() {
            return null;
        }
        
        public final int getTableId() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getTableName() {
            return null;
        }
        
        public final int getTableCapacity() {
            return 0;
        }
        
        public final int component1() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        public final int component3() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component4() {
            return null;
        }
        
        public final int component5() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection copy(int areaId, @org.jetbrains.annotations.NotNull()
        java.lang.String areaName, int tableId, @org.jetbrains.annotations.NotNull()
        java.lang.String tableName, int tableCapacity) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}