package com.thedasagroup.suminative.ui.reservations;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B!\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\u001a\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000bH\u0087B\u00a2\u0006\u0002\u0010\u000eR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/thedasagroup/suminative/ui/reservations/GetAllReservationsUseCase;", "", "reservationsRepository", "Lcom/thedasagroup/suminative/data/repo/ReservationsRepository;", "prefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "trueTimeImpl", "Lcom/instacart/truetime/time/TrueTimeImpl;", "<init>", "(Lcom/thedasagroup/suminative/data/repo/ReservationsRepository;Lcom/thedasagroup/suminative/data/prefs/Prefs;Lcom/instacart/truetime/time/TrueTimeImpl;)V", "invoke", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/reservations/ReservationsResponse;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_stagingGeneralDebug"})
public final class GetAllReservationsUseCase {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.repo.ReservationsRepository reservationsRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.prefs.Prefs prefs = null;
    @org.jetbrains.annotations.NotNull()
    private final com.instacart.truetime.time.TrueTimeImpl trueTimeImpl = null;
    
    @javax.inject.Inject()
    public GetAllReservationsUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.ReservationsRepository reservationsRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs, @org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl trueTimeImpl) {
        super();
    }
    
    @android.annotation.SuppressLint(value = {"SimpleDateFormat"})
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object invoke(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse>>> $completion) {
        return null;
    }
}