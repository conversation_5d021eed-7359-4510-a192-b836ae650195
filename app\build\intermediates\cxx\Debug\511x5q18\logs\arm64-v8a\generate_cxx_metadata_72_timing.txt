# C/C++ build system timings
generate_cxx_metadata
  [gap of 30ms]
  create-invalidation-state 90ms
  [gap of 51ms]
  write-metadata-json-to-file 20ms
generate_cxx_metadata completed in 192ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 23ms]
  create-invalidation-state 81ms
  [gap of 43ms]
generate_cxx_metadata completed in 147ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 27ms]
  create-invalidation-state 249ms
  [gap of 37ms]
  write-metadata-json-to-file 17ms
generate_cxx_metadata completed in 331ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 79ms
  [gap of 40ms]
  write-metadata-json-to-file 12ms
generate_cxx_metadata completed in 151ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 71ms
  [gap of 44ms]
generate_cxx_metadata completed in 136ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 84ms
  [gap of 36ms]
  write-metadata-json-to-file 13ms
generate_cxx_metadata completed in 152ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 152ms]
  create-invalidation-state 195ms
  [gap of 81ms]
  write-metadata-json-to-file 170ms
generate_cxx_metadata completed in 603ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 62ms
  [gap of 26ms]
  write-metadata-json-to-file 13ms
generate_cxx_metadata completed in 121ms

