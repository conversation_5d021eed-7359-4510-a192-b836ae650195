package com.thedasagroup.suminative.ui.orders;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u00b6\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b!\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\b\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0014\u0018\u0000 \u0085\u00012\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0004\u0084\u0001\u0085\u0001B\u008b\u0001\b\u0007\u0012\b\b\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0006\u0010\u000e\u001a\u00020\u000f\u0012\u0006\u0010\u0010\u001a\u00020\u0011\u0012\u0006\u0010\u0012\u001a\u00020\u0013\u0012\u0006\u0010\u0014\u001a\u00020\u0015\u0012\u0006\u0010\u0016\u001a\u00020\u0017\u0012\u0006\u0010\u0018\u001a\u00020\u0019\u0012\u0006\u0010\u001a\u001a\u00020\u001b\u0012\u0006\u0010\u001c\u001a\u00020\u001d\u0012\u0006\u0010\u001e\u001a\u00020\u001f\u0012\u0006\u0010 \u001a\u00020!\u00a2\u0006\u0004\b\"\u0010#J\"\u0010B\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020E0D0C2\u0006\u0010F\u001a\u00020GH\u0086@\u00a2\u0006\u0002\u0010HJ\u001a\u0010I\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020E0D0CH\u0086@\u00a2\u0006\u0002\u0010JJ\"\u0010K\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020E0D0C2\u0006\u0010F\u001a\u00020GH\u0086@\u00a2\u0006\u0002\u0010HJ\u001a\u0010L\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020E0D0CH\u0086@\u00a2\u0006\u0002\u0010JJ*\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020M0D0C2\u0006\u0010N\u001a\u00020O2\u0006\u0010P\u001a\u00020OH\u0086@\u00a2\u0006\u0002\u0010QJ4\u0010R\u001a\u0010\u0012\f\u0012\n\u0012\u0006\u0012\u0004\u0018\u00010E0D0C2\u0006\u0010N\u001a\u00020O2\u0006\u0010P\u001a\u00020O2\u0006\u0010F\u001a\u00020GH\u0086@\u00a2\u0006\u0002\u0010SJ4\u0010T\u001a\u0010\u0012\f\u0012\n\u0012\u0006\u0012\u0004\u0018\u00010E0D0C2\u0006\u0010N\u001a\u00020O2\u0006\u0010F\u001a\u00020G2\u0006\u0010U\u001a\u00020OH\u0086@\u00a2\u0006\u0002\u0010VJ\u000e\u0010W\u001a\u00020X2\u0006\u0010Y\u001a\u00020GJ\u000e\u0010Z\u001a\u00020X2\u0006\u0010[\u001a\u00020GJ\u000e\u0010\\\u001a\u00020X2\u0006\u0010]\u001a\u00020EJ\u001a\u0010^\u001a\u00020X2\b\u0010_\u001a\u0004\u0018\u00010`2\b\b\u0002\u0010a\u001a\u00020GJ\u000e\u0010b\u001a\u00020X2\u0006\u0010N\u001a\u00020OJ\u000e\u0010c\u001a\u00020X2\u0006\u0010d\u001a\u00020OJ\u0010\u0010e\u001a\u00020X2\b\u0010_\u001a\u0004\u0018\u00010`J\u0010\u0010f\u001a\u00020X2\b\u0010_\u001a\u0004\u0018\u00010`J\u000e\u0010g\u001a\u00020X2\u0006\u0010h\u001a\u00020OJ\u0010\u0010i\u001a\u00020X2\b\u0010_\u001a\u0004\u0018\u00010`J\u000e\u0010j\u001a\u00020X2\u0006\u0010k\u001a\u00020lJ\"\u0010m\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020n0D0C2\u0006\u0010o\u001a\u00020GH\u0086@\u00a2\u0006\u0002\u0010HJ\u001a\u0010p\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020n0D0CH\u0086@\u00a2\u0006\u0002\u0010JJ\u001a\u0010q\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020r0D0CH\u0086@\u00a2\u0006\u0002\u0010JJ\u0016\u0010s\u001a\u00020X2\u0006\u0010t\u001a\u00020l2\u0006\u0010u\u001a\u00020lJ0\u0010v\u001a\u00020X2\u0006\u0010w\u001a\u00020l2\u0006\u0010x\u001a\u00020l2\u0006\u0010y\u001a\u00020l2\u0006\u0010z\u001a\u00020l2\b\b\u0002\u0010{\u001a\u00020GJ\u0006\u0010|\u001a\u00020lJ\u000e\u0010}\u001a\u00020X2\u0006\u0010~\u001a\u00020GJ\u000e\u0010\u007f\u001a\u00020X2\u0006\u0010w\u001a\u00020lJ\u0010\u0010\u0080\u0001\u001a\u00020X2\u0007\u0010\u0081\u0001\u001a\u00020lJ\u0010\u0010\u0082\u0001\u001a\u00020X2\u0007\u0010\u0082\u0001\u001a\u00020GJ\u000f\u0010\u0083\u0001\u001a\u00020X2\u0006\u0010{\u001a\u00020GR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010%R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\'R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010)R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010+R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010-R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010/R\u0011\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u00101R\u0011\u0010\u0012\u001a\u00020\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u00103R\u0011\u0010\u0014\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b4\u00105R\u0011\u0010\u0016\u001a\u00020\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u00107R\u0011\u0010\u0018\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b8\u00109R\u0011\u0010\u001a\u001a\u00020\u001b\u00a2\u0006\b\n\u0000\u001a\u0004\b:\u0010;R\u0011\u0010\u001c\u001a\u00020\u001d\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010=R\u0011\u0010\u001e\u001a\u00020\u001f\u00a2\u0006\b\n\u0000\u001a\u0004\b>\u0010?R\u0011\u0010 \u001a\u00020!\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u0010A\u00a8\u0006\u0086\u0001"}, d2 = {"Lcom/thedasagroup/suminative/ui/orders/OrderScreenViewModel;", "Lcom/airbnb/mvrx/MavericksViewModel;", "Lcom/thedasagroup/suminative/ui/orders/OrderState;", "state", "getOrdersUseCase", "Lcom/thedasagroup/suminative/ui/orders/GetOrdersUseCase;", "getPendingOrdersPagedUseCase", "Lcom/thedasagroup/suminative/ui/orders/GetPendingOrdersPagedUseCase;", "getScheduleOrdersUseCase", "Lcom/thedasagroup/suminative/ui/orders/GetScheduleOrdersUseCase;", "getScheduleOrdersPagedUseCase", "Lcom/thedasagroup/suminative/ui/orders/GetScheduleOrdersPagedUseCase;", "changeStatusUseCase", "Lcom/thedasagroup/suminative/ui/orders/ChangeStatusUseCase;", "changeStatus", "Lcom/thedasagroup/suminative/ui/orders/ChangeStatusAndOrdersUseCase;", "acceptOrderWithDelayUseCase", "Lcom/thedasagroup/suminative/ui/orders/AcceptOrderWithDelayUseCase;", "trueTimeImpl", "Lcom/instacart/truetime/time/TrueTimeImpl;", "ordersRepository", "Lcom/thedasagroup/suminative/data/repo/OrdersRepository;", "getStoreSettings", "Lcom/thedasagroup/suminative/ui/login/GetStoreSettingsUseCase;", "closeOpenStoreUseCase", "Lcom/thedasagroup/suminative/ui/orders/CloseOpenStoreUseCase;", "prefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "soundPoolPlayer", "Lcom/thedasagroup/suminative/ui/utils/SoundPoolPlayer;", "audioManager", "Landroid/media/AudioManager;", "getPosSettings", "Lcom/thedasagroup/suminative/domain/GetPOSSettingsUseCase;", "<init>", "(Lcom/thedasagroup/suminative/ui/orders/OrderState;Lcom/thedasagroup/suminative/ui/orders/GetOrdersUseCase;Lcom/thedasagroup/suminative/ui/orders/GetPendingOrdersPagedUseCase;Lcom/thedasagroup/suminative/ui/orders/GetScheduleOrdersUseCase;Lcom/thedasagroup/suminative/ui/orders/GetScheduleOrdersPagedUseCase;Lcom/thedasagroup/suminative/ui/orders/ChangeStatusUseCase;Lcom/thedasagroup/suminative/ui/orders/ChangeStatusAndOrdersUseCase;Lcom/thedasagroup/suminative/ui/orders/AcceptOrderWithDelayUseCase;Lcom/instacart/truetime/time/TrueTimeImpl;Lcom/thedasagroup/suminative/data/repo/OrdersRepository;Lcom/thedasagroup/suminative/ui/login/GetStoreSettingsUseCase;Lcom/thedasagroup/suminative/ui/orders/CloseOpenStoreUseCase;Lcom/thedasagroup/suminative/data/prefs/Prefs;Lcom/thedasagroup/suminative/ui/utils/SoundPoolPlayer;Landroid/media/AudioManager;Lcom/thedasagroup/suminative/domain/GetPOSSettingsUseCase;)V", "getGetOrdersUseCase", "()Lcom/thedasagroup/suminative/ui/orders/GetOrdersUseCase;", "getGetPendingOrdersPagedUseCase", "()Lcom/thedasagroup/suminative/ui/orders/GetPendingOrdersPagedUseCase;", "getGetScheduleOrdersUseCase", "()Lcom/thedasagroup/suminative/ui/orders/GetScheduleOrdersUseCase;", "getGetScheduleOrdersPagedUseCase", "()Lcom/thedasagroup/suminative/ui/orders/GetScheduleOrdersPagedUseCase;", "getChangeStatusUseCase", "()Lcom/thedasagroup/suminative/ui/orders/ChangeStatusUseCase;", "getChangeStatus", "()Lcom/thedasagroup/suminative/ui/orders/ChangeStatusAndOrdersUseCase;", "getAcceptOrderWithDelayUseCase", "()Lcom/thedasagroup/suminative/ui/orders/AcceptOrderWithDelayUseCase;", "getTrueTimeImpl", "()Lcom/instacart/truetime/time/TrueTimeImpl;", "getOrdersRepository", "()Lcom/thedasagroup/suminative/data/repo/OrdersRepository;", "getGetStoreSettings", "()Lcom/thedasagroup/suminative/ui/login/GetStoreSettingsUseCase;", "getCloseOpenStoreUseCase", "()Lcom/thedasagroup/suminative/ui/orders/CloseOpenStoreUseCase;", "getPrefs", "()Lcom/thedasagroup/suminative/data/prefs/Prefs;", "getSoundPoolPlayer", "()Lcom/thedasagroup/suminative/ui/utils/SoundPoolPlayer;", "getAudioManager", "()Landroid/media/AudioManager;", "getGetPosSettings", "()Lcom/thedasagroup/suminative/domain/GetPOSSettingsUseCase;", "getOrders", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/request/pagination/OrderResponse;", "isShowAllOrders", "", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getScheduleOrders", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getOrdersSilent", "getScheduleOrdersSilent", "Lcom/thedasagroup/suminative/data/model/response/change_status/ChangeStatusResponse;", "orderId", "", "status", "(IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "changeStatusOfOrder", "(IIZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "acceptOrderWithDelay", "delayInMinutes", "(IZILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateExpanded", "", "expanded", "updateShowAllOrders", "value", "updateOrders", "ordersResponse", "updateShowPrintingPreview", "order", "Lcom/thedasagroup/suminative/data/model/request/pagination/OrderItem;", "shouldPrintInstant", "updateClickedOrderId", "updateSelectedChangeStatusId", "statusId", "updateShowChangeStatusDialog", "updateChangeStatusOrder", "updateAcceptOrderDelay", "delay", "updateShowAcceptOrderDelayDialog", "updateStoreCloseSettings", "storeCloseSettings", "", "updateIsStoreClosedManual", "Lcom/thedasagroup/suminative/data/model/response/close_open_store/CloseOpenStoreResponse;", "isStoreClosedManual", "getIsClosed", "getStoreSettingsFun", "Lcom/thedasagroup/suminative/data/model/response/store_settings/StoreSettingsResponse;", "updateStoreTimings", "openingTime", "closeTime", "updateShowDialog", "dialogType", "dialogMessage", "dialogTitle", "showDialog", "isScheduleOrder", "getCurrentUTC", "updateIsConnected", "isConnected2", "updateDialogType", "updateCurrentRoute", "currentRoute", "isNavSetupDone", "updateIsScheduleOrder", "Factory", "Companion", "app_stagingSumniPos2Debug"})
public final class OrderScreenViewModel extends com.airbnb.mvrx.MavericksViewModel<com.thedasagroup.suminative.ui.orders.OrderState> {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.orders.GetOrdersUseCase getOrdersUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.orders.GetPendingOrdersPagedUseCase getPendingOrdersPagedUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.orders.GetScheduleOrdersUseCase getScheduleOrdersUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.orders.GetScheduleOrdersPagedUseCase getScheduleOrdersPagedUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.orders.ChangeStatusUseCase changeStatusUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.orders.ChangeStatusAndOrdersUseCase changeStatus = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.orders.AcceptOrderWithDelayUseCase acceptOrderWithDelayUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.instacart.truetime.time.TrueTimeImpl trueTimeImpl = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.repo.OrdersRepository ordersRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase getStoreSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.orders.CloseOpenStoreUseCase closeOpenStoreUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.prefs.Prefs prefs = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.utils.SoundPoolPlayer soundPoolPlayer = null;
    @org.jetbrains.annotations.NotNull()
    private final android.media.AudioManager audioManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.domain.GetPOSSettingsUseCase getPosSettings = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.orders.OrderScreenViewModel.Companion Companion = null;
    
    @dagger.assisted.AssistedInject()
    public OrderScreenViewModel(@dagger.assisted.Assisted()
    @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.OrderState state, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.GetOrdersUseCase getOrdersUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.GetPendingOrdersPagedUseCase getPendingOrdersPagedUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.GetScheduleOrdersUseCase getScheduleOrdersUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.GetScheduleOrdersPagedUseCase getScheduleOrdersPagedUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.ChangeStatusUseCase changeStatusUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.ChangeStatusAndOrdersUseCase changeStatus, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.AcceptOrderWithDelayUseCase acceptOrderWithDelayUseCase, @org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl trueTimeImpl, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.OrdersRepository ordersRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase getStoreSettings, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.CloseOpenStoreUseCase closeOpenStoreUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.utils.SoundPoolPlayer soundPoolPlayer, @org.jetbrains.annotations.NotNull()
    android.media.AudioManager audioManager, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.GetPOSSettingsUseCase getPosSettings) {
        super(null, null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.orders.GetOrdersUseCase getGetOrdersUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.orders.GetPendingOrdersPagedUseCase getGetPendingOrdersPagedUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.orders.GetScheduleOrdersUseCase getGetScheduleOrdersUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.orders.GetScheduleOrdersPagedUseCase getGetScheduleOrdersPagedUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.orders.ChangeStatusUseCase getChangeStatusUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.orders.ChangeStatusAndOrdersUseCase getChangeStatus() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.orders.AcceptOrderWithDelayUseCase getAcceptOrderWithDelayUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.instacart.truetime.time.TrueTimeImpl getTrueTimeImpl() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.repo.OrdersRepository getOrdersRepository() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase getGetStoreSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.orders.CloseOpenStoreUseCase getCloseOpenStoreUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.prefs.Prefs getPrefs() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.utils.SoundPoolPlayer getSoundPoolPlayer() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.media.AudioManager getAudioManager() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.GetPOSSettingsUseCase getGetPosSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getOrders(boolean isShowAllOrders, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getScheduleOrders(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getOrdersSilent(boolean isShowAllOrders, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getScheduleOrdersSilent(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object changeStatus(int orderId, int status, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.change_status.ChangeStatusResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object changeStatusOfOrder(int orderId, int status, boolean isShowAllOrders, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object acceptOrderWithDelay(int orderId, boolean isShowAllOrders, int delayInMinutes, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse>>> $completion) {
        return null;
    }
    
    public final void updateExpanded(boolean expanded) {
    }
    
    public final void updateShowAllOrders(boolean value) {
    }
    
    public final void updateOrders(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.pagination.OrderResponse ordersResponse) {
    }
    
    public final void updateShowPrintingPreview(@org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem order, boolean shouldPrintInstant) {
    }
    
    public final void updateClickedOrderId(int orderId) {
    }
    
    public final void updateSelectedChangeStatusId(int statusId) {
    }
    
    public final void updateShowChangeStatusDialog(@org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem order) {
    }
    
    public final void updateChangeStatusOrder(@org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem order) {
    }
    
    public final void updateAcceptOrderDelay(int delay) {
    }
    
    public final void updateShowAcceptOrderDelayDialog(@org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem order) {
    }
    
    public final void updateStoreCloseSettings(@org.jetbrains.annotations.NotNull()
    java.lang.String storeCloseSettings) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateIsStoreClosedManual(boolean isStoreClosedManual, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.close_open_store.CloseOpenStoreResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getIsClosed(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.close_open_store.CloseOpenStoreResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getStoreSettingsFun(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.store_settings.StoreSettingsResponse>>> $completion) {
        return null;
    }
    
    public final void updateStoreTimings(@org.jetbrains.annotations.NotNull()
    java.lang.String openingTime, @org.jetbrains.annotations.NotNull()
    java.lang.String closeTime) {
    }
    
    public final void updateShowDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String dialogType, @org.jetbrains.annotations.NotNull()
    java.lang.String dialogMessage, @org.jetbrains.annotations.NotNull()
    java.lang.String dialogTitle, @org.jetbrains.annotations.NotNull()
    java.lang.String showDialog, boolean isScheduleOrder) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCurrentUTC() {
        return null;
    }
    
    public final void updateIsConnected(boolean isConnected2) {
    }
    
    public final void updateDialogType(@org.jetbrains.annotations.NotNull()
    java.lang.String dialogType) {
    }
    
    public final void updateCurrentRoute(@org.jetbrains.annotations.NotNull()
    java.lang.String currentRoute) {
    }
    
    public final void isNavSetupDone(boolean isNavSetupDone) {
    }
    
    public final void updateIsScheduleOrder(boolean isScheduleOrder) {
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u001b\u0010\u0006\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0003H\u0096\u0001J\u0013\u0010\n\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0007\u001a\u00020\bH\u0096\u0001\u00a8\u0006\u000b"}, d2 = {"Lcom/thedasagroup/suminative/ui/orders/OrderScreenViewModel$Companion;", "Lcom/airbnb/mvrx/MavericksViewModelFactory;", "Lcom/thedasagroup/suminative/ui/orders/OrderScreenViewModel;", "Lcom/thedasagroup/suminative/ui/orders/OrderState;", "<init>", "()V", "create", "viewModelContext", "Lcom/airbnb/mvrx/ViewModelContext;", "state", "initialState", "app_stagingSumniPos2Debug"})
    public static final class Companion implements com.airbnb.mvrx.MavericksViewModelFactory<com.thedasagroup.suminative.ui.orders.OrderScreenViewModel, com.thedasagroup.suminative.ui.orders.OrderState> {
        
        private Companion() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.orders.OrderScreenViewModel create(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.orders.OrderState state) {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.orders.OrderState initialState(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext) {
            return null;
        }
    }
    
    @dagger.assisted.AssistedFactory()
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\bg\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001J\u0010\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0003H&\u00a8\u0006\u0006"}, d2 = {"Lcom/thedasagroup/suminative/ui/orders/OrderScreenViewModel$Factory;", "Lcom/airbnb/mvrx/hilt/AssistedViewModelFactory;", "Lcom/thedasagroup/suminative/ui/orders/OrderScreenViewModel;", "Lcom/thedasagroup/suminative/ui/orders/OrderState;", "create", "state", "app_stagingSumniPos2Debug"})
    public static abstract interface Factory extends com.airbnb.mvrx.hilt.AssistedViewModelFactory<com.thedasagroup.suminative.ui.orders.OrderScreenViewModel, com.thedasagroup.suminative.ui.orders.OrderState> {
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public abstract com.thedasagroup.suminative.ui.orders.OrderScreenViewModel create(@org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.orders.OrderState state);
    }
}