package com.thedasagroup.suminative.ui.categories;

/**
 * Helper object to show the Cash Payment dialog using Compose
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J$\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000bJ*\u0010\r\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00050\u000f\u00a8\u0006\u0010"}, d2 = {"Lcom/thedasagroup/suminative/ui/categories/CashPaymentCompose;", "", "<init>", "()V", "showCashPaymentActivity", "", "activity", "Landroidx/fragment/app/FragmentActivity;", "order", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "launcher", "Landroidx/activity/result/ActivityResultLauncher;", "Landroid/content/Intent;", "showCashPaymentDialog", "onPaymentComplete", "Lkotlin/Function1;", "app_stagingGeneralDebug"})
public final class CashPaymentCompose {
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.categories.CashPaymentCompose INSTANCE = null;
    
    private CashPaymentCompose() {
        super();
    }
    
    /**
     * Show cash payment as full screen activity
     */
    public final void showCashPaymentActivity(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.FragmentActivity activity, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
    androidx.activity.result.ActivityResultLauncher<android.content.Intent> launcher) {
    }
    
    public final void showCashPaymentDialog(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.FragmentActivity activity, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.order.Order, kotlin.Unit> onPaymentComplete) {
    }
}