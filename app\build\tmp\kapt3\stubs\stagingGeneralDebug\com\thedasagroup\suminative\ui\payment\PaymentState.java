package com.thedasagroup.suminative.ui.payment;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\b\n\u0002\b&\n\u0002\u0010\u0000\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001B\u00d7\u0001\u0012\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u0012\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0003\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0003\u0012\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\f0\u0003\u0012\u000e\b\u0002\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\f0\u0003\u0012\u000e\b\u0002\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u0003\u0012\u000e\b\u0002\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00100\u0003\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0013\u0012\u000e\b\u0002\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\u0003\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0017\u0012\b\b\u0002\u0010\u0018\u001a\u00020\u0013\u0012\u0014\b\u0002\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\u001b\u0012\u0004\u0012\u00020\u00130\u001a\u00a2\u0006\u0004\b\u001c\u0010\u001dJ\u000f\u00101\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000f\u00102\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00c6\u0003J\u000f\u00103\u001a\b\u0012\u0004\u0012\u00020\b0\u0003H\u00c6\u0003J\u000b\u00104\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000f\u00105\u001a\b\u0012\u0004\u0012\u00020\f0\u0003H\u00c6\u0003J\u000f\u00106\u001a\b\u0012\u0004\u0012\u00020\f0\u0003H\u00c6\u0003J\u000f\u00107\u001a\b\u0012\u0004\u0012\u00020\f0\u0003H\u00c6\u0003J\u000f\u00108\u001a\b\u0012\u0004\u0012\u00020\u00100\u0003H\u00c6\u0003J\u000f\u00109\u001a\b\u0012\u0004\u0012\u00020\u00100\u0003H\u00c6\u0003J\t\u0010:\u001a\u00020\u0013H\u00c6\u0003J\u000f\u0010;\u001a\b\u0012\u0004\u0012\u00020\u00150\u0003H\u00c6\u0003J\t\u0010<\u001a\u00020\u0017H\u00c6\u0003J\t\u0010=\u001a\u00020\u0013H\u00c6\u0003J\u0015\u0010>\u001a\u000e\u0012\u0004\u0012\u00020\u001b\u0012\u0004\u0012\u00020\u00130\u001aH\u00c6\u0003J\u00d9\u0001\u0010?\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u00032\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\f0\u00032\u000e\b\u0002\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\f0\u00032\u000e\b\u0002\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\u000e\b\u0002\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\b\b\u0002\u0010\u0012\u001a\u00020\u00132\u000e\b\u0002\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\u00032\b\b\u0002\u0010\u0016\u001a\u00020\u00172\b\b\u0002\u0010\u0018\u001a\u00020\u00132\u0014\b\u0002\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\u001b\u0012\u0004\u0012\u00020\u00130\u001aH\u00c6\u0001J\u0013\u0010@\u001a\u00020\u00132\b\u0010A\u001a\u0004\u0018\u00010BH\u00d6\u0003J\t\u0010C\u001a\u00020\u001bH\u00d6\u0001J\t\u0010D\u001a\u00020\u0017H\u00d6\u0001R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001fR\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u001fR\u0013\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010#R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u001fR\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\f0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u001fR\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\f0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u001fR\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u001fR\u0017\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00100\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\u001fR\u0011\u0010\u0012\u001a\u00020\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010*R\u0017\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010\u001fR\u0011\u0010\u0016\u001a\u00020\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010-R\u0011\u0010\u0018\u001a\u00020\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010*R\u001d\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\u001b\u0012\u0004\u0012\u00020\u00130\u001a\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u00100\u00a8\u0006E"}, d2 = {"Lcom/thedasagroup/suminative/ui/payment/PaymentState;", "Lcom/airbnb/mvrx/MavericksState;", "order", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "createOrderResponse", "Lcom/thedasagroup/suminative/data/model/response/my_guava/orders/create_order/GuavaOrderResponse;", "terminalListResponse", "Lcom/thedasagroup/suminative/data/model/response/my_guava/terminals/GetTerminalListResponse;", "selectedTerminal", "Lcom/thedasagroup/suminative/data/model/response/my_guava/terminals/Terminal;", "createSessionResponse", "Lcom/thedasagroup/suminative/data/model/response/my_guava/sessions/Session;", "makePaymentResponse", "checkStatusSession", "checkStatusLoopResponse", "", "cancelingPayment", "showChangeDialog", "", "paymentOrderResponse", "Lcom/thedasagroup/suminative/data/model/response/order/OrderResponse2;", "amountGivenText", "", "showChangeCalculation", "paymentsMap", "", "", "<init>", "(Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/thedasagroup/suminative/data/model/response/my_guava/terminals/Terminal;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;ZLcom/airbnb/mvrx/Async;Ljava/lang/String;ZLjava/util/Map;)V", "getOrder", "()Lcom/airbnb/mvrx/Async;", "getCreateOrderResponse", "getTerminalListResponse", "getSelectedTerminal", "()Lcom/thedasagroup/suminative/data/model/response/my_guava/terminals/Terminal;", "getCreateSessionResponse", "getMakePaymentResponse", "getCheckStatusSession", "getCheckStatusLoopResponse", "getCancelingPayment", "getShowChangeDialog", "()Z", "getPaymentOrderResponse", "getAmountGivenText", "()Ljava/lang/String;", "getShowChangeCalculation", "getPaymentsMap", "()Ljava/util/Map;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "component12", "component13", "component14", "copy", "equals", "other", "", "hashCode", "toString", "app_stagingGeneralDebug"})
public final class PaymentState implements com.airbnb.mvrx.MavericksState {
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.order.Order> order = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse> createOrderResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.terminals.GetTerminalListResponse> terminalListResponse = null;
    @org.jetbrains.annotations.Nullable()
    private final com.thedasagroup.suminative.data.model.response.my_guava.terminals.Terminal selectedTerminal = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session> createSessionResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session> makePaymentResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session> checkStatusSession = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<kotlin.Unit> checkStatusLoopResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<kotlin.Unit> cancelingPayment = null;
    private final boolean showChangeDialog = false;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.order.OrderResponse2> paymentOrderResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String amountGivenText = null;
    private final boolean showChangeCalculation = false;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.Integer, java.lang.Boolean> paymentsMap = null;
    
    public PaymentState(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.order.Order> order, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse> createOrderResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.terminals.GetTerminalListResponse> terminalListResponse, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.my_guava.terminals.Terminal selectedTerminal, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session> createSessionResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session> makePaymentResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session> checkStatusSession, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<kotlin.Unit> checkStatusLoopResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<kotlin.Unit> cancelingPayment, boolean showChangeDialog, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.order.OrderResponse2> paymentOrderResponse, @org.jetbrains.annotations.NotNull()
    java.lang.String amountGivenText, boolean showChangeCalculation, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.Integer, java.lang.Boolean> paymentsMap) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.order.Order> getOrder() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse> getCreateOrderResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.terminals.GetTerminalListResponse> getTerminalListResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.response.my_guava.terminals.Terminal getSelectedTerminal() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session> getCreateSessionResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session> getMakePaymentResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session> getCheckStatusSession() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<kotlin.Unit> getCheckStatusLoopResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<kotlin.Unit> getCancelingPayment() {
        return null;
    }
    
    public final boolean getShowChangeDialog() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.order.OrderResponse2> getPaymentOrderResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getAmountGivenText() {
        return null;
    }
    
    public final boolean getShowChangeCalculation() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.Integer, java.lang.Boolean> getPaymentsMap() {
        return null;
    }
    
    public PaymentState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.order.Order> component1() {
        return null;
    }
    
    public final boolean component10() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.order.OrderResponse2> component11() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component12() {
        return null;
    }
    
    public final boolean component13() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.Integer, java.lang.Boolean> component14() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.terminals.GetTerminalListResponse> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.response.my_guava.terminals.Terminal component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session> component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<kotlin.Unit> component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<kotlin.Unit> component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.payment.PaymentState copy(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.order.Order> order, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse> createOrderResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.terminals.GetTerminalListResponse> terminalListResponse, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.my_guava.terminals.Terminal selectedTerminal, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session> createSessionResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session> makePaymentResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session> checkStatusSession, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<kotlin.Unit> checkStatusLoopResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<kotlin.Unit> cancelingPayment, boolean showChangeDialog, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.order.OrderResponse2> paymentOrderResponse, @org.jetbrains.annotations.NotNull()
    java.lang.String amountGivenText, boolean showChangeCalculation, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.Integer, java.lang.Boolean> paymentsMap) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}