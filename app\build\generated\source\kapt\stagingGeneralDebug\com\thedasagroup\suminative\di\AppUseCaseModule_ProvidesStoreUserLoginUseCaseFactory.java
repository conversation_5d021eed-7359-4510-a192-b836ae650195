package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.repo.ClockInOutRepository;
import com.thedasagroup.suminative.ui.login.StoreUserLoginUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesStoreUserLoginUseCaseFactory implements Factory<StoreUserLoginUseCase> {
  private final Provider<ClockInOutRepository> clockInOutRepositoryProvider;

  public AppUseCaseModule_ProvidesStoreUserLoginUseCaseFactory(
      Provider<ClockInOutRepository> clockInOutRepositoryProvider) {
    this.clockInOutRepositoryProvider = clockInOutRepositoryProvider;
  }

  @Override
  public StoreUserLoginUseCase get() {
    return providesStoreUserLoginUseCase(clockInOutRepositoryProvider.get());
  }

  public static AppUseCaseModule_ProvidesStoreUserLoginUseCaseFactory create(
      Provider<ClockInOutRepository> clockInOutRepositoryProvider) {
    return new AppUseCaseModule_ProvidesStoreUserLoginUseCaseFactory(clockInOutRepositoryProvider);
  }

  public static StoreUserLoginUseCase providesStoreUserLoginUseCase(
      ClockInOutRepository clockInOutRepository) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesStoreUserLoginUseCase(clockInOutRepository));
  }
}
