package com.thedasagroup.suminative.ui.local_orders;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\f\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\f\u00a8\u0006\r"}, d2 = {"Lcom/thedasagroup/suminative/ui/local_orders/OrderFilter;", "", "<init>", "(Ljava/lang/String;I)V", "ALL", "PENDING", "ACCEPTED", "PREPARING", "READY", "COMPLETED", "CANCELLED", "SYNCED", "UNSYNCED", "app_stagingGeneralDebug"})
public enum OrderFilter {
    /*public static final*/ ALL /* = new ALL() */,
    /*public static final*/ PENDING /* = new PENDING() */,
    /*public static final*/ ACCEPTED /* = new ACCEPTED() */,
    /*public static final*/ PREPARING /* = new PREPARING() */,
    /*public static final*/ READY /* = new READY() */,
    /*public static final*/ COMPLETED /* = new COMPLETED() */,
    /*public static final*/ CANCELLED /* = new CANCELLED() */,
    /*public static final*/ SYNCED /* = new SYNCED() */,
    /*public static final*/ UNSYNCED /* = new UNSYNCED() */;
    
    OrderFilter() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.thedasagroup.suminative.ui.local_orders.OrderFilter> getEntries() {
        return null;
    }
}