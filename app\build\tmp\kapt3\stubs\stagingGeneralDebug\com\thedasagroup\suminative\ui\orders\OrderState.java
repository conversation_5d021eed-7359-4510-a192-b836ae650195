package com.thedasagroup.suminative.ui.orders;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0007\n\u0002\u0010$\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b:\n\u0002\u0010\u0000\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001B\u00d1\u0002\u0012\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00070\u0003\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\t\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u0012\b\b\u0002\u0010\r\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u000e\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\f\u0012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\f\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\f\u0012\u0010\b\u0002\u0010\u0013\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00040\u0003\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u000e\u0012\u0016\b\u0002\u0010\u0015\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u000e\u0012\u0004\u0012\u00020\u000e0\u0016\u0012\b\b\u0002\u0010\u0017\u001a\u00020\t\u0012\b\b\u0002\u0010\u0018\u001a\u00020\u0019\u0012\u000e\b\u0002\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u001b0\u0003\u0012\b\b\u0002\u0010\u001c\u001a\u00020\u0019\u0012\b\b\u0002\u0010\u001d\u001a\u00020\u0019\u0012\b\b\u0002\u0010\u001e\u001a\u00020\u0019\u0012\b\b\u0002\u0010\u001f\u001a\u00020\u0019\u0012\b\b\u0002\u0010 \u001a\u00020\u0019\u0012\b\b\u0002\u0010!\u001a\u00020\t\u0012\b\b\u0002\u0010\"\u001a\u00020\u0019\u0012\b\b\u0002\u0010#\u001a\u00020\t\u0012\b\b\u0002\u0010$\u001a\u00020\t\u0012\u000e\b\u0002\u0010%\u001a\b\u0012\u0004\u0012\u00020&0\u0003\u00a2\u0006\u0004\b\'\u0010(J\u000f\u0010C\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000f\u0010D\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000f\u0010E\u001a\b\u0012\u0004\u0012\u00020\u00070\u0003H\u00c6\u0003J\t\u0010F\u001a\u00020\tH\u00c6\u0003J\t\u0010G\u001a\u00020\tH\u00c6\u0003J\u000b\u0010H\u001a\u0004\u0018\u00010\fH\u00c6\u0003J\t\u0010I\u001a\u00020\u000eH\u00c6\u0003J\t\u0010J\u001a\u00020\u000eH\u00c6\u0003J\u000b\u0010K\u001a\u0004\u0018\u00010\fH\u00c6\u0003J\u000b\u0010L\u001a\u0004\u0018\u00010\fH\u00c6\u0003J\u000b\u0010M\u001a\u0004\u0018\u00010\fH\u00c6\u0003J\u0011\u0010N\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00040\u0003H\u00c6\u0003J\t\u0010O\u001a\u00020\u000eH\u00c6\u0003J\u0017\u0010P\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u000e\u0012\u0004\u0012\u00020\u000e0\u0016H\u00c6\u0003J\t\u0010Q\u001a\u00020\tH\u00c6\u0003J\t\u0010R\u001a\u00020\u0019H\u00c6\u0003J\u000f\u0010S\u001a\b\u0012\u0004\u0012\u00020\u001b0\u0003H\u00c6\u0003J\t\u0010T\u001a\u00020\u0019H\u00c6\u0003J\t\u0010U\u001a\u00020\u0019H\u00c6\u0003J\t\u0010V\u001a\u00020\u0019H\u00c6\u0003J\t\u0010W\u001a\u00020\u0019H\u00c6\u0003J\t\u0010X\u001a\u00020\u0019H\u00c6\u0003J\t\u0010Y\u001a\u00020\tH\u00c6\u0003J\t\u0010Z\u001a\u00020\u0019H\u00c6\u0003J\t\u0010[\u001a\u00020\tH\u00c6\u0003J\t\u0010\\\u001a\u00020\tH\u00c6\u0003J\u000f\u0010]\u001a\b\u0012\u0004\u0012\u00020&0\u0003H\u00c6\u0003J\u00d3\u0002\u0010^\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00070\u00032\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f2\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u000e2\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\f2\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\f2\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\f2\u0010\b\u0002\u0010\u0013\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00040\u00032\b\b\u0002\u0010\u0014\u001a\u00020\u000e2\u0016\b\u0002\u0010\u0015\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u000e\u0012\u0004\u0012\u00020\u000e0\u00162\b\b\u0002\u0010\u0017\u001a\u00020\t2\b\b\u0002\u0010\u0018\u001a\u00020\u00192\u000e\b\u0002\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u001b0\u00032\b\b\u0002\u0010\u001c\u001a\u00020\u00192\b\b\u0002\u0010\u001d\u001a\u00020\u00192\b\b\u0002\u0010\u001e\u001a\u00020\u00192\b\b\u0002\u0010\u001f\u001a\u00020\u00192\b\b\u0002\u0010 \u001a\u00020\u00192\b\b\u0002\u0010!\u001a\u00020\t2\b\b\u0002\u0010\"\u001a\u00020\u00192\b\b\u0002\u0010#\u001a\u00020\t2\b\b\u0002\u0010$\u001a\u00020\t2\u000e\b\u0002\u0010%\u001a\b\u0012\u0004\u0012\u00020&0\u0003H\u00c6\u0001J\u0013\u0010_\u001a\u00020\t2\b\u0010`\u001a\u0004\u0018\u00010aH\u00d6\u0003J\t\u0010b\u001a\u00020\u000eH\u00d6\u0001J\t\u0010c\u001a\u00020\u0019H\u00d6\u0001R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010*R\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010*R\u0017\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00070\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010*R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010-R\u0011\u0010\n\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010-R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010.R\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u00100R\u0011\u0010\u000f\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u00100R\u0013\u0010\u0010\u001a\u0004\u0018\u00010\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010.R\u0013\u0010\u0011\u001a\u0004\u0018\u00010\f\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u0010.R\u0013\u0010\u0012\u001a\u0004\u0018\u00010\f\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010.R\u0019\u0010\u0013\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b4\u0010*R\u0011\u0010\u0014\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u00100R\u001f\u0010\u0015\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u000e\u0012\u0004\u0012\u00020\u000e0\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u00107R\u0011\u0010\u0017\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b8\u0010-R\u0011\u0010\u0018\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010:R\u0017\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u001b0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u0010*R\u0011\u0010\u001c\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010:R\u0011\u0010\u001d\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u0010:R\u0011\u0010\u001e\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b>\u0010:R\u0011\u0010\u001f\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u0010:R\u0011\u0010 \u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u0010:R\u0011\u0010!\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010-R\u0011\u0010\"\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\bA\u0010:R\u0011\u0010#\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010-R\u0011\u0010$\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010-R\u0017\u0010%\u001a\b\u0012\u0004\u0012\u00020&0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u0010*\u00a8\u0006d"}, d2 = {"Lcom/thedasagroup/suminative/ui/orders/OrderState;", "Lcom/airbnb/mvrx/MavericksState;", "ordersResponse", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/request/pagination/OrderResponse;", "scheduleOrdersResponse", "changeStatusResponse", "Lcom/thedasagroup/suminative/data/model/response/change_status/ChangeStatusResponse;", "isExpanded", "", "isShowAllOrders", "isShowPrintingPreview", "Lcom/thedasagroup/suminative/data/model/request/pagination/OrderItem;", "clickedOrderId", "", "selectedChangeStatusId", "isShowChangeStatusDialog", "showAcceptOrderDelayDialog", "changeStatusOrder", "acceptDeliveryOrderResponse", "acceptOrderDelay", "mapCount", "", "shouldPrintInstant", "storeCloseSettings", "", "closedStoreManualResponse", "Lcom/thedasagroup/suminative/data/model/response/close_open_store/CloseOpenStoreResponse;", "openingTime", "closeTime", "dialogType", "dialogMessage", "dialogTitle", "isConnected2", "currentRouteId", "isScheduleOrder", "isNavSetupDone", "storeSettingsResponse", "Lcom/thedasagroup/suminative/data/model/response/store_settings/StoreSettingsResponse;", "<init>", "(Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;ZZLcom/thedasagroup/suminative/data/model/request/pagination/OrderItem;IILcom/thedasagroup/suminative/data/model/request/pagination/OrderItem;Lcom/thedasagroup/suminative/data/model/request/pagination/OrderItem;Lcom/thedasagroup/suminative/data/model/request/pagination/OrderItem;Lcom/airbnb/mvrx/Async;ILjava/util/Map;ZLjava/lang/String;Lcom/airbnb/mvrx/Async;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;ZZLcom/airbnb/mvrx/Async;)V", "getOrdersResponse", "()Lcom/airbnb/mvrx/Async;", "getScheduleOrdersResponse", "getChangeStatusResponse", "()Z", "()Lcom/thedasagroup/suminative/data/model/request/pagination/OrderItem;", "getClickedOrderId", "()I", "getSelectedChangeStatusId", "getShowAcceptOrderDelayDialog", "getChangeStatusOrder", "getAcceptDeliveryOrderResponse", "getAcceptOrderDelay", "getMapCount", "()Ljava/util/Map;", "getShouldPrintInstant", "getStoreCloseSettings", "()Ljava/lang/String;", "getClosedStoreManualResponse", "getOpeningTime", "getCloseTime", "getDialogType", "getDialogMessage", "getDialogTitle", "getCurrentRouteId", "getStoreSettingsResponse", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component27", "copy", "equals", "other", "", "hashCode", "toString", "app_stagingGeneralDebug"})
public final class OrderState implements com.airbnb.mvrx.MavericksState {
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> ordersResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> scheduleOrdersResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.change_status.ChangeStatusResponse> changeStatusResponse = null;
    private final boolean isExpanded = false;
    private final boolean isShowAllOrders = false;
    @org.jetbrains.annotations.Nullable()
    private final com.thedasagroup.suminative.data.model.request.pagination.OrderItem isShowPrintingPreview = null;
    private final int clickedOrderId = 0;
    private final int selectedChangeStatusId = 0;
    @org.jetbrains.annotations.Nullable()
    private final com.thedasagroup.suminative.data.model.request.pagination.OrderItem isShowChangeStatusDialog = null;
    @org.jetbrains.annotations.Nullable()
    private final com.thedasagroup.suminative.data.model.request.pagination.OrderItem showAcceptOrderDelayDialog = null;
    @org.jetbrains.annotations.Nullable()
    private final com.thedasagroup.suminative.data.model.request.pagination.OrderItem changeStatusOrder = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> acceptDeliveryOrderResponse = null;
    private final int acceptOrderDelay = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.Integer, java.lang.Integer> mapCount = null;
    private final boolean shouldPrintInstant = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String storeCloseSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.close_open_store.CloseOpenStoreResponse> closedStoreManualResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String openingTime = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String closeTime = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String dialogType = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String dialogMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String dialogTitle = null;
    private final boolean isConnected2 = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String currentRouteId = null;
    private final boolean isScheduleOrder = false;
    private final boolean isNavSetupDone = false;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.store_settings.StoreSettingsResponse> storeSettingsResponse = null;
    
    public OrderState(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> ordersResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> scheduleOrdersResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.change_status.ChangeStatusResponse> changeStatusResponse, boolean isExpanded, boolean isShowAllOrders, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem isShowPrintingPreview, int clickedOrderId, int selectedChangeStatusId, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem isShowChangeStatusDialog, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem showAcceptOrderDelayDialog, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem changeStatusOrder, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> acceptDeliveryOrderResponse, int acceptOrderDelay, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.Integer, java.lang.Integer> mapCount, boolean shouldPrintInstant, @org.jetbrains.annotations.NotNull()
    java.lang.String storeCloseSettings, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.close_open_store.CloseOpenStoreResponse> closedStoreManualResponse, @org.jetbrains.annotations.NotNull()
    java.lang.String openingTime, @org.jetbrains.annotations.NotNull()
    java.lang.String closeTime, @org.jetbrains.annotations.NotNull()
    java.lang.String dialogType, @org.jetbrains.annotations.NotNull()
    java.lang.String dialogMessage, @org.jetbrains.annotations.NotNull()
    java.lang.String dialogTitle, boolean isConnected2, @org.jetbrains.annotations.NotNull()
    java.lang.String currentRouteId, boolean isScheduleOrder, boolean isNavSetupDone, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.store_settings.StoreSettingsResponse> storeSettingsResponse) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> getOrdersResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> getScheduleOrdersResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.change_status.ChangeStatusResponse> getChangeStatusResponse() {
        return null;
    }
    
    public final boolean isExpanded() {
        return false;
    }
    
    public final boolean isShowAllOrders() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.request.pagination.OrderItem isShowPrintingPreview() {
        return null;
    }
    
    public final int getClickedOrderId() {
        return 0;
    }
    
    public final int getSelectedChangeStatusId() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.request.pagination.OrderItem isShowChangeStatusDialog() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.request.pagination.OrderItem getShowAcceptOrderDelayDialog() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.request.pagination.OrderItem getChangeStatusOrder() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> getAcceptDeliveryOrderResponse() {
        return null;
    }
    
    public final int getAcceptOrderDelay() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.Integer, java.lang.Integer> getMapCount() {
        return null;
    }
    
    public final boolean getShouldPrintInstant() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getStoreCloseSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.close_open_store.CloseOpenStoreResponse> getClosedStoreManualResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOpeningTime() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCloseTime() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDialogType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDialogMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDialogTitle() {
        return null;
    }
    
    public final boolean isConnected2() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCurrentRouteId() {
        return null;
    }
    
    public final boolean isScheduleOrder() {
        return false;
    }
    
    public final boolean isNavSetupDone() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.store_settings.StoreSettingsResponse> getStoreSettingsResponse() {
        return null;
    }
    
    public OrderState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.request.pagination.OrderItem component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.request.pagination.OrderItem component11() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> component12() {
        return null;
    }
    
    public final int component13() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.Integer, java.lang.Integer> component14() {
        return null;
    }
    
    public final boolean component15() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component16() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.close_open_store.CloseOpenStoreResponse> component17() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component18() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component19() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component20() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component21() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component22() {
        return null;
    }
    
    public final boolean component23() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component24() {
        return null;
    }
    
    public final boolean component25() {
        return false;
    }
    
    public final boolean component26() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.store_settings.StoreSettingsResponse> component27() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.change_status.ChangeStatusResponse> component3() {
        return null;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean component5() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.request.pagination.OrderItem component6() {
        return null;
    }
    
    public final int component7() {
        return 0;
    }
    
    public final int component8() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.request.pagination.OrderItem component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.orders.OrderState copy(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> ordersResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> scheduleOrdersResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.change_status.ChangeStatusResponse> changeStatusResponse, boolean isExpanded, boolean isShowAllOrders, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem isShowPrintingPreview, int clickedOrderId, int selectedChangeStatusId, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem isShowChangeStatusDialog, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem showAcceptOrderDelayDialog, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem changeStatusOrder, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse> acceptDeliveryOrderResponse, int acceptOrderDelay, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.Integer, java.lang.Integer> mapCount, boolean shouldPrintInstant, @org.jetbrains.annotations.NotNull()
    java.lang.String storeCloseSettings, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.close_open_store.CloseOpenStoreResponse> closedStoreManualResponse, @org.jetbrains.annotations.NotNull()
    java.lang.String openingTime, @org.jetbrains.annotations.NotNull()
    java.lang.String closeTime, @org.jetbrains.annotations.NotNull()
    java.lang.String dialogType, @org.jetbrains.annotations.NotNull()
    java.lang.String dialogMessage, @org.jetbrains.annotations.NotNull()
    java.lang.String dialogTitle, boolean isConnected2, @org.jetbrains.annotations.NotNull()
    java.lang.String currentRouteId, boolean isScheduleOrder, boolean isNavSetupDone, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.store_settings.StoreSettingsResponse> storeSettingsResponse) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}