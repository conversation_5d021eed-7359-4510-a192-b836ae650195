package com.thedasagroup.suminative.ui.orders;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\'\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0004\b\n\u0010\u000bJ4\u0010\f\u001a\u0010\u0012\f\u0012\n\u0012\u0006\u0012\u0004\u0018\u00010\u000f0\u000e0\r2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0011H\u0086B\u00a2\u0006\u0002\u0010\u0015R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"Lcom/thedasagroup/suminative/ui/orders/AcceptOrderWithDelayUseCase;", "Lcom/thedasagroup/suminative/ui/orders/GetPendingOrdersPagedUseCase;", "repo", "Lcom/thedasagroup/suminative/data/repo/OrdersRepository;", "prefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "trueTimeImpl", "Lcom/instacart/truetime/time/TrueTimeImpl;", "getStoreSettingsUseCase", "Lcom/thedasagroup/suminative/ui/login/GetStoreSettingsUseCase;", "<init>", "(Lcom/thedasagroup/suminative/data/repo/OrdersRepository;Lcom/thedasagroup/suminative/data/prefs/Prefs;Lcom/instacart/truetime/time/TrueTimeImpl;Lcom/thedasagroup/suminative/ui/login/GetStoreSettingsUseCase;)V", "invoke", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/request/pagination/OrderResponse;", "orderId", "", "isShowAllOrder", "", "delayInMinutes", "(IZILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_stagingGeneralDebug"})
public final class AcceptOrderWithDelayUseCase extends com.thedasagroup.suminative.ui.orders.GetPendingOrdersPagedUseCase {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.repo.OrdersRepository repo = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.prefs.Prefs prefs = null;
    @org.jetbrains.annotations.NotNull()
    private final com.instacart.truetime.time.TrueTimeImpl trueTimeImpl = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase getStoreSettingsUseCase = null;
    
    public AcceptOrderWithDelayUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.OrdersRepository repo, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs, @org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl trueTimeImpl, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase getStoreSettingsUseCase) {
        super(null, null);
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object invoke(int orderId, boolean isShowAllOrder, int delayInMinutes, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse>>> $completion) {
        return null;
    }
}