package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.domain.myguava.MyGuavaCreateOrderUseCase;
import com.thedasagroup.suminative.domain.myguava.MyGuavaCreateSessionUseCase;
import com.thedasagroup.suminative.domain.myguava.MyGuavaGetTerminalsUseCase;
import com.thedasagroup.suminative.domain.myguava.MyGuavaMakePaymentUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesMyGuavaMakePaymentUseCaseFactory implements Factory<MyGuavaMakePaymentUseCase> {
  private final Provider<MyGuavaCreateOrderUseCase> orderUseCaseProvider;

  private final Provider<MyGuavaGetTerminalsUseCase> terminalsUseCaseProvider;

  private final Provider<MyGuavaCreateSessionUseCase> createSessionUseCaseProvider;

  public AppUseCaseModule_ProvidesMyGuavaMakePaymentUseCaseFactory(
      Provider<MyGuavaCreateOrderUseCase> orderUseCaseProvider,
      Provider<MyGuavaGetTerminalsUseCase> terminalsUseCaseProvider,
      Provider<MyGuavaCreateSessionUseCase> createSessionUseCaseProvider) {
    this.orderUseCaseProvider = orderUseCaseProvider;
    this.terminalsUseCaseProvider = terminalsUseCaseProvider;
    this.createSessionUseCaseProvider = createSessionUseCaseProvider;
  }

  @Override
  public MyGuavaMakePaymentUseCase get() {
    return providesMyGuavaMakePaymentUseCase(orderUseCaseProvider.get(), terminalsUseCaseProvider.get(), createSessionUseCaseProvider.get());
  }

  public static AppUseCaseModule_ProvidesMyGuavaMakePaymentUseCaseFactory create(
      Provider<MyGuavaCreateOrderUseCase> orderUseCaseProvider,
      Provider<MyGuavaGetTerminalsUseCase> terminalsUseCaseProvider,
      Provider<MyGuavaCreateSessionUseCase> createSessionUseCaseProvider) {
    return new AppUseCaseModule_ProvidesMyGuavaMakePaymentUseCaseFactory(orderUseCaseProvider, terminalsUseCaseProvider, createSessionUseCaseProvider);
  }

  public static MyGuavaMakePaymentUseCase providesMyGuavaMakePaymentUseCase(
      MyGuavaCreateOrderUseCase orderUseCase, MyGuavaGetTerminalsUseCase terminalsUseCase,
      MyGuavaCreateSessionUseCase createSessionUseCase) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesMyGuavaMakePaymentUseCase(orderUseCase, terminalsUseCase, createSessionUseCase));
  }
}
