{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3448,3546,3648,3747,3849,3958,4065,18185", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "3541,3643,3742,3844,3953,4060,4190,18281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,306,428,552,657,753,866,1009,1128,1286,1370,1482,1576,1676,1795,1917,2034,2176,2316,2459,2635,2770,2890,3013,3143,3238,3335,3462,3600,3700,3810,3916,4059,4207,4317,4418,4507,4603,4696,4782,4868,4971,5051,5134,5233,5339,5439,5540,5628,5738,5838,5943,6061,6141,6255", "endColumns": "126,123,121,123,104,95,112,142,118,157,83,111,93,99,118,121,116,141,139,142,175,134,119,122,129,94,96,126,137,99,109,105,142,147,109,100,88,95,92,85,85,102,79,82,98,105,99,100,87,109,99,104,117,79,113,106", "endOffsets": "177,301,423,547,652,748,861,1004,1123,1281,1365,1477,1571,1671,1790,1912,2029,2171,2311,2454,2630,2765,2885,3008,3138,3233,3330,3457,3595,3695,3805,3911,4054,4202,4312,4413,4502,4598,4691,4777,4863,4966,5046,5129,5228,5334,5434,5535,5623,5733,5833,5938,6056,6136,6250,6357"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6093,6220,6344,6466,6590,6695,6791,6904,7047,7166,7324,7408,7520,7614,7714,7833,7955,8072,8214,8354,8497,8673,8808,8928,9051,9181,9276,9373,9500,9638,9738,9848,9954,10097,10245,10355,10456,10545,10641,10734,10820,10906,11009,11089,11172,11271,11377,11477,11578,11666,11776,11876,11981,12099,12179,12293", "endColumns": "126,123,121,123,104,95,112,142,118,157,83,111,93,99,118,121,116,141,139,142,175,134,119,122,129,94,96,126,137,99,109,105,142,147,109,100,88,95,92,85,85,102,79,82,98,105,99,100,87,109,99,104,117,79,113,106", "endOffsets": "6215,6339,6461,6585,6690,6786,6899,7042,7161,7319,7403,7515,7609,7709,7828,7950,8067,8209,8349,8492,8668,8803,8923,9046,9176,9271,9368,9495,9633,9733,9843,9949,10092,10240,10350,10451,10540,10636,10729,10815,10901,11004,11084,11167,11266,11372,11472,11573,11661,11771,11871,11976,12094,12174,12288,12395"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,252,390,466,553,667,795,971,1132,1255,1364,1497,1556,1613,1705,1803,1867,2004,2119,2202,2363,2439,2548,2661,2790,2895,3003,3104,3176,3248,3362,3466,3687,3869,3973,4147,4254,4476,4594,4745,4846,4991,5102,5249,5356,5461,5605,5712,5855,5961,6138,6220,6362,6437,6563,6678,6759,6841,6913,6986,7068,7140,7266,7365,7530,7633,7749,7858,8018,8125,8230,8364,8459,8580,8696,8817,8917,8979,9032,9087,9141,9190,9242,9314,9363,9433,9483,9527,9600,9644,9700,9753,9813,9865,9918,9967,10047,10096,10150,10195,10258,10319,10374,10518,10631,10715,10776,10859,10928,10996,11132,11214,11290,11370,11448,11516,11613,11688,11779,11855,11993,12069,12161,12251,12350,12442,12523,12631,12736,12852,12960,13037,13125,13204,13288,13418,13551,13619,13696,13802,13893,15944,16059,16257,16304,16420,16559,16652,16798,16892,17025,17182,17331,17521,17594,17677,17748,17850,17988,18033,18115,18241,18341,18488,18624,18776,18889,18996,19062,19121,19182,19238,19305,19386,19481,19603,19673,19750,20029,20262,20384,20510,20641,20731,20840,20977,21129,21290,21381,21486,21572,21704,21815,21968,22060,22139,22196,22433,22527,22608,22666,22724,22847,22968,23074,23263,23466,23542,23609,23672,23842,23920,24011,24092,24190,24375,24477,24565,24692,24794,24959,25068,25137,25285,25358,25434,25494,25569,25618,25681,25777,25829,25901,26084,26194,26292,26355,26489,26631,26761,26874,27143,27254,27350,27447,27646,27865,27953,28155,28268,28423,28541,28623,28709,28787,28915,29061,29216,29263,29350,29475,29600,29684,29771,29870,29948,30023,30096,30160,30240,30313,30390,30484,30554,30625,30723,30802,30888,31074,31216,31303,31362,31452,31594,31654,31731,31811,31889,31971,32209,32264,32340,32430,32516,32607,32793,32874,32943,33030,33110,33178,33259,33345,33504,33589,33642,33714,33791,33872,33947,34057,34160,34273,34397,34475,34523,34572,34663,34768,34855,34967,35136,35248,35329,35463,35594,35740,35871,35986,36148,36284,36508,36687,36819,36984,37083,37184,37303,37431,37546,37682,37754,38157,38247,38337,38422", "endColumns": "196,137,75,86,113,127,175,160,122,108,132,58,56,91,97,63,136,114,82,160,75,108,112,128,104,107,100,71,71,113,103,220,181,103,173,106,221,117,150,100,144,110,146,106,104,143,106,142,105,176,81,141,74,125,114,80,81,71,72,81,71,125,98,164,102,115,108,159,106,104,133,94,120,115,120,99,61,52,54,53,48,51,71,48,69,49,43,72,43,55,52,59,51,52,48,79,48,53,44,62,60,54,143,112,83,60,82,68,67,135,81,75,79,77,67,96,74,90,75,137,75,91,89,98,91,80,107,104,115,107,76,87,78,83,129,132,67,76,105,90,2050,114,197,46,115,138,92,145,93,132,156,148,189,72,82,70,101,137,44,81,125,99,146,135,151,112,106,65,58,60,55,66,80,94,121,69,76,278,232,121,125,130,89,108,136,151,160,90,104,85,131,110,152,91,78,56,236,93,80,57,57,122,120,105,188,202,75,66,62,169,77,90,80,97,184,101,87,126,101,164,108,68,147,72,75,59,74,48,62,95,51,71,182,109,97,62,133,141,129,112,268,110,95,96,198,218,87,201,112,154,117,81,85,77,127,145,154,46,86,124,124,83,86,98,77,74,72,63,79,72,76,93,69,70,97,78,85,185,141,86,58,89,141,59,76,79,77,81,237,54,75,89,85,90,185,80,68,86,79,67,80,85,158,84,52,71,76,80,74,109,102,112,123,77,47,48,90,104,86,111,168,111,80,133,130,145,130,114,161,135,223,178,131,164,98,100,118,127,114,135,71,402,89,89,84,77", "endOffsets": "247,385,461,548,662,790,966,1127,1250,1359,1492,1551,1608,1700,1798,1862,1999,2114,2197,2358,2434,2543,2656,2785,2890,2998,3099,3171,3243,3357,3461,3682,3864,3968,4142,4249,4471,4589,4740,4841,4986,5097,5244,5351,5456,5600,5707,5850,5956,6133,6215,6357,6432,6558,6673,6754,6836,6908,6981,7063,7135,7261,7360,7525,7628,7744,7853,8013,8120,8225,8359,8454,8575,8691,8812,8912,8974,9027,9082,9136,9185,9237,9309,9358,9428,9478,9522,9595,9639,9695,9748,9808,9860,9913,9962,10042,10091,10145,10190,10253,10314,10369,10513,10626,10710,10771,10854,10923,10991,11127,11209,11285,11365,11443,11511,11608,11683,11774,11850,11988,12064,12156,12246,12345,12437,12518,12626,12731,12847,12955,13032,13120,13199,13283,13413,13546,13614,13691,13797,13888,15939,16054,16252,16299,16415,16554,16647,16793,16887,17020,17177,17326,17516,17589,17672,17743,17845,17983,18028,18110,18236,18336,18483,18619,18771,18884,18991,19057,19116,19177,19233,19300,19381,19476,19598,19668,19745,20024,20257,20379,20505,20636,20726,20835,20972,21124,21285,21376,21481,21567,21699,21810,21963,22055,22134,22191,22428,22522,22603,22661,22719,22842,22963,23069,23258,23461,23537,23604,23667,23837,23915,24006,24087,24185,24370,24472,24560,24687,24789,24954,25063,25132,25280,25353,25429,25489,25564,25613,25676,25772,25824,25896,26079,26189,26287,26350,26484,26626,26756,26869,27138,27249,27345,27442,27641,27860,27948,28150,28263,28418,28536,28618,28704,28782,28910,29056,29211,29258,29345,29470,29595,29679,29766,29865,29943,30018,30091,30155,30235,30308,30385,30479,30549,30620,30718,30797,30883,31069,31211,31298,31357,31447,31589,31649,31726,31806,31884,31966,32204,32259,32335,32425,32511,32602,32788,32869,32938,33025,33105,33173,33254,33340,33499,33584,33637,33709,33786,33867,33942,34052,34155,34268,34392,34470,34518,34567,34658,34763,34850,34962,35131,35243,35324,35458,35589,35735,35866,35981,36143,36279,36503,36682,36814,36979,37078,37179,37298,37426,37541,37677,37749,38152,38242,38332,38417,38495"}, "to": {"startLines": "193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18286,18483,18621,18697,18784,18898,19026,19202,19363,19486,19595,19728,19787,19844,19936,20034,20098,20235,20350,20433,20594,20670,20779,20892,21021,21126,21234,21335,21407,21479,21593,21697,21918,22100,22204,22378,22485,22707,22825,22976,23077,23222,23333,23480,23587,23692,23836,23943,24086,24192,24369,24451,24593,24668,24794,24909,24990,25072,25144,25217,25299,25371,25497,25596,25761,25864,25980,26089,26249,26356,26461,26595,26690,26811,26927,27048,27148,27210,27263,27318,27372,27421,27473,27545,27594,27664,27714,27758,27831,27875,27931,27984,28044,28096,28149,28198,28278,28327,28381,28426,28489,28550,28605,28749,28862,28946,29007,29090,29159,29227,29363,29445,29521,29601,29679,29747,29844,29919,30010,30086,30224,30300,30392,30482,30581,30673,30754,30862,30967,31083,31191,31268,31356,31435,31519,31649,31782,31850,31927,32033,32124,34175,34290,34488,34535,34651,34790,34883,35029,35123,35256,35413,35562,35752,35825,35908,35979,36081,36219,36264,36346,36472,36572,36719,36855,37007,37120,37227,37293,37352,37413,37469,37536,37617,37712,37834,37904,37981,38260,38493,38615,38741,38872,38962,39071,39208,39360,39521,39612,39717,39803,39935,40046,40199,40291,40370,40427,40664,40758,40839,40897,40955,41078,41199,41305,41494,41697,41773,41840,41903,42073,42151,42242,42323,42421,42606,42708,42796,42923,43025,43190,43299,43368,43516,43589,43665,43725,43800,43849,43912,44008,44060,44132,44315,44425,44523,44586,44720,44862,44992,45105,45374,45485,45581,45678,45877,46096,46184,46386,46499,46654,46772,46854,46940,47018,47146,47292,47447,47494,47581,47706,47831,47915,48002,48101,48179,48254,48327,48391,48471,48544,48621,48715,48785,48856,48954,49033,49119,49305,49447,49534,49593,49683,49825,49885,49962,50042,50120,50202,50440,50495,50571,50661,50747,50838,51024,51105,51174,51261,51341,51409,51490,51576,51735,51820,51873,51945,52022,52103,52178,52288,52391,52504,52628,52706,52754,52803,52894,52999,53086,53198,53367,53479,53560,53694,53825,53971,54102,54217,54379,54515,54739,54918,55050,55215,55314,55415,55534,55662,55777,55913,55985,56388,56478,56568,56653", "endColumns": "196,137,75,86,113,127,175,160,122,108,132,58,56,91,97,63,136,114,82,160,75,108,112,128,104,107,100,71,71,113,103,220,181,103,173,106,221,117,150,100,144,110,146,106,104,143,106,142,105,176,81,141,74,125,114,80,81,71,72,81,71,125,98,164,102,115,108,159,106,104,133,94,120,115,120,99,61,52,54,53,48,51,71,48,69,49,43,72,43,55,52,59,51,52,48,79,48,53,44,62,60,54,143,112,83,60,82,68,67,135,81,75,79,77,67,96,74,90,75,137,75,91,89,98,91,80,107,104,115,107,76,87,78,83,129,132,67,76,105,90,2050,114,197,46,115,138,92,145,93,132,156,148,189,72,82,70,101,137,44,81,125,99,146,135,151,112,106,65,58,60,55,66,80,94,121,69,76,278,232,121,125,130,89,108,136,151,160,90,104,85,131,110,152,91,78,56,236,93,80,57,57,122,120,105,188,202,75,66,62,169,77,90,80,97,184,101,87,126,101,164,108,68,147,72,75,59,74,48,62,95,51,71,182,109,97,62,133,141,129,112,268,110,95,96,198,218,87,201,112,154,117,81,85,77,127,145,154,46,86,124,124,83,86,98,77,74,72,63,79,72,76,93,69,70,97,78,85,185,141,86,58,89,141,59,76,79,77,81,237,54,75,89,85,90,185,80,68,86,79,67,80,85,158,84,52,71,76,80,74,109,102,112,123,77,47,48,90,104,86,111,168,111,80,133,130,145,130,114,161,135,223,178,131,164,98,100,118,127,114,135,71,402,89,89,84,77", "endOffsets": "18478,18616,18692,18779,18893,19021,19197,19358,19481,19590,19723,19782,19839,19931,20029,20093,20230,20345,20428,20589,20665,20774,20887,21016,21121,21229,21330,21402,21474,21588,21692,21913,22095,22199,22373,22480,22702,22820,22971,23072,23217,23328,23475,23582,23687,23831,23938,24081,24187,24364,24446,24588,24663,24789,24904,24985,25067,25139,25212,25294,25366,25492,25591,25756,25859,25975,26084,26244,26351,26456,26590,26685,26806,26922,27043,27143,27205,27258,27313,27367,27416,27468,27540,27589,27659,27709,27753,27826,27870,27926,27979,28039,28091,28144,28193,28273,28322,28376,28421,28484,28545,28600,28744,28857,28941,29002,29085,29154,29222,29358,29440,29516,29596,29674,29742,29839,29914,30005,30081,30219,30295,30387,30477,30576,30668,30749,30857,30962,31078,31186,31263,31351,31430,31514,31644,31777,31845,31922,32028,32119,34170,34285,34483,34530,34646,34785,34878,35024,35118,35251,35408,35557,35747,35820,35903,35974,36076,36214,36259,36341,36467,36567,36714,36850,37002,37115,37222,37288,37347,37408,37464,37531,37612,37707,37829,37899,37976,38255,38488,38610,38736,38867,38957,39066,39203,39355,39516,39607,39712,39798,39930,40041,40194,40286,40365,40422,40659,40753,40834,40892,40950,41073,41194,41300,41489,41692,41768,41835,41898,42068,42146,42237,42318,42416,42601,42703,42791,42918,43020,43185,43294,43363,43511,43584,43660,43720,43795,43844,43907,44003,44055,44127,44310,44420,44518,44581,44715,44857,44987,45100,45369,45480,45576,45673,45872,46091,46179,46381,46494,46649,46767,46849,46935,47013,47141,47287,47442,47489,47576,47701,47826,47910,47997,48096,48174,48249,48322,48386,48466,48539,48616,48710,48780,48851,48949,49028,49114,49300,49442,49529,49588,49678,49820,49880,49957,50037,50115,50197,50435,50490,50566,50656,50742,50833,51019,51100,51169,51256,51336,51404,51485,51571,51730,51815,51868,51940,52017,52098,52173,52283,52386,52499,52623,52701,52749,52798,52889,52994,53081,53193,53362,53474,53555,53689,53820,53966,54097,54212,54374,54510,54734,54913,55045,55210,55309,55410,55529,55657,55772,55908,55980,56383,56473,56563,56648,56726"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,352,433,510,609,704,803,943,1026,1090,1156,1251,1336,1398,1486,1548,1617,1680,1753,1816,1870,1991,2048,2110,2164,2241,2378,2463,2543,2642,2728,2810,2945,3026,3107,3253,3344,3434,3489,3540,3606,3679,3759,3830,3910,3985,4062,4131,4208,4313,4401,4490,4583,4676,4750,4830,4924,4975,5059,5125,5209,5297,5359,5423,5486,5554,5669,5783,5889,5998,6057,6112,6192,6277,6356", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,145,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79,84,78,81", "endOffsets": "263,347,428,505,604,699,798,938,1021,1085,1151,1246,1331,1393,1481,1543,1612,1675,1748,1811,1865,1986,2043,2105,2159,2236,2373,2458,2538,2637,2723,2805,2940,3021,3102,3248,3339,3429,3484,3535,3601,3674,3754,3825,3905,3980,4057,4126,4203,4308,4396,4485,4578,4671,4745,4825,4919,4970,5054,5120,5204,5292,5354,5418,5481,5549,5664,5778,5884,5993,6052,6107,6187,6272,6351,6433"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3012,3096,3177,3254,3353,4195,4294,4434,5225,5289,5355,5761,6031,12400,12488,12550,12619,12682,12755,12818,12872,12993,13050,13112,13166,13243,13380,13465,13545,13644,13730,13812,13947,14028,14109,14255,14346,14436,14491,14542,14608,14681,14761,14832,14912,14987,15064,15133,15210,15315,15403,15492,15585,15678,15752,15832,15926,15977,16061,16127,16211,16299,16361,16425,16488,16556,16671,16785,16891,17000,17059,17302,17636,17721,17885", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,145,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79,84,78,81", "endOffsets": "313,3091,3172,3249,3348,3443,4289,4429,4512,5284,5350,5445,5841,6088,12483,12545,12614,12677,12750,12813,12867,12988,13045,13107,13161,13238,13375,13460,13540,13639,13725,13807,13942,14023,14104,14250,14341,14431,14486,14537,14603,14676,14756,14827,14907,14982,15059,15128,15205,15310,15398,15487,15580,15673,15747,15827,15921,15972,16056,16122,16206,16294,16356,16420,16483,16551,16666,16780,16886,16995,17054,17109,17377,17716,17795,17962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,423,526,635,719,824,943,1021,1096,1188,1282,1375,1469,1570,1664,1761,1856,1948,2040,2121,2227,2334,2432,2536,2642,2749,2912,17554", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "418,521,630,714,819,938,1016,1091,1183,1277,1370,1464,1565,1659,1756,1851,1943,2035,2116,2222,2329,2427,2531,2637,2744,2907,3007,17631"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-it\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "131", "endOffsets": "326"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4704", "endColumns": "135", "endOffsets": "4835"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,363", "endColumns": "99,97,109,102", "endOffsets": "150,248,358,461"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4840,5450,5548,5658", "endColumns": "99,97,109,102", "endOffsets": "4935,5543,5653,5756"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,98", "endOffsets": "148,247"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "56999,57097", "endColumns": "97,98", "endOffsets": "57092,57191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,292,390,490,577,656,762,855,950,1034,1122,1207,1283,1355,1425,1503,1572", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,75,71,69,77,68,120", "endOffsets": "200,287,385,485,572,651,757,850,945,1029,1117,1202,1278,1350,1420,1498,1567,1688"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,539,540,541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4517,4617,4940,5038,5138,5846,5925,17114,17207,17382,17466,17800,17967,18043,18115,56731,56809,56878", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,75,71,69,77,68,120", "endOffsets": "4612,4699,5033,5133,5220,5920,6026,17202,17297,17461,17549,17880,18038,18110,18180,56804,56873,56994"}}]}]}