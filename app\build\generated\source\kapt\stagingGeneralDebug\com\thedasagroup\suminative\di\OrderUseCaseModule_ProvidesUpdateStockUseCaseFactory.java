package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.ProductRepository;
import com.thedasagroup.suminative.data.repo.StockRepository;
import com.thedasagroup.suminative.domain.categories.SyncCategoriesUseCase;
import com.thedasagroup.suminative.ui.stock.ChangeStockUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class OrderUseCaseModule_ProvidesUpdateStockUseCaseFactory implements Factory<ChangeStockUseCase> {
  private final Provider<StockRepository> stockRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  private final Provider<ProductRepository> productRepositoryProvider;

  private final Provider<SyncCategoriesUseCase> syncCategoriesUseCaseProvider;

  public OrderUseCaseModule_ProvidesUpdateStockUseCaseFactory(
      Provider<StockRepository> stockRepositoryProvider, Provider<Prefs> prefsProvider,
      Provider<ProductRepository> productRepositoryProvider,
      Provider<SyncCategoriesUseCase> syncCategoriesUseCaseProvider) {
    this.stockRepositoryProvider = stockRepositoryProvider;
    this.prefsProvider = prefsProvider;
    this.productRepositoryProvider = productRepositoryProvider;
    this.syncCategoriesUseCaseProvider = syncCategoriesUseCaseProvider;
  }

  @Override
  public ChangeStockUseCase get() {
    return providesUpdateStockUseCase(stockRepositoryProvider.get(), prefsProvider.get(), productRepositoryProvider.get(), syncCategoriesUseCaseProvider.get());
  }

  public static OrderUseCaseModule_ProvidesUpdateStockUseCaseFactory create(
      Provider<StockRepository> stockRepositoryProvider, Provider<Prefs> prefsProvider,
      Provider<ProductRepository> productRepositoryProvider,
      Provider<SyncCategoriesUseCase> syncCategoriesUseCaseProvider) {
    return new OrderUseCaseModule_ProvidesUpdateStockUseCaseFactory(stockRepositoryProvider, prefsProvider, productRepositoryProvider, syncCategoriesUseCaseProvider);
  }

  public static ChangeStockUseCase providesUpdateStockUseCase(StockRepository stockRepository,
      Prefs prefs, ProductRepository productRepository,
      SyncCategoriesUseCase syncCategoriesUseCase) {
    return Preconditions.checkNotNullFromProvides(OrderUseCaseModule.INSTANCE.providesUpdateStockUseCase(stockRepository, prefs, productRepository, syncCategoriesUseCase));
  }
}
