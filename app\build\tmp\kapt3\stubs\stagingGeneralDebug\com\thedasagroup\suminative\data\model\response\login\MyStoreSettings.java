package com.thedasagroup.suminative.data.model.response.login;

@kotlinx.serialization.Serializable()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0087\b\u0018\u0000 )2\u00020\u0001:\u0002()B[\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\u0010\b\u0002\u0010\t\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\f\u0018\u00010\u0003\u00a2\u0006\u0004\b\r\u0010\u000eJ\u0011\u0010\u001b\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u001c\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001d\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u0011\u0010\u001e\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u001f\u001a\n\u0012\u0004\u0012\u00020\f\u0018\u00010\u0003H\u00c6\u0003J]\u0010 \u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\u0010\b\u0002\u0010\t\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\u00032\u0010\b\u0002\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\f\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010!\u001a\u00020\"2\b\u0010#\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010$\u001a\u00020%H\u00d6\u0001J\t\u0010&\u001a\u00020\'H\u00d6\u0001R$\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u000f\u0010\u0010\u001a\u0004\b\u0011\u0010\u0012R$\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0013\u0010\u0010\u001a\u0004\b\u0014\u0010\u0012R\u001e\u0010\u0007\u001a\u0004\u0018\u00010\b8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0015\u0010\u0010\u001a\u0004\b\u0016\u0010\u0017R$\u0010\t\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0018\u0010\u0010\u001a\u0004\b\u0019\u0010\u0012R\u0019\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\f\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0012\u00a8\u0006*"}, d2 = {"Lcom/thedasagroup/suminative/data/model/response/login/MyStoreSettings;", "", "categories", "", "Lcom/thedasagroup/suminative/data/model/response/login/Category;", "storeItems", "Lcom/thedasagroup/suminative/data/model/response/login/StoreItem;", "storeSettings", "Lcom/thedasagroup/suminative/data/model/response/login/StoreSettings;", "stores", "Lcom/thedasagroup/suminative/data/model/response/login/Store;", "users", "Lcom/thedasagroup/suminative/data/model/response/login/User;", "<init>", "(Ljava/util/List;Ljava/util/List;Lcom/thedasagroup/suminative/data/model/response/login/StoreSettings;Ljava/util/List;Ljava/util/List;)V", "getCategories$annotations", "()V", "getCategories", "()Ljava/util/List;", "getStoreItems$annotations", "getStoreItems", "getStoreSettings$annotations", "getStoreSettings", "()Lcom/thedasagroup/suminative/data/model/response/login/StoreSettings;", "getStores$annotations", "getStores", "getUsers", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "", "$serializer", "Companion", "app_stagingGeneralDebug"})
public final class MyStoreSettings {
    @org.jetbrains.annotations.Nullable()
    private final java.util.List<com.thedasagroup.suminative.data.model.response.login.Category> categories = null;
    @org.jetbrains.annotations.Nullable()
    private final java.util.List<com.thedasagroup.suminative.data.model.response.login.StoreItem> storeItems = null;
    @org.jetbrains.annotations.Nullable()
    private final com.thedasagroup.suminative.data.model.response.login.StoreSettings storeSettings = null;
    @org.jetbrains.annotations.Nullable()
    private final java.util.List<com.thedasagroup.suminative.data.model.response.login.Store> stores = null;
    @org.jetbrains.annotations.Nullable()
    private final java.util.List<com.thedasagroup.suminative.data.model.response.login.User> users = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.data.model.response.login.MyStoreSettings.Companion Companion = null;
    
    public MyStoreSettings(@org.jetbrains.annotations.Nullable()
    java.util.List<com.thedasagroup.suminative.data.model.response.login.Category> categories, @org.jetbrains.annotations.Nullable()
    java.util.List<com.thedasagroup.suminative.data.model.response.login.StoreItem> storeItems, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.login.StoreSettings storeSettings, @org.jetbrains.annotations.Nullable()
    java.util.List<com.thedasagroup.suminative.data.model.response.login.Store> stores, @org.jetbrains.annotations.Nullable()
    java.util.List<com.thedasagroup.suminative.data.model.response.login.User> users) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.thedasagroup.suminative.data.model.response.login.Category> getCategories() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "categories")
    @java.lang.Deprecated()
    public static void getCategories$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.thedasagroup.suminative.data.model.response.login.StoreItem> getStoreItems() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "storeItems")
    @java.lang.Deprecated()
    public static void getStoreItems$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.response.login.StoreSettings getStoreSettings() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "storeSettings")
    @java.lang.Deprecated()
    public static void getStoreSettings$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.thedasagroup.suminative.data.model.response.login.Store> getStores() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "stores")
    @java.lang.Deprecated()
    public static void getStores$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.thedasagroup.suminative.data.model.response.login.User> getUsers() {
        return null;
    }
    
    public MyStoreSettings() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.thedasagroup.suminative.data.model.response.login.Category> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.thedasagroup.suminative.data.model.response.login.StoreItem> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.response.login.StoreSettings component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.thedasagroup.suminative.data.model.response.login.Store> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.thedasagroup.suminative.data.model.response.login.User> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.model.response.login.MyStoreSettings copy(@org.jetbrains.annotations.Nullable()
    java.util.List<com.thedasagroup.suminative.data.model.response.login.Category> categories, @org.jetbrains.annotations.Nullable()
    java.util.List<com.thedasagroup.suminative.data.model.response.login.StoreItem> storeItems, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.login.StoreSettings storeSettings, @org.jetbrains.annotations.Nullable()
    java.util.List<com.thedasagroup.suminative.data.model.response.login.Store> stores, @org.jetbrains.annotations.Nullable()
    java.util.List<com.thedasagroup.suminative.data.model.response.login.User> users) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0003\u0010\u0004J\u0015\u0010\u0005\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00070\u0006\u00a2\u0006\u0002\u0010\bJ\u000e\u0010\t\u001a\u00020\u00022\u0006\u0010\n\u001a\u00020\u000bJ\u0016\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0002R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014\u00a8\u0006\u0015"}, d2 = {"com/thedasagroup/suminative/data/model/response/login/MyStoreSettings.$serializer", "Lkotlinx/serialization/internal/GeneratedSerializer;", "Lcom/thedasagroup/suminative/data/model/response/login/MyStoreSettings;", "<init>", "()V", "childSerializers", "", "Lkotlinx/serialization/KSerializer;", "()[Lkotlinx/serialization/KSerializer;", "deserialize", "decoder", "Lkotlinx/serialization/encoding/Decoder;", "serialize", "", "encoder", "Lkotlinx/serialization/encoding/Encoder;", "value", "descriptor", "Lkotlinx/serialization/descriptors/SerialDescriptor;", "getDescriptor", "()Lkotlinx/serialization/descriptors/SerialDescriptor;", "app_stagingGeneralDebug"})
    @java.lang.Deprecated()
    public static final class $serializer implements kotlinx.serialization.internal.GeneratedSerializer<com.thedasagroup.suminative.data.model.response.login.MyStoreSettings> {
        @org.jetbrains.annotations.NotNull()
        public static final com.thedasagroup.suminative.data.model.response.login.MyStoreSettings.$serializer INSTANCE = null;
        @org.jetbrains.annotations.NotNull()
        private static final kotlinx.serialization.descriptors.SerialDescriptor descriptor = null;
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<?>[] childSerializers() {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final com.thedasagroup.suminative.data.model.response.login.MyStoreSettings deserialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Decoder decoder) {
            return null;
        }
        
        @java.lang.Override()
        public final void serialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Encoder encoder, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.data.model.response.login.MyStoreSettings value) {
        }
        
        private $serializer() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.descriptors.SerialDescriptor getDescriptor() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a8\u0006\u0007"}, d2 = {"Lcom/thedasagroup/suminative/data/model/response/login/MyStoreSettings$Companion;", "", "<init>", "()V", "serializer", "Lkotlinx/serialization/KSerializer;", "Lcom/thedasagroup/suminative/data/model/response/login/MyStoreSettings;", "app_stagingGeneralDebug"})
    public static final class Companion {
        
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<com.thedasagroup.suminative.data.model.response.login.MyStoreSettings> serializer() {
            return null;
        }
        
        private Companion() {
            super();
        }
    }
}