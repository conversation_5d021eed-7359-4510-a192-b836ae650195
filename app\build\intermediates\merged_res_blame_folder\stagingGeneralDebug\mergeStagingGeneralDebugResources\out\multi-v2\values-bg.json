{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeStagingGeneralDebugResources-106:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "55900,55988", "endColumns": "87,94", "endOffsets": "55983,56078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3537,3634,3744,3846,3947,4054,4159,18250", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "3629,3739,3841,3942,4049,4154,4273,18346"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,432,551,648,744,857,987,1108,1255,1339,1438,1534,1630,1743,1872,1976,2119,2262,2407,2595,2735,2862,2992,3126,3223,3320,3457,3592,3695,3800,3905,4050,4200,4308,4411,4498,4590,4685,4782,4872,4981,5061,5144,5244,5346,5442,5540,5628,5735,5835,5939,6058,6138,6248", "endColumns": "118,120,136,118,96,95,112,129,120,146,83,98,95,95,112,128,103,142,142,144,187,139,126,129,133,96,96,136,134,102,104,104,144,149,107,102,86,91,94,96,89,108,79,82,99,101,95,97,87,106,99,103,118,79,109,96", "endOffsets": "169,290,427,546,643,739,852,982,1103,1250,1334,1433,1529,1625,1738,1867,1971,2114,2257,2402,2590,2730,2857,2987,3121,3218,3315,3452,3587,3690,3795,3900,4045,4195,4303,4406,4493,4585,4680,4777,4867,4976,5056,5139,5239,5341,5437,5535,5623,5730,5830,5934,6053,6133,6243,6340"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6195,6314,6435,6572,6691,6788,6884,6997,7127,7248,7395,7479,7578,7674,7770,7883,8012,8116,8259,8402,8547,8735,8875,9002,9132,9266,9363,9460,9597,9732,9835,9940,10045,10190,10340,10448,10551,10638,10730,10825,10922,11012,11121,11201,11284,11384,11486,11582,11680,11768,11875,11975,12079,12198,12278,12388", "endColumns": "118,120,136,118,96,95,112,129,120,146,83,98,95,95,112,128,103,142,142,144,187,139,126,129,133,96,96,136,134,102,104,104,144,149,107,102,86,91,94,96,89,108,79,82,99,101,95,97,87,106,99,103,118,79,109,96", "endOffsets": "6309,6430,6567,6686,6783,6879,6992,7122,7243,7390,7474,7573,7669,7765,7878,8007,8111,8254,8397,8542,8730,8870,8997,9127,9261,9358,9455,9592,9727,9830,9935,10040,10185,10335,10443,10546,10633,10725,10820,10917,11007,11116,11196,11279,11379,11481,11577,11675,11763,11870,11970,12074,12193,12273,12383,12480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,274,386", "endColumns": "110,107,111,109", "endOffsets": "161,269,381,491"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4929,5556,5664,5776", "endColumns": "110,107,111,109", "endOffsets": "5035,5659,5771,5881"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,226,366,442,529,656,777,960,1109,1236,1337,1473,1534,1593,1685,1781,1855,2022,2126,2216,2379,2454,2549,2655,2795,2899,3006,3106,3178,3252,3376,3462,3660,3829,3936,4094,4203,4392,4502,4646,4752,4895,4997,5126,5233,5331,5475,5570,5713,5810,5972,6060,6199,6276,6396,6493,6571,6664,6737,6808,6885,6956,7083,7181,7330,7426,7550,7645,7787,7894,7992,8114,8206,8322,8428,8543,8638,8698,8750,8806,8859,8909,8964,9037,9090,9154,9205,9249,9319,9366,9419,9474,9527,9578,9631,9684,9756,9808,9860,9905,9966,10026,10087,10232,10343,10425,10486,10570,10639,10707,10822,10892,10967,11047,11125,11192,11291,11367,11453,11537,11660,11738,11830,11914,12008,12098,12176,12265,12372,12487,12587,12663,12757,12825,12903,13033,13188,13257,13334,13423,13515,15566,15681,15891,15938,16071,16238,16324,16456,16562,16703,16878,17001,17179,17250,17335,17401,17504,17621,17666,17753,17874,17965,18087,18209,18347,18451,18560,18625,18684,18743,18800,18865,18947,19039,19156,19226,19299,19568,19771,19901,20021,20154,20241,20345,20473,20617,20766,20857,20959,21049,21166,21274,21429,21523,21593,21650,21892,21975,22052,22108,22163,22271,22382,22485,22645,22832,22903,22966,23027,23187,23263,23361,23440,23536,23712,23804,23873,23973,24052,24199,24337,24393,24509,24579,24650,24712,24785,24833,24896,24981,25033,25114,25286,25395,25492,25554,25706,25838,25964,26084,26346,26446,26544,26643,26813,27005,27091,27267,27395,27561,27667,27748,27838,27912,28050,28221,28362,28414,28497,28629,28738,28819,28901,29009,29089,29161,29235,29296,29371,29441,29517,29619,29684,29751,29830,29898,29992,30163,30311,30409,30466,30546,30691,30750,30829,30910,30984,31060,31298,31353,31433,31515,31591,31685,31865,31954,32032,32118,32199,32267,32338,32428,32565,32647,32706,32794,32871,32946,33027,33137,33253,33361,33492,33565,33613,33662,33752,33849,33930,34043,34223,34325,34404,34525,34653,34788,34900,35001,35155,35282,35457,35625,35742,35873,35964,36051,36161,36285,36382,36515,36591,37002,37089,37175,37255", "endColumns": "170,139,75,86,126,120,182,148,126,100,135,60,58,91,95,73,166,103,89,162,74,94,105,139,103,106,99,71,73,123,85,197,168,106,157,108,188,109,143,105,142,101,128,106,97,143,94,142,96,161,87,138,76,119,96,77,92,72,70,76,70,126,97,148,95,123,94,141,106,97,121,91,115,105,114,94,59,51,55,52,49,54,72,52,63,50,43,69,46,52,54,52,50,52,52,71,51,51,44,60,59,60,144,110,81,60,83,68,67,114,69,74,79,77,66,98,75,85,83,122,77,91,83,93,89,77,88,106,114,99,75,93,67,77,129,154,68,76,88,91,2050,114,209,46,132,166,85,131,105,140,174,122,177,70,84,65,102,116,44,86,120,90,121,121,137,103,108,64,58,58,56,64,81,91,116,69,72,268,202,129,119,132,86,103,127,143,148,90,101,89,116,107,154,93,69,56,241,82,76,55,54,107,110,102,159,186,70,62,60,159,75,97,78,95,175,91,68,99,78,146,137,55,115,69,70,61,72,47,62,84,51,80,171,108,96,61,151,131,125,119,261,99,97,98,169,191,85,175,127,165,105,80,89,73,137,170,140,51,82,131,108,80,81,107,79,71,73,60,74,69,75,101,64,66,78,67,93,170,147,97,56,79,144,58,78,80,73,75,237,54,79,81,75,93,179,88,77,85,80,67,70,89,136,81,58,87,76,74,80,109,115,107,130,72,47,48,89,96,80,112,179,101,78,120,127,134,111,100,153,126,174,167,116,130,90,86,109,123,96,132,75,410,86,85,79,75", "endOffsets": "221,361,437,524,651,772,955,1104,1231,1332,1468,1529,1588,1680,1776,1850,2017,2121,2211,2374,2449,2544,2650,2790,2894,3001,3101,3173,3247,3371,3457,3655,3824,3931,4089,4198,4387,4497,4641,4747,4890,4992,5121,5228,5326,5470,5565,5708,5805,5967,6055,6194,6271,6391,6488,6566,6659,6732,6803,6880,6951,7078,7176,7325,7421,7545,7640,7782,7889,7987,8109,8201,8317,8423,8538,8633,8693,8745,8801,8854,8904,8959,9032,9085,9149,9200,9244,9314,9361,9414,9469,9522,9573,9626,9679,9751,9803,9855,9900,9961,10021,10082,10227,10338,10420,10481,10565,10634,10702,10817,10887,10962,11042,11120,11187,11286,11362,11448,11532,11655,11733,11825,11909,12003,12093,12171,12260,12367,12482,12582,12658,12752,12820,12898,13028,13183,13252,13329,13418,13510,15561,15676,15886,15933,16066,16233,16319,16451,16557,16698,16873,16996,17174,17245,17330,17396,17499,17616,17661,17748,17869,17960,18082,18204,18342,18446,18555,18620,18679,18738,18795,18860,18942,19034,19151,19221,19294,19563,19766,19896,20016,20149,20236,20340,20468,20612,20761,20852,20954,21044,21161,21269,21424,21518,21588,21645,21887,21970,22047,22103,22158,22266,22377,22480,22640,22827,22898,22961,23022,23182,23258,23356,23435,23531,23707,23799,23868,23968,24047,24194,24332,24388,24504,24574,24645,24707,24780,24828,24891,24976,25028,25109,25281,25390,25487,25549,25701,25833,25959,26079,26341,26441,26539,26638,26808,27000,27086,27262,27390,27556,27662,27743,27833,27907,28045,28216,28357,28409,28492,28624,28733,28814,28896,29004,29084,29156,29230,29291,29366,29436,29512,29614,29679,29746,29825,29893,29987,30158,30306,30404,30461,30541,30686,30745,30824,30905,30979,31055,31293,31348,31428,31510,31586,31680,31860,31949,32027,32113,32194,32262,32333,32423,32560,32642,32701,32789,32866,32941,33022,33132,33248,33356,33487,33560,33608,33657,33747,33844,33925,34038,34218,34320,34399,34520,34648,34783,34895,34996,35150,35277,35452,35620,35737,35868,35959,36046,36156,36280,36377,36510,36586,36997,37084,37170,37250,37326"}, "to": {"startLines": "193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18351,18522,18662,18738,18825,18952,19073,19256,19405,19532,19633,19769,19830,19889,19981,20077,20151,20318,20422,20512,20675,20750,20845,20951,21091,21195,21302,21402,21474,21548,21672,21758,21956,22125,22232,22390,22499,22688,22798,22942,23048,23191,23293,23422,23529,23627,23771,23866,24009,24106,24268,24356,24495,24572,24692,24789,24867,24960,25033,25104,25181,25252,25379,25477,25626,25722,25846,25941,26083,26190,26288,26410,26502,26618,26724,26839,26934,26994,27046,27102,27155,27205,27260,27333,27386,27450,27501,27545,27615,27662,27715,27770,27823,27874,27927,27980,28052,28104,28156,28201,28262,28322,28383,28528,28639,28721,28782,28866,28935,29003,29118,29188,29263,29343,29421,29488,29587,29663,29749,29833,29956,30034,30126,30210,30304,30394,30472,30561,30668,30783,30883,30959,31053,31121,31199,31329,31484,31553,31630,31719,31811,33862,33977,34187,34234,34367,34534,34620,34752,34858,34999,35174,35297,35475,35546,35631,35697,35800,35917,35962,36049,36170,36261,36383,36505,36643,36747,36856,36921,36980,37039,37096,37161,37243,37335,37452,37522,37595,37864,38067,38197,38317,38450,38537,38641,38769,38913,39062,39153,39255,39345,39462,39570,39725,39819,39889,39946,40188,40271,40348,40404,40459,40567,40678,40781,40941,41128,41199,41262,41323,41483,41559,41657,41736,41832,42008,42100,42169,42269,42348,42495,42633,42689,42805,42875,42946,43008,43081,43129,43192,43277,43329,43410,43582,43691,43788,43850,44002,44134,44260,44380,44642,44742,44840,44939,45109,45301,45387,45563,45691,45857,45963,46044,46134,46208,46346,46517,46658,46710,46793,46925,47034,47115,47197,47305,47385,47457,47531,47592,47667,47737,47813,47915,47980,48047,48126,48194,48288,48459,48607,48705,48762,48842,48987,49046,49125,49206,49280,49356,49594,49649,49729,49811,49887,49981,50161,50250,50328,50414,50495,50563,50634,50724,50861,50943,51002,51090,51167,51242,51323,51433,51549,51657,51788,51861,51909,51958,52048,52145,52226,52339,52519,52621,52700,52821,52949,53084,53196,53297,53451,53578,53753,53921,54038,54169,54260,54347,54457,54581,54678,54811,54887,55298,55385,55471,55551", "endColumns": "170,139,75,86,126,120,182,148,126,100,135,60,58,91,95,73,166,103,89,162,74,94,105,139,103,106,99,71,73,123,85,197,168,106,157,108,188,109,143,105,142,101,128,106,97,143,94,142,96,161,87,138,76,119,96,77,92,72,70,76,70,126,97,148,95,123,94,141,106,97,121,91,115,105,114,94,59,51,55,52,49,54,72,52,63,50,43,69,46,52,54,52,50,52,52,71,51,51,44,60,59,60,144,110,81,60,83,68,67,114,69,74,79,77,66,98,75,85,83,122,77,91,83,93,89,77,88,106,114,99,75,93,67,77,129,154,68,76,88,91,2050,114,209,46,132,166,85,131,105,140,174,122,177,70,84,65,102,116,44,86,120,90,121,121,137,103,108,64,58,58,56,64,81,91,116,69,72,268,202,129,119,132,86,103,127,143,148,90,101,89,116,107,154,93,69,56,241,82,76,55,54,107,110,102,159,186,70,62,60,159,75,97,78,95,175,91,68,99,78,146,137,55,115,69,70,61,72,47,62,84,51,80,171,108,96,61,151,131,125,119,261,99,97,98,169,191,85,175,127,165,105,80,89,73,137,170,140,51,82,131,108,80,81,107,79,71,73,60,74,69,75,101,64,66,78,67,93,170,147,97,56,79,144,58,78,80,73,75,237,54,79,81,75,93,179,88,77,85,80,67,70,89,136,81,58,87,76,74,80,109,115,107,130,72,47,48,89,96,80,112,179,101,78,120,127,134,111,100,153,126,174,167,116,130,90,86,109,123,96,132,75,410,86,85,79,75", "endOffsets": "18517,18657,18733,18820,18947,19068,19251,19400,19527,19628,19764,19825,19884,19976,20072,20146,20313,20417,20507,20670,20745,20840,20946,21086,21190,21297,21397,21469,21543,21667,21753,21951,22120,22227,22385,22494,22683,22793,22937,23043,23186,23288,23417,23524,23622,23766,23861,24004,24101,24263,24351,24490,24567,24687,24784,24862,24955,25028,25099,25176,25247,25374,25472,25621,25717,25841,25936,26078,26185,26283,26405,26497,26613,26719,26834,26929,26989,27041,27097,27150,27200,27255,27328,27381,27445,27496,27540,27610,27657,27710,27765,27818,27869,27922,27975,28047,28099,28151,28196,28257,28317,28378,28523,28634,28716,28777,28861,28930,28998,29113,29183,29258,29338,29416,29483,29582,29658,29744,29828,29951,30029,30121,30205,30299,30389,30467,30556,30663,30778,30878,30954,31048,31116,31194,31324,31479,31548,31625,31714,31806,33857,33972,34182,34229,34362,34529,34615,34747,34853,34994,35169,35292,35470,35541,35626,35692,35795,35912,35957,36044,36165,36256,36378,36500,36638,36742,36851,36916,36975,37034,37091,37156,37238,37330,37447,37517,37590,37859,38062,38192,38312,38445,38532,38636,38764,38908,39057,39148,39250,39340,39457,39565,39720,39814,39884,39941,40183,40266,40343,40399,40454,40562,40673,40776,40936,41123,41194,41257,41318,41478,41554,41652,41731,41827,42003,42095,42164,42264,42343,42490,42628,42684,42800,42870,42941,43003,43076,43124,43187,43272,43324,43405,43577,43686,43783,43845,43997,44129,44255,44375,44637,44737,44835,44934,45104,45296,45382,45558,45686,45852,45958,46039,46129,46203,46341,46512,46653,46705,46788,46920,47029,47110,47192,47300,47380,47452,47526,47587,47662,47732,47808,47910,47975,48042,48121,48189,48283,48454,48602,48700,48757,48837,48982,49041,49120,49201,49275,49351,49589,49644,49724,49806,49882,49976,50156,50245,50323,50409,50490,50558,50629,50719,50856,50938,50997,51085,51162,51237,51318,51428,51544,51652,51783,51856,51904,51953,52043,52140,52221,52334,52514,52616,52695,52816,52944,53079,53191,53292,53446,53573,53748,53916,54033,54164,54255,54342,54452,54576,54673,54806,54882,55293,55380,55466,55546,55622"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,435,541,646,732,842,963,1043,1120,1211,1304,1399,1493,1593,1686,1781,1889,1980,2071,2154,2268,2376,2476,2590,2697,2805,2965,17617", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "430,536,641,727,837,958,1038,1115,1206,1299,1394,1488,1588,1681,1776,1884,1975,2066,2149,2263,2371,2471,2585,2692,2800,2960,3059,17696"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-bg\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4791", "endColumns": "137", "endOffsets": "4924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,301,404,507,591,667,758,849,933,1017,1105,1177,1254,1332,1408,1491,1560", "endColumns": "102,92,102,102,83,75,90,90,83,83,87,71,76,77,75,82,68,120", "endOffsets": "203,296,399,502,586,662,753,844,928,1012,1100,1172,1249,1327,1403,1486,1555,1676"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,539,540,541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4595,4698,5040,5143,5246,5966,6042,17184,17275,17445,17529,17868,18019,18096,18174,55627,55710,55779", "endColumns": "102,92,102,102,83,75,90,90,83,83,87,71,76,77,75,82,68,120", "endOffsets": "4693,4786,5138,5241,5325,6037,6128,17270,17354,17524,17612,17935,18091,18169,18245,55705,55774,55895"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,423,506,628,738,833,966,1055,1118,1184,1281,1361,1423,1512,1575,1640,1699,1772,1835,1889,2017,2074,2136,2190,2263,2406,2490,2568,2661,2743,2831,2967,3055,3143,3279,3364,3441,3494,3545,3611,3686,3762,3833,3912,3989,4065,4142,4216,4328,4419,4494,4585,4677,4751,4838,4929,4984,5066,5132,5215,5301,5363,5427,5490,5560,5677,5789,5900,6010,6067,6122,6208,6299,6375", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,78,82,121,109,94,132,88,62,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,77,92,81,87,135,87,87,135,84,76,52,50,65,74,75,70,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,111,110,109,56,54,85,90,75,78", "endOffsets": "260,339,418,501,623,733,828,961,1050,1113,1179,1276,1356,1418,1507,1570,1635,1694,1767,1830,1884,2012,2069,2131,2185,2258,2401,2485,2563,2656,2738,2826,2962,3050,3138,3274,3359,3436,3489,3540,3606,3681,3757,3828,3907,3984,4060,4137,4211,4323,4414,4489,4580,4672,4746,4833,4924,4979,5061,5127,5210,5296,5358,5422,5485,5555,5672,5784,5895,6005,6062,6117,6203,6294,6370,6449"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3064,3143,3222,3305,3427,4278,4373,4506,5330,5393,5459,5886,6133,12485,12574,12637,12702,12761,12834,12897,12951,13079,13136,13198,13252,13325,13468,13552,13630,13723,13805,13893,14029,14117,14205,14341,14426,14503,14556,14607,14673,14748,14824,14895,14974,15051,15127,15204,15278,15390,15481,15556,15647,15739,15813,15900,15991,16046,16128,16194,16277,16363,16425,16489,16552,16622,16739,16851,16962,17072,17129,17359,17701,17792,17940", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,78,78,82,121,109,94,132,88,62,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,77,92,81,87,135,87,87,135,84,76,52,50,65,74,75,70,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,111,110,109,56,54,85,90,75,78", "endOffsets": "310,3138,3217,3300,3422,3532,4368,4501,4590,5388,5454,5551,5961,6190,12569,12632,12697,12756,12829,12892,12946,13074,13131,13193,13247,13320,13463,13547,13625,13718,13800,13888,14024,14112,14200,14336,14421,14498,14551,14602,14668,14743,14819,14890,14969,15046,15122,15199,15273,15385,15476,15551,15642,15734,15808,15895,15986,16041,16123,16189,16272,16358,16420,16484,16547,16617,16734,16846,16957,17067,17124,17179,17440,17787,17863,18014"}}]}]}