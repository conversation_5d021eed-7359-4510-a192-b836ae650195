package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.database.DatabaseManager;
import com.thedasagroup.suminative.data.database.LocalOrderRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RepoModule_ProvidesOrderLocalRepositoryFactory implements Factory<LocalOrderRepository> {
  private final Provider<DatabaseManager> databaseManagerProvider;

  public RepoModule_ProvidesOrderLocalRepositoryFactory(
      Provider<DatabaseManager> databaseManagerProvider) {
    this.databaseManagerProvider = databaseManagerProvider;
  }

  @Override
  public LocalOrderRepository get() {
    return providesOrderLocalRepository(databaseManagerProvider.get());
  }

  public static RepoModule_ProvidesOrderLocalRepositoryFactory create(
      Provider<DatabaseManager> databaseManagerProvider) {
    return new RepoModule_ProvidesOrderLocalRepositoryFactory(databaseManagerProvider);
  }

  public static LocalOrderRepository providesOrderLocalRepository(DatabaseManager databaseManager) {
    return Preconditions.checkNotNullFromProvides(RepoModule.INSTANCE.providesOrderLocalRepository(databaseManager));
  }
}
