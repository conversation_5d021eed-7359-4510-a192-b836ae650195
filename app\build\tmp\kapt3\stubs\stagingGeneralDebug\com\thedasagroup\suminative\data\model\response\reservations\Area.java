package com.thedasagroup.suminative.data.model.response.reservations;

/**
 * Area data model matching the areas API response structure
 * Endpoint: /api/reservations/areas?storeId=158
 */
@kotlinx.serialization.Serializable()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0014\n\u0002\u0010\u000b\n\u0002\b\u0006\b\u0087\b\u0018\u0000  2\u00020\u0001:\u0002\u001f B+\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\b\u0010\tJ\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0006H\u00c6\u0003J\u000b\u0010\u0018\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J3\u0010\u0019\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001J\u0013\u0010\u001a\u001a\u00020\u001b2\b\u0010\u001c\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001d\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u001e\u001a\u00020\u0006H\u00d6\u0001R\u001c\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\n\u0010\u000b\u001a\u0004\b\f\u0010\rR\u001c\u0010\u0004\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u000e\u0010\u000b\u001a\u0004\b\u000f\u0010\rR\u001c\u0010\u0005\u001a\u00020\u00068\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0010\u0010\u000b\u001a\u0004\b\u0011\u0010\u0012R\u001e\u0010\u0007\u001a\u0004\u0018\u00010\u00068\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0013\u0010\u000b\u001a\u0004\b\u0014\u0010\u0012\u00a8\u0006!"}, d2 = {"Lcom/thedasagroup/suminative/data/model/response/reservations/Area;", "", "id", "", "storeId", "description", "", "areaDetailsJson", "<init>", "(IILjava/lang/String;Ljava/lang/String;)V", "getId$annotations", "()V", "getId", "()I", "getStoreId$annotations", "getStoreId", "getDescription$annotations", "getDescription", "()Ljava/lang/String;", "getAreaDetailsJson$annotations", "getAreaDetailsJson", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "toString", "$serializer", "Companion", "app_stagingGeneralDebug"})
public final class Area {
    private final int id = 0;
    private final int storeId = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String description = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String areaDetailsJson = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.data.model.response.reservations.Area.Companion Companion = null;
    
    public Area(int id, int storeId, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    java.lang.String areaDetailsJson) {
        super();
    }
    
    public final int getId() {
        return 0;
    }
    
    @kotlinx.serialization.SerialName(value = "id")
    @java.lang.Deprecated()
    public static void getId$annotations() {
    }
    
    public final int getStoreId() {
        return 0;
    }
    
    @kotlinx.serialization.SerialName(value = "storeId")
    @java.lang.Deprecated()
    public static void getStoreId$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescription() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "description")
    @java.lang.Deprecated()
    public static void getDescription$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getAreaDetailsJson() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "areaDetailsJson")
    @java.lang.Deprecated()
    public static void getAreaDetailsJson$annotations() {
    }
    
    public final int component1() {
        return 0;
    }
    
    public final int component2() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.model.response.reservations.Area copy(int id, int storeId, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    java.lang.String areaDetailsJson) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    /**
     * Area data model matching the areas API response structure
     * Endpoint: /api/reservations/areas?storeId=158
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0003\u0010\u0004J\u0015\u0010\u0005\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00070\u0006\u00a2\u0006\u0002\u0010\bJ\u000e\u0010\t\u001a\u00020\u00022\u0006\u0010\n\u001a\u00020\u000bJ\u0016\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0002R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014\u00a8\u0006\u0015"}, d2 = {"com/thedasagroup/suminative/data/model/response/reservations/Area.$serializer", "Lkotlinx/serialization/internal/GeneratedSerializer;", "Lcom/thedasagroup/suminative/data/model/response/reservations/Area;", "<init>", "()V", "childSerializers", "", "Lkotlinx/serialization/KSerializer;", "()[Lkotlinx/serialization/KSerializer;", "deserialize", "decoder", "Lkotlinx/serialization/encoding/Decoder;", "serialize", "", "encoder", "Lkotlinx/serialization/encoding/Encoder;", "value", "descriptor", "Lkotlinx/serialization/descriptors/SerialDescriptor;", "getDescriptor", "()Lkotlinx/serialization/descriptors/SerialDescriptor;", "app_stagingGeneralDebug"})
    @java.lang.Deprecated()
    public static final class $serializer implements kotlinx.serialization.internal.GeneratedSerializer<com.thedasagroup.suminative.data.model.response.reservations.Area> {
        @org.jetbrains.annotations.NotNull()
        public static final com.thedasagroup.suminative.data.model.response.reservations.Area.$serializer INSTANCE = null;
        @org.jetbrains.annotations.NotNull()
        private static final kotlinx.serialization.descriptors.SerialDescriptor descriptor = null;
        
        /**
         * Area data model matching the areas API response structure
         * Endpoint: /api/reservations/areas?storeId=158
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<?>[] childSerializers() {
            return null;
        }
        
        /**
         * Area data model matching the areas API response structure
         * Endpoint: /api/reservations/areas?storeId=158
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final com.thedasagroup.suminative.data.model.response.reservations.Area deserialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Decoder decoder) {
            return null;
        }
        
        /**
         * Area data model matching the areas API response structure
         * Endpoint: /api/reservations/areas?storeId=158
         */
        @java.lang.Override()
        public final void serialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Encoder encoder, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.data.model.response.reservations.Area value) {
        }
        
        private $serializer() {
            super();
        }
        
        /**
         * Area data model matching the areas API response structure
         * Endpoint: /api/reservations/areas?storeId=158
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.descriptors.SerialDescriptor getDescriptor() {
            return null;
        }
    }
    
    /**
     * Area data model matching the areas API response structure
     * Endpoint: /api/reservations/areas?storeId=158
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a8\u0006\u0007"}, d2 = {"Lcom/thedasagroup/suminative/data/model/response/reservations/Area$Companion;", "", "<init>", "()V", "serializer", "Lkotlinx/serialization/KSerializer;", "Lcom/thedasagroup/suminative/data/model/response/reservations/Area;", "app_stagingGeneralDebug"})
    public static final class Companion {
        
        /**
         * Area data model matching the areas API response structure
         * Endpoint: /api/reservations/areas?storeId=158
         */
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<com.thedasagroup.suminative.data.model.response.reservations.Area> serializer() {
            return null;
        }
        
        private Companion() {
            super();
        }
    }
}