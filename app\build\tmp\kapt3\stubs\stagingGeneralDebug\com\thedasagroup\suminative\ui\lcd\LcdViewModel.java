package com.thedasagroup.suminative.ui.lcd;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0006\u0010\u0004\u001a\u00020\u0005J\u000e\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\bJ\u0006\u0010\t\u001a\u00020\u0005J\u000e\u0010\n\u001a\u00020\u00052\u0006\u0010\u000b\u001a\u00020\fJ\u000e\u0010\r\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\b\u00a8\u0006\u000e"}, d2 = {"Lcom/thedasagroup/suminative/ui/lcd/LcdViewModel;", "Landroidx/lifecycle/ViewModel;", "<init>", "()V", "lcdCtrl", "", "lcdLine", "price", "", "lcdLines", "lcdLogo", "view", "Landroid/view/View;", "lcdDigital", "app_stagingGeneralDebug"})
public final class LcdViewModel extends androidx.lifecycle.ViewModel {
    
    public LcdViewModel() {
        super();
    }
    
    /**
     * 初始化并清屏操作
     * 其他操作可参考文档中Command命令
     * 注意在不支持客显的设备上调用接口将返回异常
     */
    public final void lcdCtrl() {
    }
    
    /**
     * 显示单行内容(T1mini\T2mini)
     * 可根据具体情况设置字体大小
     * 可根据显示情况决定是否拉伸字体到屏幕高度
     */
    public final void lcdLine(@org.jetbrains.annotations.NotNull()
    java.lang.String price) {
    }
    
    /**
     * 显示多行内容(T1mini\T2mini)
     * 多行内容展示字体大小固定由所在行比例控制
     */
    public final void lcdLines() {
    }
    
    /**
     * 显示位图(T1mini\T2mini)
     * 位图大小需要限制在128*40像素内
     */
    public final void lcdLogo(@org.jetbrains.annotations.NotNull()
    android.view.View view) {
    }
    
    /**
     * 显示价格（D3mini)
     * showDigital可以显示7位的数字，并可在任意数字后插入位数分割符
     */
    public final void lcdDigital(@org.jetbrains.annotations.NotNull()
    java.lang.String price) {
    }
}