{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeStagingGeneralDebugResources-106:/values-pt/values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,11146", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,11227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,411,527,629,726,840,974,1092,1244,1328,1429,1524,1624,1739,1869,1975,2114,2250,2381,2547,2674,2794,2918,3038,3134,3231,3351,3467,3567,3678,3787,3927,4072,4182,4285,4371,4465,4557,4647,4736,4837,4917,5001,5102,5208,5300,5399,5487,5599,5700,5804,5923,6003,6103", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "169,290,406,522,624,721,835,969,1087,1239,1323,1424,1519,1619,1734,1864,1970,2109,2245,2376,2542,2669,2789,2913,3033,3129,3226,3346,3462,3562,3673,3782,3922,4067,4177,4280,4366,4460,4552,4642,4731,4832,4912,4996,5097,5203,5295,5394,5482,5594,5695,5799,5918,5998,6098,6190"}, "to": {"startLines": "47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4653,4772,4893,5009,5125,5227,5324,5438,5572,5690,5842,5926,6027,6122,6222,6337,6467,6573,6712,6848,6979,7145,7272,7392,7516,7636,7732,7829,7949,8065,8165,8276,8385,8525,8670,8780,8883,8969,9063,9155,9245,9334,9435,9515,9599,9700,9806,9898,9997,10085,10197,10298,10402,10521,10601,10701", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "4767,4888,5004,5120,5222,5319,5433,5567,5685,5837,5921,6022,6117,6217,6332,6462,6568,6707,6843,6974,7140,7267,7387,7511,7631,7727,7824,7944,8060,8160,8271,8380,8520,8665,8775,8878,8964,9058,9150,9240,9329,9430,9510,9594,9695,9801,9893,9992,10080,10192,10293,10397,10516,10596,10696,10788"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,242,381,457,538,654,780,966,1117,1242,1346,1487,1550,1611,1701,1793,1869,2020,2130,2213,2364,2441,2528,2621,2770,2874,2980,3077,3148,3220,3349,3449,3644,3833,3939,4105,4219,4386,4489,4624,4725,4854,4956,5093,5196,5297,5437,5529,5666,5763,5939,6026,6159,6239,6355,6454,6531,6612,6683,6755,6837,6908,7041,7149,7292,7386,7496,7590,7740,7843,7944,8066,8159,8269,8385,8506,8607,8688,8742,8794,8847,8900,8950,9016,9071,9138,9190,9235,9306,9355,9411,9467,9524,9585,9637,9687,9762,9811,9863,9909,9973,10034,10098,10256,10357,10439,10500,10575,10644,10712,10831,10913,10988,11068,11146,11213,11310,11385,11477,11562,11703,11782,11874,11962,12061,12149,12230,12342,12448,12567,12676,12753,12848,12917,13002,13139,13280,13353,13430,13531,13623,15674,15789,16010,16055,16186,16357,16451,16566,16671,16808,16964,17084,17286,17356,17437,17507,17596,17701,17746,17831,17960,18057,18183,18315,18460,18567,18666,18732,18788,18845,18901,18968,19050,19144,19282,19351,19431,19688,19924,20055,20184,20319,20412,20528,20647,20798,20971,21072,21179,21266,21383,21494,21648,21739,21818,21874,22103,22195,22289,22355,22420,22541,22662,22761,22929,23118,23195,23265,23333,23503,23581,23668,23747,23843,24011,24107,24186,24315,24393,24557,24678,24731,24882,24951,25031,25089,25165,25216,25282,25371,25423,25482,25665,25791,25891,25955,26107,26255,26365,26476,26766,26867,26964,27060,27221,27468,27553,27755,27879,28028,28141,28223,28316,28402,28535,28703,28855,28904,28987,29119,29244,29332,29414,29521,29604,29681,29756,29819,29900,29971,30050,30132,30199,30271,30357,30423,30510,30692,30849,30936,30995,31080,31230,31301,31381,31464,31545,31627,31893,31948,32023,32108,32178,32267,32450,32541,32619,32703,32783,32854,32938,33024,33160,33241,33294,33372,33449,33515,33599,33709,33812,33925,34044,34124,34172,34226,34318,34429,34513,34621,34778,34895,34975,35107,35240,35376,35486,35584,35731,35873,36068,36249,36366,36508,36596,36681,36784,36915,37023,37159,37230,37634,37724,37810,37894", "endColumns": "186,138,75,80,115,125,185,150,124,103,140,62,60,89,91,75,150,109,82,150,76,86,92,148,103,105,96,70,71,128,99,194,188,105,165,113,166,102,134,100,128,101,136,102,100,139,91,136,96,175,86,132,79,115,98,76,80,70,71,81,70,132,107,142,93,109,93,149,102,100,121,92,109,115,120,100,80,53,51,52,52,49,65,54,66,51,44,70,48,55,55,56,60,51,49,74,48,51,45,63,60,63,157,100,81,60,74,68,67,118,81,74,79,77,66,96,74,91,84,140,78,91,87,98,87,80,111,105,118,108,76,94,68,84,136,140,72,76,100,91,2050,114,220,44,130,170,93,114,104,136,155,119,201,69,80,69,88,104,44,84,128,96,125,131,144,106,98,65,55,56,55,66,81,93,137,68,79,256,235,130,128,134,92,115,118,150,172,100,106,86,116,110,153,90,78,55,228,91,93,65,64,120,120,98,167,188,76,69,67,169,77,86,78,95,167,95,78,128,77,163,120,52,150,68,79,57,75,50,65,88,51,58,182,125,99,63,151,147,109,110,289,100,96,95,160,246,84,201,123,148,112,81,92,85,132,167,151,48,82,131,124,87,81,106,82,76,74,62,80,70,78,81,66,71,85,65,86,181,156,86,58,84,149,70,79,82,80,81,265,54,74,84,69,88,182,90,77,83,79,70,83,85,135,80,52,77,76,65,83,109,102,112,118,79,47,53,91,110,83,107,156,116,79,131,132,135,109,97,146,141,194,180,116,141,87,84,102,130,107,135,70,403,89,85,83,74", "endOffsets": "237,376,452,533,649,775,961,1112,1237,1341,1482,1545,1606,1696,1788,1864,2015,2125,2208,2359,2436,2523,2616,2765,2869,2975,3072,3143,3215,3344,3444,3639,3828,3934,4100,4214,4381,4484,4619,4720,4849,4951,5088,5191,5292,5432,5524,5661,5758,5934,6021,6154,6234,6350,6449,6526,6607,6678,6750,6832,6903,7036,7144,7287,7381,7491,7585,7735,7838,7939,8061,8154,8264,8380,8501,8602,8683,8737,8789,8842,8895,8945,9011,9066,9133,9185,9230,9301,9350,9406,9462,9519,9580,9632,9682,9757,9806,9858,9904,9968,10029,10093,10251,10352,10434,10495,10570,10639,10707,10826,10908,10983,11063,11141,11208,11305,11380,11472,11557,11698,11777,11869,11957,12056,12144,12225,12337,12443,12562,12671,12748,12843,12912,12997,13134,13275,13348,13425,13526,13618,15669,15784,16005,16050,16181,16352,16446,16561,16666,16803,16959,17079,17281,17351,17432,17502,17591,17696,17741,17826,17955,18052,18178,18310,18455,18562,18661,18727,18783,18840,18896,18963,19045,19139,19277,19346,19426,19683,19919,20050,20179,20314,20407,20523,20642,20793,20966,21067,21174,21261,21378,21489,21643,21734,21813,21869,22098,22190,22284,22350,22415,22536,22657,22756,22924,23113,23190,23260,23328,23498,23576,23663,23742,23838,24006,24102,24181,24310,24388,24552,24673,24726,24877,24946,25026,25084,25160,25211,25277,25366,25418,25477,25660,25786,25886,25950,26102,26250,26360,26471,26761,26862,26959,27055,27216,27463,27548,27750,27874,28023,28136,28218,28311,28397,28530,28698,28850,28899,28982,29114,29239,29327,29409,29516,29599,29676,29751,29814,29895,29966,30045,30127,30194,30266,30352,30418,30505,30687,30844,30931,30990,31075,31225,31296,31376,31459,31540,31622,31888,31943,32018,32103,32173,32262,32445,32536,32614,32698,32778,32849,32933,33019,33155,33236,33289,33367,33444,33510,33594,33704,33807,33920,34039,34119,34167,34221,34313,34424,34508,34616,34773,34890,34970,35102,35235,35371,35481,35579,35726,35868,36063,36244,36361,36503,36591,36676,36779,36910,37018,37154,37225,37629,37719,37805,37889,37964"}, "to": {"startLines": "113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11639,11826,11965,12041,12122,12238,12364,12550,12701,12826,12930,13071,13134,13195,13285,13377,13453,13604,13714,13797,13948,14025,14112,14205,14354,14458,14564,14661,14732,14804,14933,15033,15228,15417,15523,15689,15803,15970,16073,16208,16309,16438,16540,16677,16780,16881,17021,17113,17250,17347,17523,17610,17743,17823,17939,18038,18115,18196,18267,18339,18421,18492,18625,18733,18876,18970,19080,19174,19324,19427,19528,19650,19743,19853,19969,20090,20191,20272,20326,20378,20431,20484,20534,20600,20655,20722,20774,20819,20890,20939,20995,21051,21108,21169,21221,21271,21346,21395,21447,21493,21557,21618,21682,21840,21941,22023,22084,22159,22228,22296,22415,22497,22572,22652,22730,22797,22894,22969,23061,23146,23287,23366,23458,23546,23645,23733,23814,23926,24032,24151,24260,24337,24432,24501,24586,24723,24864,24937,25014,25115,25207,27258,27373,27594,27639,27770,27941,28035,28150,28255,28392,28548,28668,28870,28940,29021,29091,29180,29285,29330,29415,29544,29641,29767,29899,30044,30151,30250,30316,30372,30429,30485,30552,30634,30728,30866,30935,31015,31272,31508,31639,31768,31903,31996,32112,32231,32382,32555,32656,32763,32850,32967,33078,33232,33323,33402,33458,33687,33779,33873,33939,34004,34125,34246,34345,34513,34702,34779,34849,34917,35087,35165,35252,35331,35427,35595,35691,35770,35899,35977,36141,36262,36315,36466,36535,36615,36673,36749,36800,36866,36955,37007,37066,37249,37375,37475,37539,37691,37839,37949,38060,38350,38451,38548,38644,38805,39052,39137,39339,39463,39612,39725,39807,39900,39986,40119,40287,40439,40488,40571,40703,40828,40916,40998,41105,41188,41265,41340,41403,41484,41555,41634,41716,41783,41855,41941,42007,42094,42276,42433,42520,42579,42664,42814,42885,42965,43048,43129,43211,43477,43532,43607,43692,43762,43851,44034,44125,44203,44287,44367,44438,44522,44608,44744,44825,44878,44956,45033,45099,45183,45293,45396,45509,45628,45708,45756,45810,45902,46013,46097,46205,46362,46479,46559,46691,46824,46960,47070,47168,47315,47457,47652,47833,47950,48092,48180,48265,48368,48499,48607,48743,48814,49218,49308,49394,49478", "endColumns": "186,138,75,80,115,125,185,150,124,103,140,62,60,89,91,75,150,109,82,150,76,86,92,148,103,105,96,70,71,128,99,194,188,105,165,113,166,102,134,100,128,101,136,102,100,139,91,136,96,175,86,132,79,115,98,76,80,70,71,81,70,132,107,142,93,109,93,149,102,100,121,92,109,115,120,100,80,53,51,52,52,49,65,54,66,51,44,70,48,55,55,56,60,51,49,74,48,51,45,63,60,63,157,100,81,60,74,68,67,118,81,74,79,77,66,96,74,91,84,140,78,91,87,98,87,80,111,105,118,108,76,94,68,84,136,140,72,76,100,91,2050,114,220,44,130,170,93,114,104,136,155,119,201,69,80,69,88,104,44,84,128,96,125,131,144,106,98,65,55,56,55,66,81,93,137,68,79,256,235,130,128,134,92,115,118,150,172,100,106,86,116,110,153,90,78,55,228,91,93,65,64,120,120,98,167,188,76,69,67,169,77,86,78,95,167,95,78,128,77,163,120,52,150,68,79,57,75,50,65,88,51,58,182,125,99,63,151,147,109,110,289,100,96,95,160,246,84,201,123,148,112,81,92,85,132,167,151,48,82,131,124,87,81,106,82,76,74,62,80,70,78,81,66,71,85,65,86,181,156,86,58,84,149,70,79,82,80,81,265,54,74,84,69,88,182,90,77,83,79,70,83,85,135,80,52,77,76,65,83,109,102,112,118,79,47,53,91,110,83,107,156,116,79,131,132,135,109,97,146,141,194,180,116,141,87,84,102,130,107,135,70,403,89,85,83,74", "endOffsets": "11821,11960,12036,12117,12233,12359,12545,12696,12821,12925,13066,13129,13190,13280,13372,13448,13599,13709,13792,13943,14020,14107,14200,14349,14453,14559,14656,14727,14799,14928,15028,15223,15412,15518,15684,15798,15965,16068,16203,16304,16433,16535,16672,16775,16876,17016,17108,17245,17342,17518,17605,17738,17818,17934,18033,18110,18191,18262,18334,18416,18487,18620,18728,18871,18965,19075,19169,19319,19422,19523,19645,19738,19848,19964,20085,20186,20267,20321,20373,20426,20479,20529,20595,20650,20717,20769,20814,20885,20934,20990,21046,21103,21164,21216,21266,21341,21390,21442,21488,21552,21613,21677,21835,21936,22018,22079,22154,22223,22291,22410,22492,22567,22647,22725,22792,22889,22964,23056,23141,23282,23361,23453,23541,23640,23728,23809,23921,24027,24146,24255,24332,24427,24496,24581,24718,24859,24932,25009,25110,25202,27253,27368,27589,27634,27765,27936,28030,28145,28250,28387,28543,28663,28865,28935,29016,29086,29175,29280,29325,29410,29539,29636,29762,29894,30039,30146,30245,30311,30367,30424,30480,30547,30629,30723,30861,30930,31010,31267,31503,31634,31763,31898,31991,32107,32226,32377,32550,32651,32758,32845,32962,33073,33227,33318,33397,33453,33682,33774,33868,33934,33999,34120,34241,34340,34508,34697,34774,34844,34912,35082,35160,35247,35326,35422,35590,35686,35765,35894,35972,36136,36257,36310,36461,36530,36610,36668,36744,36795,36861,36950,37002,37061,37244,37370,37470,37534,37686,37834,37944,38055,38345,38446,38543,38639,38800,39047,39132,39334,39458,39607,39720,39802,39895,39981,40114,40282,40434,40483,40566,40698,40823,40911,40993,41100,41183,41260,41335,41398,41479,41550,41629,41711,41778,41850,41936,42002,42089,42271,42428,42515,42574,42659,42809,42880,42960,43043,43124,43206,43472,43527,43602,43687,43757,43846,44029,44120,44198,44282,44362,44433,44517,44603,44739,44820,44873,44951,45028,45094,45178,45288,45391,45504,45623,45703,45751,45805,45897,46008,46092,46200,46357,46474,46554,46686,46819,46955,47065,47163,47310,47452,47647,47828,47945,48087,48175,48260,48363,48494,48602,48738,48809,49213,49303,49389,49473,49548"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,1011,1101,1177,1253,1332,1407,1483,1550", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,75,78,74,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,1006,1096,1172,1248,1327,1402,1478,1545,1658"}, "to": {"startLines": "36,37,39,40,41,45,46,103,104,105,106,108,109,110,111,459,460,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3578,3673,3874,3971,4070,4473,4556,10793,10884,10971,11056,11232,11308,11384,11463,49553,49629,49696", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,75,78,74,75,66,112", "endOffsets": "3668,3754,3966,4065,4151,4551,4648,10879,10966,11051,11141,11303,11379,11458,11533,49624,49691,49804"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "38,42,43,44", "startColumns": "4,4,4,4", "startOffsets": "3759,4156,4255,4367", "endColumns": "114,98,111,105", "endOffsets": "3869,4250,4362,4468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "29,30,31,32,33,34,35,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2843,2940,3042,3141,3241,3348,3458,11538", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "2935,3037,3136,3236,3343,3453,3573,11634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "462,463", "startColumns": "4,4", "startOffsets": "49809,49892", "endColumns": "82,84", "endOffsets": "49887,49972"}}]}]}