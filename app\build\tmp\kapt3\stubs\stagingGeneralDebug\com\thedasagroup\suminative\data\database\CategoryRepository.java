package com.thedasagroup.suminative.data.database;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\f\b\u0007\u0018\u00002\u00020\u0001B\u0011\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u0016\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010\u000eJ\u0016\u0010\u000f\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010\u000eJ\u001a\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00130\u00120\u00112\u0006\u0010\u0014\u001a\u00020\u0015J\u001c\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00130\u00122\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0017J\u001a\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00130\u00120\u00112\u0006\u0010\u0014\u001a\u00020\u0015J\u001c\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00130\u00122\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0017J \u0010\u001a\u001a\u0004\u0018\u00010\u00132\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u001dJ\u0018\u0010\u001e\u001a\u0004\u0018\u00010\u00132\u0006\u0010\u001f\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0017J\u001e\u0010 \u001a\u00020\u000b2\u0006\u0010\u001f\u001a\u00020\u00152\u0006\u0010!\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\"J\u001e\u0010#\u001a\u00020\u000b2\u0006\u0010\u001f\u001a\u00020\u00152\u0006\u0010$\u001a\u00020%H\u0086@\u00a2\u0006\u0002\u0010&J \u0010\'\u001a\u0004\u0018\u00010\u00152\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u001dJ0\u0010(\u001a\u00020\u000b2\u0018\u0010)\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u00150*0\u00122\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010+J$\u0010,\u001a\u00020\u000b2\f\u0010)\u001a\b\u0012\u0004\u0012\u00020\u001c0\u00122\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010+J\u0016\u0010-\u001a\u00020\u000b2\u0006\u0010\u001f\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0017J\u0016\u0010.\u001a\u00020\u000b2\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0017J\u0016\u0010/\u001a\u00020\u00152\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0017J\u0014\u00100\u001a\b\u0012\u0004\u0012\u00020\u00130\u0012H\u0086@\u00a2\u0006\u0002\u00101J\u0016\u00102\u001a\u00020\u000b2\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0017J\u0016\u00103\u001a\u00020\u000b2\u0006\u0010\u001f\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0017J\u001c\u00104\u001a\b\u0012\u0004\u0012\u00020\u001c0\u00122\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0017J\u0016\u00105\u001a\u00020%2\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0017R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00066"}, d2 = {"Lcom/thedasagroup/suminative/data/database/CategoryRepository;", "", "databaseManager", "Lcom/thedasagroup/suminative/data/database/DatabaseManager;", "<init>", "(Lcom/thedasagroup/suminative/data/database/DatabaseManager;)V", "database", "Lcom/thedasagroup/suminative/database/POSDatabase;", "categoryQueries", "Lcom/thedasagroup/suminative/database/CategoryQueries;", "insertCategory", "", "category", "Lcom/thedasagroup/suminative/data/database/LocalCategory;", "(Lcom/thedasagroup/suminative/data/database/LocalCategory;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertOrReplaceCategory", "getCategoriesByStoreFlow", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/thedasagroup/suminative/database/CategoryEntity;", "storeId", "", "getCategoriesByStore", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllCategoriesByStoreFlow", "getAllCategoriesByStore", "getCategoryByNameAndStore", "name", "", "(Ljava/lang/String;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCategoryById", "id", "updateCategorySortOrder", "sortOrder", "(JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateCategoryActiveStatus", "isActive", "", "(JZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCategorySortOrderByName", "updateCategoriesSortOrder", "categories", "Lkotlin/Pair;", "(Ljava/util/List;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "syncCategoriesFromApi", "deleteCategoryById", "deleteCategoriesByStore", "getCategoriesCountByStore", "getUnsyncedCategories", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "markCategoriesSynced", "updateCategorySyncedAt", "getCategoryNamesSorted", "hasCategoriesForStore", "app_stagingGeneralDebug"})
public final class CategoryRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.database.DatabaseManager databaseManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.database.POSDatabase database = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.database.CategoryQueries categoryQueries = null;
    
    @javax.inject.Inject()
    public CategoryRepository(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.database.DatabaseManager databaseManager) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertCategory(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.database.LocalCategory category, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertOrReplaceCategory(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.database.LocalCategory category, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.thedasagroup.suminative.database.CategoryEntity>> getCategoriesByStoreFlow(long storeId) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCategoriesByStore(long storeId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.thedasagroup.suminative.database.CategoryEntity>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.thedasagroup.suminative.database.CategoryEntity>> getAllCategoriesByStoreFlow(long storeId) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAllCategoriesByStore(long storeId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.thedasagroup.suminative.database.CategoryEntity>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCategoryByNameAndStore(@org.jetbrains.annotations.NotNull()
    java.lang.String name, long storeId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.thedasagroup.suminative.database.CategoryEntity> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCategoryById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.thedasagroup.suminative.database.CategoryEntity> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateCategorySortOrder(long id, long sortOrder, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateCategoryActiveStatus(long id, boolean isActive, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCategorySortOrderByName(@org.jetbrains.annotations.NotNull()
    java.lang.String name, long storeId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateCategoriesSortOrder(@org.jetbrains.annotations.NotNull()
    java.util.List<kotlin.Pair<java.lang.String, java.lang.Long>> categories, long storeId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object syncCategoriesFromApi(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> categories, long storeId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteCategoryById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteCategoriesByStore(long storeId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCategoriesCountByStore(long storeId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getUnsyncedCategories(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.thedasagroup.suminative.database.CategoryEntity>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object markCategoriesSynced(long storeId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateCategorySyncedAt(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCategoryNamesSorted(long storeId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object hasCategoriesForStore(long storeId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
}