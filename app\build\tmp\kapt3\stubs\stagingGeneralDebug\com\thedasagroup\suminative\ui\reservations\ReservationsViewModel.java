package com.thedasagroup.suminative.ui.reservations;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\b\u0018\u0000 +2\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0002*+BK\b\u0007\u0012\b\b\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0006\u0010\u000e\u001a\u00020\u000f\u0012\u0006\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\u0004\b\u0012\u0010\u0013J\u0006\u0010\u0014\u001a\u00020\u0015J\u0006\u0010\u0016\u001a\u00020\u0015J\u000e\u0010\u0017\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\u0019J\u0016\u0010\u001a\u001a\u00020\u00152\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u0018\u001a\u00020\u001dJ\u000e\u0010\u001e\u001a\u00020\u00152\u0006\u0010\u001b\u001a\u00020\u001cJ\u000e\u0010\u001f\u001a\u00020\u00152\u0006\u0010 \u001a\u00020\u001cJ\u0006\u0010!\u001a\u00020\u0015J\u000e\u0010\"\u001a\u00020\u00152\u0006\u0010#\u001a\u00020$J\u0006\u0010%\u001a\u00020\u0015J\u0006\u0010&\u001a\u00020\u0015J\u000e\u0010\'\u001a\u00020\u00152\u0006\u0010(\u001a\u00020\u001cJ\u0006\u0010)\u001a\u00020\u0015R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006,"}, d2 = {"Lcom/thedasagroup/suminative/ui/reservations/ReservationsViewModel;", "Lcom/airbnb/mvrx/MavericksViewModel;", "Lcom/thedasagroup/suminative/ui/reservations/ReservationsState;", "state", "getActiveReservationsUseCase", "Lcom/thedasagroup/suminative/ui/reservations/GetActiveReservationsUseCase;", "getAllReservationsUseCase", "Lcom/thedasagroup/suminative/ui/reservations/GetAllReservationsUseCase;", "createReservationUseCase", "Lcom/thedasagroup/suminative/ui/reservations/CreateReservationUseCase;", "editReservationUseCase", "Lcom/thedasagroup/suminative/ui/reservations/EditReservationUseCase;", "cancelReservationUseCase", "Lcom/thedasagroup/suminative/ui/reservations/CancelReservationUseCase;", "getReservationAreasUseCase", "Lcom/thedasagroup/suminative/ui/reservations/GetReservationAreasUseCase;", "getReservationTablesUseCase", "Lcom/thedasagroup/suminative/ui/reservations/GetReservationTablesUseCase;", "<init>", "(Lcom/thedasagroup/suminative/ui/reservations/ReservationsState;Lcom/thedasagroup/suminative/ui/reservations/GetActiveReservationsUseCase;Lcom/thedasagroup/suminative/ui/reservations/GetAllReservationsUseCase;Lcom/thedasagroup/suminative/ui/reservations/CreateReservationUseCase;Lcom/thedasagroup/suminative/ui/reservations/EditReservationUseCase;Lcom/thedasagroup/suminative/ui/reservations/CancelReservationUseCase;Lcom/thedasagroup/suminative/ui/reservations/GetReservationAreasUseCase;Lcom/thedasagroup/suminative/ui/reservations/GetReservationTablesUseCase;)V", "loadActiveReservations", "", "loadAllReservations", "createReservation", "request", "Lcom/thedasagroup/suminative/data/model/request/reservations/CreateReservationRequest;", "editReservation", "reservationId", "", "Lcom/thedasagroup/suminative/data/model/request/reservations/EditReservationRequest;", "cancelReservation", "setSelectedTab", "index", "refresh", "showEditDialog", "reservation", "Lcom/thedasagroup/suminative/data/model/response/reservations/Reservation;", "hideEditDialog", "loadReservationAreas", "loadReservationTables", "areaId", "clearAreaSelection", "Factory", "Companion", "app_stagingGeneralDebug"})
public final class ReservationsViewModel extends com.airbnb.mvrx.MavericksViewModel<com.thedasagroup.suminative.ui.reservations.ReservationsState> {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.reservations.GetActiveReservationsUseCase getActiveReservationsUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.reservations.GetAllReservationsUseCase getAllReservationsUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.reservations.CreateReservationUseCase createReservationUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.reservations.EditReservationUseCase editReservationUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.reservations.CancelReservationUseCase cancelReservationUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.reservations.GetReservationAreasUseCase getReservationAreasUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.reservations.GetReservationTablesUseCase getReservationTablesUseCase = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.reservations.ReservationsViewModel.Companion Companion = null;
    
    @dagger.assisted.AssistedInject()
    public ReservationsViewModel(@dagger.assisted.Assisted()
    @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.reservations.ReservationsState state, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.reservations.GetActiveReservationsUseCase getActiveReservationsUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.reservations.GetAllReservationsUseCase getAllReservationsUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.reservations.CreateReservationUseCase createReservationUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.reservations.EditReservationUseCase editReservationUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.reservations.CancelReservationUseCase cancelReservationUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.reservations.GetReservationAreasUseCase getReservationAreasUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.reservations.GetReservationTablesUseCase getReservationTablesUseCase) {
        super(null, null);
    }
    
    public final void loadActiveReservations() {
    }
    
    public final void loadAllReservations() {
    }
    
    public final void createReservation(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.reservations.CreateReservationRequest request) {
    }
    
    public final void editReservation(int reservationId, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.reservations.EditReservationRequest request) {
    }
    
    public final void cancelReservation(int reservationId) {
    }
    
    public final void setSelectedTab(int index) {
    }
    
    public final void refresh() {
    }
    
    public final void showEditDialog(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.reservations.Reservation reservation) {
    }
    
    public final void hideEditDialog() {
    }
    
    /**
     * Load reservation areas for the current store
     */
    public final void loadReservationAreas() {
    }
    
    /**
     * Load tables for a specific area
     * @param areaId The area ID to load tables for
     */
    public final void loadReservationTables(int areaId) {
    }
    
    /**
     * Clear selected area and tables
     */
    public final void clearAreaSelection() {
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u001b\u0010\u0006\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0003H\u0096\u0001J\u0013\u0010\n\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0007\u001a\u00020\bH\u0096\u0001\u00a8\u0006\u000b"}, d2 = {"Lcom/thedasagroup/suminative/ui/reservations/ReservationsViewModel$Companion;", "Lcom/airbnb/mvrx/MavericksViewModelFactory;", "Lcom/thedasagroup/suminative/ui/reservations/ReservationsViewModel;", "Lcom/thedasagroup/suminative/ui/reservations/ReservationsState;", "<init>", "()V", "create", "viewModelContext", "Lcom/airbnb/mvrx/ViewModelContext;", "state", "initialState", "app_stagingGeneralDebug"})
    public static final class Companion implements com.airbnb.mvrx.MavericksViewModelFactory<com.thedasagroup.suminative.ui.reservations.ReservationsViewModel, com.thedasagroup.suminative.ui.reservations.ReservationsState> {
        
        private Companion() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.reservations.ReservationsViewModel create(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.reservations.ReservationsState state) {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.reservations.ReservationsState initialState(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext) {
            return null;
        }
    }
    
    @dagger.assisted.AssistedFactory()
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\bg\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001J\u0010\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0003H&\u00a8\u0006\u0006"}, d2 = {"Lcom/thedasagroup/suminative/ui/reservations/ReservationsViewModel$Factory;", "Lcom/airbnb/mvrx/hilt/AssistedViewModelFactory;", "Lcom/thedasagroup/suminative/ui/reservations/ReservationsViewModel;", "Lcom/thedasagroup/suminative/ui/reservations/ReservationsState;", "create", "state", "app_stagingGeneralDebug"})
    public static abstract interface Factory extends com.airbnb.mvrx.hilt.AssistedViewModelFactory<com.thedasagroup.suminative.ui.reservations.ReservationsViewModel, com.thedasagroup.suminative.ui.reservations.ReservationsState> {
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public abstract com.thedasagroup.suminative.ui.reservations.ReservationsViewModel create(@org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.reservations.ReservationsState state);
    }
}