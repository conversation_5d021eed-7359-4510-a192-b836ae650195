package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.thedasagroup.suminative.App",
    rootPackage = "com.thedasagroup.suminative",
    originatingRoot = "com.thedasagroup.suminative.App",
    originatingRootPackage = "com.thedasagroup.suminative",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "App",
    originatingRootSimpleNames = "App"
)
public class _com_thedasagroup_suminative_App {
}
