package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.repo.ClockInOutRepository;
import com.thedasagroup.suminative.ui.login.ClockInUserTimeUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesClockInUserTimeUseCaseFactory implements Factory<ClockInUserTimeUseCase> {
  private final Provider<ClockInOutRepository> clockInOutRepositoryProvider;

  public AppUseCaseModule_ProvidesClockInUserTimeUseCaseFactory(
      Provider<ClockInOutRepository> clockInOutRepositoryProvider) {
    this.clockInOutRepositoryProvider = clockInOutRepositoryProvider;
  }

  @Override
  public ClockInUserTimeUseCase get() {
    return providesClockInUserTimeUseCase(clockInOutRepositoryProvider.get());
  }

  public static AppUseCaseModule_ProvidesClockInUserTimeUseCaseFactory create(
      Provider<ClockInOutRepository> clockInOutRepositoryProvider) {
    return new AppUseCaseModule_ProvidesClockInUserTimeUseCaseFactory(clockInOutRepositoryProvider);
  }

  public static ClockInUserTimeUseCase providesClockInUserTimeUseCase(
      ClockInOutRepository clockInOutRepository) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesClockInUserTimeUseCase(clockInOutRepository));
  }
}
