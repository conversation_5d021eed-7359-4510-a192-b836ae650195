package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.database.CategoryRepository;
import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.OptionRepository;
import com.thedasagroup.suminative.data.repo.ProductRepository;
import com.thedasagroup.suminative.data.repo.StockRepository;
import com.thedasagroup.suminative.ui.products.DownloadProductsUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvideDownloadProductsUseCaseFactory implements Factory<DownloadProductsUseCase> {
  private final Provider<StockRepository> stockRepositoryProvider;

  private final Provider<ProductRepository> productRepositoryProvider;

  private final Provider<OptionRepository> optionRepositoryProvider;

  private final Provider<CategoryRepository> categoryRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  public AppUseCaseModule_ProvideDownloadProductsUseCaseFactory(
      Provider<StockRepository> stockRepositoryProvider,
      Provider<ProductRepository> productRepositoryProvider,
      Provider<OptionRepository> optionRepositoryProvider,
      Provider<CategoryRepository> categoryRepositoryProvider, Provider<Prefs> prefsProvider) {
    this.stockRepositoryProvider = stockRepositoryProvider;
    this.productRepositoryProvider = productRepositoryProvider;
    this.optionRepositoryProvider = optionRepositoryProvider;
    this.categoryRepositoryProvider = categoryRepositoryProvider;
    this.prefsProvider = prefsProvider;
  }

  @Override
  public DownloadProductsUseCase get() {
    return provideDownloadProductsUseCase(stockRepositoryProvider.get(), productRepositoryProvider.get(), optionRepositoryProvider.get(), categoryRepositoryProvider.get(), prefsProvider.get());
  }

  public static AppUseCaseModule_ProvideDownloadProductsUseCaseFactory create(
      Provider<StockRepository> stockRepositoryProvider,
      Provider<ProductRepository> productRepositoryProvider,
      Provider<OptionRepository> optionRepositoryProvider,
      Provider<CategoryRepository> categoryRepositoryProvider, Provider<Prefs> prefsProvider) {
    return new AppUseCaseModule_ProvideDownloadProductsUseCaseFactory(stockRepositoryProvider, productRepositoryProvider, optionRepositoryProvider, categoryRepositoryProvider, prefsProvider);
  }

  public static DownloadProductsUseCase provideDownloadProductsUseCase(
      StockRepository stockRepository, ProductRepository productRepository,
      OptionRepository optionRepository, CategoryRepository categoryRepository, Prefs prefs) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.provideDownloadProductsUseCase(stockRepository, productRepository, optionRepository, categoryRepository, prefs));
  }
}
