package com.thedasagroup.suminative.data.repo;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\"\u0010\u0004\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00060\u00052\u0006\u0010\b\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\nJ\"\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u00060\u00052\u0006\u0010\b\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010\u000eJ\"\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u00060\u00052\u0006\u0010\b\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010\u000eJ\"\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u00060\u00052\u0006\u0010\b\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010\u000eJ\"\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00120\u00060\u00052\u0006\u0010\b\u001a\u00020\u0013H\u0086@\u00a2\u0006\u0002\u0010\u0014J*\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160\u00060\u00052\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001aH\u0086@\u00a2\u0006\u0002\u0010\u001bJ\"\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160\u00060\u00052\u0006\u0010\u0017\u001a\u00020\u0018H\u0086@\u00a2\u0006\u0002\u0010\u001dJ\"\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001f0\u00060\u00052\u0006\u0010\b\u001a\u00020 H\u0086@\u00a2\u0006\u0002\u0010!\u00a8\u0006\""}, d2 = {"Lcom/thedasagroup/suminative/data/repo/OrdersRepository;", "Lcom/thedasagroup/suminative/data/repo/BaseRepository;", "<init>", "()V", "getOrders", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/store_orders/OrdersResponse;", "request", "Lcom/thedasagroup/suminative/data/model/request/login/OrderRequest;", "(Lcom/thedasagroup/suminative/data/model/request/login/OrderRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPagedPendingOrders", "Lcom/thedasagroup/suminative/data/model/request/pagination/OrderResponse;", "Lcom/thedasagroup/suminative/data/model/request/pagination/GetPagedOrderRequest;", "(Lcom/thedasagroup/suminative/data/model/request/pagination/GetPagedOrderRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPagedAllOrders", "getPagedScheduleOrders", "changeStatus", "Lcom/thedasagroup/suminative/data/model/response/change_status/ChangeStatusResponse;", "Lcom/thedasagroup/suminative/data/model/request/change_status/ChangeStatusRequest;", "(Lcom/thedasagroup/suminative/data/model/request/change_status/ChangeStatusRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "closeOpenStore", "Lcom/thedasagroup/suminative/data/model/response/close_open_store/CloseOpenStoreResponse;", "storeId", "", "closed", "", "(Ljava/lang/String;ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isClosed", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addNotification", "Lcom/thedasagroup/suminative/data/model/response/notification/NotificationResponse;", "Lcom/thedasagroup/suminative/data/model/request/notification/NotificationRequest;", "(Lcom/thedasagroup/suminative/data/model/request/notification/NotificationRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_stagingGeneralDebug"})
public final class OrdersRepository extends com.thedasagroup.suminative.data.repo.BaseRepository {
    
    public OrdersRepository() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getOrders(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.login.OrderRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.store_orders.OrdersResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getPagedPendingOrders(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.pagination.GetPagedOrderRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getPagedAllOrders(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.pagination.GetPagedOrderRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getPagedScheduleOrders(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.pagination.GetPagedOrderRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.pagination.OrderResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object changeStatus(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.change_status.ChangeStatusRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.change_status.ChangeStatusResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object closeOpenStore(@org.jetbrains.annotations.NotNull()
    java.lang.String storeId, boolean closed, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.close_open_store.CloseOpenStoreResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object isClosed(@org.jetbrains.annotations.NotNull()
    java.lang.String storeId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.close_open_store.CloseOpenStoreResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object addNotification(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.notification.NotificationRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.notification.NotificationResponse>>> $completion) {
        return null;
    }
}