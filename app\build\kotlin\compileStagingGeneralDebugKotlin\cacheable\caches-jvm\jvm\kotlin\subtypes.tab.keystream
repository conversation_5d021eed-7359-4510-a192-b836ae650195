"app.cash.sqldelight.TransacterImplapp.cash.sqldelight.Queryapp.cash.sqldelight.Transacter0com.thedasagroup.suminative.database.POSDatabase app.cash.sqldelight.db.SqlSchemaandroid.app.Application2android.app.Application.ActivityLifecycleCallbacks$androidx.work.Configuration.Provider2kotlinx.serialization.internal.GeneratedSerializerandroid.os.Parcelable4com.thedasagroup.suminative.data.repo.BaseRepository4com.thedasagroup.suminative.domain.orders.SyncResult(androidx.appcompat.app.AppCompatActivitycom.airbnb.mvrx.MavericksView"com.airbnb.mvrx.MavericksViewModel-com.airbnb.mvrx.hilt.AssistedViewModelFactory)com.airbnb.mvrx.MavericksViewModelFactorycom.airbnb.mvrx.MavericksStatekotlin.Enumandroidx.lifecycle.ViewModelBcom.thedasagroup.suminative.ui.orders.GetPendingOrdersPagedUseCase"androidx.compose.ui.graphics.Shape$androidx.fragment.app.DialogFragment-com.airbnb.mvrx.mocking.MockableMavericksView#androidx.activity.ComponentActivityandroidx.fragment.app.Fragmentandroid.app.Serviceandroid.app.job.JobServiceandroidx.work.Worker!android.content.BroadcastReceiver)org.java_websocket.client.WebSocketClientandroid.media.SoundPool+com.thedasagroup.suminative.work.SyncStatusandroidx.work.CoroutineWorker1com.thedasagroup.suminative.ui.stock.StockUseCase                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     