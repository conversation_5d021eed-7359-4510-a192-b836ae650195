package com.thedasagroup.suminative.ui.common.customComposableViews;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000$\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001aB\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u001e\u0010\u0004\u001a\u001a\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0007\u00a8\u0006\u000b"}, d2 = {"DateRangeDropdown", "", "selectedRange", "Lcom/thedasagroup/suminative/ui/common/customComposableViews/DateRange;", "onRangeSelected", "Lkotlin/Function3;", "", "modifier", "Landroidx/compose/ui/Modifier;", "viewModel", "Lcom/thedasagroup/suminative/ui/products/ProductsScreenViewModel;", "app_stagingGeneralDebug"})
public final class DateRangeDropdownKt {
    
    @androidx.compose.runtime.Composable()
    public static final void DateRangeDropdown(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.common.customComposableViews.DateRange selectedRange, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.String, ? super java.lang.String, ? super com.thedasagroup.suminative.ui.common.customComposableViews.DateRange, kotlin.Unit> onRangeSelected, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.ProductsScreenViewModel viewModel) {
    }
}