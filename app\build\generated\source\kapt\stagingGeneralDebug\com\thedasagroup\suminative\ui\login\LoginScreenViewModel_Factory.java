package com.thedasagroup.suminative.ui.login;

import com.thedasagroup.suminative.data.prefs.Prefs;
import dagger.internal.DaggerGenerated;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class LoginScreenViewModel_Factory {
  private final Provider<LoginUseCase> loginUseCaseProvider;

  private final Provider<Prefs> prefsProvider;

  public LoginScreenViewModel_Factory(Provider<LoginUseCase> loginUseCaseProvider,
      Provider<Prefs> prefsProvider) {
    this.loginUseCaseProvider = loginUseCaseProvider;
    this.prefsProvider = prefsProvider;
  }

  public LoginScreenViewModel get(LoginScreenState state) {
    return newInstance(state, loginUseCaseProvider.get(), prefsProvider.get());
  }

  public static LoginScreenViewModel_Factory create(Provider<LoginUseCase> loginUseCaseProvider,
      Provider<Prefs> prefsProvider) {
    return new LoginScreenViewModel_Factory(loginUseCaseProvider, prefsProvider);
  }

  public static LoginScreenViewModel newInstance(LoginScreenState state, LoginUseCase loginUseCase,
      Prefs prefs) {
    return new LoginScreenViewModel(state, loginUseCase, prefs);
  }
}
