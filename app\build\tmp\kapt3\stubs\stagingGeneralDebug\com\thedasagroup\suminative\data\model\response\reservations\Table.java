package com.thedasagroup.suminative.data.model.response.reservations;

/**
 * Table data model matching the tables API response structure
 * Endpoint: /api/reservations/tables?areaId=5
 */
@kotlinx.serialization.Serializable()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\'\b\u0087\b\u0018\u0000 12\u00020\u0001:\u000201BO\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0003\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0007\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\u000b\u00a2\u0006\u0004\b\r\u0010\u000eJ\t\u0010#\u001a\u00020\u0003H\u00c6\u0003J\t\u0010$\u001a\u00020\u0003H\u00c6\u0003J\t\u0010%\u001a\u00020\u0003H\u00c6\u0003J\t\u0010&\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\'\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010(\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\t\u0010)\u001a\u00020\u000bH\u00c6\u0003J\t\u0010*\u001a\u00020\u000bH\u00c6\u0003J[\u0010+\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00072\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\u000bH\u00c6\u0001J\u0013\u0010,\u001a\u00020\u000b2\b\u0010-\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010.\u001a\u00020\u0003H\u00d6\u0001J\t\u0010/\u001a\u00020\u0007H\u00d6\u0001R\u001c\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u000f\u0010\u0010\u001a\u0004\b\u0011\u0010\u0012R\u001c\u0010\u0004\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0013\u0010\u0010\u001a\u0004\b\u0014\u0010\u0012R\u001c\u0010\u0005\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0015\u0010\u0010\u001a\u0004\b\u0016\u0010\u0012R\u001c\u0010\u0006\u001a\u00020\u00078\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0017\u0010\u0010\u001a\u0004\b\u0018\u0010\u0019R\u001c\u0010\b\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u001a\u0010\u0010\u001a\u0004\b\u001b\u0010\u0012R\u001e\u0010\t\u001a\u0004\u0018\u00010\u00078\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u001c\u0010\u0010\u001a\u0004\b\u001d\u0010\u0019R\u001c\u0010\n\u001a\u00020\u000b8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u001e\u0010\u0010\u001a\u0004\b\u001f\u0010 R\u001c\u0010\f\u001a\u00020\u000b8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b!\u0010\u0010\u001a\u0004\b\"\u0010 \u00a8\u00062"}, d2 = {"Lcom/thedasagroup/suminative/data/model/response/reservations/Table;", "", "id", "", "storeId", "areaId", "tableName", "", "seatingCapacity", "tableDetailsJson", "occupied", "", "reserved", "<init>", "(IIILjava/lang/String;ILjava/lang/String;ZZ)V", "getId$annotations", "()V", "getId", "()I", "getStoreId$annotations", "getStoreId", "getAreaId$annotations", "getAreaId", "getTableName$annotations", "getTableName", "()Ljava/lang/String;", "getSeatingCapacity$annotations", "getSeatingCapacity", "getTableDetailsJson$annotations", "getTableDetailsJson", "getOccupied$annotations", "getOccupied", "()Z", "getReserved$annotations", "getReserved", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "other", "hashCode", "toString", "$serializer", "Companion", "app_stagingGeneralDebug"})
public final class Table {
    private final int id = 0;
    private final int storeId = 0;
    private final int areaId = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String tableName = null;
    private final int seatingCapacity = 0;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String tableDetailsJson = null;
    private final boolean occupied = false;
    private final boolean reserved = false;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.data.model.response.reservations.Table.Companion Companion = null;
    
    public Table(int id, int storeId, int areaId, @org.jetbrains.annotations.NotNull()
    java.lang.String tableName, int seatingCapacity, @org.jetbrains.annotations.Nullable()
    java.lang.String tableDetailsJson, boolean occupied, boolean reserved) {
        super();
    }
    
    public final int getId() {
        return 0;
    }
    
    @kotlinx.serialization.SerialName(value = "id")
    @java.lang.Deprecated()
    public static void getId$annotations() {
    }
    
    public final int getStoreId() {
        return 0;
    }
    
    @kotlinx.serialization.SerialName(value = "storeId")
    @java.lang.Deprecated()
    public static void getStoreId$annotations() {
    }
    
    public final int getAreaId() {
        return 0;
    }
    
    @kotlinx.serialization.SerialName(value = "areaId")
    @java.lang.Deprecated()
    public static void getAreaId$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTableName() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "tableName")
    @java.lang.Deprecated()
    public static void getTableName$annotations() {
    }
    
    public final int getSeatingCapacity() {
        return 0;
    }
    
    @kotlinx.serialization.SerialName(value = "seatingCapacity")
    @java.lang.Deprecated()
    public static void getSeatingCapacity$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getTableDetailsJson() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "tableDetailsJson")
    @java.lang.Deprecated()
    public static void getTableDetailsJson$annotations() {
    }
    
    public final boolean getOccupied() {
        return false;
    }
    
    @kotlinx.serialization.SerialName(value = "occupied")
    @java.lang.Deprecated()
    public static void getOccupied$annotations() {
    }
    
    public final boolean getReserved() {
        return false;
    }
    
    @kotlinx.serialization.SerialName(value = "reserved")
    @java.lang.Deprecated()
    public static void getReserved$annotations() {
    }
    
    public final int component1() {
        return 0;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final int component3() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    public final int component5() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    public final boolean component7() {
        return false;
    }
    
    public final boolean component8() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.model.response.reservations.Table copy(int id, int storeId, int areaId, @org.jetbrains.annotations.NotNull()
    java.lang.String tableName, int seatingCapacity, @org.jetbrains.annotations.Nullable()
    java.lang.String tableDetailsJson, boolean occupied, boolean reserved) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    /**
     * Table data model matching the tables API response structure
     * Endpoint: /api/reservations/tables?areaId=5
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0003\u0010\u0004J\u0015\u0010\u0005\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00070\u0006\u00a2\u0006\u0002\u0010\bJ\u000e\u0010\t\u001a\u00020\u00022\u0006\u0010\n\u001a\u00020\u000bJ\u0016\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0002R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014\u00a8\u0006\u0015"}, d2 = {"com/thedasagroup/suminative/data/model/response/reservations/Table.$serializer", "Lkotlinx/serialization/internal/GeneratedSerializer;", "Lcom/thedasagroup/suminative/data/model/response/reservations/Table;", "<init>", "()V", "childSerializers", "", "Lkotlinx/serialization/KSerializer;", "()[Lkotlinx/serialization/KSerializer;", "deserialize", "decoder", "Lkotlinx/serialization/encoding/Decoder;", "serialize", "", "encoder", "Lkotlinx/serialization/encoding/Encoder;", "value", "descriptor", "Lkotlinx/serialization/descriptors/SerialDescriptor;", "getDescriptor", "()Lkotlinx/serialization/descriptors/SerialDescriptor;", "app_stagingGeneralDebug"})
    @java.lang.Deprecated()
    public static final class $serializer implements kotlinx.serialization.internal.GeneratedSerializer<com.thedasagroup.suminative.data.model.response.reservations.Table> {
        @org.jetbrains.annotations.NotNull()
        public static final com.thedasagroup.suminative.data.model.response.reservations.Table.$serializer INSTANCE = null;
        @org.jetbrains.annotations.NotNull()
        private static final kotlinx.serialization.descriptors.SerialDescriptor descriptor = null;
        
        /**
         * Table data model matching the tables API response structure
         * Endpoint: /api/reservations/tables?areaId=5
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<?>[] childSerializers() {
            return null;
        }
        
        /**
         * Table data model matching the tables API response structure
         * Endpoint: /api/reservations/tables?areaId=5
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final com.thedasagroup.suminative.data.model.response.reservations.Table deserialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Decoder decoder) {
            return null;
        }
        
        /**
         * Table data model matching the tables API response structure
         * Endpoint: /api/reservations/tables?areaId=5
         */
        @java.lang.Override()
        public final void serialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Encoder encoder, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.data.model.response.reservations.Table value) {
        }
        
        private $serializer() {
            super();
        }
        
        /**
         * Table data model matching the tables API response structure
         * Endpoint: /api/reservations/tables?areaId=5
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.descriptors.SerialDescriptor getDescriptor() {
            return null;
        }
    }
    
    /**
     * Table data model matching the tables API response structure
     * Endpoint: /api/reservations/tables?areaId=5
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a8\u0006\u0007"}, d2 = {"Lcom/thedasagroup/suminative/data/model/response/reservations/Table$Companion;", "", "<init>", "()V", "serializer", "Lkotlinx/serialization/KSerializer;", "Lcom/thedasagroup/suminative/data/model/response/reservations/Table;", "app_stagingGeneralDebug"})
    public static final class Companion {
        
        /**
         * Table data model matching the tables API response structure
         * Endpoint: /api/reservations/tables?areaId=5
         */
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<com.thedasagroup.suminative.data.model.response.reservations.Table> serializer() {
            return null;
        }
        
        private Companion() {
            super();
        }
    }
}