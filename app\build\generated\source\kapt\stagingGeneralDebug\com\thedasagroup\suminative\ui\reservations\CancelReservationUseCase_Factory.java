package com.thedasagroup.suminative.ui.reservations;

import com.thedasagroup.suminative.data.repo.ReservationsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class CancelReservationUseCase_Factory implements Factory<CancelReservationUseCase> {
  private final Provider<ReservationsRepository> reservationsRepositoryProvider;

  public CancelReservationUseCase_Factory(
      Provider<ReservationsRepository> reservationsRepositoryProvider) {
    this.reservationsRepositoryProvider = reservationsRepositoryProvider;
  }

  @Override
  public CancelReservationUseCase get() {
    return newInstance(reservationsRepositoryProvider.get());
  }

  public static CancelReservationUseCase_Factory create(
      Provider<ReservationsRepository> reservationsRepositoryProvider) {
    return new CancelReservationUseCase_Factory(reservationsRepositoryProvider);
  }

  public static CancelReservationUseCase newInstance(
      ReservationsRepository reservationsRepository) {
    return new CancelReservationUseCase(reservationsRepository);
  }
}
