package com.thedasagroup.suminative.data.database;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000j\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u001a\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0006\n\u0002\b\u0005\b\u0007\u0018\u00002\u00020\u0001B\u0011\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u0016\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u0010J\u0012\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00130\u0012J\u0018\u0010\u0015\u001a\u0004\u0018\u00010\u00142\u0006\u0010\u0016\u001a\u00020\u0017H\u0086@\u00a2\u0006\u0002\u0010\u0018J\u0018\u0010\u0019\u001a\u0004\u0018\u00010\u00142\u0006\u0010\u001a\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010\u001cJ\u001a\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00130\u00122\u0006\u0010\u001e\u001a\u00020\u001bJ\u001a\u0010\u001f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00130\u00122\u0006\u0010 \u001a\u00020\u0017J\u0012\u0010!\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00130\u0012J\u0012\u0010\"\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00130\u0012J\u0014\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00140\u0013H\u0086@\u00a2\u0006\u0002\u0010$J\u0012\u0010%\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00130\u0012J\u000e\u0010&\u001a\u00020\u0017H\u0086@\u00a2\u0006\u0002\u0010$J\u0016\u0010\'\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u0017H\u0086@\u00a2\u0006\u0002\u0010\u0018J\u0016\u0010(\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u0017H\u0086@\u00a2\u0006\u0002\u0010\u0018J\u001c\u0010)\u001a\u00020\r2\f\u0010*\u001a\b\u0012\u0004\u0012\u00020\u001b0\u0013H\u0086@\u00a2\u0006\u0002\u0010+J\u001e\u0010,\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u001e\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010-J&\u0010.\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010/\u001a\u00020\u001b2\u0006\u00100\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u00101J\u0016\u00102\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u0017H\u0086@\u00a2\u0006\u0002\u0010\u0018J\u0016\u00103\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u0017H\u0086@\u00a2\u0006\u0002\u0010\u0018J\u0016\u00104\u001a\u00020\r2\u0006\u00105\u001a\u000206H\u0086@\u00a2\u0006\u0002\u00107J\u001a\u00108\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002090\u00130\u00122\u0006\u0010\u001a\u001a\u00020\u001bJ\u001c\u0010:\u001a\b\u0012\u0004\u0012\u0002090\u00132\u0006\u0010\u001a\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010\u001cJ\u0018\u0010;\u001a\u0004\u0018\u0001092\u0006\u0010\u0016\u001a\u00020\u0017H\u0086@\u00a2\u0006\u0002\u0010\u0018J0\u0010<\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010=\u001a\u00020\u00172\u0006\u0010>\u001a\u00020?2\b\u0010@\u001a\u0004\u0018\u00010\u001bH\u0086@\u00a2\u0006\u0002\u0010AJ\u0016\u0010B\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u0017H\u0086@\u00a2\u0006\u0002\u0010\u0018J\u0016\u0010C\u001a\u00020\r2\u0006\u0010\u001a\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010\u001cR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006D"}, d2 = {"Lcom/thedasagroup/suminative/data/database/LocalOrderRepository;", "", "databaseManager", "Lcom/thedasagroup/suminative/data/database/DatabaseManager;", "<init>", "(Lcom/thedasagroup/suminative/data/database/DatabaseManager;)V", "database", "Lcom/thedasagroup/suminative/database/POSDatabase;", "orderQueries", "Lcom/thedasagroup/suminative/database/OrderQueries;", "orderItemQueries", "Lcom/thedasagroup/suminative/database/OrderItemQueries;", "insertOrder", "", "order", "Lcom/thedasagroup/suminative/data/database/LocalOrder;", "(Lcom/thedasagroup/suminative/data/database/LocalOrder;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllOrdersFlow", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/thedasagroup/suminative/database/OrderEntity;", "getOrderById", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getOrderByOrderId", "orderId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getOrdersByStatusFlow", "status", "getOrdersByStoreFlow", "storeId", "getPendingOrdersFlow", "getUnsyncedOrdersFlow", "getUnsyncedOrders", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getSyncedOrdersFlow", "getUnsyncedOrderCount", "markOrderSynced", "markOrderUnsynced", "markOrdersSyncedByOrderIds", "orderIds", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateOrderStatus", "(JLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateOrderPayment", "paymentMethod", "paymentStatus", "(JLjava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "markOrderComplete", "deleteOrder", "insertOrderItem", "orderItem", "Lcom/thedasagroup/suminative/data/database/LocalOrderItem;", "(Lcom/thedasagroup/suminative/data/database/LocalOrderItem;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getOrderItemsByOrderIdFlow", "Lcom/thedasagroup/suminative/database/OrderItemEntity;", "getOrderItemsByOrderId", "getOrderItemById", "updateOrderItemQuantity", "quantity", "totalPrice", "", "modifiers", "(JJDLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteOrderItem", "deleteOrderItemsByOrderId", "app_stagingGeneralDebug"})
public final class LocalOrderRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.database.DatabaseManager databaseManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.database.POSDatabase database = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.database.OrderQueries orderQueries = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.database.OrderItemQueries orderItemQueries = null;
    
    @javax.inject.Inject()
    public LocalOrderRepository(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.database.DatabaseManager databaseManager) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertOrder(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.database.LocalOrder order, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.thedasagroup.suminative.database.OrderEntity>> getAllOrdersFlow() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getOrderById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.thedasagroup.suminative.database.OrderEntity> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getOrderByOrderId(@org.jetbrains.annotations.NotNull()
    java.lang.String orderId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.thedasagroup.suminative.database.OrderEntity> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.thedasagroup.suminative.database.OrderEntity>> getOrdersByStatusFlow(@org.jetbrains.annotations.NotNull()
    java.lang.String status) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.thedasagroup.suminative.database.OrderEntity>> getOrdersByStoreFlow(long storeId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.thedasagroup.suminative.database.OrderEntity>> getPendingOrdersFlow() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.thedasagroup.suminative.database.OrderEntity>> getUnsyncedOrdersFlow() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getUnsyncedOrders(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.thedasagroup.suminative.database.OrderEntity>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.thedasagroup.suminative.database.OrderEntity>> getSyncedOrdersFlow() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getUnsyncedOrderCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object markOrderSynced(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object markOrderUnsynced(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object markOrdersSyncedByOrderIds(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> orderIds, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateOrderStatus(long id, @org.jetbrains.annotations.NotNull()
    java.lang.String status, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateOrderPayment(long id, @org.jetbrains.annotations.NotNull()
    java.lang.String paymentMethod, @org.jetbrains.annotations.NotNull()
    java.lang.String paymentStatus, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object markOrderComplete(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteOrder(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertOrderItem(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.database.LocalOrderItem orderItem, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.thedasagroup.suminative.database.OrderItemEntity>> getOrderItemsByOrderIdFlow(@org.jetbrains.annotations.NotNull()
    java.lang.String orderId) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getOrderItemsByOrderId(@org.jetbrains.annotations.NotNull()
    java.lang.String orderId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.thedasagroup.suminative.database.OrderItemEntity>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getOrderItemById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.thedasagroup.suminative.database.OrderItemEntity> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateOrderItemQuantity(long id, long quantity, double totalPrice, @org.jetbrains.annotations.Nullable()
    java.lang.String modifiers, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteOrderItem(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteOrderItemsByOrderId(@org.jetbrains.annotations.NotNull()
    java.lang.String orderId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}