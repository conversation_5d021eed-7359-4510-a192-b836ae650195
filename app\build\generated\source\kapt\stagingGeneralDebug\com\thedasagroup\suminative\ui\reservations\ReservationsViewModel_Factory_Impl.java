package com.thedasagroup.suminative.ui.reservations;

import dagger.internal.DaggerGenerated;
import dagger.internal.InstanceFactory;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ReservationsViewModel_Factory_Impl implements ReservationsViewModel.Factory {
  private final ReservationsViewModel_Factory delegateFactory;

  ReservationsViewModel_Factory_Impl(ReservationsViewModel_Factory delegateFactory) {
    this.delegateFactory = delegateFactory;
  }

  @Override
  public ReservationsViewModel create(ReservationsState state) {
    return delegateFactory.get(state);
  }

  public static Provider<ReservationsViewModel.Factory> create(
      ReservationsViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new ReservationsViewModel_Factory_Impl(delegateFactory));
  }

  public static dagger.internal.Provider<ReservationsViewModel.Factory> createFactoryProvider(
      ReservationsViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new ReservationsViewModel_Factory_Impl(delegateFactory));
  }
}
