{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,320,398,476,561,658,751,847,977,1061,1128,1196,1292,1360,1423,1531,1591,1657,1713,1784,1844,1898,2024,2081,2143,2197,2272,2406,2491,2569,2664,2749,2830,2967,3051,3137,3270,3361,3439,3495,3550,3616,3690,3768,3839,3921,3993,4070,4150,4224,4331,4424,4497,4589,4685,4759,4835,4931,4983,5065,5132,5219,5306,5368,5432,5495,5565,5671,5787,5884,5998,6058,6117,6197,6280,6357", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,77,77,84,96,92,95,129,83,66,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79,82,76,74", "endOffsets": "315,393,471,556,653,746,842,972,1056,1123,1191,1287,1355,1418,1526,1586,1652,1708,1779,1839,1893,2019,2076,2138,2192,2267,2401,2486,2564,2659,2744,2825,2962,3046,3132,3265,3356,3434,3490,3545,3611,3685,3763,3834,3916,3988,4065,4145,4219,4326,4419,4492,4584,4680,4754,4830,4926,4978,5060,5127,5214,5301,5363,5427,5490,5560,5666,5782,5879,5993,6053,6112,6192,6275,6352,6427"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,56,57,58,62,65,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,182,186,187,189", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3081,3159,3237,3322,3419,4238,4334,4464,5260,5327,5395,5807,6047,12304,12412,12472,12538,12594,12665,12725,12779,12905,12962,13024,13078,13153,13287,13372,13450,13545,13630,13711,13848,13932,14018,14151,14242,14320,14376,14431,14497,14571,14649,14720,14802,14874,14951,15031,15105,15212,15305,15378,15470,15566,15640,15716,15812,15864,15946,16013,16100,16187,16249,16313,16376,16446,16552,16668,16765,16879,16939,17178,17510,17593,17743", "endLines": "6,34,35,36,37,38,46,47,48,56,57,58,62,65,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,182,186,187,189", "endColumns": "12,77,77,84,96,92,95,129,83,66,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79,82,76,74", "endOffsets": "365,3154,3232,3317,3414,3507,4329,4459,4543,5322,5390,5486,5870,6105,12407,12467,12533,12589,12660,12720,12774,12900,12957,13019,13073,13148,13282,13367,13445,13540,13625,13706,13843,13927,14013,14146,14237,14315,14371,14426,14492,14566,14644,14715,14797,14869,14946,15026,15100,15207,15300,15373,15465,15561,15635,15711,15807,15859,15941,16008,16095,16182,16244,16308,16371,16441,16547,16663,16760,16874,16934,16993,17253,17588,17665,17813"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,297,391,490,580,659,752,847,932,1013,1099,1172,1249,1328,1405,1484,1554", "endColumns": "104,86,93,98,89,78,92,94,84,80,85,72,76,78,76,78,69,117", "endOffsets": "205,292,386,485,575,654,747,842,927,1008,1094,1167,1244,1323,1400,1479,1549,1667"}, "to": {"startLines": "49,50,53,54,55,63,64,180,181,183,184,188,190,191,192,540,541,542", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4548,4653,4977,5071,5170,5875,5954,16998,17093,17258,17339,17670,17818,17895,17974,55440,55519,55589", "endColumns": "104,86,93,98,89,78,92,94,84,80,85,72,76,78,76,78,69,117", "endOffsets": "4648,4735,5066,5165,5255,5949,6042,17088,17173,17334,17420,17738,17890,17969,18046,55514,55584,55702"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,296,416,537,637,731,842,983,1102,1247,1332,1432,1527,1625,1744,1870,1975,2111,2246,2380,2548,2674,2798,2926,3050,3146,3244,3374,3508,3605,3707,3816,3957,4104,4213,4313,4398,4491,4586,4680,4766,4875,4963,5046,5143,5244,5337,5434,5522,5630,5727,5829,5967,6057,6157", "endColumns": "119,120,119,120,99,93,110,140,118,144,84,99,94,97,118,125,104,135,134,133,167,125,123,127,123,95,97,129,133,96,101,108,140,146,108,99,84,92,94,93,85,108,87,82,96,100,92,96,87,107,96,101,137,89,99,91", "endOffsets": "170,291,411,532,632,726,837,978,1097,1242,1327,1427,1522,1620,1739,1865,1970,2106,2241,2375,2543,2669,2793,2921,3045,3141,3239,3369,3503,3600,3702,3811,3952,4099,4208,4308,4393,4486,4581,4675,4761,4870,4958,5041,5138,5239,5332,5429,5517,5625,5722,5824,5962,6052,6152,6244"}, "to": {"startLines": "66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6110,6230,6351,6471,6592,6692,6786,6897,7038,7157,7302,7387,7487,7582,7680,7799,7925,8030,8166,8301,8435,8603,8729,8853,8981,9105,9201,9299,9429,9563,9660,9762,9871,10012,10159,10268,10368,10453,10546,10641,10735,10821,10930,11018,11101,11198,11299,11392,11489,11577,11685,11782,11884,12022,12112,12212", "endColumns": "119,120,119,120,99,93,110,140,118,144,84,99,94,97,118,125,104,135,134,133,167,125,123,127,123,95,97,129,133,96,101,108,140,146,108,99,84,92,94,93,85,108,87,82,96,100,92,96,87,107,96,101,137,89,99,91", "endOffsets": "6225,6346,6466,6587,6687,6781,6892,7033,7152,7297,7382,7482,7577,7675,7794,7920,8025,8161,8296,8430,8598,8724,8848,8976,9100,9196,9294,9424,9558,9655,9757,9866,10007,10154,10263,10363,10448,10541,10636,10730,10816,10925,11013,11096,11193,11294,11387,11484,11572,11680,11777,11879,12017,12107,12207,12299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "39,40,41,42,43,44,45,193", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3512,3610,3717,3814,3913,4017,4121,18051", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3605,3712,3809,3908,4012,4116,4233,18147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "543,544", "startColumns": "4,4", "startOffsets": "55707,55790", "endColumns": "82,84", "endOffsets": "55785,55870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,475,570,677,763,867,986,1071,1153,1244,1337,1432,1526,1626,1719,1814,1909,2000,2091,2177,2281,2393,2494,2599,2713,2815,2984,17425", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "470,565,672,758,862,981,1066,1148,1239,1332,1427,1521,1621,1714,1809,1904,1995,2086,2172,2276,2388,2489,2594,2708,2810,2979,3076,17505"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,230,372,448,531,636,763,948,1097,1223,1329,1460,1522,1582,1670,1774,1844,2005,2110,2204,2358,2435,2534,2637,2783,2881,2999,3103,3182,3254,3385,3476,3711,3893,4012,4171,4288,4473,4582,4716,4820,4968,5075,5211,5312,5408,5556,5648,5795,5893,6051,6136,6266,6343,6472,6580,6655,6748,6821,6895,6976,7050,7183,7282,7430,7529,7648,7748,7896,7997,8093,8222,8317,8425,8528,8644,8744,8808,8862,8914,8967,9017,9066,9133,9185,9252,9301,9345,9415,9463,9520,9574,9636,9695,9747,9798,9870,9922,9974,10019,10080,10140,10202,10341,10455,10536,10597,10687,10756,10824,10950,11027,11103,11190,11268,11336,11433,11505,11595,11678,11805,11888,11978,12060,12157,12248,12327,12436,12540,12654,12760,12836,12933,13003,13092,13220,13370,13436,13513,13604,13692,15743,15858,16041,16090,16229,16384,16474,16616,16706,16847,17004,17135,17297,17375,17454,17535,17623,17735,17780,17866,17980,18073,18200,18337,18468,18579,18685,18750,18817,18876,18933,18995,19073,19167,19291,19361,19441,19687,19886,20016,20142,20284,20370,20491,20619,20771,20916,21005,21118,21204,21321,21419,21567,21656,21729,21786,22002,22086,22167,22226,22284,22393,22509,22622,22779,22958,23030,23096,23158,23310,23386,23480,23557,23651,23809,23901,23974,24092,24179,24320,24447,24502,24624,24694,24770,24831,24905,24953,25018,25107,25159,25228,25389,25511,25616,25679,25825,25958,26066,26178,26445,26548,26648,26754,26930,27112,27199,27387,27489,27630,27748,27837,27923,28000,28153,28321,28459,28505,28595,28726,28835,28915,28998,29089,29165,29234,29306,29367,29443,29515,29591,29686,29751,29820,29907,29977,30058,30233,30359,30447,30505,30585,30721,30789,30867,30944,31021,31097,31346,31401,31487,31569,31641,31739,31909,31996,32071,32157,32238,32304,32384,32464,32598,32676,32731,32811,32888,32965,33047,33157,33256,33362,33483,33563,33611,33663,33757,33853,33938,34047,34201,34305,34391,34513,34635,34773,34897,34997,35146,35281,35466,35643,35768,35905,36002,36098,36219,36347,36446,36579,36649,37020,37107,37193,37271", "endColumns": "174,141,75,82,104,126,184,148,125,105,130,61,59,87,103,69,160,104,93,153,76,98,102,145,97,117,103,78,71,130,90,234,181,118,158,116,184,108,133,103,147,106,135,100,95,147,91,146,97,157,84,129,76,128,107,74,92,72,73,80,73,132,98,147,98,118,99,147,100,95,128,94,107,102,115,99,63,53,51,52,49,48,66,51,66,48,43,69,47,56,53,61,58,51,50,71,51,51,44,60,59,61,138,113,80,60,89,68,67,125,76,75,86,77,67,96,71,89,82,126,82,89,81,96,90,78,108,103,113,105,75,96,69,88,127,149,65,76,90,87,2050,114,182,48,138,154,89,141,89,140,156,130,161,77,78,80,87,111,44,85,113,92,126,136,130,110,105,64,66,58,56,61,77,93,123,69,79,245,198,129,125,141,85,120,127,151,144,88,112,85,116,97,147,88,72,56,215,83,80,58,57,108,115,112,156,178,71,65,61,151,75,93,76,93,157,91,72,117,86,140,126,54,121,69,75,60,73,47,64,88,51,68,160,121,104,62,145,132,107,111,266,102,99,105,175,181,86,187,101,140,117,88,85,76,152,167,137,45,89,130,108,79,82,90,75,68,71,60,75,71,75,94,64,68,86,69,80,174,125,87,57,79,135,67,77,76,76,75,248,54,85,81,71,97,169,86,74,85,80,65,79,79,133,77,54,79,76,76,81,109,98,105,120,79,47,51,93,95,84,108,153,103,85,121,121,137,123,99,148,134,184,176,124,136,96,95,120,127,98,132,69,370,86,85,77,71", "endOffsets": "225,367,443,526,631,758,943,1092,1218,1324,1455,1517,1577,1665,1769,1839,2000,2105,2199,2353,2430,2529,2632,2778,2876,2994,3098,3177,3249,3380,3471,3706,3888,4007,4166,4283,4468,4577,4711,4815,4963,5070,5206,5307,5403,5551,5643,5790,5888,6046,6131,6261,6338,6467,6575,6650,6743,6816,6890,6971,7045,7178,7277,7425,7524,7643,7743,7891,7992,8088,8217,8312,8420,8523,8639,8739,8803,8857,8909,8962,9012,9061,9128,9180,9247,9296,9340,9410,9458,9515,9569,9631,9690,9742,9793,9865,9917,9969,10014,10075,10135,10197,10336,10450,10531,10592,10682,10751,10819,10945,11022,11098,11185,11263,11331,11428,11500,11590,11673,11800,11883,11973,12055,12152,12243,12322,12431,12535,12649,12755,12831,12928,12998,13087,13215,13365,13431,13508,13599,13687,15738,15853,16036,16085,16224,16379,16469,16611,16701,16842,16999,17130,17292,17370,17449,17530,17618,17730,17775,17861,17975,18068,18195,18332,18463,18574,18680,18745,18812,18871,18928,18990,19068,19162,19286,19356,19436,19682,19881,20011,20137,20279,20365,20486,20614,20766,20911,21000,21113,21199,21316,21414,21562,21651,21724,21781,21997,22081,22162,22221,22279,22388,22504,22617,22774,22953,23025,23091,23153,23305,23381,23475,23552,23646,23804,23896,23969,24087,24174,24315,24442,24497,24619,24689,24765,24826,24900,24948,25013,25102,25154,25223,25384,25506,25611,25674,25820,25953,26061,26173,26440,26543,26643,26749,26925,27107,27194,27382,27484,27625,27743,27832,27918,27995,28148,28316,28454,28500,28590,28721,28830,28910,28993,29084,29160,29229,29301,29362,29438,29510,29586,29681,29746,29815,29902,29972,30053,30228,30354,30442,30500,30580,30716,30784,30862,30939,31016,31092,31341,31396,31482,31564,31636,31734,31904,31991,32066,32152,32233,32299,32379,32459,32593,32671,32726,32806,32883,32960,33042,33152,33251,33357,33478,33558,33606,33658,33752,33848,33933,34042,34196,34300,34386,34508,34630,34768,34892,34992,35141,35276,35461,35638,35763,35900,35997,36093,36214,36342,36441,36574,36644,37015,37102,37188,37266,37338"}, "to": {"startLines": "194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18152,18327,18469,18545,18628,18733,18860,19045,19194,19320,19426,19557,19619,19679,19767,19871,19941,20102,20207,20301,20455,20532,20631,20734,20880,20978,21096,21200,21279,21351,21482,21573,21808,21990,22109,22268,22385,22570,22679,22813,22917,23065,23172,23308,23409,23505,23653,23745,23892,23990,24148,24233,24363,24440,24569,24677,24752,24845,24918,24992,25073,25147,25280,25379,25527,25626,25745,25845,25993,26094,26190,26319,26414,26522,26625,26741,26841,26905,26959,27011,27064,27114,27163,27230,27282,27349,27398,27442,27512,27560,27617,27671,27733,27792,27844,27895,27967,28019,28071,28116,28177,28237,28299,28438,28552,28633,28694,28784,28853,28921,29047,29124,29200,29287,29365,29433,29530,29602,29692,29775,29902,29985,30075,30157,30254,30345,30424,30533,30637,30751,30857,30933,31030,31100,31189,31317,31467,31533,31610,31701,31789,33840,33955,34138,34187,34326,34481,34571,34713,34803,34944,35101,35232,35394,35472,35551,35632,35720,35832,35877,35963,36077,36170,36297,36434,36565,36676,36782,36847,36914,36973,37030,37092,37170,37264,37388,37458,37538,37784,37983,38113,38239,38381,38467,38588,38716,38868,39013,39102,39215,39301,39418,39516,39664,39753,39826,39883,40099,40183,40264,40323,40381,40490,40606,40719,40876,41055,41127,41193,41255,41407,41483,41577,41654,41748,41906,41998,42071,42189,42276,42417,42544,42599,42721,42791,42867,42928,43002,43050,43115,43204,43256,43325,43486,43608,43713,43776,43922,44055,44163,44275,44542,44645,44745,44851,45027,45209,45296,45484,45586,45727,45845,45934,46020,46097,46250,46418,46556,46602,46692,46823,46932,47012,47095,47186,47262,47331,47403,47464,47540,47612,47688,47783,47848,47917,48004,48074,48155,48330,48456,48544,48602,48682,48818,48886,48964,49041,49118,49194,49443,49498,49584,49666,49738,49836,50006,50093,50168,50254,50335,50401,50481,50561,50695,50773,50828,50908,50985,51062,51144,51254,51353,51459,51580,51660,51708,51760,51854,51950,52035,52144,52298,52402,52488,52610,52732,52870,52994,53094,53243,53378,53563,53740,53865,54002,54099,54195,54316,54444,54543,54676,54746,55117,55204,55290,55368", "endColumns": "174,141,75,82,104,126,184,148,125,105,130,61,59,87,103,69,160,104,93,153,76,98,102,145,97,117,103,78,71,130,90,234,181,118,158,116,184,108,133,103,147,106,135,100,95,147,91,146,97,157,84,129,76,128,107,74,92,72,73,80,73,132,98,147,98,118,99,147,100,95,128,94,107,102,115,99,63,53,51,52,49,48,66,51,66,48,43,69,47,56,53,61,58,51,50,71,51,51,44,60,59,61,138,113,80,60,89,68,67,125,76,75,86,77,67,96,71,89,82,126,82,89,81,96,90,78,108,103,113,105,75,96,69,88,127,149,65,76,90,87,2050,114,182,48,138,154,89,141,89,140,156,130,161,77,78,80,87,111,44,85,113,92,126,136,130,110,105,64,66,58,56,61,77,93,123,69,79,245,198,129,125,141,85,120,127,151,144,88,112,85,116,97,147,88,72,56,215,83,80,58,57,108,115,112,156,178,71,65,61,151,75,93,76,93,157,91,72,117,86,140,126,54,121,69,75,60,73,47,64,88,51,68,160,121,104,62,145,132,107,111,266,102,99,105,175,181,86,187,101,140,117,88,85,76,152,167,137,45,89,130,108,79,82,90,75,68,71,60,75,71,75,94,64,68,86,69,80,174,125,87,57,79,135,67,77,76,76,75,248,54,85,81,71,97,169,86,74,85,80,65,79,79,133,77,54,79,76,76,81,109,98,105,120,79,47,51,93,95,84,108,153,103,85,121,121,137,123,99,148,134,184,176,124,136,96,95,120,127,98,132,69,370,86,85,77,71", "endOffsets": "18322,18464,18540,18623,18728,18855,19040,19189,19315,19421,19552,19614,19674,19762,19866,19936,20097,20202,20296,20450,20527,20626,20729,20875,20973,21091,21195,21274,21346,21477,21568,21803,21985,22104,22263,22380,22565,22674,22808,22912,23060,23167,23303,23404,23500,23648,23740,23887,23985,24143,24228,24358,24435,24564,24672,24747,24840,24913,24987,25068,25142,25275,25374,25522,25621,25740,25840,25988,26089,26185,26314,26409,26517,26620,26736,26836,26900,26954,27006,27059,27109,27158,27225,27277,27344,27393,27437,27507,27555,27612,27666,27728,27787,27839,27890,27962,28014,28066,28111,28172,28232,28294,28433,28547,28628,28689,28779,28848,28916,29042,29119,29195,29282,29360,29428,29525,29597,29687,29770,29897,29980,30070,30152,30249,30340,30419,30528,30632,30746,30852,30928,31025,31095,31184,31312,31462,31528,31605,31696,31784,33835,33950,34133,34182,34321,34476,34566,34708,34798,34939,35096,35227,35389,35467,35546,35627,35715,35827,35872,35958,36072,36165,36292,36429,36560,36671,36777,36842,36909,36968,37025,37087,37165,37259,37383,37453,37533,37779,37978,38108,38234,38376,38462,38583,38711,38863,39008,39097,39210,39296,39413,39511,39659,39748,39821,39878,40094,40178,40259,40318,40376,40485,40601,40714,40871,41050,41122,41188,41250,41402,41478,41572,41649,41743,41901,41993,42066,42184,42271,42412,42539,42594,42716,42786,42862,42923,42997,43045,43110,43199,43251,43320,43481,43603,43708,43771,43917,44050,44158,44270,44537,44640,44740,44846,45022,45204,45291,45479,45581,45722,45840,45929,46015,46092,46245,46413,46551,46597,46687,46818,46927,47007,47090,47181,47257,47326,47398,47459,47535,47607,47683,47778,47843,47912,47999,48069,48150,48325,48451,48539,48597,48677,48813,48881,48959,49036,49113,49189,49438,49493,49579,49661,49733,49831,50001,50088,50163,50249,50330,50396,50476,50556,50690,50768,50823,50903,50980,51057,51139,51249,51348,51454,51575,51655,51703,51755,51849,51945,52030,52139,52293,52397,52483,52605,52727,52865,52989,53089,53238,53373,53558,53735,53860,53997,54094,54190,54311,54439,54538,54671,54741,55112,55199,55285,55363,55435"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,374", "endColumns": "104,99,113,101", "endOffsets": "155,255,369,471"}, "to": {"startLines": "52,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "4872,5491,5591,5705", "endColumns": "104,99,113,101", "endOffsets": "4972,5586,5700,5802"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-hr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "51", "startColumns": "4", "startOffsets": "4740", "endColumns": "131", "endOffsets": "4867"}}]}]}