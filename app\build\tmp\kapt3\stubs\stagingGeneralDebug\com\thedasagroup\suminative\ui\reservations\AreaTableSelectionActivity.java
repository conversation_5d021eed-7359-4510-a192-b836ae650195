package com.thedasagroup.suminative.ui.reservations;

@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u0000 \b2\u00020\u0001:\u0001\bB\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0012\u0010\u0004\u001a\u00020\u00052\b\u0010\u0006\u001a\u0004\u0018\u00010\u0007H\u0014\u00a8\u0006\t"}, d2 = {"Lcom/thedasagroup/suminative/ui/reservations/AreaTableSelectionActivity;", "Landroidx/activity/ComponentActivity;", "<init>", "()V", "onCreate", "", "savedInstanceState", "Landroid/os/Bundle;", "Companion", "app_stagingGeneralDebug"})
public final class AreaTableSelectionActivity extends androidx.activity.ComponentActivity {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_SELECTED_AREA_ID = "selected_area_id";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_SELECTED_AREA_NAME = "selected_area_name";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_SELECTED_TABLE_ID = "selected_table_id";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_SELECTED_TABLE_NAME = "selected_table_name";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_SELECTED_TABLE_CAPACITY = "selected_table_capacity";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_EXCLUDED_TABLE_IDS = "excluded_table_ids";
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.reservations.AreaTableSelectionActivity.Companion Companion = null;
    
    public AreaTableSelectionActivity() {
        super(0);
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u001e\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u000e\b\u0002\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u0010R\u000e\u0010\u0004\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/thedasagroup/suminative/ui/reservations/AreaTableSelectionActivity$Companion;", "", "<init>", "()V", "EXTRA_SELECTED_AREA_ID", "", "EXTRA_SELECTED_AREA_NAME", "EXTRA_SELECTED_TABLE_ID", "EXTRA_SELECTED_TABLE_NAME", "EXTRA_SELECTED_TABLE_CAPACITY", "EXTRA_EXCLUDED_TABLE_IDS", "createIntent", "Landroid/content/Intent;", "context", "Landroid/content/Context;", "excludedTableIds", "", "", "app_stagingGeneralDebug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.content.Intent createIntent(@org.jetbrains.annotations.NotNull()
        android.content.Context context, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.Integer> excludedTableIds) {
            return null;
        }
    }
}