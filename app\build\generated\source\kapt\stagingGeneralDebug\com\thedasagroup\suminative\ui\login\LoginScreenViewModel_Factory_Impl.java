package com.thedasagroup.suminative.ui.login;

import dagger.internal.DaggerGenerated;
import dagger.internal.InstanceFactory;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class LoginScreenViewModel_Factory_Impl implements LoginScreenViewModel.Factory {
  private final LoginScreenViewModel_Factory delegateFactory;

  LoginScreenViewModel_Factory_Impl(LoginScreenViewModel_Factory delegateFactory) {
    this.delegateFactory = delegateFactory;
  }

  @Override
  public LoginScreenViewModel create(LoginScreenState state) {
    return delegateFactory.get(state);
  }

  public static Provider<LoginScreenViewModel.Factory> create(
      LoginScreenViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new LoginScreenViewModel_Factory_Impl(delegateFactory));
  }

  public static dagger.internal.Provider<LoginScreenViewModel.Factory> createFactoryProvider(
      LoginScreenViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new LoginScreenViewModel_Factory_Impl(delegateFactory));
  }
}
