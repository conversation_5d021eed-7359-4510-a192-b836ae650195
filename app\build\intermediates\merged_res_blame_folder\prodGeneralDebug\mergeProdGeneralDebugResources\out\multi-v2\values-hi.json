{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,413,494,601,697,804,936,1019,1086,1151,1245,1314,1373,1458,1521,1584,1642,1707,1768,1829,1935,1993,2053,2112,2182,2298,2377,2468,2561,2659,2739,2873,2948,3024,3161,3258,3356,3413,3468,3534,3604,3681,3752,3837,3905,3981,4062,4140,4241,4327,4414,4511,4610,4684,4754,4858,4912,4999,5066,5156,5248,5310,5374,5437,5503,5608,5718,5819,5926,5987,6046,6125,6210,6290", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,80,106,95,106,131,82,66,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,90,92,97,79,133,74,75,136,96,97,56,54,65,69,76,70,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78,84,79,72", "endOffsets": "254,332,408,489,596,692,799,931,1014,1081,1146,1240,1309,1368,1453,1516,1579,1637,1702,1763,1824,1930,1988,2048,2107,2177,2293,2372,2463,2556,2654,2734,2868,2943,3019,3156,3253,3351,3408,3463,3529,3599,3676,3747,3832,3900,3976,4057,4135,4236,4322,4409,4506,4605,4679,4749,4853,4907,4994,5061,5151,5243,5305,5369,5432,5498,5603,5713,5814,5921,5982,6041,6120,6205,6285,6358"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2991,3069,3145,3226,3333,4182,4289,4421,5214,5281,5346,5757,6001,12247,12332,12395,12458,12516,12581,12642,12703,12809,12867,12927,12986,13056,13172,13251,13342,13435,13533,13613,13747,13822,13898,14035,14132,14230,14287,14342,14408,14478,14555,14626,14711,14779,14855,14936,15014,15115,15201,15288,15385,15484,15558,15628,15732,15786,15873,15940,16030,16122,16184,16248,16311,16377,16482,16592,16693,16800,16861,17094,17421,17506,17659", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,77,75,80,106,95,106,131,82,66,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,90,92,97,79,133,74,75,136,96,97,56,54,65,69,76,70,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78,84,79,72", "endOffsets": "304,3064,3140,3221,3328,3424,4284,4416,4499,5276,5341,5435,5821,6055,12327,12390,12453,12511,12576,12637,12698,12804,12862,12922,12981,13051,13167,13246,13337,13430,13528,13608,13742,13817,13893,14030,14127,14225,14282,14337,14403,14473,14550,14621,14706,14774,14850,14931,15009,15110,15196,15283,15380,15479,15553,15623,15727,15781,15868,15935,16025,16117,16179,16243,16306,16372,16477,16587,16688,16795,16856,16915,17168,17501,17581,17727"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,415,513,623,709,811,932,1010,1087,1178,1271,1366,1460,1560,1653,1748,1842,1933,2024,2105,2210,2312,2410,2520,2623,2732,2890,17339", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "410,508,618,704,806,927,1005,1082,1173,1266,1361,1455,1555,1648,1743,1837,1928,2019,2100,2205,2307,2405,2515,2618,2727,2885,2986,17416"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,376,474,563,641,738,827,912,993,1078,1151,1244,1319,1394,1475,1541", "endColumns": "94,82,92,97,88,77,96,88,84,80,84,72,92,74,74,80,65,119", "endOffsets": "195,278,371,469,558,636,733,822,907,988,1073,1146,1239,1314,1389,1470,1536,1656"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,193,194,195", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4504,4599,4934,5027,5125,5826,5904,16920,17009,17173,17254,17586,17732,17825,17900,18076,18157,18223", "endColumns": "94,82,92,97,88,77,96,88,84,80,84,72,92,74,74,80,65,119", "endOffsets": "4594,4677,5022,5120,5209,5899,5996,17004,17089,17249,17334,17654,17820,17895,17970,18152,18218,18338"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3429,3527,3630,3735,3836,3949,4055,17975", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "3522,3625,3730,3831,3944,4050,4177,18071"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,85", "endOffsets": "135,221"}, "to": {"startLines": "196,197", "startColumns": "4,4", "startOffsets": "18343,18428", "endColumns": "84,85", "endOffsets": "18423,18509"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,375", "endColumns": "105,101,111,102", "endOffsets": "156,258,370,473"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4828,5440,5542,5654", "endColumns": "105,101,111,102", "endOffsets": "4929,5537,5649,5752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,415,531,626,722,835,973,1093,1243,1328,1431,1522,1619,1749,1869,1977,2122,2268,2398,2587,2714,2832,2954,3080,3172,3267,3395,3521,3620,3722,3834,3980,4132,4246,4346,4422,4522,4621,4707,4797,4902,4982,5066,5166,5266,5361,5463,5549,5651,5749,5853,5968,6048,6148", "endColumns": "117,117,123,115,94,95,112,137,119,149,84,102,90,96,129,119,107,144,145,129,188,126,117,121,125,91,94,127,125,98,101,111,145,151,113,99,75,99,98,85,89,104,79,83,99,99,94,101,85,101,97,103,114,79,99,93", "endOffsets": "168,286,410,526,621,717,830,968,1088,1238,1323,1426,1517,1614,1744,1864,1972,2117,2263,2393,2582,2709,2827,2949,3075,3167,3262,3390,3516,3615,3717,3829,3975,4127,4241,4341,4417,4517,4616,4702,4792,4897,4977,5061,5161,5261,5356,5458,5544,5646,5744,5848,5963,6043,6143,6237"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6060,6178,6296,6420,6536,6631,6727,6840,6978,7098,7248,7333,7436,7527,7624,7754,7874,7982,8127,8273,8403,8592,8719,8837,8959,9085,9177,9272,9400,9526,9625,9727,9839,9985,10137,10251,10351,10427,10527,10626,10712,10802,10907,10987,11071,11171,11271,11366,11468,11554,11656,11754,11858,11973,12053,12153", "endColumns": "117,117,123,115,94,95,112,137,119,149,84,102,90,96,129,119,107,144,145,129,188,126,117,121,125,91,94,127,125,98,101,111,145,151,113,99,75,99,98,85,89,104,79,83,99,99,94,101,85,101,97,103,114,79,99,93", "endOffsets": "6173,6291,6415,6531,6626,6722,6835,6973,7093,7243,7328,7431,7522,7619,7749,7869,7977,8122,8268,8398,8587,8714,8832,8954,9080,9172,9267,9395,9521,9620,9722,9834,9980,10132,10246,10346,10422,10522,10621,10707,10797,10902,10982,11066,11166,11266,11361,11463,11549,11651,11749,11853,11968,12048,12148,12242"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-hi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4682", "endColumns": "145", "endOffsets": "4823"}}]}]}