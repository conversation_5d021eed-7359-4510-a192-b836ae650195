package com.thedasagroup.suminative.ui.guava_orders;

import dagger.internal.DaggerGenerated;
import dagger.internal.InstanceFactory;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class GuavaOrdersViewModel_Factory_Impl implements GuavaOrdersViewModel.Factory {
  private final GuavaOrdersViewModel_Factory delegateFactory;

  GuavaOrdersViewModel_Factory_Impl(GuavaOrdersViewModel_Factory delegateFactory) {
    this.delegateFactory = delegateFactory;
  }

  @Override
  public GuavaOrdersViewModel create(GuavaOrdersState state) {
    return delegateFactory.get(state);
  }

  public static Provider<GuavaOrdersViewModel.Factory> create(
      GuavaOrdersViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new GuavaOrdersViewModel_Factory_Impl(delegateFactory));
  }

  public static dagger.internal.Provider<GuavaOrdersViewModel.Factory> createFactoryProvider(
      GuavaOrdersViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new GuavaOrdersViewModel_Factory_Impl(delegateFactory));
  }
}
