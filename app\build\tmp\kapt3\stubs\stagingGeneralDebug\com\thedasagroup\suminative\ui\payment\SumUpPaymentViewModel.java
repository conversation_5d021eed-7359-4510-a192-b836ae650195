package com.thedasagroup.suminative.ui.payment;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u0006\u0010\u0006\u001a\u00020\u0007J\u0016\u0010\b\u001a\u00020\u00072\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fJ*\u0010\r\u001a\u00020\u00072\u0006\u0010\t\u001a\u00020\n2\b\u0010\u000e\u001a\u0004\u0018\u00010\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u00112\u0006\u0010\u000b\u001a\u00020\fJ\u0006\u0010\u0012\u001a\u00020\u0007J\u0006\u0010\u0013\u001a\u00020\u0007J\u0006\u0010\u0014\u001a\u00020\u0007J\u000e\u0010\u0015\u001a\u00020\u00072\u0006\u0010\u000e\u001a\u00020\u000f\u00a8\u0006\u0016"}, d2 = {"Lcom/thedasagroup/suminative/ui/payment/SumUpPaymentViewModel;", "Lcom/airbnb/mvrx/MavericksViewModel;", "Lcom/thedasagroup/suminative/ui/payment/SumUpPaymentState;", "initialState", "<init>", "(Lcom/thedasagroup/suminative/ui/payment/SumUpPaymentState;)V", "checkLoginStatus", "", "onLoginResult", "success", "", "message", "", "onPaymentResult", "order", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "transactionInfo", "Lcom/sumup/merchant/reader/models/TransactionInfo;", "startLogin", "startPayment", "clearMessages", "setOrder", "app_stagingGeneralDebug"})
public final class SumUpPaymentViewModel extends com.airbnb.mvrx.MavericksViewModel<com.thedasagroup.suminative.ui.payment.SumUpPaymentState> {
    
    public SumUpPaymentViewModel(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.payment.SumUpPaymentState initialState) {
        super(null, null);
    }
    
    public final void checkLoginStatus() {
    }
    
    public final void onLoginResult(boolean success, @org.jetbrains.annotations.NotNull()
    java.lang.String message) {
    }
    
    public final void onPaymentResult(boolean success, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.Nullable()
    com.sumup.merchant.reader.models.TransactionInfo transactionInfo, @org.jetbrains.annotations.NotNull()
    java.lang.String message) {
    }
    
    public final void startLogin() {
    }
    
    public final void startPayment() {
    }
    
    public final void clearMessages() {
    }
    
    public final void setOrder(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order) {
    }
}