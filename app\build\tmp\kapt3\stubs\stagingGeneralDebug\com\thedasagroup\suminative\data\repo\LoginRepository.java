package com.thedasagroup.suminative.data.repo;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\"\u0010\u0004\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00060\u00052\u0006\u0010\b\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\nJ\"\u0010\u0004\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00060\u00052\u0006\u0010\b\u001a\u00020\u000bH\u0086@\u00a2\u0006\u0002\u0010\fJ\"\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00060\u00052\u0006\u0010\u000f\u001a\u00020\u0010H\u0086@\u00a2\u0006\u0002\u0010\u0011J\"\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00060\u00052\u0006\u0010\u000f\u001a\u00020\u0013H\u0086@\u00a2\u0006\u0002\u0010\u0014J\"\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160\u00060\u00052\u0006\u0010\u0017\u001a\u00020\u0018H\u0086@\u00a2\u0006\u0002\u0010\u0019\u00a8\u0006\u001a"}, d2 = {"Lcom/thedasagroup/suminative/data/repo/LoginRepository;", "Lcom/thedasagroup/suminative/data/repo/BaseRepository;", "<init>", "()V", "login", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/login/LoginResponse;", "loginRequest", "Lcom/thedasagroup/suminative/data/model/request/login/LoginRequest;", "(Lcom/thedasagroup/suminative/data/model/request/login/LoginRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Lcom/thedasagroup/suminative/data/model/request/login2/LoginRequest2;", "(Lcom/thedasagroup/suminative/data/model/request/login2/LoginRequest2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getStoreSettings", "Lcom/thedasagroup/suminative/data/model/response/store_settings/StoreSettingsResponse;", "storeSettingsRequest", "Lcom/thedasagroup/suminative/data/model/request/store_settings/StoreSettingsRequest;", "(Lcom/thedasagroup/suminative/data/model/request/store_settings/StoreSettingsRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPOSSettings", "Lcom/thedasagroup/suminative/data/model/request/store_settings/GetPosSettingsRequest;", "(Lcom/thedasagroup/suminative/data/model/request/store_settings/GetPosSettingsRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getStoreConfigurations", "Lcom/thedasagroup/suminative/data/model/response/store_configurations/StoreConfigurationsResponse;", "storeConfigurationsRequest", "Lcom/thedasagroup/suminative/data/model/request/store_configurations/StoreConfigurationsRequest;", "(Lcom/thedasagroup/suminative/data/model/request/store_configurations/StoreConfigurationsRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_stagingGeneralDebug"})
public final class LoginRepository extends com.thedasagroup.suminative.data.repo.BaseRepository {
    
    public LoginRepository() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object login(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.login.LoginRequest loginRequest, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object login(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.login2.LoginRequest2 loginRequest, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getStoreSettings(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.store_settings.StoreSettingsRequest storeSettingsRequest, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.store_settings.StoreSettingsResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getPOSSettings(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.store_settings.GetPosSettingsRequest storeSettingsRequest, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.store_settings.StoreSettingsResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getStoreConfigurations(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.store_configurations.StoreConfigurationsRequest storeConfigurationsRequest, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.store_configurations.StoreConfigurationsResponse>>> $completion) {
        return null;
    }
}