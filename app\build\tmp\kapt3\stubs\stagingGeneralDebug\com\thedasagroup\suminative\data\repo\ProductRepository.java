package com.thedasagroup.suminative.data.repo;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\b\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\"\u0010\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\t2\u0006\u0010\f\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010\u000eJ(\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\t2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\r0\u0011H\u0086@\u00a2\u0006\u0002\u0010\u0012J(\u0010\u0013\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00110\n0\t2\u0006\u0010\u0015\u001a\u00020\u0016H\u0086@\u00a2\u0006\u0002\u0010\u0017J(\u0010\u0018\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00110\n0\t2\u0006\u0010\u0015\u001a\u00020\u0016H\u0086@\u00a2\u0006\u0002\u0010\u0017J*\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\t2\u0006\u0010\u001a\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u0016H\u0086@\u00a2\u0006\u0002\u0010\u001cJ\n\u0010\u001d\u001a\u00020\r*\u00020\u0014R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001e"}, d2 = {"Lcom/thedasagroup/suminative/data/repo/ProductRepository;", "Lcom/thedasagroup/suminative/data/repo/BaseRepository;", "databaseManager", "Lcom/thedasagroup/suminative/data/database/DatabaseManager;", "<init>", "(Lcom/thedasagroup/suminative/data/database/DatabaseManager;)V", "productQueries", "Lcom/thedasagroup/suminative/database/ProductQueries;", "insertProduct", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "", "stockItem", "Lcom/thedasagroup/suminative/data/model/response/stock/StockItem;", "(Lcom/thedasagroup/suminative/data/model/response/stock/StockItem;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertMultipleProducts", "stockItems", "", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getProductsByStore", "Lcom/thedasagroup/suminative/database/ProductEntity;", "storeId", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getInStockProductsByStore", "updateProductStock", "productId", "stock", "(IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "toStockItem", "app_stagingGeneralDebug"})
public final class ProductRepository extends com.thedasagroup.suminative.data.repo.BaseRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.database.DatabaseManager databaseManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.database.ProductQueries productQueries = null;
    
    public ProductRepository(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.database.DatabaseManager databaseManager) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertProduct(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.stock.StockItem stockItem, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<java.lang.Boolean>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertMultipleProducts(@org.jetbrains.annotations.NotNull()
    java.util.List<com.thedasagroup.suminative.data.model.response.stock.StockItem> stockItems, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<java.lang.Boolean>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getProductsByStore(int storeId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.database.ProductEntity>>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getInStockProductsByStore(int storeId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.database.ProductEntity>>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateProductStock(int productId, int stock, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<java.lang.Boolean>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.model.response.stock.StockItem toStockItem(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.database.ProductEntity $this$toStockItem) {
        return null;
    }
}