package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.domain.myguava.MyGuavaCreateRefundOrderUseCase;
import com.thedasagroup.suminative.domain.myguava.MyGuavaCreateSessionUseCase;
import com.thedasagroup.suminative.domain.myguava.MyGuavaGetTerminalsUseCase;
import com.thedasagroup.suminative.domain.myguava.MyGuavaMakeRefundUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesMyGuavaMakeRefundUseCaseFactory implements Factory<MyGuavaMakeRefundUseCase> {
  private final Provider<MyGuavaCreateRefundOrderUseCase> createOrderUseCaseProvider;

  private final Provider<MyGuavaGetTerminalsUseCase> getTerminalsUseCaseProvider;

  private final Provider<MyGuavaCreateSessionUseCase> createSessionUseCaseProvider;

  public AppUseCaseModule_ProvidesMyGuavaMakeRefundUseCaseFactory(
      Provider<MyGuavaCreateRefundOrderUseCase> createOrderUseCaseProvider,
      Provider<MyGuavaGetTerminalsUseCase> getTerminalsUseCaseProvider,
      Provider<MyGuavaCreateSessionUseCase> createSessionUseCaseProvider) {
    this.createOrderUseCaseProvider = createOrderUseCaseProvider;
    this.getTerminalsUseCaseProvider = getTerminalsUseCaseProvider;
    this.createSessionUseCaseProvider = createSessionUseCaseProvider;
  }

  @Override
  public MyGuavaMakeRefundUseCase get() {
    return providesMyGuavaMakeRefundUseCase(createOrderUseCaseProvider.get(), getTerminalsUseCaseProvider.get(), createSessionUseCaseProvider.get());
  }

  public static AppUseCaseModule_ProvidesMyGuavaMakeRefundUseCaseFactory create(
      Provider<MyGuavaCreateRefundOrderUseCase> createOrderUseCaseProvider,
      Provider<MyGuavaGetTerminalsUseCase> getTerminalsUseCaseProvider,
      Provider<MyGuavaCreateSessionUseCase> createSessionUseCaseProvider) {
    return new AppUseCaseModule_ProvidesMyGuavaMakeRefundUseCaseFactory(createOrderUseCaseProvider, getTerminalsUseCaseProvider, createSessionUseCaseProvider);
  }

  public static MyGuavaMakeRefundUseCase providesMyGuavaMakeRefundUseCase(
      MyGuavaCreateRefundOrderUseCase createOrderUseCase,
      MyGuavaGetTerminalsUseCase getTerminalsUseCase,
      MyGuavaCreateSessionUseCase createSessionUseCase) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesMyGuavaMakeRefundUseCase(createOrderUseCase, getTerminalsUseCase, createSessionUseCase));
  }
}
