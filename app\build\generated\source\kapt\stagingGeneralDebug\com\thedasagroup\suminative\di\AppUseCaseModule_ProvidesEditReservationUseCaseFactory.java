package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.repo.ReservationsRepository;
import com.thedasagroup.suminative.ui.reservations.EditReservationUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesEditReservationUseCaseFactory implements Factory<EditReservationUseCase> {
  private final Provider<ReservationsRepository> reservationsRepositoryProvider;

  public AppUseCaseModule_ProvidesEditReservationUseCaseFactory(
      Provider<ReservationsRepository> reservationsRepositoryProvider) {
    this.reservationsRepositoryProvider = reservationsRepositoryProvider;
  }

  @Override
  public EditReservationUseCase get() {
    return providesEditReservationUseCase(reservationsRepositoryProvider.get());
  }

  public static AppUseCaseModule_ProvidesEditReservationUseCaseFactory create(
      Provider<ReservationsRepository> reservationsRepositoryProvider) {
    return new AppUseCaseModule_ProvidesEditReservationUseCaseFactory(reservationsRepositoryProvider);
  }

  public static EditReservationUseCase providesEditReservationUseCase(
      ReservationsRepository reservationsRepository) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesEditReservationUseCase(reservationsRepository));
  }
}
