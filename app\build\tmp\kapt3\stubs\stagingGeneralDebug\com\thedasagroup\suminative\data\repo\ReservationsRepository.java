package com.thedasagroup.suminative.data.repo;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\"\u0010\u0004\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00060\u00052\u0006\u0010\b\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\nJ*\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u00060\u00052\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\b\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u0010J\"\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u00060\u00052\u0006\u0010\r\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010\u0012J2\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00060\u00052\u0006\u0010\u0015\u001a\u00020\u000e2\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010\u0019J*\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00060\u00052\u0006\u0010\u0015\u001a\u00020\u000e2\u0006\u0010\u0016\u001a\u00020\u0017H\u0086@\u00a2\u0006\u0002\u0010\u001bJ\u0006\u0010\u001c\u001a\u00020\u001dJ(\u0010\u001e\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020 0\u001f0\u00060\u00052\u0006\u0010\u0015\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010\u0012J(\u0010!\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\"0\u001f0\u00060\u00052\u0006\u0010#\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010\u0012\u00a8\u0006$"}, d2 = {"Lcom/thedasagroup/suminative/data/repo/ReservationsRepository;", "Lcom/thedasagroup/suminative/data/repo/BaseRepository;", "<init>", "()V", "createReservation", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/reservations/CreateReservationResponse;", "request", "Lcom/thedasagroup/suminative/data/model/request/reservations/CreateReservationRequest;", "(Lcom/thedasagroup/suminative/data/model/request/reservations/CreateReservationRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "editReservation", "", "reservationId", "", "Lcom/thedasagroup/suminative/data/model/request/reservations/EditReservationRequest;", "(ILcom/thedasagroup/suminative/data/model/request/reservations/EditReservationRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cancelReservation", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActiveReservationsRetrofit", "Lcom/thedasagroup/suminative/data/model/response/reservations/ReservationsResponse;", "storeId", "currentTime", "", "timezoneOffset", "(ILjava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllReservationsRetrofit", "(ILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getReservationRetrofitService", "Lcom/thedasagroup/suminative/data/api/ReservationsRetrofitService;", "getReservationAreas", "", "Lcom/thedasagroup/suminative/data/model/response/reservations/Area;", "getReservationTables", "Lcom/thedasagroup/suminative/data/model/response/reservations/Table;", "areaId", "app_stagingGeneralDebug"})
public final class ReservationsRepository extends com.thedasagroup.suminative.data.repo.BaseRepository {
    
    public ReservationsRepository() {
        super();
    }
    
    /**
     * Create a new reservation or update an existing one
     * @param request The create reservation request containing reservation data
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object createReservation(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.reservations.CreateReservationRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse>>> $completion) {
        return null;
    }
    
    /**
     * Edit an existing reservation
     * @param reservationId The reservation ID to edit
     * @param request The edit reservation request containing updated data
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object editReservation(int reservationId, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.reservations.EditReservationRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<java.lang.Boolean>>> $completion) {
        return null;
    }
    
    /**
     * Cancel a reservation
     * @param reservationId The reservation ID to cancel
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object cancelReservation(int reservationId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<java.lang.Boolean>>> $completion) {
        return null;
    }
    
    /**
     * Get currently active reservations using Retrofit
     * @param storeId The store ID
     * @param currentTime Current time in ISO format (e.g., "2025-07-15T18:00:00")
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getActiveReservationsRetrofit(int storeId, @org.jetbrains.annotations.NotNull()
    java.lang.String currentTime, int timezoneOffset, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse>>> $completion) {
        return null;
    }
    
    /**
     * Get all reservations using Retrofit
     * @param storeId The store ID
     * @param currentTime Current time in ISO format (e.g., "2025-07-15T18:00:00")
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAllReservationsRetrofit(int storeId, @org.jetbrains.annotations.NotNull()
    java.lang.String currentTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.api.ReservationsRetrofitService getReservationRetrofitService() {
        return null;
    }
    
    /**
     * Get reservation areas for a store using Retrofit
     * @param storeId The store ID to filter areas
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getReservationAreas(int storeId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.data.model.response.reservations.Area>>>> $completion) {
        return null;
    }
    
    /**
     * Get tables for a specific area using Retrofit
     * @param areaId The area ID to filter tables
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getReservationTables(int areaId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.data.model.response.reservations.Table>>>> $completion) {
        return null;
    }
}