package com.thedasagroup.suminative.data.model.response.reservations;

/**
 * Response model for create/update reservation API
 * Matches the response structure from the POST API
 */
@kotlinx.serialization.Serializable()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b+\n\u0002\u0010\u000b\n\u0002\b\u0006\b\u0087\b\u0018\u0000 :2\u00020\u0001:\u00029:B\u007f\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\t\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\t\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\t\u00a2\u0006\u0004\b\u000e\u0010\u000fJ\u0010\u0010(\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0013J\u0010\u0010)\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0013J\u0010\u0010*\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0013J\u0010\u0010+\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0013J\u0010\u0010,\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0013J\u000b\u0010-\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\u000b\u0010.\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\u000b\u0010/\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\u0010\u00100\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0013J\u000b\u00101\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\u0086\u0001\u00102\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\tH\u00c6\u0001\u00a2\u0006\u0002\u00103J\u0013\u00104\u001a\u0002052\b\u00106\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00107\u001a\u00020\u0003H\u00d6\u0001J\t\u00108\u001a\u00020\tH\u00d6\u0001R \u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u0010\n\u0002\u0010\u0014\u0012\u0004\b\u0010\u0010\u0011\u001a\u0004\b\u0012\u0010\u0013R \u0010\u0004\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u0010\n\u0002\u0010\u0014\u0012\u0004\b\u0015\u0010\u0011\u001a\u0004\b\u0016\u0010\u0013R \u0010\u0005\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u0010\n\u0002\u0010\u0014\u0012\u0004\b\u0017\u0010\u0011\u001a\u0004\b\u0018\u0010\u0013R \u0010\u0006\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u0010\n\u0002\u0010\u0014\u0012\u0004\b\u0019\u0010\u0011\u001a\u0004\b\u001a\u0010\u0013R \u0010\u0007\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u0010\n\u0002\u0010\u0014\u0012\u0004\b\u001b\u0010\u0011\u001a\u0004\b\u001c\u0010\u0013R\u001e\u0010\b\u001a\u0004\u0018\u00010\t8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u001d\u0010\u0011\u001a\u0004\b\u001e\u0010\u001fR\u001e\u0010\n\u001a\u0004\u0018\u00010\t8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b \u0010\u0011\u001a\u0004\b!\u0010\u001fR\u001e\u0010\u000b\u001a\u0004\u0018\u00010\t8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\"\u0010\u0011\u001a\u0004\b#\u0010\u001fR \u0010\f\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\u0010\n\u0002\u0010\u0014\u0012\u0004\b$\u0010\u0011\u001a\u0004\b%\u0010\u0013R\u001e\u0010\r\u001a\u0004\u0018\u00010\t8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b&\u0010\u0011\u001a\u0004\b\'\u0010\u001f\u00a8\u0006;"}, d2 = {"Lcom/thedasagroup/suminative/data/model/response/reservations/CreateReservationResponse;", "", "id", "", "storeId", "tableId", "customerId", "reservationStatus", "guestName", "", "guestPhone", "reservationTime", "numPeople", "createdAt", "<init>", "(Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;)V", "getId$annotations", "()V", "getId", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getStoreId$annotations", "getStoreId", "getTableId$annotations", "getTableId", "getCustomerId$annotations", "getCustomerId", "getReservationStatus$annotations", "getReservationStatus", "getGuestName$annotations", "getGuestName", "()Ljava/lang/String;", "getGuestPhone$annotations", "getGuestPhone", "getReservationTime$annotations", "getReservationTime", "getNumPeople$annotations", "getNumPeople", "getCreatedAt$annotations", "getCreatedAt", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "copy", "(Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;)Lcom/thedasagroup/suminative/data/model/response/reservations/CreateReservationResponse;", "equals", "", "other", "hashCode", "toString", "$serializer", "Companion", "app_stagingGeneralDebug"})
public final class CreateReservationResponse {
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer id = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer storeId = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer tableId = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer customerId = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer reservationStatus = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String guestName = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String guestPhone = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String reservationTime = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer numPeople = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String createdAt = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse.Companion Companion = null;
    
    public CreateReservationResponse(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.Integer storeId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer tableId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer customerId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer reservationStatus, @org.jetbrains.annotations.Nullable()
    java.lang.String guestName, @org.jetbrains.annotations.Nullable()
    java.lang.String guestPhone, @org.jetbrains.annotations.Nullable()
    java.lang.String reservationTime, @org.jetbrains.annotations.Nullable()
    java.lang.Integer numPeople, @org.jetbrains.annotations.Nullable()
    java.lang.String createdAt) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getId() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "id")
    @java.lang.Deprecated()
    public static void getId$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getStoreId() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "storeId")
    @java.lang.Deprecated()
    public static void getStoreId$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getTableId() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "tableId")
    @java.lang.Deprecated()
    public static void getTableId$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getCustomerId() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "customerId")
    @java.lang.Deprecated()
    public static void getCustomerId$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getReservationStatus() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "reservationStatus")
    @java.lang.Deprecated()
    public static void getReservationStatus$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getGuestName() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "guestName")
    @java.lang.Deprecated()
    public static void getGuestName$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getGuestPhone() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "guestPhone")
    @java.lang.Deprecated()
    public static void getGuestPhone$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getReservationTime() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "reservationTime")
    @java.lang.Deprecated()
    public static void getReservationTime$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getNumPeople() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "numPeople")
    @java.lang.Deprecated()
    public static void getNumPeople$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCreatedAt() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "createdAt")
    @java.lang.Deprecated()
    public static void getCreatedAt$annotations() {
    }
    
    public CreateReservationResponse() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse copy(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.Integer storeId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer tableId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer customerId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer reservationStatus, @org.jetbrains.annotations.Nullable()
    java.lang.String guestName, @org.jetbrains.annotations.Nullable()
    java.lang.String guestPhone, @org.jetbrains.annotations.Nullable()
    java.lang.String reservationTime, @org.jetbrains.annotations.Nullable()
    java.lang.Integer numPeople, @org.jetbrains.annotations.Nullable()
    java.lang.String createdAt) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    /**
     * Response model for create/update reservation API
     * Matches the response structure from the POST API
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0003\u0010\u0004J\u0015\u0010\u0005\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00070\u0006\u00a2\u0006\u0002\u0010\bJ\u000e\u0010\t\u001a\u00020\u00022\u0006\u0010\n\u001a\u00020\u000bJ\u0016\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0002R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014\u00a8\u0006\u0015"}, d2 = {"com/thedasagroup/suminative/data/model/response/reservations/CreateReservationResponse.$serializer", "Lkotlinx/serialization/internal/GeneratedSerializer;", "Lcom/thedasagroup/suminative/data/model/response/reservations/CreateReservationResponse;", "<init>", "()V", "childSerializers", "", "Lkotlinx/serialization/KSerializer;", "()[Lkotlinx/serialization/KSerializer;", "deserialize", "decoder", "Lkotlinx/serialization/encoding/Decoder;", "serialize", "", "encoder", "Lkotlinx/serialization/encoding/Encoder;", "value", "descriptor", "Lkotlinx/serialization/descriptors/SerialDescriptor;", "getDescriptor", "()Lkotlinx/serialization/descriptors/SerialDescriptor;", "app_stagingGeneralDebug"})
    @java.lang.Deprecated()
    public static final class $serializer implements kotlinx.serialization.internal.GeneratedSerializer<com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse> {
        @org.jetbrains.annotations.NotNull()
        public static final com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse.$serializer INSTANCE = null;
        @org.jetbrains.annotations.NotNull()
        private static final kotlinx.serialization.descriptors.SerialDescriptor descriptor = null;
        
        /**
         * Response model for create/update reservation API
         * Matches the response structure from the POST API
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<?>[] childSerializers() {
            return null;
        }
        
        /**
         * Response model for create/update reservation API
         * Matches the response structure from the POST API
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse deserialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Decoder decoder) {
            return null;
        }
        
        /**
         * Response model for create/update reservation API
         * Matches the response structure from the POST API
         */
        @java.lang.Override()
        public final void serialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Encoder encoder, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse value) {
        }
        
        private $serializer() {
            super();
        }
        
        /**
         * Response model for create/update reservation API
         * Matches the response structure from the POST API
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.descriptors.SerialDescriptor getDescriptor() {
            return null;
        }
    }
    
    /**
     * Response model for create/update reservation API
     * Matches the response structure from the POST API
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a8\u0006\u0007"}, d2 = {"Lcom/thedasagroup/suminative/data/model/response/reservations/CreateReservationResponse$Companion;", "", "<init>", "()V", "serializer", "Lkotlinx/serialization/KSerializer;", "Lcom/thedasagroup/suminative/data/model/response/reservations/CreateReservationResponse;", "app_stagingGeneralDebug"})
    public static final class Companion {
        
        /**
         * Response model for create/update reservation API
         * Matches the response structure from the POST API
         */
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse> serializer() {
            return null;
        }
        
        private Companion() {
            super();
        }
    }
}