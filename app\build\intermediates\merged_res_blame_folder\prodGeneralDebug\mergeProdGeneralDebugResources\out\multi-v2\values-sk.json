{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,284,379,482,574,653,747,837,918,1001,1088,1160,1238,1314,1389,1467,1535", "endColumns": "94,83,94,102,91,78,93,89,80,82,86,71,77,75,74,77,67,113", "endOffsets": "195,279,374,477,569,648,742,832,913,996,1083,1155,1233,1309,1384,1462,1530,1644"}, "to": {"startLines": "50,51,54,55,56,64,65,181,182,184,185,189,191,192,193,541,542,543", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4581,4676,5006,5101,5204,5917,5996,16846,16936,17096,17179,17507,17655,17733,17809,56480,56558,56626", "endColumns": "94,83,94,102,91,78,93,89,80,82,86,71,77,75,74,77,67,113", "endOffsets": "4671,4755,5096,5199,5291,5991,6085,16931,17012,17174,17261,17574,17728,17804,17879,56553,56621,56735"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "40,41,42,43,44,45,46,194", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3543,3639,3741,3842,3940,4050,4158,17884", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "3634,3736,3837,3935,4045,4153,4275,17980"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-sk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "4760", "endColumns": "138", "endOffsets": "4894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "544,545", "startColumns": "4,4", "startOffsets": "56740,56824", "endColumns": "83,86", "endOffsets": "56819,56906"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,378,453,528,606,698,781,873,1001,1082,1143,1208,1307,1383,1448,1538,1602,1668,1722,1791,1851,1905,2022,2082,2144,2198,2270,2400,2487,2567,2663,2747,2839,2978,3047,3125,3256,3344,3424,3478,3529,3595,3667,3744,3815,3897,3969,4046,4119,4190,4295,4383,4455,4547,4643,4717,4791,4887,4939,5021,5088,5175,5262,5324,5388,5451,5519,5625,5732,5830,5947,6005,6060,6139,6222,6297", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,74,74,77,91,82,91,127,80,60,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,79,95,83,91,138,68,77,130,87,79,53,50,65,71,76,70,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78,82,74,75", "endOffsets": "373,448,523,601,693,776,868,996,1077,1138,1203,1302,1378,1443,1533,1597,1663,1717,1786,1846,1900,2017,2077,2139,2193,2265,2395,2482,2562,2658,2742,2834,2973,3042,3120,3251,3339,3419,3473,3524,3590,3662,3739,3810,3892,3964,4041,4114,4185,4290,4378,4450,4542,4638,4712,4786,4882,4934,5016,5083,5170,5257,5319,5383,5446,5514,5620,5727,5825,5942,6000,6055,6134,6217,6292,6368"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,57,58,59,63,66,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,183,187,188,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3140,3215,3290,3368,3460,4280,4372,4500,5296,5357,5422,5841,6090,12234,12324,12388,12454,12508,12577,12637,12691,12808,12868,12930,12984,13056,13186,13273,13353,13449,13533,13625,13764,13833,13911,14042,14130,14210,14264,14315,14381,14453,14530,14601,14683,14755,14832,14905,14976,15081,15169,15241,15333,15429,15503,15577,15673,15725,15807,15874,15961,16048,16110,16174,16237,16305,16411,16518,16616,16733,16791,17017,17349,17432,17579", "endLines": "7,35,36,37,38,39,47,48,49,57,58,59,63,66,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,183,187,188,190", "endColumns": "12,74,74,77,91,82,91,127,80,60,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,79,95,83,91,138,68,77,130,87,79,53,50,65,71,76,70,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78,82,74,75", "endOffsets": "423,3210,3285,3363,3455,3538,4367,4495,4576,5352,5417,5516,5912,6150,12319,12383,12449,12503,12572,12632,12686,12803,12863,12925,12979,13051,13181,13268,13348,13444,13528,13620,13759,13828,13906,14037,14125,14205,14259,14310,14376,14448,14525,14596,14678,14750,14827,14900,14971,15076,15164,15236,15328,15424,15498,15572,15668,15720,15802,15869,15956,16043,16105,16169,16232,16300,16406,16513,16611,16728,16786,16841,17091,17427,17502,17650"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,265,380", "endColumns": "106,102,114,101", "endOffsets": "157,260,375,477"}, "to": {"startLines": "53,60,61,62", "startColumns": "4,4,4,4", "startOffsets": "4899,5521,5624,5739", "endColumns": "106,102,114,101", "endOffsets": "5001,5619,5734,5836"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,186", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "428,535,636,747,833,941,1059,1138,1215,1306,1399,1497,1591,1691,1784,1879,1977,2068,2159,2243,2348,2456,2555,2661,2773,2876,3042,17266", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "530,631,742,828,936,1054,1133,1210,1301,1394,1492,1586,1686,1779,1874,1972,2063,2154,2238,2343,2451,2550,2656,2768,2871,3037,3135,17344"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,285,395,510,608,703,815,950,1066,1218,1303,1404,1496,1593,1709,1831,1937,2070,2203,2337,2501,2629,2753,2883,3003,3096,3193,3314,3437,3535,3638,3747,3888,4037,4146,4246,4330,4424,4519,4606,4693,4794,4874,4960,5057,5160,5253,5350,5438,5543,5640,5739,5859,5939,6041", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "165,280,390,505,603,698,810,945,1061,1213,1298,1399,1491,1588,1704,1826,1932,2065,2198,2332,2496,2624,2748,2878,2998,3091,3188,3309,3432,3530,3633,3742,3883,4032,4141,4241,4325,4419,4514,4601,4688,4789,4869,4955,5052,5155,5248,5345,5433,5538,5635,5734,5854,5934,6036,6129"}, "to": {"startLines": "67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6155,6270,6385,6495,6610,6708,6803,6915,7050,7166,7318,7403,7504,7596,7693,7809,7931,8037,8170,8303,8437,8601,8729,8853,8983,9103,9196,9293,9414,9537,9635,9738,9847,9988,10137,10246,10346,10430,10524,10619,10706,10793,10894,10974,11060,11157,11260,11353,11450,11538,11643,11740,11839,11959,12039,12141", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "6265,6380,6490,6605,6703,6798,6910,7045,7161,7313,7398,7499,7591,7688,7804,7926,8032,8165,8298,8432,8596,8724,8848,8978,9098,9191,9288,9409,9532,9630,9733,9842,9983,10132,10241,10341,10425,10519,10614,10701,10788,10889,10969,11055,11152,11255,11348,11445,11533,11638,11735,11834,11954,12034,12136,12229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,229,361,437,524,625,768,946,1119,1249,1370,1526,1589,1651,1750,1855,1928,2063,2161,2249,2384,2459,2562,2668,2839,2942,3064,3185,3269,3342,3501,3594,3800,3957,4055,4188,4281,4448,4555,4730,4844,5005,5122,5245,5355,5459,5598,5695,5834,5936,6093,6179,6319,6396,6516,6611,6689,6769,6846,6910,6985,7049,7165,7273,7448,7552,7691,7803,7960,8070,8174,8308,8407,8511,8615,8734,8842,8907,8959,9014,9063,9113,9164,9239,9290,9355,9404,9449,9518,9562,9614,9666,9726,9783,9833,9884,9957,10010,10062,10108,10172,10231,10291,10473,10576,10664,10725,10809,10878,10946,11062,11141,11217,11297,11375,11442,11538,11613,11707,11788,11931,12011,12103,12191,12284,12375,12454,12557,12662,12789,12892,12968,13073,13151,13235,13367,13519,13587,13664,13752,13849,15900,16015,16213,16259,16387,16557,16647,16807,16903,17037,17232,17384,17576,17662,17742,17826,17921,18030,18075,18159,18282,18378,18504,18651,18799,18913,19023,19087,19147,19207,19261,19337,19414,19512,19628,19698,19790,20045,20270,20392,20538,20673,20777,20883,21013,21179,21353,21447,21555,21645,21778,21885,22043,22139,22213,22270,22531,22619,22695,22759,22822,22934,23062,23145,23316,23495,23579,23647,23707,23872,23945,24035,24111,24204,24368,24461,24540,24658,24782,24948,25054,25110,25241,25312,25392,25455,25532,25581,25644,25739,25791,25863,26048,26163,26253,26330,26483,26633,26768,26868,27159,27267,27363,27469,27644,27849,27934,28144,28257,28396,28514,28593,28681,28768,28907,29073,29202,29251,29342,29483,29606,29696,29781,29887,29970,30042,30118,30179,30255,30328,30403,30492,30557,30624,30725,30803,30892,31111,31247,31341,31397,31476,31654,31714,31794,31875,31954,32041,32316,32371,32451,32534,32614,32722,32887,32975,33048,33134,33218,33285,33367,33458,33583,33664,33723,33807,33884,33954,34034,34144,34257,34374,34536,34608,34656,34707,34804,34916,34995,35085,35263,35372,35466,35583,35708,35859,35986,36091,36224,36348,36572,36751,36882,37032,37127,37219,37339,37461,37562,37686,37776,38218,38297,38387,38473", "endColumns": "173,131,75,86,100,142,177,172,129,120,155,62,61,98,104,72,134,97,87,134,74,102,105,170,102,121,120,83,72,158,92,205,156,97,132,92,166,106,174,113,160,116,122,109,103,138,96,138,101,156,85,139,76,119,94,77,79,76,63,74,63,115,107,174,103,138,111,156,109,103,133,98,103,103,118,107,64,51,54,48,49,50,74,50,64,48,44,68,43,51,51,59,56,49,50,72,52,51,45,63,58,59,181,102,87,60,83,68,67,115,78,75,79,77,66,95,74,93,80,142,79,91,87,92,90,78,102,104,126,102,75,104,77,83,131,151,67,76,87,96,2050,114,197,45,127,169,89,159,95,133,194,151,191,85,79,83,94,108,44,83,122,95,125,146,147,113,109,63,59,59,53,75,76,97,115,69,91,254,224,121,145,134,103,105,129,165,173,93,107,89,132,106,157,95,73,56,260,87,75,63,62,111,127,82,170,178,83,67,59,164,72,89,75,92,163,92,78,117,123,165,105,55,130,70,79,62,76,48,62,94,51,71,184,114,89,76,152,149,134,99,290,107,95,105,174,204,84,209,112,138,117,78,87,86,138,165,128,48,90,140,122,89,84,105,82,71,75,60,75,72,74,88,64,66,100,77,88,218,135,93,55,78,177,59,79,80,78,86,274,54,79,82,79,107,164,87,72,85,83,66,81,90,124,80,58,83,76,69,79,109,112,116,161,71,47,50,96,111,78,89,177,108,93,116,124,150,126,104,132,123,223,178,130,149,94,91,119,121,100,123,89,441,78,89,85,76", "endOffsets": "224,356,432,519,620,763,941,1114,1244,1365,1521,1584,1646,1745,1850,1923,2058,2156,2244,2379,2454,2557,2663,2834,2937,3059,3180,3264,3337,3496,3589,3795,3952,4050,4183,4276,4443,4550,4725,4839,5000,5117,5240,5350,5454,5593,5690,5829,5931,6088,6174,6314,6391,6511,6606,6684,6764,6841,6905,6980,7044,7160,7268,7443,7547,7686,7798,7955,8065,8169,8303,8402,8506,8610,8729,8837,8902,8954,9009,9058,9108,9159,9234,9285,9350,9399,9444,9513,9557,9609,9661,9721,9778,9828,9879,9952,10005,10057,10103,10167,10226,10286,10468,10571,10659,10720,10804,10873,10941,11057,11136,11212,11292,11370,11437,11533,11608,11702,11783,11926,12006,12098,12186,12279,12370,12449,12552,12657,12784,12887,12963,13068,13146,13230,13362,13514,13582,13659,13747,13844,15895,16010,16208,16254,16382,16552,16642,16802,16898,17032,17227,17379,17571,17657,17737,17821,17916,18025,18070,18154,18277,18373,18499,18646,18794,18908,19018,19082,19142,19202,19256,19332,19409,19507,19623,19693,19785,20040,20265,20387,20533,20668,20772,20878,21008,21174,21348,21442,21550,21640,21773,21880,22038,22134,22208,22265,22526,22614,22690,22754,22817,22929,23057,23140,23311,23490,23574,23642,23702,23867,23940,24030,24106,24199,24363,24456,24535,24653,24777,24943,25049,25105,25236,25307,25387,25450,25527,25576,25639,25734,25786,25858,26043,26158,26248,26325,26478,26628,26763,26863,27154,27262,27358,27464,27639,27844,27929,28139,28252,28391,28509,28588,28676,28763,28902,29068,29197,29246,29337,29478,29601,29691,29776,29882,29965,30037,30113,30174,30250,30323,30398,30487,30552,30619,30720,30798,30887,31106,31242,31336,31392,31471,31649,31709,31789,31870,31949,32036,32311,32366,32446,32529,32609,32717,32882,32970,33043,33129,33213,33280,33362,33453,33578,33659,33718,33802,33879,33949,34029,34139,34252,34369,34531,34603,34651,34702,34799,34911,34990,35080,35258,35367,35461,35578,35703,35854,35981,36086,36219,36343,36567,36746,36877,37027,37122,37214,37334,37456,37557,37681,37771,38213,38292,38382,38468,38545"}, "to": {"startLines": "195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17985,18159,18291,18367,18454,18555,18698,18876,19049,19179,19300,19456,19519,19581,19680,19785,19858,19993,20091,20179,20314,20389,20492,20598,20769,20872,20994,21115,21199,21272,21431,21524,21730,21887,21985,22118,22211,22378,22485,22660,22774,22935,23052,23175,23285,23389,23528,23625,23764,23866,24023,24109,24249,24326,24446,24541,24619,24699,24776,24840,24915,24979,25095,25203,25378,25482,25621,25733,25890,26000,26104,26238,26337,26441,26545,26664,26772,26837,26889,26944,26993,27043,27094,27169,27220,27285,27334,27379,27448,27492,27544,27596,27656,27713,27763,27814,27887,27940,27992,28038,28102,28161,28221,28403,28506,28594,28655,28739,28808,28876,28992,29071,29147,29227,29305,29372,29468,29543,29637,29718,29861,29941,30033,30121,30214,30305,30384,30487,30592,30719,30822,30898,31003,31081,31165,31297,31449,31517,31594,31682,31779,33830,33945,34143,34189,34317,34487,34577,34737,34833,34967,35162,35314,35506,35592,35672,35756,35851,35960,36005,36089,36212,36308,36434,36581,36729,36843,36953,37017,37077,37137,37191,37267,37344,37442,37558,37628,37720,37975,38200,38322,38468,38603,38707,38813,38943,39109,39283,39377,39485,39575,39708,39815,39973,40069,40143,40200,40461,40549,40625,40689,40752,40864,40992,41075,41246,41425,41509,41577,41637,41802,41875,41965,42041,42134,42298,42391,42470,42588,42712,42878,42984,43040,43171,43242,43322,43385,43462,43511,43574,43669,43721,43793,43978,44093,44183,44260,44413,44563,44698,44798,45089,45197,45293,45399,45574,45779,45864,46074,46187,46326,46444,46523,46611,46698,46837,47003,47132,47181,47272,47413,47536,47626,47711,47817,47900,47972,48048,48109,48185,48258,48333,48422,48487,48554,48655,48733,48822,49041,49177,49271,49327,49406,49584,49644,49724,49805,49884,49971,50246,50301,50381,50464,50544,50652,50817,50905,50978,51064,51148,51215,51297,51388,51513,51594,51653,51737,51814,51884,51964,52074,52187,52304,52466,52538,52586,52637,52734,52846,52925,53015,53193,53302,53396,53513,53638,53789,53916,54021,54154,54278,54502,54681,54812,54962,55057,55149,55269,55391,55492,55616,55706,56148,56227,56317,56403", "endColumns": "173,131,75,86,100,142,177,172,129,120,155,62,61,98,104,72,134,97,87,134,74,102,105,170,102,121,120,83,72,158,92,205,156,97,132,92,166,106,174,113,160,116,122,109,103,138,96,138,101,156,85,139,76,119,94,77,79,76,63,74,63,115,107,174,103,138,111,156,109,103,133,98,103,103,118,107,64,51,54,48,49,50,74,50,64,48,44,68,43,51,51,59,56,49,50,72,52,51,45,63,58,59,181,102,87,60,83,68,67,115,78,75,79,77,66,95,74,93,80,142,79,91,87,92,90,78,102,104,126,102,75,104,77,83,131,151,67,76,87,96,2050,114,197,45,127,169,89,159,95,133,194,151,191,85,79,83,94,108,44,83,122,95,125,146,147,113,109,63,59,59,53,75,76,97,115,69,91,254,224,121,145,134,103,105,129,165,173,93,107,89,132,106,157,95,73,56,260,87,75,63,62,111,127,82,170,178,83,67,59,164,72,89,75,92,163,92,78,117,123,165,105,55,130,70,79,62,76,48,62,94,51,71,184,114,89,76,152,149,134,99,290,107,95,105,174,204,84,209,112,138,117,78,87,86,138,165,128,48,90,140,122,89,84,105,82,71,75,60,75,72,74,88,64,66,100,77,88,218,135,93,55,78,177,59,79,80,78,86,274,54,79,82,79,107,164,87,72,85,83,66,81,90,124,80,58,83,76,69,79,109,112,116,161,71,47,50,96,111,78,89,177,108,93,116,124,150,126,104,132,123,223,178,130,149,94,91,119,121,100,123,89,441,78,89,85,76", "endOffsets": "18154,18286,18362,18449,18550,18693,18871,19044,19174,19295,19451,19514,19576,19675,19780,19853,19988,20086,20174,20309,20384,20487,20593,20764,20867,20989,21110,21194,21267,21426,21519,21725,21882,21980,22113,22206,22373,22480,22655,22769,22930,23047,23170,23280,23384,23523,23620,23759,23861,24018,24104,24244,24321,24441,24536,24614,24694,24771,24835,24910,24974,25090,25198,25373,25477,25616,25728,25885,25995,26099,26233,26332,26436,26540,26659,26767,26832,26884,26939,26988,27038,27089,27164,27215,27280,27329,27374,27443,27487,27539,27591,27651,27708,27758,27809,27882,27935,27987,28033,28097,28156,28216,28398,28501,28589,28650,28734,28803,28871,28987,29066,29142,29222,29300,29367,29463,29538,29632,29713,29856,29936,30028,30116,30209,30300,30379,30482,30587,30714,30817,30893,30998,31076,31160,31292,31444,31512,31589,31677,31774,33825,33940,34138,34184,34312,34482,34572,34732,34828,34962,35157,35309,35501,35587,35667,35751,35846,35955,36000,36084,36207,36303,36429,36576,36724,36838,36948,37012,37072,37132,37186,37262,37339,37437,37553,37623,37715,37970,38195,38317,38463,38598,38702,38808,38938,39104,39278,39372,39480,39570,39703,39810,39968,40064,40138,40195,40456,40544,40620,40684,40747,40859,40987,41070,41241,41420,41504,41572,41632,41797,41870,41960,42036,42129,42293,42386,42465,42583,42707,42873,42979,43035,43166,43237,43317,43380,43457,43506,43569,43664,43716,43788,43973,44088,44178,44255,44408,44558,44693,44793,45084,45192,45288,45394,45569,45774,45859,46069,46182,46321,46439,46518,46606,46693,46832,46998,47127,47176,47267,47408,47531,47621,47706,47812,47895,47967,48043,48104,48180,48253,48328,48417,48482,48549,48650,48728,48817,49036,49172,49266,49322,49401,49579,49639,49719,49800,49879,49966,50241,50296,50376,50459,50539,50647,50812,50900,50973,51059,51143,51210,51292,51383,51508,51589,51648,51732,51809,51879,51959,52069,52182,52299,52461,52533,52581,52632,52729,52841,52920,53010,53188,53297,53391,53508,53633,53784,53911,54016,54149,54273,54497,54676,54807,54957,55052,55144,55264,55386,55487,55611,55701,56143,56222,56312,56398,56475"}}]}]}