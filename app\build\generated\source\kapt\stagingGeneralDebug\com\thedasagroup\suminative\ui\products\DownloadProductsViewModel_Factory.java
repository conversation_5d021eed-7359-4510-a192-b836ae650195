package com.thedasagroup.suminative.ui.products;

import dagger.internal.DaggerGenerated;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DownloadProductsViewModel_Factory {
  private final Provider<DownloadProductsUseCase> downloadProductsUseCaseProvider;

  public DownloadProductsViewModel_Factory(
      Provider<DownloadProductsUseCase> downloadProductsUseCaseProvider) {
    this.downloadProductsUseCaseProvider = downloadProductsUseCaseProvider;
  }

  public DownloadProductsViewModel get(DownloadProductsState state) {
    return newInstance(state, downloadProductsUseCaseProvider.get());
  }

  public static DownloadProductsViewModel_Factory create(
      Provider<DownloadProductsUseCase> downloadProductsUseCaseProvider) {
    return new DownloadProductsViewModel_Factory(downloadProductsUseCaseProvider);
  }

  public static DownloadProductsViewModel newInstance(DownloadProductsState state,
      DownloadProductsUseCase downloadProductsUseCase) {
    return new DownloadProductsViewModel(state, downloadProductsUseCase);
  }
}
