{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-es-rPE/values-es-rPE.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-es-rPE\\values-es-rPE.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,254,330,415,521,672,859,1016,1143,1247,1387,1450,1512,1601,1708,1784,1931,2047,2130,2281,2358,2455,2556,2682,2790,2897,2994,3065,3138,3254,3350,3563,3745,3842,4007,4111,4298,4401,4545,4647,4785,4891,5010,5112,5215,5354,5448,5589,5690,5847,5929,6073,6149,6267,6365,6444,6527,6597,6668,6735,6806,6927,7031,7187,7286,7404,7500,7644,7746,7849,7975,8066,8173,8273,8377,8478,8542,8596,8651,8707,8756,8806,8880,8934,9003,9056,9100,9165,9214,9270,9326,9389,9444,9496,9546,9617,9667,9719,9764,9827,9887,9945,10059,10137,10223,10301,10368,10465,10540,10627,10714,10832,10911,11004,11081,11180,11271,11352,11443,11544,11652,11745,11822,11916,11991,12106,12251,12327,12404,12498,12544,12728,12816,12916,13105,13183,13266,13382,13427,13511,13619,13711,13836,13963,14070,14183,14249,14309,14370,14426,14554,14709,14888,14981,15081,15169,15288,15397,15558,15649,15729,15787,16045,16139,16225,16290,16478,16555,16635,16727,16803,16896,17065,17161,17245,17356,17438,17584,17646,17716,17782,17842,17918,17970,18169,18282,18347,18508,18639,18759,19028,19129,19226,19322,19405,19478,19645,19783,19867,19954,20035,20129,20209,20286,20362,20443,20517,20594,20689,20760,20835,20904,20992,21162,21319,21405,21459,21540,21612,21706,21790,21871,21951,22006,22081,22162,22234,22324,22506,22592,22666,22751,22830,22927,23020,23158,23241,23294,23359,23437,23547,23650,23754,23885,23964,24015,24110,24239,24409,24507,24660,24783,24961,25143,25265,25395,25483,25571,25677,25791,25864,26257,26331,26417,26499", "endColumns": "198,75,84,105,150,186,156,126,103,139,62,61,88,106,75,146,115,82,150,76,96,100,125,107,106,96,70,72,115,95,212,181,96,164,103,186,102,143,101,137,105,118,101,102,138,93,140,100,156,81,143,75,117,97,78,82,69,70,66,70,120,103,155,98,117,95,143,101,102,125,90,106,99,103,100,63,53,54,55,48,49,73,53,68,52,43,64,48,55,55,62,54,51,49,70,49,51,44,62,59,57,113,77,85,77,66,96,74,86,86,117,78,92,76,98,90,80,90,100,107,92,76,93,74,114,144,75,76,93,45,183,87,99,188,77,82,115,44,83,107,91,124,126,106,112,65,59,60,55,127,154,178,92,99,87,118,108,160,90,79,57,257,93,85,64,187,76,79,91,75,92,168,95,83,110,81,145,61,69,65,59,75,51,198,112,64,160,130,119,268,100,96,95,82,72,166,137,83,86,80,93,79,76,75,80,73,76,94,70,74,68,87,169,156,85,53,80,71,93,83,80,79,54,74,80,71,89,181,85,73,84,78,96,92,137,82,52,64,77,109,102,103,130,78,50,94,128,169,97,152,122,177,181,121,129,87,87,105,113,72,392,73,85,81,76", "endOffsets": "249,325,410,516,667,854,1011,1138,1242,1382,1445,1507,1596,1703,1779,1926,2042,2125,2276,2353,2450,2551,2677,2785,2892,2989,3060,3133,3249,3345,3558,3740,3837,4002,4106,4293,4396,4540,4642,4780,4886,5005,5107,5210,5349,5443,5584,5685,5842,5924,6068,6144,6262,6360,6439,6522,6592,6663,6730,6801,6922,7026,7182,7281,7399,7495,7639,7741,7844,7970,8061,8168,8268,8372,8473,8537,8591,8646,8702,8751,8801,8875,8929,8998,9051,9095,9160,9209,9265,9321,9384,9439,9491,9541,9612,9662,9714,9759,9822,9882,9940,10054,10132,10218,10296,10363,10460,10535,10622,10709,10827,10906,10999,11076,11175,11266,11347,11438,11539,11647,11740,11817,11911,11986,12101,12246,12322,12399,12493,12539,12723,12811,12911,13100,13178,13261,13377,13422,13506,13614,13706,13831,13958,14065,14178,14244,14304,14365,14421,14549,14704,14883,14976,15076,15164,15283,15392,15553,15644,15724,15782,16040,16134,16220,16285,16473,16550,16630,16722,16798,16891,17060,17156,17240,17351,17433,17579,17641,17711,17777,17837,17913,17965,18164,18277,18342,18503,18634,18754,19023,19124,19221,19317,19400,19473,19640,19778,19862,19949,20030,20124,20204,20281,20357,20438,20512,20589,20684,20755,20830,20899,20987,21157,21314,21400,21454,21535,21607,21701,21785,21866,21946,22001,22076,22157,22229,22319,22501,22587,22661,22746,22825,22922,23015,23153,23236,23289,23354,23432,23542,23645,23749,23880,23959,24010,24105,24234,24404,24502,24655,24778,24956,25138,25260,25390,25478,25566,25672,25786,25859,26252,26326,26412,26494,26571"}}]}]}