#Sat Aug 02 20:04:07 PKT 2025
com.thedasagroup.suminative.app-main-103\:/drawable/buy_some_items.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_buy_some_items.xml.flat
com.thedasagroup.suminative.app-main-103\:/drawable/cart_minus.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_cart_minus.xml.flat
com.thedasagroup.suminative.app-main-103\:/drawable/cart_plus.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_cart_plus.xml.flat
com.thedasagroup.suminative.app-main-103\:/drawable/collapse.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_collapse.xml.flat
com.thedasagroup.suminative.app-main-103\:/drawable/dasa_logo.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_dasa_logo.png.flat
com.thedasagroup.suminative.app-main-103\:/drawable/ic_launcher_background.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_ic_launcher_background.xml.flat
com.thedasagroup.suminative.app-main-103\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.thedasagroup.suminative.app-main-103\:/drawable/ic_note.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_ic_note.xml.flat
com.thedasagroup.suminative.app-main-103\:/drawable/profile_icon.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_profile_icon.xml.flat
com.thedasagroup.suminative.app-main-103\:/drawable/store_closed.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_store_closed.png.flat
com.thedasagroup.suminative.app-main-103\:/drawable/trash.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_trash.xml.flat
com.thedasagroup.suminative.app-main-103\:/font/nunito.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\font_nunito.xml.flat
com.thedasagroup.suminative.app-main-103\:/font/nunito_bold.ttf=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\font_nunito_bold.ttf.flat
com.thedasagroup.suminative.app-main-103\:/font/nunito_regular.ttf=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\font_nunito_regular.ttf.flat
com.thedasagroup.suminative.app-main-103\:/font/popins.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\font_popins.xml.flat
com.thedasagroup.suminative.app-main-103\:/font/poppins_bold.ttf=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\font_poppins_bold.ttf.flat
com.thedasagroup.suminative.app-main-103\:/font/poppins_regular.ttf=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\font_poppins_regular.ttf.flat
com.thedasagroup.suminative.app-main-103\:/layout/activity_lcd.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\layout_activity_lcd.xml.flat
com.thedasagroup.suminative.app-main-103\:/layout/activity_tracking.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\layout_activity_tracking.xml.flat
com.thedasagroup.suminative.app-main-103\:/layout/dialog_cancel_order.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\layout_dialog_cancel_order.xml.flat
com.thedasagroup.suminative.app-main-103\:/layout/dialog_new_order.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\layout_dialog_new_order.xml.flat
com.thedasagroup.suminative.app-main-103\:/layout/dialog_update_order.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\layout_dialog_update_order.xml.flat
com.thedasagroup.suminative.app-main-103\:/menu/main_menu.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\menu_main_menu.xml.flat
com.thedasagroup.suminative.app-main-103\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.thedasagroup.suminative.app-main-103\:/mipmap-hdpi/ic_launcher.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-hdpi_ic_launcher.png.flat
com.thedasagroup.suminative.app-main-103\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.thedasagroup.suminative.app-main-103\:/mipmap-mdpi/ic_launcher.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-mdpi_ic_launcher.png.flat
com.thedasagroup.suminative.app-main-103\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.thedasagroup.suminative.app-main-103\:/mipmap-xhdpi/ic_launcher.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-xhdpi_ic_launcher.png.flat
com.thedasagroup.suminative.app-main-103\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.thedasagroup.suminative.app-main-103\:/mipmap-xxhdpi/ic_launcher.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-xxhdpi_ic_launcher.png.flat
com.thedasagroup.suminative.app-main-103\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.thedasagroup.suminative.app-main-103\:/mipmap-xxxhdpi/ic_launcher.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-xxxhdpi_ic_launcher.png.flat
com.thedasagroup.suminative.app-main-103\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.thedasagroup.suminative.app-main-103\:/raw/cancelled_order.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\raw_cancelled_order.mp3.flat
com.thedasagroup.suminative.app-main-103\:/raw/courier_arriving.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\raw_courier_arriving.mp3.flat
com.thedasagroup.suminative.app-main-103\:/raw/courier_assigned.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\raw_courier_assigned.mp3.flat
com.thedasagroup.suminative.app-main-103\:/raw/delivered.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\raw_delivered.mp3.flat
com.thedasagroup.suminative.app-main-103\:/raw/delivery_imminent.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\raw_delivery_imminent.mp3.flat
com.thedasagroup.suminative.app-main-103\:/raw/notification.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\raw_notification.mp3.flat
com.thedasagroup.suminative.app-main-103\:/raw/out_for_delivery.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\raw_out_for_delivery.mp3.flat
com.thedasagroup.suminative.app-main-103\:/raw/schedule_order.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\raw_schedule_order.mp3.flat
com.thedasagroup.suminative.app-main-103\:/xml/backup_rules.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\xml_backup_rules.xml.flat
com.thedasagroup.suminative.app-main-103\:/xml/data_extraction_rules.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\xml_data_extraction_rules.xml.flat
com.thedasagroup.suminative.app-main-110\:/drawable/buy_some_items.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_buy_some_items.xml.flat
com.thedasagroup.suminative.app-main-110\:/drawable/cart_minus.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_cart_minus.xml.flat
com.thedasagroup.suminative.app-main-110\:/drawable/cart_plus.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_cart_plus.xml.flat
com.thedasagroup.suminative.app-main-110\:/drawable/collapse.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_collapse.xml.flat
com.thedasagroup.suminative.app-main-110\:/drawable/dasa_logo.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_dasa_logo.png.flat
com.thedasagroup.suminative.app-main-110\:/drawable/ic_launcher_background.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_ic_launcher_background.xml.flat
com.thedasagroup.suminative.app-main-110\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.thedasagroup.suminative.app-main-110\:/drawable/ic_note.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_ic_note.xml.flat
com.thedasagroup.suminative.app-main-110\:/drawable/profile_icon.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_profile_icon.xml.flat
com.thedasagroup.suminative.app-main-110\:/drawable/store_closed.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_store_closed.png.flat
com.thedasagroup.suminative.app-main-110\:/drawable/trash.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\drawable_trash.xml.flat
com.thedasagroup.suminative.app-main-110\:/font/nunito.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\font_nunito.xml.flat
com.thedasagroup.suminative.app-main-110\:/font/nunito_bold.ttf=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\font_nunito_bold.ttf.flat
com.thedasagroup.suminative.app-main-110\:/font/nunito_regular.ttf=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\font_nunito_regular.ttf.flat
com.thedasagroup.suminative.app-main-110\:/font/popins.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\font_popins.xml.flat
com.thedasagroup.suminative.app-main-110\:/font/poppins_bold.ttf=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\font_poppins_bold.ttf.flat
com.thedasagroup.suminative.app-main-110\:/font/poppins_regular.ttf=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\font_poppins_regular.ttf.flat
com.thedasagroup.suminative.app-main-110\:/layout/activity_lcd.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\layout_activity_lcd.xml.flat
com.thedasagroup.suminative.app-main-110\:/layout/activity_tracking.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\layout_activity_tracking.xml.flat
com.thedasagroup.suminative.app-main-110\:/layout/dialog_cancel_order.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\layout_dialog_cancel_order.xml.flat
com.thedasagroup.suminative.app-main-110\:/layout/dialog_new_order.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\layout_dialog_new_order.xml.flat
com.thedasagroup.suminative.app-main-110\:/layout/dialog_update_order.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\layout_dialog_update_order.xml.flat
com.thedasagroup.suminative.app-main-110\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.thedasagroup.suminative.app-main-110\:/mipmap-hdpi/ic_launcher.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-hdpi_ic_launcher.png.flat
com.thedasagroup.suminative.app-main-110\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.thedasagroup.suminative.app-main-110\:/mipmap-mdpi/ic_launcher.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-mdpi_ic_launcher.png.flat
com.thedasagroup.suminative.app-main-110\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.thedasagroup.suminative.app-main-110\:/mipmap-xhdpi/ic_launcher.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-xhdpi_ic_launcher.png.flat
com.thedasagroup.suminative.app-main-110\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.thedasagroup.suminative.app-main-110\:/mipmap-xxhdpi/ic_launcher.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-xxhdpi_ic_launcher.png.flat
com.thedasagroup.suminative.app-main-110\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.thedasagroup.suminative.app-main-110\:/mipmap-xxxhdpi/ic_launcher.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-xxxhdpi_ic_launcher.png.flat
com.thedasagroup.suminative.app-main-110\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.thedasagroup.suminative.app-main-110\:/raw/cancelled_order.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\raw_cancelled_order.mp3.flat
com.thedasagroup.suminative.app-main-110\:/raw/courier_arriving.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\raw_courier_arriving.mp3.flat
com.thedasagroup.suminative.app-main-110\:/raw/courier_assigned.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\raw_courier_assigned.mp3.flat
com.thedasagroup.suminative.app-main-110\:/raw/delivered.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\raw_delivered.mp3.flat
com.thedasagroup.suminative.app-main-110\:/raw/delivery_imminent.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\raw_delivery_imminent.mp3.flat
com.thedasagroup.suminative.app-main-110\:/raw/notification.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\raw_notification.mp3.flat
com.thedasagroup.suminative.app-main-110\:/raw/out_for_delivery.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\raw_out_for_delivery.mp3.flat
com.thedasagroup.suminative.app-main-110\:/raw/schedule_order.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\raw_schedule_order.mp3.flat
com.thedasagroup.suminative.app-main-110\:/xml/backup_rules.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\xml_backup_rules.xml.flat
com.thedasagroup.suminative.app-main-110\:/xml/data_extraction_rules.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\merged_res\\stagingGeneralDebug\\mergeStagingGeneralDebugResources\\xml_data_extraction_rules.xml.flat
