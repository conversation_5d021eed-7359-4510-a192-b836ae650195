package com.thedasagroup.suminative.ui.reservations;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b#\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u00b9\u0001\u0012\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00070\u0003\u0012\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0003\u0012\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\t0\u0003\u0012\u0014\b\u0002\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u0003\u0012\u0014\b\u0002\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\f0\u0003\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0011\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0013\u001a\u00020\t\u0012\b\b\u0002\u0010\u0014\u001a\u00020\t\u0012\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\u0004\b\u0017\u0010\u0018J\u000f\u0010*\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000f\u0010+\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000f\u0010,\u001a\b\u0012\u0004\u0012\u00020\u00070\u0003H\u00c6\u0003J\u000f\u0010-\u001a\b\u0012\u0004\u0012\u00020\t0\u0003H\u00c6\u0003J\u000f\u0010.\u001a\b\u0012\u0004\u0012\u00020\t0\u0003H\u00c6\u0003J\u0015\u0010/\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u0003H\u00c6\u0003J\u0015\u00100\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\f0\u0003H\u00c6\u0003J\u0010\u00101\u001a\u0004\u0018\u00010\u0011H\u00c6\u0003\u00a2\u0006\u0002\u0010\"J\t\u00102\u001a\u00020\u0011H\u00c6\u0003J\t\u00103\u001a\u00020\tH\u00c6\u0003J\t\u00104\u001a\u00020\tH\u00c6\u0003J\u000b\u00105\u001a\u0004\u0018\u00010\u0016H\u00c6\u0003J\u00c0\u0001\u00106\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00070\u00032\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\u0014\b\u0002\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u00032\u0014\b\u0002\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\f0\u00032\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u00112\b\b\u0002\u0010\u0013\u001a\u00020\t2\b\b\u0002\u0010\u0014\u001a\u00020\t2\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u0016H\u00c6\u0001\u00a2\u0006\u0002\u00107J\u0013\u00108\u001a\u00020\t2\b\u00109\u001a\u0004\u0018\u00010:H\u00d6\u0003J\t\u0010;\u001a\u00020\u0011H\u00d6\u0001J\t\u0010<\u001a\u00020=H\u00d6\u0001R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001aR\u0017\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00070\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001aR\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001aR\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\t0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001aR\u001d\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u001aR\u001d\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\f0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001aR\u0015\u0010\u0010\u001a\u0004\u0018\u00010\u0011\u00a2\u0006\n\n\u0002\u0010#\u001a\u0004\b!\u0010\"R\u0011\u0010\u0012\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010%R\u0011\u0010\u0013\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010&R\u0011\u0010\u0014\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010&R\u0013\u0010\u0015\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010)\u00a8\u0006>"}, d2 = {"Lcom/thedasagroup/suminative/ui/reservations/ReservationsState;", "Lcom/airbnb/mvrx/MavericksState;", "activeReservationsResponse", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/reservations/ReservationsResponse;", "allReservationsResponse", "createReservationResponse", "Lcom/thedasagroup/suminative/data/model/response/reservations/CreateReservationResponse;", "editReservationResponse", "", "cancelReservationResponse", "areasResponse", "", "Lcom/thedasagroup/suminative/data/model/response/reservations/Area;", "tablesResponse", "Lcom/thedasagroup/suminative/data/model/response/reservations/Table;", "selectedAreaId", "", "selectedTabIndex", "isRefreshing", "showEditDialog", "editingReservation", "Lcom/thedasagroup/suminative/ui/reservations/EditReservationData;", "<init>", "(Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Ljava/lang/Integer;IZZLcom/thedasagroup/suminative/ui/reservations/EditReservationData;)V", "getActiveReservationsResponse", "()Lcom/airbnb/mvrx/Async;", "getAllReservationsResponse", "getCreateReservationResponse", "getEditReservationResponse", "getCancelReservationResponse", "getAreasResponse", "getTablesResponse", "getSelectedAreaId", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getSelectedTabIndex", "()I", "()Z", "getShowEditDialog", "getEditingReservation", "()Lcom/thedasagroup/suminative/ui/reservations/EditReservationData;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "component12", "copy", "(Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Ljava/lang/Integer;IZZLcom/thedasagroup/suminative/ui/reservations/EditReservationData;)Lcom/thedasagroup/suminative/ui/reservations/ReservationsState;", "equals", "other", "", "hashCode", "toString", "", "app_stagingGeneralDebug"})
public final class ReservationsState implements com.airbnb.mvrx.MavericksState {
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse> activeReservationsResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse> allReservationsResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse> createReservationResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<java.lang.Boolean> editReservationResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<java.lang.Boolean> cancelReservationResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<java.util.List<com.thedasagroup.suminative.data.model.response.reservations.Area>> areasResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<java.util.List<com.thedasagroup.suminative.data.model.response.reservations.Table>> tablesResponse = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer selectedAreaId = null;
    private final int selectedTabIndex = 0;
    private final boolean isRefreshing = false;
    private final boolean showEditDialog = false;
    @org.jetbrains.annotations.Nullable()
    private final com.thedasagroup.suminative.ui.reservations.EditReservationData editingReservation = null;
    
    public ReservationsState(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse> activeReservationsResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse> allReservationsResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse> createReservationResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<java.lang.Boolean> editReservationResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<java.lang.Boolean> cancelReservationResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.data.model.response.reservations.Area>> areasResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.data.model.response.reservations.Table>> tablesResponse, @org.jetbrains.annotations.Nullable()
    java.lang.Integer selectedAreaId, int selectedTabIndex, boolean isRefreshing, boolean showEditDialog, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.ui.reservations.EditReservationData editingReservation) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse> getActiveReservationsResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse> getAllReservationsResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse> getCreateReservationResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<java.lang.Boolean> getEditReservationResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<java.lang.Boolean> getCancelReservationResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<java.util.List<com.thedasagroup.suminative.data.model.response.reservations.Area>> getAreasResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<java.util.List<com.thedasagroup.suminative.data.model.response.reservations.Table>> getTablesResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getSelectedAreaId() {
        return null;
    }
    
    public final int getSelectedTabIndex() {
        return 0;
    }
    
    public final boolean isRefreshing() {
        return false;
    }
    
    public final boolean getShowEditDialog() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.ui.reservations.EditReservationData getEditingReservation() {
        return null;
    }
    
    public ReservationsState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse> component1() {
        return null;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final boolean component11() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.ui.reservations.EditReservationData component12() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<java.lang.Boolean> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<java.lang.Boolean> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<java.util.List<com.thedasagroup.suminative.data.model.response.reservations.Area>> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<java.util.List<com.thedasagroup.suminative.data.model.response.reservations.Table>> component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component8() {
        return null;
    }
    
    public final int component9() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.reservations.ReservationsState copy(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse> activeReservationsResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse> allReservationsResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse> createReservationResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<java.lang.Boolean> editReservationResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<java.lang.Boolean> cancelReservationResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.data.model.response.reservations.Area>> areasResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.data.model.response.reservations.Table>> tablesResponse, @org.jetbrains.annotations.Nullable()
    java.lang.Integer selectedAreaId, int selectedTabIndex, boolean isRefreshing, boolean showEditDialog, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.ui.reservations.EditReservationData editingReservation) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}