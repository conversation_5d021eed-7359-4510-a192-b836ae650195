{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-en-rAU/values-en-rAU.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "38,42,43,44", "startColumns": "4,4,4,4", "startOffsets": "3655,4033,4130,4239", "endColumns": "97,96,108,98", "endOffsets": "3748,4125,4234,4333"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,559,641,730,819,903,981,1063,1136,1212,1284,1354,1431,1497", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,75,71,69,76,65,120", "endOffsets": "192,274,368,467,554,636,725,814,898,976,1058,1131,1207,1279,1349,1426,1492,1613"}, "to": {"startLines": "36,37,39,40,41,45,46,103,104,105,106,108,109,110,111,378,379,380", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3481,3573,3753,3847,3946,4338,4420,10518,10607,10691,10769,10934,11007,11083,11155,36390,36467,36533", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,75,71,69,76,65,120", "endOffsets": "3568,3650,3842,3941,4028,4415,4504,10602,10686,10764,10846,11002,11078,11150,11220,36462,36528,36649"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,400,514,613,708,820,956,1072,1208,1292,1391,1482,1579,1698,1823,1927,2054,2177,2305,2466,2587,2703,2826,2951,3043,3141,3258,3382,3479,3581,3683,3813,3952,4058,4157,4235,4331,4425,4512,4599,4701,4783,4867,4968,5069,5169,5268,5356,5462,5563,5667,5787,5869,5969", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,160,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,77,95,93,86,86,101,81,83,100,100,99,98,87,105,100,103,119,81,99,94", "endOffsets": "168,284,395,509,608,703,815,951,1067,1203,1287,1386,1477,1574,1693,1818,1922,2049,2172,2300,2461,2582,2698,2821,2946,3038,3136,3253,3377,3474,3576,3678,3808,3947,4053,4152,4230,4326,4420,4507,4594,4696,4778,4862,4963,5064,5164,5263,5351,5457,5558,5662,5782,5864,5964,6059"}, "to": {"startLines": "47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4509,4627,4743,4854,4968,5067,5162,5274,5410,5526,5662,5746,5845,5936,6033,6152,6277,6381,6508,6631,6759,6920,7041,7157,7280,7405,7497,7595,7712,7836,7933,8035,8137,8267,8406,8512,8611,8689,8785,8879,8966,9053,9155,9237,9321,9422,9523,9623,9722,9810,9916,10017,10121,10241,10323,10423", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,160,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,77,95,93,86,86,101,81,83,100,100,99,98,87,105,100,103,119,81,99,94", "endOffsets": "4622,4738,4849,4963,5062,5157,5269,5405,5521,5657,5741,5840,5931,6028,6147,6272,6376,6503,6626,6754,6915,7036,7152,7275,7400,7492,7590,7707,7831,7928,8030,8132,8262,8401,8507,8606,8684,8780,8874,8961,9048,9150,9232,9316,9417,9518,9618,9717,9805,9911,10012,10116,10236,10318,10418,10513"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "381,382", "startColumns": "4,4", "startOffsets": "36654,36740", "endColumns": "85,84", "endOffsets": "36735,36820"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "29,30,31,32,33,34,35,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2762,2858,2960,3059,3158,3262,3365,11225", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "2853,2955,3054,3153,3257,3360,3476,11321"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,10851", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,10929"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,231,307,386,498,620,795,935,1041,1139,1269,1328,1385,1466,1555,1625,1766,1858,1938,2093,2168,2255,2343,2470,2566,2667,2762,2832,2901,3018,3104,3299,3462,3565,3709,3808,3980,4082,4213,4317,4451,4549,4671,4766,4862,4987,5077,5201,5295,5454,5536,5654,5729,5839,5940,6014,6098,6170,6240,6317,6389,6503,6592,6735,6835,6951,7044,7186,7281,7377,7488,7578,7681,7787,7898,7999,8059,8111,8165,8219,8267,8315,8381,8431,8491,8539,8583,8649,8693,8749,8803,8855,8905,8953,9001,9069,9117,9169,9215,9278,9335,9388,9500,9579,9659,9737,9804,9901,9975,10059,10137,10258,10333,10425,10506,10599,10692,10771,10873,10977,11089,11193,11270,11357,11439,11565,11707,11773,11850,11937,11983,12117,12201,12289,12459,12531,12607,12719,12763,12845,12960,13043,13166,13292,13396,13500,13566,13622,13676,13729,13850,13990,14136,14223,14322,14406,14514,14612,14749,14837,14911,14966,15192,15278,15354,15411,15563,15629,15704,15797,15874,15968,16123,16212,16293,16409,16486,16578,16632,16702,16776,16835,16910,16962,17130,17253,17317,17444,17560,17668,17922,18018,18118,18219,18309,18388,18537,18670,18751,18827,18901,18993,19076,19146,19212,19293,19362,19436,19524,19591,19674,19743,19822,19993,20137,20224,20281,20362,20421,20499,20577,20653,20729,20784,20858,20936,21002,21086,21251,21332,21401,21486,21565,21644,21730,21854,21934,21984,22055,22128,22238,22341,22444,22561,22641,22691,22779,22906,23047,23147,23286,23413,23588,23753,23862,23991,24080,24164,24263,24389,24456,24800,24881,24967,25042", "endColumns": "175,75,78,111,121,174,139,105,97,129,58,56,80,88,69,140,91,79,154,74,86,87,126,95,100,94,69,68,116,85,194,162,102,143,98,171,101,130,103,133,97,121,94,95,124,89,123,93,158,81,117,74,109,100,73,83,71,69,76,71,113,88,142,99,115,92,141,94,95,110,89,102,105,110,100,59,51,53,53,47,47,65,49,59,47,43,65,43,55,53,51,49,47,47,67,47,51,45,62,56,52,111,78,79,77,66,96,73,83,77,120,74,91,80,92,92,78,101,103,111,103,76,86,81,125,141,65,76,86,45,133,83,87,169,71,75,111,43,81,114,82,122,125,103,103,65,55,53,52,120,139,145,86,98,83,107,97,136,87,73,54,225,85,75,56,151,65,74,92,76,93,154,88,80,115,76,91,53,69,73,58,74,51,167,122,63,126,115,107,253,95,99,100,89,78,148,132,80,75,73,91,82,69,65,80,68,73,87,66,82,68,78,170,143,86,56,80,58,77,77,75,75,54,73,77,65,83,164,80,68,84,78,78,85,123,79,49,70,72,109,102,102,116,79,49,87,126,140,99,138,126,174,164,108,128,88,83,98,125,66,343,80,85,74,76", "endOffsets": "226,302,381,493,615,790,930,1036,1134,1264,1323,1380,1461,1550,1620,1761,1853,1933,2088,2163,2250,2338,2465,2561,2662,2757,2827,2896,3013,3099,3294,3457,3560,3704,3803,3975,4077,4208,4312,4446,4544,4666,4761,4857,4982,5072,5196,5290,5449,5531,5649,5724,5834,5935,6009,6093,6165,6235,6312,6384,6498,6587,6730,6830,6946,7039,7181,7276,7372,7483,7573,7676,7782,7893,7994,8054,8106,8160,8214,8262,8310,8376,8426,8486,8534,8578,8644,8688,8744,8798,8850,8900,8948,8996,9064,9112,9164,9210,9273,9330,9383,9495,9574,9654,9732,9799,9896,9970,10054,10132,10253,10328,10420,10501,10594,10687,10766,10868,10972,11084,11188,11265,11352,11434,11560,11702,11768,11845,11932,11978,12112,12196,12284,12454,12526,12602,12714,12758,12840,12955,13038,13161,13287,13391,13495,13561,13617,13671,13724,13845,13985,14131,14218,14317,14401,14509,14607,14744,14832,14906,14961,15187,15273,15349,15406,15558,15624,15699,15792,15869,15963,16118,16207,16288,16404,16481,16573,16627,16697,16771,16830,16905,16957,17125,17248,17312,17439,17555,17663,17917,18013,18113,18214,18304,18383,18532,18665,18746,18822,18896,18988,19071,19141,19207,19288,19357,19431,19519,19586,19669,19738,19817,19988,20132,20219,20276,20357,20416,20494,20572,20648,20724,20779,20853,20931,20997,21081,21246,21327,21396,21481,21560,21639,21725,21849,21929,21979,22050,22123,22233,22336,22439,22556,22636,22686,22774,22901,23042,23142,23281,23408,23583,23748,23857,23986,24075,24159,24258,24384,24451,24795,24876,24962,25037,25114"}, "to": {"startLines": "113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11326,11502,11578,11657,11769,11891,12066,12206,12312,12410,12540,12599,12656,12737,12826,12896,13037,13129,13209,13364,13439,13526,13614,13741,13837,13938,14033,14103,14172,14289,14375,14570,14733,14836,14980,15079,15251,15353,15484,15588,15722,15820,15942,16037,16133,16258,16348,16472,16566,16725,16807,16925,17000,17110,17211,17285,17369,17441,17511,17588,17660,17774,17863,18006,18106,18222,18315,18457,18552,18648,18759,18849,18952,19058,19169,19270,19330,19382,19436,19490,19538,19586,19652,19702,19762,19810,19854,19920,19964,20020,20074,20126,20176,20224,20272,20340,20388,20440,20486,20549,20606,20659,20771,20850,20930,21008,21075,21172,21246,21330,21408,21529,21604,21696,21777,21870,21963,22042,22144,22248,22360,22464,22541,22628,22710,22836,22978,23044,23121,23208,23254,23388,23472,23560,23730,23802,23878,23990,24034,24116,24231,24314,24437,24563,24667,24771,24837,24893,24947,25000,25121,25261,25407,25494,25593,25677,25785,25883,26020,26108,26182,26237,26463,26549,26625,26682,26834,26900,26975,27068,27145,27239,27394,27483,27564,27680,27757,27849,27903,27973,28047,28106,28181,28233,28401,28524,28588,28715,28831,28939,29193,29289,29389,29490,29580,29659,29808,29941,30022,30098,30172,30264,30347,30417,30483,30564,30633,30707,30795,30862,30945,31014,31093,31264,31408,31495,31552,31633,31692,31770,31848,31924,32000,32055,32129,32207,32273,32357,32522,32603,32672,32757,32836,32915,33001,33125,33205,33255,33326,33399,33509,33612,33715,33832,33912,33962,34050,34177,34318,34418,34557,34684,34859,35024,35133,35262,35351,35435,35534,35660,35727,36071,36152,36238,36313", "endColumns": "175,75,78,111,121,174,139,105,97,129,58,56,80,88,69,140,91,79,154,74,86,87,126,95,100,94,69,68,116,85,194,162,102,143,98,171,101,130,103,133,97,121,94,95,124,89,123,93,158,81,117,74,109,100,73,83,71,69,76,71,113,88,142,99,115,92,141,94,95,110,89,102,105,110,100,59,51,53,53,47,47,65,49,59,47,43,65,43,55,53,51,49,47,47,67,47,51,45,62,56,52,111,78,79,77,66,96,73,83,77,120,74,91,80,92,92,78,101,103,111,103,76,86,81,125,141,65,76,86,45,133,83,87,169,71,75,111,43,81,114,82,122,125,103,103,65,55,53,52,120,139,145,86,98,83,107,97,136,87,73,54,225,85,75,56,151,65,74,92,76,93,154,88,80,115,76,91,53,69,73,58,74,51,167,122,63,126,115,107,253,95,99,100,89,78,148,132,80,75,73,91,82,69,65,80,68,73,87,66,82,68,78,170,143,86,56,80,58,77,77,75,75,54,73,77,65,83,164,80,68,84,78,78,85,123,79,49,70,72,109,102,102,116,79,49,87,126,140,99,138,126,174,164,108,128,88,83,98,125,66,343,80,85,74,76", "endOffsets": "11497,11573,11652,11764,11886,12061,12201,12307,12405,12535,12594,12651,12732,12821,12891,13032,13124,13204,13359,13434,13521,13609,13736,13832,13933,14028,14098,14167,14284,14370,14565,14728,14831,14975,15074,15246,15348,15479,15583,15717,15815,15937,16032,16128,16253,16343,16467,16561,16720,16802,16920,16995,17105,17206,17280,17364,17436,17506,17583,17655,17769,17858,18001,18101,18217,18310,18452,18547,18643,18754,18844,18947,19053,19164,19265,19325,19377,19431,19485,19533,19581,19647,19697,19757,19805,19849,19915,19959,20015,20069,20121,20171,20219,20267,20335,20383,20435,20481,20544,20601,20654,20766,20845,20925,21003,21070,21167,21241,21325,21403,21524,21599,21691,21772,21865,21958,22037,22139,22243,22355,22459,22536,22623,22705,22831,22973,23039,23116,23203,23249,23383,23467,23555,23725,23797,23873,23985,24029,24111,24226,24309,24432,24558,24662,24766,24832,24888,24942,24995,25116,25256,25402,25489,25588,25672,25780,25878,26015,26103,26177,26232,26458,26544,26620,26677,26829,26895,26970,27063,27140,27234,27389,27478,27559,27675,27752,27844,27898,27968,28042,28101,28176,28228,28396,28519,28583,28710,28826,28934,29188,29284,29384,29485,29575,29654,29803,29936,30017,30093,30167,30259,30342,30412,30478,30559,30628,30702,30790,30857,30940,31009,31088,31259,31403,31490,31547,31628,31687,31765,31843,31919,31995,32050,32124,32202,32268,32352,32517,32598,32667,32752,32831,32910,32996,33120,33200,33250,33321,33394,33504,33607,33710,33827,33907,33957,34045,34172,34313,34413,34552,34679,34854,35019,35128,35257,35346,35430,35529,35655,35722,36066,36147,36233,36308,36385"}}]}]}