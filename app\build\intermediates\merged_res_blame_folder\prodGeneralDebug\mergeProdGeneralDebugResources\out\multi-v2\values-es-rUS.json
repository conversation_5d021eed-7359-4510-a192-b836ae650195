{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,384,487,576,655,751,843,930,1017,1107,1184,1260,1340,1416,1494,1564", "endColumns": "98,81,97,102,88,78,95,91,86,86,89,76,75,79,75,77,69,122", "endOffsets": "199,281,379,482,571,650,746,838,925,1012,1102,1179,1255,1335,1411,1489,1559,1682"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,539,540,541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4536,4635,4972,5070,5173,5877,5956,17105,17197,17367,17454,17786,17940,18016,18096,56651,56729,56799", "endColumns": "98,81,97,102,88,78,95,91,86,86,89,76,75,79,75,77,69,122", "endOffsets": "4630,4712,5065,5168,5257,5951,6047,17192,17279,17449,17539,17858,18011,18091,18167,56724,56794,56917"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,448,557,665,750,852,968,1053,1133,1224,1317,1412,1506,1605,1698,1797,1893,1984,2075,2157,2264,2363,2462,2570,2678,2785,2944,17544", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "443,552,660,745,847,963,1048,1128,1219,1312,1407,1501,1600,1693,1792,1888,1979,2070,2152,2259,2358,2457,2565,2673,2780,2939,3039,17622"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,266,381", "endColumns": "108,101,114,105", "endOffsets": "159,261,376,482"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4863,5484,5586,5701", "endColumns": "108,101,114,105", "endOffsets": "4967,5581,5696,5802"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,299,422,542,642,740,855,998,1116,1268,1353,1455,1552,1654,1772,1895,2002,2138,2271,2410,2592,2723,2843,2965,3092,3190,3286,3407,3540,3641,3746,3861,3996,4137,4248,4353,4430,4526,4621,4708,4797,4908,4988,5072,5173,5279,5379,5478,5566,5681,5782,5886,6009,6089,6196", "endColumns": "121,121,122,119,99,97,114,142,117,151,84,101,96,101,117,122,106,135,132,138,181,130,119,121,126,97,95,120,132,100,104,114,134,140,110,104,76,95,94,86,88,110,79,83,100,105,99,98,87,114,100,103,122,79,106,98", "endOffsets": "172,294,417,537,637,735,850,993,1111,1263,1348,1450,1547,1649,1767,1890,1997,2133,2266,2405,2587,2718,2838,2960,3087,3185,3281,3402,3535,3636,3741,3856,3991,4132,4243,4348,4425,4521,4616,4703,4792,4903,4983,5067,5168,5274,5374,5473,5561,5676,5777,5881,6004,6084,6191,6290"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6115,6237,6359,6482,6602,6702,6800,6915,7058,7176,7328,7413,7515,7612,7714,7832,7955,8062,8198,8331,8470,8652,8783,8903,9025,9152,9250,9346,9467,9600,9701,9806,9921,10056,10197,10308,10413,10490,10586,10681,10768,10857,10968,11048,11132,11233,11339,11439,11538,11626,11741,11842,11946,12069,12149,12256", "endColumns": "121,121,122,119,99,97,114,142,117,151,84,101,96,101,117,122,106,135,132,138,181,130,119,121,126,97,95,120,132,100,104,114,134,140,110,104,76,95,94,86,88,110,79,83,100,105,99,98,87,114,100,103,122,79,106,98", "endOffsets": "6232,6354,6477,6597,6697,6795,6910,7053,7171,7323,7408,7510,7607,7709,7827,7950,8057,8193,8326,8465,8647,8778,8898,9020,9147,9245,9341,9462,9595,9696,9801,9916,10051,10192,10303,10408,10485,10581,10676,10763,10852,10963,11043,11127,11228,11334,11434,11533,11621,11736,11837,11941,12064,12144,12251,12350"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "56922,57022", "endColumns": "99,101", "endOffsets": "57017,57119"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,254,404,480,571,677,832,1018,1175,1302,1406,1546,1613,1676,1765,1872,1948,2093,2209,2292,2443,2520,2617,2718,2844,2952,3059,3156,3227,3300,3422,3518,3731,3913,4015,4161,4265,4459,4573,4717,4822,4962,5078,5214,5316,5419,5556,5659,5798,5908,6070,6152,6296,6372,6504,6608,6687,6765,6835,6906,6978,7049,7174,7282,7438,7535,7645,7751,7900,8002,8103,8239,8333,8441,8546,8660,8761,8825,8879,8934,8990,9039,9089,9157,9211,9280,9333,9377,9447,9496,9552,9608,9671,9726,9778,9828,9896,9946,9998,10043,10106,10166,10224,10359,10473,10551,10612,10697,10766,10834,10944,11030,11105,11185,11263,11330,11427,11502,11593,11681,11819,11898,11990,12072,12168,12272,12353,12449,12550,12658,12756,12833,12927,12996,13076,13196,13346,13422,13499,13598,13691,15742,15857,16067,16113,16293,16480,16568,16713,16813,16953,17137,17271,17456,17533,17616,17686,17778,17894,17939,18023,18136,18228,18353,18480,18618,18725,18838,18904,18964,19025,19081,19147,19230,19324,19442,19513,19592,19862,20103,20235,20366,20507,20600,20739,20867,21022,21198,21291,21391,21479,21598,21706,21864,21955,22035,22096,22354,22448,22534,22600,22665,22787,22920,23036,23203,23394,23471,23541,23606,23792,23872,23964,24040,24133,24301,24397,24481,24597,24690,24841,24960,25022,25169,25241,25312,25372,25448,25497,25562,25666,25718,25791,25990,26108,26204,26269,26433,26567,26698,26818,27092,27193,27290,27386,27570,27774,27870,28066,28183,28328,28436,28515,28604,28676,28848,29033,29171,29220,29304,29461,29591,29678,29759,29853,29933,30010,30086,30149,30230,30304,30381,30476,30544,30615,30699,30768,30856,31036,31193,31279,31333,31416,31561,31633,31727,31811,31892,31974,32237,32292,32367,32445,32526,32627,32809,32895,32969,33054,33133,33206,33303,33394,33532,33615,33668,33749,33826,33891,33969,34079,34182,34288,34410,34489,34538,34589,34684,34804,34890,35009,35176,35289,35374,35511,35641,35800,35926,36036,36170,36299,36493,36676,36807,36942,37030,37139,37269,37388,37497,37623,37696,38111,38190,38276,38356", "endColumns": "198,149,75,90,105,154,185,156,126,103,139,66,62,88,106,75,144,115,82,150,76,96,100,125,107,106,96,70,72,121,95,212,181,101,145,103,193,113,143,104,139,115,135,101,102,136,102,138,109,161,81,143,75,131,103,78,77,69,70,71,70,124,107,155,96,109,105,148,101,100,135,93,107,104,113,100,63,53,54,55,48,49,67,53,68,52,43,69,48,55,55,62,54,51,49,67,49,51,44,62,59,57,134,113,77,60,84,68,67,109,85,74,79,77,66,96,74,90,87,137,78,91,81,95,103,80,95,100,107,97,76,93,68,79,119,149,75,76,98,92,2050,114,209,45,179,186,87,144,99,139,183,133,184,76,82,69,91,115,44,83,112,91,124,126,137,106,112,65,59,60,55,65,82,93,117,70,78,269,240,131,130,140,92,138,127,154,175,92,99,87,118,107,157,90,79,60,257,93,85,65,64,121,132,115,166,190,76,69,64,185,79,91,75,92,167,95,83,115,92,150,118,61,146,71,70,59,75,48,64,103,51,72,198,117,95,64,163,133,130,119,273,100,96,95,183,203,95,195,116,144,107,78,88,71,171,184,137,48,83,156,129,86,80,93,79,76,75,62,80,73,76,94,67,70,83,68,87,179,156,85,53,82,144,71,93,83,80,81,262,54,74,77,80,100,181,85,73,84,78,72,96,90,137,82,52,80,76,64,77,109,102,105,121,78,48,50,94,119,85,118,166,112,84,136,129,158,125,109,133,128,193,182,130,134,87,108,129,118,108,125,72,414,78,85,79,76", "endOffsets": "249,399,475,566,672,827,1013,1170,1297,1401,1541,1608,1671,1760,1867,1943,2088,2204,2287,2438,2515,2612,2713,2839,2947,3054,3151,3222,3295,3417,3513,3726,3908,4010,4156,4260,4454,4568,4712,4817,4957,5073,5209,5311,5414,5551,5654,5793,5903,6065,6147,6291,6367,6499,6603,6682,6760,6830,6901,6973,7044,7169,7277,7433,7530,7640,7746,7895,7997,8098,8234,8328,8436,8541,8655,8756,8820,8874,8929,8985,9034,9084,9152,9206,9275,9328,9372,9442,9491,9547,9603,9666,9721,9773,9823,9891,9941,9993,10038,10101,10161,10219,10354,10468,10546,10607,10692,10761,10829,10939,11025,11100,11180,11258,11325,11422,11497,11588,11676,11814,11893,11985,12067,12163,12267,12348,12444,12545,12653,12751,12828,12922,12991,13071,13191,13341,13417,13494,13593,13686,15737,15852,16062,16108,16288,16475,16563,16708,16808,16948,17132,17266,17451,17528,17611,17681,17773,17889,17934,18018,18131,18223,18348,18475,18613,18720,18833,18899,18959,19020,19076,19142,19225,19319,19437,19508,19587,19857,20098,20230,20361,20502,20595,20734,20862,21017,21193,21286,21386,21474,21593,21701,21859,21950,22030,22091,22349,22443,22529,22595,22660,22782,22915,23031,23198,23389,23466,23536,23601,23787,23867,23959,24035,24128,24296,24392,24476,24592,24685,24836,24955,25017,25164,25236,25307,25367,25443,25492,25557,25661,25713,25786,25985,26103,26199,26264,26428,26562,26693,26813,27087,27188,27285,27381,27565,27769,27865,28061,28178,28323,28431,28510,28599,28671,28843,29028,29166,29215,29299,29456,29586,29673,29754,29848,29928,30005,30081,30144,30225,30299,30376,30471,30539,30610,30694,30763,30851,31031,31188,31274,31328,31411,31556,31628,31722,31806,31887,31969,32232,32287,32362,32440,32521,32622,32804,32890,32964,33049,33128,33201,33298,33389,33527,33610,33663,33744,33821,33886,33964,34074,34177,34283,34405,34484,34533,34584,34679,34799,34885,35004,35171,35284,35369,35506,35636,35795,35921,36031,36165,36294,36488,36671,36802,36937,37025,37134,37264,37383,37492,37618,37691,38106,38185,38271,38351,38428"}, "to": {"startLines": "193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18273,18472,18622,18698,18789,18895,19050,19236,19393,19520,19624,19764,19831,19894,19983,20090,20166,20311,20427,20510,20661,20738,20835,20936,21062,21170,21277,21374,21445,21518,21640,21736,21949,22131,22233,22379,22483,22677,22791,22935,23040,23180,23296,23432,23534,23637,23774,23877,24016,24126,24288,24370,24514,24590,24722,24826,24905,24983,25053,25124,25196,25267,25392,25500,25656,25753,25863,25969,26118,26220,26321,26457,26551,26659,26764,26878,26979,27043,27097,27152,27208,27257,27307,27375,27429,27498,27551,27595,27665,27714,27770,27826,27889,27944,27996,28046,28114,28164,28216,28261,28324,28384,28442,28577,28691,28769,28830,28915,28984,29052,29162,29248,29323,29403,29481,29548,29645,29720,29811,29899,30037,30116,30208,30290,30386,30490,30571,30667,30768,30876,30974,31051,31145,31214,31294,31414,31564,31640,31717,31816,31909,33960,34075,34285,34331,34511,34698,34786,34931,35031,35171,35355,35489,35674,35751,35834,35904,35996,36112,36157,36241,36354,36446,36571,36698,36836,36943,37056,37122,37182,37243,37299,37365,37448,37542,37660,37731,37810,38080,38321,38453,38584,38725,38818,38957,39085,39240,39416,39509,39609,39697,39816,39924,40082,40173,40253,40314,40572,40666,40752,40818,40883,41005,41138,41254,41421,41612,41689,41759,41824,42010,42090,42182,42258,42351,42519,42615,42699,42815,42908,43059,43178,43240,43387,43459,43530,43590,43666,43715,43780,43884,43936,44009,44208,44326,44422,44487,44651,44785,44916,45036,45310,45411,45508,45604,45788,45992,46088,46284,46401,46546,46654,46733,46822,46894,47066,47251,47389,47438,47522,47679,47809,47896,47977,48071,48151,48228,48304,48367,48448,48522,48599,48694,48762,48833,48917,48986,49074,49254,49411,49497,49551,49634,49779,49851,49945,50029,50110,50192,50455,50510,50585,50663,50744,50845,51027,51113,51187,51272,51351,51424,51521,51612,51750,51833,51886,51967,52044,52109,52187,52297,52400,52506,52628,52707,52756,52807,52902,53022,53108,53227,53394,53507,53592,53729,53859,54018,54144,54254,54388,54517,54711,54894,55025,55160,55248,55357,55487,55606,55715,55841,55914,56329,56408,56494,56574", "endColumns": "198,149,75,90,105,154,185,156,126,103,139,66,62,88,106,75,144,115,82,150,76,96,100,125,107,106,96,70,72,121,95,212,181,101,145,103,193,113,143,104,139,115,135,101,102,136,102,138,109,161,81,143,75,131,103,78,77,69,70,71,70,124,107,155,96,109,105,148,101,100,135,93,107,104,113,100,63,53,54,55,48,49,67,53,68,52,43,69,48,55,55,62,54,51,49,67,49,51,44,62,59,57,134,113,77,60,84,68,67,109,85,74,79,77,66,96,74,90,87,137,78,91,81,95,103,80,95,100,107,97,76,93,68,79,119,149,75,76,98,92,2050,114,209,45,179,186,87,144,99,139,183,133,184,76,82,69,91,115,44,83,112,91,124,126,137,106,112,65,59,60,55,65,82,93,117,70,78,269,240,131,130,140,92,138,127,154,175,92,99,87,118,107,157,90,79,60,257,93,85,65,64,121,132,115,166,190,76,69,64,185,79,91,75,92,167,95,83,115,92,150,118,61,146,71,70,59,75,48,64,103,51,72,198,117,95,64,163,133,130,119,273,100,96,95,183,203,95,195,116,144,107,78,88,71,171,184,137,48,83,156,129,86,80,93,79,76,75,62,80,73,76,94,67,70,83,68,87,179,156,85,53,82,144,71,93,83,80,81,262,54,74,77,80,100,181,85,73,84,78,72,96,90,137,82,52,80,76,64,77,109,102,105,121,78,48,50,94,119,85,118,166,112,84,136,129,158,125,109,133,128,193,182,130,134,87,108,129,118,108,125,72,414,78,85,79,76", "endOffsets": "18467,18617,18693,18784,18890,19045,19231,19388,19515,19619,19759,19826,19889,19978,20085,20161,20306,20422,20505,20656,20733,20830,20931,21057,21165,21272,21369,21440,21513,21635,21731,21944,22126,22228,22374,22478,22672,22786,22930,23035,23175,23291,23427,23529,23632,23769,23872,24011,24121,24283,24365,24509,24585,24717,24821,24900,24978,25048,25119,25191,25262,25387,25495,25651,25748,25858,25964,26113,26215,26316,26452,26546,26654,26759,26873,26974,27038,27092,27147,27203,27252,27302,27370,27424,27493,27546,27590,27660,27709,27765,27821,27884,27939,27991,28041,28109,28159,28211,28256,28319,28379,28437,28572,28686,28764,28825,28910,28979,29047,29157,29243,29318,29398,29476,29543,29640,29715,29806,29894,30032,30111,30203,30285,30381,30485,30566,30662,30763,30871,30969,31046,31140,31209,31289,31409,31559,31635,31712,31811,31904,33955,34070,34280,34326,34506,34693,34781,34926,35026,35166,35350,35484,35669,35746,35829,35899,35991,36107,36152,36236,36349,36441,36566,36693,36831,36938,37051,37117,37177,37238,37294,37360,37443,37537,37655,37726,37805,38075,38316,38448,38579,38720,38813,38952,39080,39235,39411,39504,39604,39692,39811,39919,40077,40168,40248,40309,40567,40661,40747,40813,40878,41000,41133,41249,41416,41607,41684,41754,41819,42005,42085,42177,42253,42346,42514,42610,42694,42810,42903,43054,43173,43235,43382,43454,43525,43585,43661,43710,43775,43879,43931,44004,44203,44321,44417,44482,44646,44780,44911,45031,45305,45406,45503,45599,45783,45987,46083,46279,46396,46541,46649,46728,46817,46889,47061,47246,47384,47433,47517,47674,47804,47891,47972,48066,48146,48223,48299,48362,48443,48517,48594,48689,48757,48828,48912,48981,49069,49249,49406,49492,49546,49629,49774,49846,49940,50024,50105,50187,50450,50505,50580,50658,50739,50840,51022,51108,51182,51267,51346,51419,51516,51607,51745,51828,51881,51962,52039,52104,52182,52292,52395,52501,52623,52702,52751,52802,52897,53017,53103,53222,53389,53502,53587,53724,53854,54013,54139,54249,54383,54512,54706,54889,55020,55155,55243,55352,55482,55601,55710,55836,55909,56324,56403,56489,56569,56646"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3493,3592,3694,3794,3892,3999,4105,18172", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3587,3689,3789,3887,3994,4100,4220,18268"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-es-rUS\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "141", "endOffsets": "340"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4717", "endColumns": "145", "endOffsets": "4858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,360,440,526,631,727,829,957,1038,1100,1165,1260,1330,1393,1486,1550,1622,1685,1759,1823,1879,1997,2055,2117,2173,2253,2387,2476,2552,2650,2731,2812,2953,3034,3114,3265,3355,3432,3488,3544,3610,3689,3771,3842,3931,4004,4081,4151,4228,4334,4423,4497,4591,4693,4765,4846,4950,5003,5088,5155,5248,5337,5399,5463,5526,5594,5705,5816,5918,6023,6083,6143,6226,6309,6385", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,79,85,104,95,101,127,80,61,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,75,97,80,80,140,80,79,150,89,76,55,55,65,78,81,70,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,110,101,104,59,59,82,82,75,76", "endOffsets": "273,355,435,521,626,722,824,952,1033,1095,1160,1255,1325,1388,1481,1545,1617,1680,1754,1818,1874,1992,2050,2112,2168,2248,2382,2471,2547,2645,2726,2807,2948,3029,3109,3260,3350,3427,3483,3539,3605,3684,3766,3837,3926,3999,4076,4146,4223,4329,4418,4492,4586,4688,4760,4841,4945,4998,5083,5150,5243,5332,5394,5458,5521,5589,5700,5811,5913,6018,6078,6138,6221,6304,6380,6457"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3044,3126,3206,3292,3397,4225,4327,4455,5262,5324,5389,5807,6052,12355,12448,12512,12584,12647,12721,12785,12841,12959,13017,13079,13135,13215,13349,13438,13514,13612,13693,13774,13915,13996,14076,14227,14317,14394,14450,14506,14572,14651,14733,14804,14893,14966,15043,15113,15190,15296,15385,15459,15553,15655,15727,15808,15912,15965,16050,16117,16210,16299,16361,16425,16488,16556,16667,16778,16880,16985,17045,17284,17627,17710,17863", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,81,79,85,104,95,101,127,80,61,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,75,97,80,80,140,80,79,150,89,76,55,55,65,78,81,70,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,110,101,104,59,59,82,82,75,76", "endOffsets": "323,3121,3201,3287,3392,3488,4322,4450,4531,5319,5384,5479,5872,6110,12443,12507,12579,12642,12716,12780,12836,12954,13012,13074,13130,13210,13344,13433,13509,13607,13688,13769,13910,13991,14071,14222,14312,14389,14445,14501,14567,14646,14728,14799,14888,14961,15038,15108,15185,15291,15380,15454,15548,15650,15722,15803,15907,15960,16045,16112,16205,16294,16356,16420,16483,16551,16662,16773,16875,16980,17040,17100,17362,17705,17781,17935"}}]}]}