package com.thedasagroup.suminative.ui.stock;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B=\u0012\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\u0004\b\n\u0010\u000bJ\u000f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0007H\u00c6\u0003J\u000b\u0010\u0016\u001a\u0004\u0018\u00010\tH\u00c6\u0003J?\u0010\u0017\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\tH\u00c6\u0001J\u0013\u0010\u0018\u001a\u00020\u00192\b\u0010\u001a\u001a\u0004\u0018\u00010\u001bH\u00d6\u0003J\t\u0010\u001c\u001a\u00020\u0007H\u00d6\u0001J\t\u0010\u001d\u001a\u00020\u001eH\u00d6\u0001R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\rR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0013\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012\u00a8\u0006\u001f"}, d2 = {"Lcom/thedasagroup/suminative/ui/stock/StockScreenState;", "Lcom/airbnb/mvrx/MavericksState;", "stockItemsResponse", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/stock/StockItemsResponse;", "stockResponse", "stock", "", "showUpdateStockDialog", "Lcom/thedasagroup/suminative/data/model/response/stock/StockItem;", "<init>", "(Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;ILcom/thedasagroup/suminative/data/model/response/stock/StockItem;)V", "getStockItemsResponse", "()Lcom/airbnb/mvrx/Async;", "getStockResponse", "getStock", "()I", "getShowUpdateStockDialog", "()Lcom/thedasagroup/suminative/data/model/response/stock/StockItem;", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "", "hashCode", "toString", "", "app_stagingGeneralDebug"})
public final class StockScreenState implements com.airbnb.mvrx.MavericksState {
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> stockItemsResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> stockResponse = null;
    private final int stock = 0;
    @org.jetbrains.annotations.Nullable()
    private final com.thedasagroup.suminative.data.model.response.stock.StockItem showUpdateStockDialog = null;
    
    public StockScreenState(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> stockItemsResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> stockResponse, int stock, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.stock.StockItem showUpdateStockDialog) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> getStockItemsResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> getStockResponse() {
        return null;
    }
    
    public final int getStock() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.response.stock.StockItem getShowUpdateStockDialog() {
        return null;
    }
    
    public StockScreenState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> component2() {
        return null;
    }
    
    public final int component3() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.response.stock.StockItem component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.stock.StockScreenState copy(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> stockItemsResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse> stockResponse, int stock, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.stock.StockItem showUpdateStockDialog) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}