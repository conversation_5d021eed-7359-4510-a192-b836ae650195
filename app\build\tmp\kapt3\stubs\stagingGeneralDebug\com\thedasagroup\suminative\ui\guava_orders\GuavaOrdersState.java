package com.thedasagroup.suminative.ui.guava_orders;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B1\u0012\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u0012\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0003\u00a2\u0006\u0004\b\t\u0010\nJ\u000f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0006H\u00c6\u0003J\u000f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\b0\u0003H\u00c6\u0003J3\u0010\u0013\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0003H\u00c6\u0001J\u0013\u0010\u0014\u001a\u00020\u00062\b\u0010\u0015\u001a\u0004\u0018\u00010\u0016H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\f\u00a8\u0006\u001b"}, d2 = {"Lcom/thedasagroup/suminative/ui/guava_orders/GuavaOrdersState;", "Lcom/airbnb/mvrx/MavericksState;", "ordersResponse", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/my_guava/orders/list_of_orders/GetListOfOrdersResponse;", "refreshing", "", "voidOrderResponse", "Lcom/thedasagroup/suminative/data/model/response/my_guava/orders/create_order/GuavaOrderResponse;", "<init>", "(Lcom/airbnb/mvrx/Async;ZLcom/airbnb/mvrx/Async;)V", "getOrdersResponse", "()Lcom/airbnb/mvrx/Async;", "getRefreshing", "()Z", "getVoidOrderResponse", "component1", "component2", "component3", "copy", "equals", "other", "", "hashCode", "", "toString", "", "app_stagingGeneralDebug"})
public final class GuavaOrdersState implements com.airbnb.mvrx.MavericksState {
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GetListOfOrdersResponse> ordersResponse = null;
    private final boolean refreshing = false;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse> voidOrderResponse = null;
    
    public GuavaOrdersState(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GetListOfOrdersResponse> ordersResponse, boolean refreshing, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse> voidOrderResponse) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GetListOfOrdersResponse> getOrdersResponse() {
        return null;
    }
    
    public final boolean getRefreshing() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse> getVoidOrderResponse() {
        return null;
    }
    
    public GuavaOrdersState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GetListOfOrdersResponse> component1() {
        return null;
    }
    
    public final boolean component2() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersState copy(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GetListOfOrdersResponse> ordersResponse, boolean refreshing, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse> voidOrderResponse) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}