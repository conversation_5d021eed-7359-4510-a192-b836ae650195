package com.thedasagroup.suminative.data.repo;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J2\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000b2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0011\u001a\u00020\u0012H\u0086@\u00a2\u0006\u0002\u0010\u0013J(\u0010\u0014\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160\u00150\f0\u000b2\u0006\u0010\u000e\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u0017J(\u0010\u0018\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190\u00150\f0\u000b2\u0006\u0010\u000e\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u0017J\"\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000b2\u0006\u0010\u000e\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u0017J\"\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000b2\u0006\u0010\u000e\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u0017J\"\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00120\f0\u000b2\u0006\u0010\u000e\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u0017J\u0018\u0010\u001d\u001a\u00020\u001e*\u00020\u00192\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00160\u0015J\n\u0010 \u001a\u00020!*\u00020\u0016R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\""}, d2 = {"Lcom/thedasagroup/suminative/data/repo/OptionRepository;", "Lcom/thedasagroup/suminative/data/repo/BaseRepository;", "databaseManager", "Lcom/thedasagroup/suminative/data/database/DatabaseManager;", "<init>", "(Lcom/thedasagroup/suminative/data/database/DatabaseManager;)V", "optionQueries", "Lcom/thedasagroup/suminative/database/OptionQueries;", "optionSetQueries", "Lcom/thedasagroup/suminative/database/OptionSetQueries;", "saveOptionDetails", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "", "productId", "", "storeId", "optionDetails", "Lcom/thedasagroup/suminative/data/model/response/options_details/OptionDetails;", "(IILcom/thedasagroup/suminative/data/model/response/options_details/OptionDetails;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getOptionsByProduct", "", "Lcom/thedasagroup/suminative/database/OptionEntity;", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getOptionSetsByProduct", "Lcom/thedasagroup/suminative/database/OptionSetEntity;", "hasOptions", "deleteOptionsByProduct", "getOptionDetailsFromDatabase", "toOptionSet", "Lcom/thedasagroup/suminative/data/model/request/order/OptionSet;", "options", "toOption", "Lcom/thedasagroup/suminative/data/model/response/store_orders/Option;", "app_stagingGeneralDebug"})
public final class OptionRepository extends com.thedasagroup.suminative.data.repo.BaseRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.database.DatabaseManager databaseManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.database.OptionQueries optionQueries = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.database.OptionSetQueries optionSetQueries = null;
    
    public OptionRepository(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.database.DatabaseManager databaseManager) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveOptionDetails(int productId, int storeId, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.options_details.OptionDetails optionDetails, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<java.lang.Boolean>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getOptionsByProduct(int productId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.database.OptionEntity>>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getOptionSetsByProduct(int productId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.database.OptionSetEntity>>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object hasOptions(int productId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<java.lang.Boolean>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteOptionsByProduct(int productId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<java.lang.Boolean>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getOptionDetailsFromDatabase(int productId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.options_details.OptionDetails>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.model.request.order.OptionSet toOptionSet(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.database.OptionSetEntity $this$toOptionSet, @org.jetbrains.annotations.NotNull()
    java.util.List<com.thedasagroup.suminative.database.OptionEntity> options) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.model.response.store_orders.Option toOption(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.database.OptionEntity $this$toOption) {
        return null;
    }
}