{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-et\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4661", "endColumns": "139", "endOffsets": "4796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,362", "endColumns": "99,99,106,98", "endOffsets": "150,250,357,456"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4801,5383,5483,5590", "endColumns": "99,99,106,98", "endOffsets": "4896,5478,5585,5684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,88", "endOffsets": "140,229"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "54124,54214", "endColumns": "89,88", "endOffsets": "54209,54298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,278,374,469,551,629,720,811,895,977,1062,1134,1209,1284,1356,1433,1504", "endColumns": "92,79,95,94,81,77,90,90,83,81,84,71,74,74,71,76,70,121", "endOffsets": "193,273,369,464,546,624,715,806,890,972,1057,1129,1204,1279,1351,1428,1499,1621"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,539,540,541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4488,4581,4901,4997,5092,5757,5835,16730,16821,16986,17068,17402,17552,17627,17702,53854,53931,54002", "endColumns": "92,79,95,94,81,77,90,90,83,81,84,71,74,74,71,76,70,121", "endOffsets": "4576,4656,4992,5087,5169,5830,5921,16816,16900,17063,17148,17469,17622,17697,17769,53926,53997,54119"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3461,3556,3658,3756,3859,3965,4070,17774", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "3551,3653,3751,3854,3960,4065,4185,17870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,331,407,491,602,728,909,1043,1154,1257,1387,1449,1508,1595,1686,1751,1904,1989,2079,2234,2310,2400,2499,2643,2743,2849,2950,3024,3097,3216,3302,3491,3646,3762,3895,3992,4156,4257,4392,4490,4623,4726,4845,4941,5035,5173,5267,5403,5504,5666,5752,5878,5955,6075,6174,6248,6336,6405,6471,6546,6617,6733,6825,6957,7053,7169,7265,7398,7494,7588,7711,7803,7914,8018,8131,8224,8284,8337,8390,8444,8494,8543,8618,8669,8732,8784,8828,8898,8942,8997,9049,9110,9168,9216,9265,9337,9392,9444,9490,9552,9609,9670,9804,9921,9996,10057,10140,10209,10277,10391,10463,10534,10614,10692,10755,10853,10926,11008,11090,11210,11290,11382,11467,11556,11660,11738,11846,11963,12071,12169,12246,12336,12402,12484,12596,12717,12782,12859,12936,13026,15077,15192,15340,15385,15502,15676,15761,15886,15978,16117,16284,16404,16581,16655,16739,16810,16906,17024,17067,17154,17283,17375,17504,17627,17777,17887,17997,18064,18121,18182,18236,18299,18378,18473,18589,18657,18731,18941,19132,19253,19381,19506,19592,19694,19811,19951,20092,20181,20280,20367,20484,20595,20744,20834,20912,20970,21187,21271,21349,21411,21472,21570,21686,21788,21942,22113,22192,22250,22311,22469,22545,22637,22708,22796,22957,23054,23137,23255,23333,23470,23586,23638,23747,23817,23892,23951,24023,24071,24131,24214,24266,24335,24484,24584,24686,24750,24900,25033,25154,25268,25504,25601,25698,25795,25941,26145,26231,26399,26497,26634,26752,26834,26916,26990,27139,27285,27427,27474,27557,27689,27784,27862,27929,28018,28094,28162,28228,28289,28366,28435,28509,28599,28667,28735,28816,28885,28973,29130,29262,29360,29415,29490,29643,29703,29781,29867,29944,30020,30254,30309,30385,30467,30538,30622,30784,30873,30939,31026,31106,31174,31255,31343,31469,31550,31598,31680,31757,31824,31905,32015,32115,32225,32340,32415,32469,32520,32605,32707,32789,32891,33050,33142,33222,33337,33446,33580,33680,33786,33933,34050,34222,34367,34479,34611,34704,34792,34895,35010,35102,35225,35295,35695,35792,35879,35964", "endColumns": "164,110,75,83,110,125,180,133,110,102,129,61,58,86,90,64,152,84,89,154,75,89,98,143,99,105,100,73,72,118,85,188,154,115,132,96,163,100,134,97,132,102,118,95,93,137,93,135,100,161,85,125,76,119,98,73,87,68,65,74,70,115,91,131,95,115,95,132,95,93,122,91,110,103,112,92,59,52,52,53,49,48,74,50,62,51,43,69,43,54,51,60,57,47,48,71,54,51,45,61,56,60,133,116,74,60,82,68,67,113,71,70,79,77,62,97,72,81,81,119,79,91,84,88,103,77,107,116,107,97,76,89,65,81,111,120,64,76,76,89,2050,114,147,44,116,173,84,124,91,138,166,119,176,73,83,70,95,117,42,86,128,91,128,122,149,109,109,66,56,60,53,62,78,94,115,67,73,209,190,120,127,124,85,101,116,139,140,88,98,86,116,110,148,89,77,57,216,83,77,61,60,97,115,101,153,170,78,57,60,157,75,91,70,87,160,96,82,117,77,136,115,51,108,69,74,58,71,47,59,82,51,68,148,99,101,63,149,132,120,113,235,96,96,96,145,203,85,167,97,136,117,81,81,73,148,145,141,46,82,131,94,77,66,88,75,67,65,60,76,68,73,89,67,67,80,68,87,156,131,97,54,74,152,59,77,85,76,75,233,54,75,81,70,83,161,88,65,86,79,67,80,87,125,80,47,81,76,66,80,109,99,109,114,74,53,50,84,101,81,101,158,91,79,114,108,133,99,105,146,116,171,144,111,131,92,87,102,114,91,122,69,399,96,86,84,69", "endOffsets": "215,326,402,486,597,723,904,1038,1149,1252,1382,1444,1503,1590,1681,1746,1899,1984,2074,2229,2305,2395,2494,2638,2738,2844,2945,3019,3092,3211,3297,3486,3641,3757,3890,3987,4151,4252,4387,4485,4618,4721,4840,4936,5030,5168,5262,5398,5499,5661,5747,5873,5950,6070,6169,6243,6331,6400,6466,6541,6612,6728,6820,6952,7048,7164,7260,7393,7489,7583,7706,7798,7909,8013,8126,8219,8279,8332,8385,8439,8489,8538,8613,8664,8727,8779,8823,8893,8937,8992,9044,9105,9163,9211,9260,9332,9387,9439,9485,9547,9604,9665,9799,9916,9991,10052,10135,10204,10272,10386,10458,10529,10609,10687,10750,10848,10921,11003,11085,11205,11285,11377,11462,11551,11655,11733,11841,11958,12066,12164,12241,12331,12397,12479,12591,12712,12777,12854,12931,13021,15072,15187,15335,15380,15497,15671,15756,15881,15973,16112,16279,16399,16576,16650,16734,16805,16901,17019,17062,17149,17278,17370,17499,17622,17772,17882,17992,18059,18116,18177,18231,18294,18373,18468,18584,18652,18726,18936,19127,19248,19376,19501,19587,19689,19806,19946,20087,20176,20275,20362,20479,20590,20739,20829,20907,20965,21182,21266,21344,21406,21467,21565,21681,21783,21937,22108,22187,22245,22306,22464,22540,22632,22703,22791,22952,23049,23132,23250,23328,23465,23581,23633,23742,23812,23887,23946,24018,24066,24126,24209,24261,24330,24479,24579,24681,24745,24895,25028,25149,25263,25499,25596,25693,25790,25936,26140,26226,26394,26492,26629,26747,26829,26911,26985,27134,27280,27422,27469,27552,27684,27779,27857,27924,28013,28089,28157,28223,28284,28361,28430,28504,28594,28662,28730,28811,28880,28968,29125,29257,29355,29410,29485,29638,29698,29776,29862,29939,30015,30249,30304,30380,30462,30533,30617,30779,30868,30934,31021,31101,31169,31250,31338,31464,31545,31593,31675,31752,31819,31900,32010,32110,32220,32335,32410,32464,32515,32600,32702,32784,32886,33045,33137,33217,33332,33441,33575,33675,33781,33928,34045,34217,34362,34474,34606,34699,34787,34890,35005,35097,35220,35290,35690,35787,35874,35959,36029"}, "to": {"startLines": "193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17875,18040,18151,18227,18311,18422,18548,18729,18863,18974,19077,19207,19269,19328,19415,19506,19571,19724,19809,19899,20054,20130,20220,20319,20463,20563,20669,20770,20844,20917,21036,21122,21311,21466,21582,21715,21812,21976,22077,22212,22310,22443,22546,22665,22761,22855,22993,23087,23223,23324,23486,23572,23698,23775,23895,23994,24068,24156,24225,24291,24366,24437,24553,24645,24777,24873,24989,25085,25218,25314,25408,25531,25623,25734,25838,25951,26044,26104,26157,26210,26264,26314,26363,26438,26489,26552,26604,26648,26718,26762,26817,26869,26930,26988,27036,27085,27157,27212,27264,27310,27372,27429,27490,27624,27741,27816,27877,27960,28029,28097,28211,28283,28354,28434,28512,28575,28673,28746,28828,28910,29030,29110,29202,29287,29376,29480,29558,29666,29783,29891,29989,30066,30156,30222,30304,30416,30537,30602,30679,30756,30846,32897,33012,33160,33205,33322,33496,33581,33706,33798,33937,34104,34224,34401,34475,34559,34630,34726,34844,34887,34974,35103,35195,35324,35447,35597,35707,35817,35884,35941,36002,36056,36119,36198,36293,36409,36477,36551,36761,36952,37073,37201,37326,37412,37514,37631,37771,37912,38001,38100,38187,38304,38415,38564,38654,38732,38790,39007,39091,39169,39231,39292,39390,39506,39608,39762,39933,40012,40070,40131,40289,40365,40457,40528,40616,40777,40874,40957,41075,41153,41290,41406,41458,41567,41637,41712,41771,41843,41891,41951,42034,42086,42155,42304,42404,42506,42570,42720,42853,42974,43088,43324,43421,43518,43615,43761,43965,44051,44219,44317,44454,44572,44654,44736,44810,44959,45105,45247,45294,45377,45509,45604,45682,45749,45838,45914,45982,46048,46109,46186,46255,46329,46419,46487,46555,46636,46705,46793,46950,47082,47180,47235,47310,47463,47523,47601,47687,47764,47840,48074,48129,48205,48287,48358,48442,48604,48693,48759,48846,48926,48994,49075,49163,49289,49370,49418,49500,49577,49644,49725,49835,49935,50045,50160,50235,50289,50340,50425,50527,50609,50711,50870,50962,51042,51157,51266,51400,51500,51606,51753,51870,52042,52187,52299,52431,52524,52612,52715,52830,52922,53045,53115,53515,53612,53699,53784", "endColumns": "164,110,75,83,110,125,180,133,110,102,129,61,58,86,90,64,152,84,89,154,75,89,98,143,99,105,100,73,72,118,85,188,154,115,132,96,163,100,134,97,132,102,118,95,93,137,93,135,100,161,85,125,76,119,98,73,87,68,65,74,70,115,91,131,95,115,95,132,95,93,122,91,110,103,112,92,59,52,52,53,49,48,74,50,62,51,43,69,43,54,51,60,57,47,48,71,54,51,45,61,56,60,133,116,74,60,82,68,67,113,71,70,79,77,62,97,72,81,81,119,79,91,84,88,103,77,107,116,107,97,76,89,65,81,111,120,64,76,76,89,2050,114,147,44,116,173,84,124,91,138,166,119,176,73,83,70,95,117,42,86,128,91,128,122,149,109,109,66,56,60,53,62,78,94,115,67,73,209,190,120,127,124,85,101,116,139,140,88,98,86,116,110,148,89,77,57,216,83,77,61,60,97,115,101,153,170,78,57,60,157,75,91,70,87,160,96,82,117,77,136,115,51,108,69,74,58,71,47,59,82,51,68,148,99,101,63,149,132,120,113,235,96,96,96,145,203,85,167,97,136,117,81,81,73,148,145,141,46,82,131,94,77,66,88,75,67,65,60,76,68,73,89,67,67,80,68,87,156,131,97,54,74,152,59,77,85,76,75,233,54,75,81,70,83,161,88,65,86,79,67,80,87,125,80,47,81,76,66,80,109,99,109,114,74,53,50,84,101,81,101,158,91,79,114,108,133,99,105,146,116,171,144,111,131,92,87,102,114,91,122,69,399,96,86,84,69", "endOffsets": "18035,18146,18222,18306,18417,18543,18724,18858,18969,19072,19202,19264,19323,19410,19501,19566,19719,19804,19894,20049,20125,20215,20314,20458,20558,20664,20765,20839,20912,21031,21117,21306,21461,21577,21710,21807,21971,22072,22207,22305,22438,22541,22660,22756,22850,22988,23082,23218,23319,23481,23567,23693,23770,23890,23989,24063,24151,24220,24286,24361,24432,24548,24640,24772,24868,24984,25080,25213,25309,25403,25526,25618,25729,25833,25946,26039,26099,26152,26205,26259,26309,26358,26433,26484,26547,26599,26643,26713,26757,26812,26864,26925,26983,27031,27080,27152,27207,27259,27305,27367,27424,27485,27619,27736,27811,27872,27955,28024,28092,28206,28278,28349,28429,28507,28570,28668,28741,28823,28905,29025,29105,29197,29282,29371,29475,29553,29661,29778,29886,29984,30061,30151,30217,30299,30411,30532,30597,30674,30751,30841,32892,33007,33155,33200,33317,33491,33576,33701,33793,33932,34099,34219,34396,34470,34554,34625,34721,34839,34882,34969,35098,35190,35319,35442,35592,35702,35812,35879,35936,35997,36051,36114,36193,36288,36404,36472,36546,36756,36947,37068,37196,37321,37407,37509,37626,37766,37907,37996,38095,38182,38299,38410,38559,38649,38727,38785,39002,39086,39164,39226,39287,39385,39501,39603,39757,39928,40007,40065,40126,40284,40360,40452,40523,40611,40772,40869,40952,41070,41148,41285,41401,41453,41562,41632,41707,41766,41838,41886,41946,42029,42081,42150,42299,42399,42501,42565,42715,42848,42969,43083,43319,43416,43513,43610,43756,43960,44046,44214,44312,44449,44567,44649,44731,44805,44954,45100,45242,45289,45372,45504,45599,45677,45744,45833,45909,45977,46043,46104,46181,46250,46324,46414,46482,46550,46631,46700,46788,46945,47077,47175,47230,47305,47458,47518,47596,47682,47759,47835,48069,48124,48200,48282,48353,48437,48599,48688,48754,48841,48921,48989,49070,49158,49284,49365,49413,49495,49572,49639,49720,49830,49930,50040,50155,50230,50284,50335,50420,50522,50604,50706,50865,50957,51037,51152,51261,51395,51495,51601,51748,51865,52037,52182,52294,52426,52519,52607,52710,52825,52917,53040,53110,53510,53607,53694,53779,53849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,346,425,510,602,689,788,905,987,1047,1111,1196,1264,1328,1415,1479,1543,1602,1674,1738,1792,1911,1971,2032,2086,2159,2292,2376,2453,2546,2626,2719,2857,2937,3016,3142,3230,3309,3364,3415,3481,3554,3633,3704,3783,3856,3931,4005,4077,4190,4278,4355,4446,4538,4612,4686,4777,4831,4913,4982,5065,5151,5213,5277,5340,5408,5511,5614,5711,5812,5871,5926,6007,6096,6173", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,78,84,91,86,98,116,81,59,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,76,92,79,92,137,79,78,125,87,78,54,50,65,72,78,70,78,72,74,73,71,112,87,76,90,91,73,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80,88,76,77", "endOffsets": "261,341,420,505,597,684,783,900,982,1042,1106,1191,1259,1323,1410,1474,1538,1597,1669,1733,1787,1906,1966,2027,2081,2154,2287,2371,2448,2541,2621,2714,2852,2932,3011,3137,3225,3304,3359,3410,3476,3549,3628,3699,3778,3851,3926,4000,4072,4185,4273,4350,4441,4533,4607,4681,4772,4826,4908,4977,5060,5146,5208,5272,5335,5403,5506,5609,5706,5807,5866,5921,6002,6091,6168,6246"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3038,3118,3197,3282,3374,4190,4289,4406,5174,5234,5298,5689,5926,12132,12219,12283,12347,12406,12478,12542,12596,12715,12775,12836,12890,12963,13096,13180,13257,13350,13430,13523,13661,13741,13820,13946,14034,14113,14168,14219,14285,14358,14437,14508,14587,14660,14735,14809,14881,14994,15082,15159,15250,15342,15416,15490,15581,15635,15717,15786,15869,15955,16017,16081,16144,16212,16315,16418,16515,16616,16675,16905,17236,17325,17474", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,79,78,84,91,86,98,116,81,59,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,76,92,79,92,137,79,78,125,87,78,54,50,65,72,78,70,78,72,74,73,71,112,87,76,90,91,73,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80,88,76,77", "endOffsets": "311,3113,3192,3277,3369,3456,4284,4401,4483,5229,5293,5378,5752,5985,12214,12278,12342,12401,12473,12537,12591,12710,12770,12831,12885,12958,13091,13175,13252,13345,13425,13518,13656,13736,13815,13941,14029,14108,14163,14214,14280,14353,14432,14503,14582,14655,14730,14804,14876,14989,15077,15154,15245,15337,15411,15485,15576,15630,15712,15781,15864,15950,16012,16076,16139,16207,16310,16413,16510,16611,16670,16725,16981,17320,17397,17547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "316,422,521,632,718,820,937,1018,1095,1187,1281,1377,1479,1588,1682,1783,1877,1969,2062,2145,2256,2360,2459,2569,2671,2770,2936,17153", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "417,516,627,713,815,932,1013,1090,1182,1276,1372,1474,1583,1677,1778,1872,1964,2057,2140,2251,2355,2454,2564,2666,2765,2931,3033,17231"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,297,411,530,629,730,848,981,1101,1249,1336,1437,1531,1630,1746,1873,1979,2114,2247,2378,2553,2679,2798,2919,3041,3136,3233,3353,3487,3592,3695,3800,3931,4066,4174,4277,4354,4450,4546,4633,4718,4824,4904,4990,5091,5195,5289,5393,5480,5589,5690,5797,5914,5994,6098", "endColumns": "119,121,113,118,98,100,117,132,119,147,86,100,93,98,115,126,105,134,132,130,174,125,118,120,121,94,96,119,133,104,102,104,130,134,107,102,76,95,95,86,84,105,79,85,100,103,93,103,86,108,100,106,116,79,103,98", "endOffsets": "170,292,406,525,624,725,843,976,1096,1244,1331,1432,1526,1625,1741,1868,1974,2109,2242,2373,2548,2674,2793,2914,3036,3131,3228,3348,3482,3587,3690,3795,3926,4061,4169,4272,4349,4445,4541,4628,4713,4819,4899,4985,5086,5190,5284,5388,5475,5584,5685,5792,5909,5989,6093,6192"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5990,6110,6232,6346,6465,6564,6665,6783,6916,7036,7184,7271,7372,7466,7565,7681,7808,7914,8049,8182,8313,8488,8614,8733,8854,8976,9071,9168,9288,9422,9527,9630,9735,9866,10001,10109,10212,10289,10385,10481,10568,10653,10759,10839,10925,11026,11130,11224,11328,11415,11524,11625,11732,11849,11929,12033", "endColumns": "119,121,113,118,98,100,117,132,119,147,86,100,93,98,115,126,105,134,132,130,174,125,118,120,121,94,96,119,133,104,102,104,130,134,107,102,76,95,95,86,84,105,79,85,100,103,93,103,86,108,100,106,116,79,103,98", "endOffsets": "6105,6227,6341,6460,6559,6660,6778,6911,7031,7179,7266,7367,7461,7560,7676,7803,7909,8044,8177,8308,8483,8609,8728,8849,8971,9066,9163,9283,9417,9522,9625,9730,9861,9996,10104,10207,10284,10380,10476,10563,10648,10754,10834,10920,11021,11125,11219,11323,11410,11519,11620,11727,11844,11924,12028,12127"}}]}]}