package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.repo.ClockInOutRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RepoModule_ProvidesClockInOutRepositoryFactory implements Factory<ClockInOutRepository> {
  @Override
  public ClockInOutRepository get() {
    return providesClockInOutRepository();
  }

  public static RepoModule_ProvidesClockInOutRepositoryFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static ClockInOutRepository providesClockInOutRepository() {
    return Preconditions.checkNotNullFromProvides(RepoModule.INSTANCE.providesClockInOutRepository());
  }

  private static final class InstanceHolder {
    static final RepoModule_ProvidesClockInOutRepositoryFactory INSTANCE = new RepoModule_ProvidesClockInOutRepositoryFactory();
  }
}
