package com.thedasagroup.suminative.di;

import com.instacart.truetime.time.TrueTimeImpl;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RepoModule_ProvidesTrueTimeFactory implements Factory<TrueTimeImpl> {
  @Override
  public TrueTimeImpl get() {
    return providesTrueTime();
  }

  public static RepoModule_ProvidesTrueTimeFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static TrueTimeImpl providesTrueTime() {
    return Preconditions.checkNotNullFromProvides(RepoModule.INSTANCE.providesTrueTime());
  }

  private static final class InstanceHolder {
    static final RepoModule_ProvidesTrueTimeFactory INSTANCE = new RepoModule_ProvidesTrueTimeFactory();
  }
}
