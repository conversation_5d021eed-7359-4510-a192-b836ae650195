{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeStagingGeneralDebugResources-106:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,186", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "418,525,627,737,823,928,1045,1123,1199,1290,1383,1478,1572,1666,1759,1854,1951,2042,2133,2217,2321,2433,2532,2638,2749,2851,3014,17237", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "520,622,732,818,923,1040,1118,1194,1285,1378,1473,1567,1661,1754,1849,1946,2037,2128,2212,2316,2428,2527,2633,2744,2846,3009,3107,17315"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,368,446,524,601,704,796,888,1014,1095,1156,1221,1320,1396,1457,1546,1610,1677,1731,1799,1859,1913,2030,2090,2152,2206,2278,2400,2484,2563,2657,2740,2832,2969,3047,3129,3256,3344,3424,3478,3529,3595,3667,3744,3815,3896,3968,4045,4119,4190,4295,4383,4454,4547,4642,4716,4790,4886,4938,5021,5088,5174,5262,5324,5388,5451,5519,5629,5735,5834,5948,6006,6061,6140,6223,6298", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,76,102,91,91,125,80,60,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,78,93,82,91,136,77,81,126,87,79,53,50,65,71,76,70,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,105,98,113,57,54,78,82,74,78", "endOffsets": "363,441,519,596,699,791,883,1009,1090,1151,1216,1315,1391,1452,1541,1605,1672,1726,1794,1854,1908,2025,2085,2147,2201,2273,2395,2479,2558,2652,2735,2827,2964,3042,3124,3251,3339,3419,3473,3524,3590,3662,3739,3810,3891,3963,4040,4114,4185,4290,4378,4449,4542,4637,4711,4785,4881,4933,5016,5083,5169,5257,5319,5383,5446,5514,5624,5730,5829,5943,6001,6056,6135,6218,6293,6372"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,57,58,59,63,66,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,183,187,188,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3112,3190,3268,3345,3448,4271,4363,4489,5279,5340,5405,5822,6068,12214,12303,12367,12434,12488,12556,12616,12670,12787,12847,12909,12963,13035,13157,13241,13320,13414,13497,13589,13726,13804,13886,14013,14101,14181,14235,14286,14352,14424,14501,14572,14653,14725,14802,14876,14947,15052,15140,15211,15304,15399,15473,15547,15643,15695,15778,15845,15931,16019,16081,16145,16208,16276,16386,16492,16591,16705,16763,16990,17320,17403,17550", "endLines": "7,35,36,37,38,39,47,48,49,57,58,59,63,66,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,183,187,188,190", "endColumns": "12,77,77,76,102,91,91,125,80,60,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,78,93,82,91,136,77,81,126,87,79,53,50,65,71,76,70,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,105,98,113,57,54,78,82,74,78", "endOffsets": "413,3185,3263,3340,3443,3535,4358,4484,4565,5335,5400,5499,5893,6124,12298,12362,12429,12483,12551,12611,12665,12782,12842,12904,12958,13030,13152,13236,13315,13409,13492,13584,13721,13799,13881,14008,14096,14176,14230,14281,14347,14419,14496,14567,14648,14720,14797,14871,14942,15047,15135,15206,15299,15394,15468,15542,15638,15690,15773,15840,15926,16014,16076,16140,16203,16271,16381,16487,16586,16700,16758,16813,17064,17398,17473,17624"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,409,527,628,723,835,969,1085,1224,1309,1409,1502,1599,1715,1837,1942,2075,2205,2347,2510,2638,2755,2879,3000,3091,3188,3308,3423,3521,3624,3732,3864,4005,4115,4214,4298,4392,4487,4579,4665,4778,4858,4944,5045,5148,5245,5346,5434,5540,5639,5742,5861,5941,6045", "endColumns": "117,116,118,117,100,94,111,133,115,138,84,99,92,96,115,121,104,132,129,141,162,127,116,123,120,90,96,119,114,97,102,107,131,140,109,98,83,93,94,91,85,112,79,85,100,102,96,100,87,105,98,102,118,79,103,94", "endOffsets": "168,285,404,522,623,718,830,964,1080,1219,1304,1404,1497,1594,1710,1832,1937,2070,2200,2342,2505,2633,2750,2874,2995,3086,3183,3303,3418,3516,3619,3727,3859,4000,4110,4209,4293,4387,4482,4574,4660,4773,4853,4939,5040,5143,5240,5341,5429,5535,5634,5737,5856,5936,6040,6135"}, "to": {"startLines": "67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6129,6247,6364,6483,6601,6702,6797,6909,7043,7159,7298,7383,7483,7576,7673,7789,7911,8016,8149,8279,8421,8584,8712,8829,8953,9074,9165,9262,9382,9497,9595,9698,9806,9938,10079,10189,10288,10372,10466,10561,10653,10739,10852,10932,11018,11119,11222,11319,11420,11508,11614,11713,11816,11935,12015,12119", "endColumns": "117,116,118,117,100,94,111,133,115,138,84,99,92,96,115,121,104,132,129,141,162,127,116,123,120,90,96,119,114,97,102,107,131,140,109,98,83,93,94,91,85,112,79,85,100,102,96,100,87,105,98,102,118,79,103,94", "endOffsets": "6242,6359,6478,6596,6697,6792,6904,7038,7154,7293,7378,7478,7571,7668,7784,7906,8011,8144,8274,8416,8579,8707,8824,8948,9069,9160,9257,9377,9492,9590,9693,9801,9933,10074,10184,10283,10367,10461,10556,10648,10734,10847,10927,11013,11114,11217,11314,11415,11503,11609,11708,11811,11930,12010,12114,12209"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,210,341,417,501,617,756,928,1088,1200,1304,1442,1501,1559,1657,1750,1830,1966,2053,2148,2313,2388,2488,2590,2721,2822,2940,3053,3135,3207,3322,3409,3604,3763,3878,4030,4133,4303,4409,4555,4671,4815,4924,5048,5150,5243,5363,5452,5572,5666,5842,5925,6054,6131,6243,6339,6417,6506,6589,6657,6737,6805,6902,7013,7152,7249,7374,7478,7602,7704,7796,7916,8004,8100,8203,8320,8412,8476,8528,8583,8632,8682,8733,8803,8855,8919,8967,9011,9089,9133,9190,9241,9301,9354,9408,9459,9538,9591,9643,9689,9752,9815,9871,10040,10140,10222,10283,10367,10436,10504,10620,10699,10775,10856,10934,11001,11098,11173,11262,11343,11461,11541,11629,11708,11800,11892,11971,12070,12174,12284,12382,12459,12563,12639,12724,12854,13004,13071,13148,13242,13336,15387,15502,15702,15748,15874,16019,16108,16265,16361,16496,16651,16801,16990,17066,17145,17219,17312,17448,17493,17578,17690,17789,17903,18039,18171,18274,18383,18447,18512,18571,18625,18699,18776,18873,18992,19062,19153,19418,19640,19760,19906,20040,20138,20241,20359,20515,20663,20757,20870,20960,21085,21189,21334,21418,21492,21547,21802,21889,21966,22030,22093,22206,22338,22438,22590,22757,22834,22902,22962,23114,23187,23281,23357,23450,23621,23711,23786,23894,23983,24123,24249,24305,24432,24503,24583,24659,24736,24784,24847,24940,24992,25063,25243,25361,25452,25528,25677,25811,25945,26046,26335,26441,26535,26640,26815,27019,27104,27312,27424,27562,27680,27759,27848,27934,28072,28237,28357,28408,28500,28638,28755,28844,28926,29029,29110,29181,29259,29320,29396,29465,29540,29629,29694,29761,29842,29912,29999,30176,30294,30378,30434,30513,30662,30722,30802,30883,30962,31049,31317,31372,31460,31549,31619,31719,31893,31980,32051,32137,32221,32288,32370,32461,32604,32685,32744,32825,32902,32973,33049,33159,33269,33382,33525,33605,33653,33705,33802,33913,33992,34082,34260,34368,34462,34578,34701,34851,34976,35073,35221,35342,35543,35712,35842,35994,36087,36171,36283,36408,36506,36630,36703,37048,37130,37220,37306", "endColumns": "154,130,75,83,115,138,171,159,111,103,137,58,57,97,92,79,135,86,94,164,74,99,101,130,100,117,112,81,71,114,86,194,158,114,151,102,169,105,145,115,143,108,123,101,92,119,88,119,93,175,82,128,76,111,95,77,88,82,67,79,67,96,110,138,96,124,103,123,101,91,119,87,95,102,116,91,63,51,54,48,49,50,69,51,63,47,43,77,43,56,50,59,52,53,50,78,52,51,45,62,62,55,168,99,81,60,83,68,67,115,78,75,80,77,66,96,74,88,80,117,79,87,78,91,91,78,98,103,109,97,76,103,75,84,129,149,66,76,93,93,2050,114,199,45,125,144,88,156,95,134,154,149,188,75,78,73,92,135,44,84,111,98,113,135,131,102,108,63,64,58,53,73,76,96,118,69,90,264,221,119,145,133,97,102,117,155,147,93,112,89,124,103,144,83,73,54,254,86,76,63,62,112,131,99,151,166,76,67,59,151,72,93,75,92,170,89,74,107,88,139,125,55,126,70,79,75,76,47,62,92,51,70,179,117,90,75,148,133,133,100,288,105,93,104,174,203,84,207,111,137,117,78,88,85,137,164,119,50,91,137,116,88,81,102,80,70,77,60,75,68,74,88,64,66,80,69,86,176,117,83,55,78,148,59,79,80,78,86,267,54,87,88,69,99,173,86,70,85,83,66,81,90,142,80,58,80,76,70,75,109,109,112,142,79,47,51,96,110,78,89,177,107,93,115,122,149,124,96,147,120,200,168,129,151,92,83,111,124,97,123,72,344,81,89,85,76", "endOffsets": "205,336,412,496,612,751,923,1083,1195,1299,1437,1496,1554,1652,1745,1825,1961,2048,2143,2308,2383,2483,2585,2716,2817,2935,3048,3130,3202,3317,3404,3599,3758,3873,4025,4128,4298,4404,4550,4666,4810,4919,5043,5145,5238,5358,5447,5567,5661,5837,5920,6049,6126,6238,6334,6412,6501,6584,6652,6732,6800,6897,7008,7147,7244,7369,7473,7597,7699,7791,7911,7999,8095,8198,8315,8407,8471,8523,8578,8627,8677,8728,8798,8850,8914,8962,9006,9084,9128,9185,9236,9296,9349,9403,9454,9533,9586,9638,9684,9747,9810,9866,10035,10135,10217,10278,10362,10431,10499,10615,10694,10770,10851,10929,10996,11093,11168,11257,11338,11456,11536,11624,11703,11795,11887,11966,12065,12169,12279,12377,12454,12558,12634,12719,12849,12999,13066,13143,13237,13331,15382,15497,15697,15743,15869,16014,16103,16260,16356,16491,16646,16796,16985,17061,17140,17214,17307,17443,17488,17573,17685,17784,17898,18034,18166,18269,18378,18442,18507,18566,18620,18694,18771,18868,18987,19057,19148,19413,19635,19755,19901,20035,20133,20236,20354,20510,20658,20752,20865,20955,21080,21184,21329,21413,21487,21542,21797,21884,21961,22025,22088,22201,22333,22433,22585,22752,22829,22897,22957,23109,23182,23276,23352,23445,23616,23706,23781,23889,23978,24118,24244,24300,24427,24498,24578,24654,24731,24779,24842,24935,24987,25058,25238,25356,25447,25523,25672,25806,25940,26041,26330,26436,26530,26635,26810,27014,27099,27307,27419,27557,27675,27754,27843,27929,28067,28232,28352,28403,28495,28633,28750,28839,28921,29024,29105,29176,29254,29315,29391,29460,29535,29624,29689,29756,29837,29907,29994,30171,30289,30373,30429,30508,30657,30717,30797,30878,30957,31044,31312,31367,31455,31544,31614,31714,31888,31975,32046,32132,32216,32283,32365,32456,32599,32680,32739,32820,32897,32968,33044,33154,33264,33377,33520,33600,33648,33700,33797,33908,33987,34077,34255,34363,34457,34573,34696,34846,34971,35068,35216,35337,35538,35707,35837,35989,36082,36166,36278,36403,36501,36625,36698,37043,37125,37215,37301,37378"}, "to": {"startLines": "195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17959,18114,18245,18321,18405,18521,18660,18832,18992,19104,19208,19346,19405,19463,19561,19654,19734,19870,19957,20052,20217,20292,20392,20494,20625,20726,20844,20957,21039,21111,21226,21313,21508,21667,21782,21934,22037,22207,22313,22459,22575,22719,22828,22952,23054,23147,23267,23356,23476,23570,23746,23829,23958,24035,24147,24243,24321,24410,24493,24561,24641,24709,24806,24917,25056,25153,25278,25382,25506,25608,25700,25820,25908,26004,26107,26224,26316,26380,26432,26487,26536,26586,26637,26707,26759,26823,26871,26915,26993,27037,27094,27145,27205,27258,27312,27363,27442,27495,27547,27593,27656,27719,27775,27944,28044,28126,28187,28271,28340,28408,28524,28603,28679,28760,28838,28905,29002,29077,29166,29247,29365,29445,29533,29612,29704,29796,29875,29974,30078,30188,30286,30363,30467,30543,30628,30758,30908,30975,31052,31146,31240,33291,33406,33606,33652,33778,33923,34012,34169,34265,34400,34555,34705,34894,34970,35049,35123,35216,35352,35397,35482,35594,35693,35807,35943,36075,36178,36287,36351,36416,36475,36529,36603,36680,36777,36896,36966,37057,37322,37544,37664,37810,37944,38042,38145,38263,38419,38567,38661,38774,38864,38989,39093,39238,39322,39396,39451,39706,39793,39870,39934,39997,40110,40242,40342,40494,40661,40738,40806,40866,41018,41091,41185,41261,41354,41525,41615,41690,41798,41887,42027,42153,42209,42336,42407,42487,42563,42640,42688,42751,42844,42896,42967,43147,43265,43356,43432,43581,43715,43849,43950,44239,44345,44439,44544,44719,44923,45008,45216,45328,45466,45584,45663,45752,45838,45976,46141,46261,46312,46404,46542,46659,46748,46830,46933,47014,47085,47163,47224,47300,47369,47444,47533,47598,47665,47746,47816,47903,48080,48198,48282,48338,48417,48566,48626,48706,48787,48866,48953,49221,49276,49364,49453,49523,49623,49797,49884,49955,50041,50125,50192,50274,50365,50508,50589,50648,50729,50806,50877,50953,51063,51173,51286,51429,51509,51557,51609,51706,51817,51896,51986,52164,52272,52366,52482,52605,52755,52880,52977,53125,53246,53447,53616,53746,53898,53991,54075,54187,54312,54410,54534,54607,54952,55034,55124,55210", "endColumns": "154,130,75,83,115,138,171,159,111,103,137,58,57,97,92,79,135,86,94,164,74,99,101,130,100,117,112,81,71,114,86,194,158,114,151,102,169,105,145,115,143,108,123,101,92,119,88,119,93,175,82,128,76,111,95,77,88,82,67,79,67,96,110,138,96,124,103,123,101,91,119,87,95,102,116,91,63,51,54,48,49,50,69,51,63,47,43,77,43,56,50,59,52,53,50,78,52,51,45,62,62,55,168,99,81,60,83,68,67,115,78,75,80,77,66,96,74,88,80,117,79,87,78,91,91,78,98,103,109,97,76,103,75,84,129,149,66,76,93,93,2050,114,199,45,125,144,88,156,95,134,154,149,188,75,78,73,92,135,44,84,111,98,113,135,131,102,108,63,64,58,53,73,76,96,118,69,90,264,221,119,145,133,97,102,117,155,147,93,112,89,124,103,144,83,73,54,254,86,76,63,62,112,131,99,151,166,76,67,59,151,72,93,75,92,170,89,74,107,88,139,125,55,126,70,79,75,76,47,62,92,51,70,179,117,90,75,148,133,133,100,288,105,93,104,174,203,84,207,111,137,117,78,88,85,137,164,119,50,91,137,116,88,81,102,80,70,77,60,75,68,74,88,64,66,80,69,86,176,117,83,55,78,148,59,79,80,78,86,267,54,87,88,69,99,173,86,70,85,83,66,81,90,142,80,58,80,76,70,75,109,109,112,142,79,47,51,96,110,78,89,177,107,93,115,122,149,124,96,147,120,200,168,129,151,92,83,111,124,97,123,72,344,81,89,85,76", "endOffsets": "18109,18240,18316,18400,18516,18655,18827,18987,19099,19203,19341,19400,19458,19556,19649,19729,19865,19952,20047,20212,20287,20387,20489,20620,20721,20839,20952,21034,21106,21221,21308,21503,21662,21777,21929,22032,22202,22308,22454,22570,22714,22823,22947,23049,23142,23262,23351,23471,23565,23741,23824,23953,24030,24142,24238,24316,24405,24488,24556,24636,24704,24801,24912,25051,25148,25273,25377,25501,25603,25695,25815,25903,25999,26102,26219,26311,26375,26427,26482,26531,26581,26632,26702,26754,26818,26866,26910,26988,27032,27089,27140,27200,27253,27307,27358,27437,27490,27542,27588,27651,27714,27770,27939,28039,28121,28182,28266,28335,28403,28519,28598,28674,28755,28833,28900,28997,29072,29161,29242,29360,29440,29528,29607,29699,29791,29870,29969,30073,30183,30281,30358,30462,30538,30623,30753,30903,30970,31047,31141,31235,33286,33401,33601,33647,33773,33918,34007,34164,34260,34395,34550,34700,34889,34965,35044,35118,35211,35347,35392,35477,35589,35688,35802,35938,36070,36173,36282,36346,36411,36470,36524,36598,36675,36772,36891,36961,37052,37317,37539,37659,37805,37939,38037,38140,38258,38414,38562,38656,38769,38859,38984,39088,39233,39317,39391,39446,39701,39788,39865,39929,39992,40105,40237,40337,40489,40656,40733,40801,40861,41013,41086,41180,41256,41349,41520,41610,41685,41793,41882,42022,42148,42204,42331,42402,42482,42558,42635,42683,42746,42839,42891,42962,43142,43260,43351,43427,43576,43710,43844,43945,44234,44340,44434,44539,44714,44918,45003,45211,45323,45461,45579,45658,45747,45833,45971,46136,46256,46307,46399,46537,46654,46743,46825,46928,47009,47080,47158,47219,47295,47364,47439,47528,47593,47660,47741,47811,47898,48075,48193,48277,48333,48412,48561,48621,48701,48782,48861,48948,49216,49271,49359,49448,49518,49618,49792,49879,49950,50036,50120,50187,50269,50360,50503,50584,50643,50724,50801,50872,50948,51058,51168,51281,51424,51504,51552,51604,51701,51812,51891,51981,52159,52267,52361,52477,52600,52750,52875,52972,53120,53241,53442,53611,53741,53893,53986,54070,54182,54307,54405,54529,54602,54947,55029,55119,55205,55282"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-cs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "4746", "endColumns": "142", "endOffsets": "4884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,375,477,569,647,739,830,911,993,1079,1151,1229,1305,1380,1459,1527", "endColumns": "92,82,93,101,91,77,91,90,80,81,85,71,77,75,74,78,67,119", "endOffsets": "193,276,370,472,564,642,734,825,906,988,1074,1146,1224,1300,1375,1454,1522,1642"}, "to": {"startLines": "50,51,54,55,56,64,65,181,182,184,185,189,191,192,193,541,542,543", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4570,4663,4991,5085,5187,5898,5976,16818,16909,17069,17151,17478,17629,17707,17783,55287,55366,55434", "endColumns": "92,82,93,101,91,77,91,90,80,81,85,71,77,75,74,78,67,119", "endOffsets": "4658,4741,5080,5182,5274,5971,6063,16904,16985,17146,17232,17545,17702,17778,17853,55361,55429,55549"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,157,260,374", "endColumns": "101,102,113,100", "endOffsets": "152,255,369,470"}, "to": {"startLines": "53,60,61,62", "startColumns": "4,4,4,4", "startOffsets": "4889,5504,5607,5721", "endColumns": "101,102,113,100", "endOffsets": "4986,5602,5716,5817"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "40,41,42,43,44,45,46,194", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3540,3638,3740,3841,3940,4045,4152,17858", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "3633,3735,3836,3935,4040,4147,4266,17954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,88", "endOffsets": "136,225"}, "to": {"startLines": "544,545", "startColumns": "4,4", "startOffsets": "55554,55640", "endColumns": "85,88", "endOffsets": "55635,55724"}}]}]}