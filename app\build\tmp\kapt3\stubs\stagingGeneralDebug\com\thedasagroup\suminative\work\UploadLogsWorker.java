package com.thedasagroup.suminative.work;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0000\b\u0007\u0018\u00002\u00020\u0001B%\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0001\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\b\u0010\f\u001a\u00020\rH\u0016J\u0010\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0003J\u0016\u0010\u0012\u001a\u00020\u00132\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00110\u0015H\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082D\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"Lcom/thedasagroup/suminative/work/UploadLogsWorker;", "Landroidx/work/Worker;", "logsRepository", "Lcom/thedasagroup/suminative/data/repo/LogsRepository;", "appContext", "Landroid/content/Context;", "params", "Landroidx/work/WorkerParameters;", "<init>", "(Lcom/thedasagroup/suminative/data/repo/LogsRepository;Landroid/content/Context;Landroidx/work/WorkerParameters;)V", "TAG", "", "doWork", "Landroidx/work/ListenableWorker$Result;", "uploadLogFiles", "", "directory", "Ljava/io/File;", "deleteSuccessfullyUploadedFiles", "", "files", "", "app_stagingGeneralDebug"})
@androidx.hilt.work.HiltWorker()
public final class UploadLogsWorker extends androidx.work.Worker {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.repo.LogsRepository logsRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "UploadLogsWorker";
    
    @dagger.assisted.AssistedInject()
    public UploadLogsWorker(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.LogsRepository logsRepository, @dagger.assisted.Assisted()
    @org.jetbrains.annotations.NotNull()
    android.content.Context appContext, @dagger.assisted.Assisted()
    @org.jetbrains.annotations.NotNull()
    androidx.work.WorkerParameters params) {
        super(null, null);
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public androidx.work.ListenableWorker.Result doWork() {
        return null;
    }
    
    @android.annotation.SuppressLint(value = {"SuspiciousIndentation"})
    private final boolean uploadLogFiles(java.io.File directory) {
        return false;
    }
    
    private final void deleteSuccessfullyUploadedFiles(java.util.List<? extends java.io.File> files) {
    }
}