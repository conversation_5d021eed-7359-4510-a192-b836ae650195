package com.thedasagroup.suminative.ui.printer;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010!\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\f\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u000e\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001eJ\u0006\u0010\u001f\u001a\u00020\u001cJ\u0012\u0010 \u001a\u00020\u001c2\n\u0010\u0004\u001a\u00060\u0005R\u00020\u0006J\u0012\u0010!\u001a\u00020\u001c2\n\u0010\u0004\u001a\u00060\u0005R\u00020\u0006J\u0006\u0010\"\u001a\u00020\u001cR\u0014\u0010\u0004\u001a\b\u0018\u00010\u0005R\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R,\u0010\u0007\u001a\u0014\u0012\u0010\u0012\u000e\u0012\b\u0012\u00060\u0005R\u00020\u0006\u0018\u00010\t0\bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010\u000b\"\u0004\b\f\u0010\rR \u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0010\u0010\u000b\"\u0004\b\u0011\u0010\rR \u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u000f0\bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0013\u0010\u000b\"\u0004\b\u0014\u0010\rR \u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u000f0\bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0016\u0010\u000b\"\u0004\b\u0017\u0010\rR \u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u000f0\bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0019\u0010\u000b\"\u0004\b\u001a\u0010\r\u00a8\u0006#"}, d2 = {"Lcom/thedasagroup/suminative/ui/printer/PrinterViewModel;", "Landroidx/lifecycle/ViewModel;", "<init>", "()V", "printer", "Lcom/sunmi/printerx/PrinterSdk$Printer;", "Lcom/sunmi/printerx/PrinterSdk;", "showPrinters", "Landroidx/lifecycle/MutableLiveData;", "", "getShowPrinters", "()Landroidx/lifecycle/MutableLiveData;", "setShowPrinters", "(Landroidx/lifecycle/MutableLiveData;)V", "printerStatus", "", "getPrinterStatus", "setPrinterStatus", "printerName", "getPrinterName", "setPrinterName", "printerType", "getPrinterType", "setPrinterType", "printerPaper", "getPrinterPaper", "setPrinterPaper", "initPrinter", "", "context", "Landroid/content/Context;", "changeSelectPrinter", "showPrinter", "checkPrinterPaper", "releaseSdk", "app_stagingGeneralDebug"})
public final class PrinterViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.Nullable()
    private com.sunmi.printerx.PrinterSdk.Printer printer;
    @org.jetbrains.annotations.NotNull()
    private androidx.lifecycle.MutableLiveData<java.util.List<com.sunmi.printerx.PrinterSdk.Printer>> showPrinters;
    @org.jetbrains.annotations.NotNull()
    private androidx.lifecycle.MutableLiveData<java.lang.String> printerStatus;
    @org.jetbrains.annotations.NotNull()
    private androidx.lifecycle.MutableLiveData<java.lang.String> printerName;
    @org.jetbrains.annotations.NotNull()
    private androidx.lifecycle.MutableLiveData<java.lang.String> printerType;
    @org.jetbrains.annotations.NotNull()
    private androidx.lifecycle.MutableLiveData<java.lang.String> printerPaper;
    
    public PrinterViewModel() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.MutableLiveData<java.util.List<com.sunmi.printerx.PrinterSdk.Printer>> getShowPrinters() {
        return null;
    }
    
    public final void setShowPrinters(@org.jetbrains.annotations.NotNull()
    androidx.lifecycle.MutableLiveData<java.util.List<com.sunmi.printerx.PrinterSdk.Printer>> p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.MutableLiveData<java.lang.String> getPrinterStatus() {
        return null;
    }
    
    public final void setPrinterStatus(@org.jetbrains.annotations.NotNull()
    androidx.lifecycle.MutableLiveData<java.lang.String> p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.MutableLiveData<java.lang.String> getPrinterName() {
        return null;
    }
    
    public final void setPrinterName(@org.jetbrains.annotations.NotNull()
    androidx.lifecycle.MutableLiveData<java.lang.String> p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.MutableLiveData<java.lang.String> getPrinterType() {
        return null;
    }
    
    public final void setPrinterType(@org.jetbrains.annotations.NotNull()
    androidx.lifecycle.MutableLiveData<java.lang.String> p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.MutableLiveData<java.lang.String> getPrinterPaper() {
        return null;
    }
    
    public final void setPrinterPaper(@org.jetbrains.annotations.NotNull()
    androidx.lifecycle.MutableLiveData<java.lang.String> p0) {
    }
    
    public final void initPrinter(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    public final void changeSelectPrinter() {
    }
    
    /**
     * 获取状态等信息
     * 因为是阻塞操作最好在协程或线程中获取
     */
    public final void showPrinter(@org.jetbrains.annotations.NotNull()
    com.sunmi.printerx.PrinterSdk.Printer printer) {
    }
    
    /**
     * 一般打印小票可能需要打印纸张判断用来决定小票布局
     * 可参考此方法进行判断
     */
    public final void checkPrinterPaper(@org.jetbrains.annotations.NotNull()
    com.sunmi.printerx.PrinterSdk.Printer printer) {
    }
    
    public final void releaseSdk() {
    }
}