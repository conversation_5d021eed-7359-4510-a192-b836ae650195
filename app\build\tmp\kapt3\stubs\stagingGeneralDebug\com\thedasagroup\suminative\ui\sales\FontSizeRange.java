package com.thedasagroup.suminative.ui.sales;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0013\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\b\u0018\u0000 \u001d2\u00020\u0001:\u0001\u001dB!\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u0010\u0010\r\u001a\u00020\u0003H\u00c6\u0003\u00a2\u0006\u0004\b\u000e\u0010\tJ\u0010\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003\u00a2\u0006\u0004\b\u0010\u0010\tJ\u0010\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003\u00a2\u0006\u0004\b\u0012\u0010\tJ.\u0010\u0013\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0003H\u00c6\u0001\u00a2\u0006\u0004\b\u0014\u0010\u0015J\u0013\u0010\u0016\u001a\u00020\u00172\b\u0010\u0018\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\n\n\u0002\u0010\n\u001a\u0004\b\b\u0010\tR\u0013\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\n\n\u0002\u0010\n\u001a\u0004\b\u000b\u0010\tR\u0013\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\n\n\u0002\u0010\n\u001a\u0004\b\f\u0010\t\u00a8\u0006\u001e"}, d2 = {"Lcom/thedasagroup/suminative/ui/sales/FontSizeRange;", "", "min", "Landroidx/compose/ui/unit/TextUnit;", "max", "step", "<init>", "(JJJLkotlin/jvm/internal/DefaultConstructorMarker;)V", "getMin-XSAIIZE", "()J", "J", "getMax-XSAIIZE", "getStep-XSAIIZE", "component1", "component1-XSAIIZE", "component2", "component2-XSAIIZE", "component3", "component3-XSAIIZE", "copy", "copy-vU-0ePk", "(JJJ)Lcom/thedasagroup/suminative/ui/sales/FontSizeRange;", "equals", "", "other", "hashCode", "", "toString", "", "Companion", "app_stagingGeneralDebug"})
public final class FontSizeRange {
    private final long min = 0L;
    private final long max = 0L;
    private final long step = 0L;
    private static final long DEFAULT_TEXT_STEP = 0L;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.sales.FontSizeRange.Companion Companion = null;
    
    private FontSizeRange(long min, long max, long step) {
        super();
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u0010\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0004\n\u0002\u0010\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/thedasagroup/suminative/ui/sales/FontSizeRange$Companion;", "", "<init>", "()V", "DEFAULT_TEXT_STEP", "Landroidx/compose/ui/unit/TextUnit;", "J", "app_stagingGeneralDebug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}