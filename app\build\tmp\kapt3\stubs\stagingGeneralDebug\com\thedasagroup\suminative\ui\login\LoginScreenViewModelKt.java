package com.thedasagroup.suminative.ui.login;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000\u0010\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\u001a\u0010\u0010\u0000\u001a\u00020\u00012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u001a\u0016\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0002\u001a\u00020\u0001\u00a8\u0006\u0006"}, d2 = {"decodePublicKey", "Ljava/security/PublicKey;", "publicKey", "", "encrypt", "data", "app_stagingGeneralDebug"})
public final class LoginScreenViewModelKt {
    
    @org.jetbrains.annotations.NotNull()
    public static final java.security.PublicKey decodePublicKey(@org.jetbrains.annotations.Nullable()
    java.lang.String publicKey) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String encrypt(@org.jetbrains.annotations.NotNull()
    java.lang.String data, @org.jetbrains.annotations.NotNull()
    java.security.PublicKey publicKey) {
        return null;
    }
}