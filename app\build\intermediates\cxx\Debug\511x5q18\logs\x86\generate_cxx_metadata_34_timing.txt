# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 71ms
  [gap of 40ms]
generate_cxx_metadata completed in 129ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 10ms
generate_cxx_metadata completed in 21ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 25ms]
  create-invalidation-state 80ms
  [gap of 70ms]
  write-metadata-json-to-file 17ms
generate_cxx_metadata completed in 196ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 75ms
  [gap of 35ms]
  write-metadata-json-to-file 11ms
generate_cxx_metadata completed in 141ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 35ms]
  create-invalidation-state 1726ms
  [gap of 290ms]
  write-metadata-json-to-file 36ms
generate_cxx_metadata completed in 2092ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 86ms
  [gap of 38ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 154ms

# C/C++ build system timings
generate_cxx_metadata 31ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 32ms]
  create-invalidation-state 119ms
  [gap of 63ms]
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 229ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 24ms]
  create-invalidation-state 90ms
  [gap of 42ms]
  write-metadata-json-to-file 11ms
generate_cxx_metadata completed in 168ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 28ms]
  create-invalidation-state 102ms
  [gap of 52ms]
  write-metadata-json-to-file 15ms
generate_cxx_metadata completed in 198ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 22ms]
  create-invalidation-state 95ms
  [gap of 44ms]
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 176ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 11ms]
  create-invalidation-state 15ms
  [gap of 19ms]
generate_cxx_metadata completed in 45ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 30ms
  [gap of 22ms]
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 76ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 78ms]
  create-invalidation-state 237ms
  [gap of 345ms]
  write-metadata-json-to-file 13ms
  [gap of 12ms]
generate_cxx_metadata completed in 685ms

