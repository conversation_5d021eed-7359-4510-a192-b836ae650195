package com.thedasagroup.suminative.ui.reservations;

/**
 * Example Activity showing how to use the AreaTableSelectionActivity
 * This demonstrates the integration pattern for other activities that need
 * to select areas and tables for reservations.
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0012\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0013H\u0014J\b\u0010\u0014\u001a\u00020\u0011H\u0007J\u0010\u0010\u0015\u001a\u00020\u00112\u0006\u0010\u0016\u001a\u00020\u0005H\u0002J\u0010\u0010\u0017\u001a\u00020\u00112\u0006\u0010\u0016\u001a\u00020\u0005H\u0002R/\u0010\u0006\u001a\u0004\u0018\u00010\u00052\b\u0010\u0004\u001a\u0004\u0018\u00010\u00058B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\b\u000b\u0010\f\u001a\u0004\b\u0007\u0010\b\"\u0004\b\t\u0010\nR\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Lcom/thedasagroup/suminative/ui/reservations/AreaTableSelectionUsageExample;", "Landroidx/activity/ComponentActivity;", "<init>", "()V", "<set-?>", "Lcom/thedasagroup/suminative/ui/reservations/AreaTableSelectionHelper$AreaTableSelection;", "selectedAreaTable", "getSelectedAreaTable", "()Lcom/thedasagroup/suminative/ui/reservations/AreaTableSelectionHelper$AreaTableSelection;", "setSelectedAreaTable", "(Lcom/thedasagroup/suminative/ui/reservations/AreaTableSelectionHelper$AreaTableSelection;)V", "selectedAreaTable$delegate", "Landroidx/compose/runtime/MutableState;", "areaTableSelectionLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "Landroid/content/Intent;", "onCreate", "", "savedInstanceState", "Landroid/os/Bundle;", "ExampleScreen", "handleAreaTableSelection", "selection", "createReservationWithSelection", "app_stagingGeneralDebug"})
public final class AreaTableSelectionUsageExample extends androidx.activity.ComponentActivity {
    @org.jetbrains.annotations.NotNull()
    private final androidx.compose.runtime.MutableState selectedAreaTable$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.activity.result.ActivityResultLauncher<android.content.Intent> areaTableSelectionLauncher = null;
    
    public AreaTableSelectionUsageExample() {
        super(0);
    }
    
    private final com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection getSelectedAreaTable() {
        return null;
    }
    
    private final void setSelectedAreaTable(com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection p0) {
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @androidx.compose.runtime.Composable()
    public final void ExampleScreen() {
    }
    
    /**
     * Handle the selected area and table
     */
    private final void handleAreaTableSelection(com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection selection) {
    }
    
    /**
     * Example of creating a reservation with the selected area and table
     */
    private final void createReservationWithSelection(com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection selection) {
    }
}