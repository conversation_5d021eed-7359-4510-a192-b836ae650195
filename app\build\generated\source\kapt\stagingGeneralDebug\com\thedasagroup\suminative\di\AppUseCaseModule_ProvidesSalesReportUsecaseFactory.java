package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.SalesRepository;
import com.thedasagroup.suminative.domain.sales_report.GetSalesReportUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesSalesReportUsecaseFactory implements Factory<GetSalesReportUseCase> {
  private final Provider<SalesRepository> salesRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  public AppUseCaseModule_ProvidesSalesReportUsecaseFactory(
      Provider<SalesRepository> salesRepositoryProvider, Provider<Prefs> prefsProvider) {
    this.salesRepositoryProvider = salesRepositoryProvider;
    this.prefsProvider = prefsProvider;
  }

  @Override
  public GetSalesReportUseCase get() {
    return providesSalesReportUsecase(salesRepositoryProvider.get(), prefsProvider.get());
  }

  public static AppUseCaseModule_ProvidesSalesReportUsecaseFactory create(
      Provider<SalesRepository> salesRepositoryProvider, Provider<Prefs> prefsProvider) {
    return new AppUseCaseModule_ProvidesSalesReportUsecaseFactory(salesRepositoryProvider, prefsProvider);
  }

  public static GetSalesReportUseCase providesSalesReportUsecase(SalesRepository salesRepository,
      Prefs prefs) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesSalesReportUsecase(salesRepository, prefs));
  }
}
