package com.thedasagroup.suminative.ui.common;

import dagger.internal.DaggerGenerated;
import dagger.internal.InstanceFactory;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class CommonViewModel_Factory_Impl implements CommonViewModel.Factory {
  private final CommonViewModel_Factory delegateFactory;

  CommonViewModel_Factory_Impl(CommonViewModel_Factory delegateFactory) {
    this.delegateFactory = delegateFactory;
  }

  @Override
  public CommonViewModel create(CommonState state) {
    return delegateFactory.get(state);
  }

  public static Provider<CommonViewModel.Factory> create(CommonViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new CommonViewModel_Factory_Impl(delegateFactory));
  }

  public static dagger.internal.Provider<CommonViewModel.Factory> createFactoryProvider(
      CommonViewModel_Factory delegateFactory) {
    return InstanceFactory.create(new CommonViewModel_Factory_Impl(delegateFactory));
  }
}
