package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.repo.StockRepository;
import com.thedasagroup.suminative.ui.products.OptionDetailsUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class OrderUseCaseModule_ProvidesOptionDetailsUseCaseFactory implements Factory<OptionDetailsUseCase> {
  private final Provider<StockRepository> stockRepositoryProvider;

  public OrderUseCaseModule_ProvidesOptionDetailsUseCaseFactory(
      Provider<StockRepository> stockRepositoryProvider) {
    this.stockRepositoryProvider = stockRepositoryProvider;
  }

  @Override
  public OptionDetailsUseCase get() {
    return providesOptionDetailsUseCase(stockRepositoryProvider.get());
  }

  public static OrderUseCaseModule_ProvidesOptionDetailsUseCaseFactory create(
      Provider<StockRepository> stockRepositoryProvider) {
    return new OrderUseCaseModule_ProvidesOptionDetailsUseCaseFactory(stockRepositoryProvider);
  }

  public static OptionDetailsUseCase providesOptionDetailsUseCase(StockRepository stockRepository) {
    return Preconditions.checkNotNullFromProvides(OrderUseCaseModule.INSTANCE.providesOptionDetailsUseCase(stockRepository));
  }
}
