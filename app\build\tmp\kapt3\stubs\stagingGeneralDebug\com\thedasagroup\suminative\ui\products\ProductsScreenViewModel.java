package com.thedasagroup.suminative.ui.products;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u00f4\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0002\b\u0015\n\u0002\u0018\u0002\n\u0002\b\u0006\u0018\u0000 \u0096\u00012\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0004\u0095\u0001\u0096\u0001Bs\b\u0007\u0012\b\b\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0006\u0010\u000e\u001a\u00020\u000f\u0012\u0006\u0010\u0010\u001a\u00020\u0011\u0012\u0006\u0010\u0012\u001a\u00020\u0013\u0012\u0006\u0010\u0014\u001a\u00020\u0015\u0012\u0006\u0010\u0016\u001a\u00020\u0017\u0012\u0006\u0010\u0018\u001a\u00020\u0019\u0012\u0006\u0010\u001a\u001a\u00020\u001b\u00a2\u0006\u0004\b\u001c\u0010\u001dJ\u001a\u00106\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002090807H\u0086@\u00a2\u0006\u0002\u0010:J\u001a\u0010;\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020<0807H\u0086@\u00a2\u0006\u0002\u0010:J\"\u0010=\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020>08072\u0006\u0010?\u001a\u00020@H\u0086@\u00a2\u0006\u0002\u0010AJ\"\u0010B\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020C08072\u0006\u0010?\u001a\u00020@H\u0086@\u00a2\u0006\u0002\u0010AJP\u0010D\u001a\u00020E2\u0006\u0010F\u001a\u00020G2\u0006\u0010H\u001a\u00020I2\u0006\u0010J\u001a\u00020K2\u0006\u0010L\u001a\u00020M2\u0012\u0010N\u001a\u000e\u0012\u0004\u0012\u00020I\u0012\u0004\u0012\u00020G0O2\u0006\u0010P\u001a\u00020I2\f\u0010Q\u001a\b\u0012\u0004\u0012\u00020S0RJ\u0006\u0010T\u001a\u00020EJP\u0010U\u001a\u00020E2\u0006\u0010F\u001a\u00020G2\u0006\u0010H\u001a\u00020I2\u0006\u0010L\u001a\u00020M2\u0006\u0010J\u001a\u00020K2\u0012\u0010N\u001a\u000e\u0012\u0004\u0012\u00020I\u0012\u0004\u0012\u00020G0O2\u0006\u0010P\u001a\u00020I2\f\u0010Q\u001a\b\u0012\u0004\u0012\u00020S0RJ\u001e\u0010V\u001a\u00020E2\u0006\u0010F\u001a\u00020G2\u0006\u0010W\u001a\u00020X2\u0006\u0010Y\u001a\u00020ZJ@\u0010[\u001a\u00020E2\u0006\u0010F\u001a\u00020G2\u0006\u0010W\u001a\u00020X2\f\u0010Q\u001a\b\u0012\u0004\u0012\u00020S0R2\u0006\u0010P\u001a\u00020I2\u0012\u0010N\u001a\u000e\u0012\u0004\u0012\u00020I\u0012\u0004\u0012\u00020G0OJP\u0010\\\u001a\u00020E2\u0006\u0010F\u001a\u00020G2\u0006\u0010L\u001a\u00020]2\u0006\u0010J\u001a\u00020K2\u0006\u0010^\u001a\u00020I2\u0012\u0010N\u001a\u000e\u0012\u0004\u0012\u00020I\u0012\u0004\u0012\u00020G0O2\u0006\u0010P\u001a\u00020I2\f\u0010Q\u001a\b\u0012\u0004\u0012\u00020S0RJ(\u0010_\u001a\u00020<2\u000e\u0010`\u001a\n\u0012\u0004\u0012\u00020a\u0018\u00010R2\u000e\u0010b\u001a\n\u0012\u0004\u0012\u00020a\u0018\u00010RH\u0002J,\u0010c\u001a\u00020<2\u0010\u0010d\u001a\f\u0012\u0006\u0012\u0004\u0018\u00010e\u0018\u00010R2\u0010\u0010f\u001a\f\u0012\u0006\u0012\u0004\u0018\u00010e\u0018\u00010RH\u0002J\f\u0010g\u001a\u00020]*\u00020MH\u0002JL\u0010h\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020G08072\u0006\u0010F\u001a\u00020G2\u0006\u0010L\u001a\u00020M2\u0012\u0010N\u001a\u000e\u0012\u0004\u0012\u00020I\u0012\u0004\u0012\u00020G0O2\u0006\u0010P\u001a\u00020I2\f\u0010Q\u001a\b\u0012\u0004\u0012\u00020S0RJ\u0010\u0010i\u001a\u00020E2\b\u0010L\u001a\u0004\u0018\u00010]J*\u0010j\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020k08072\u0006\u0010F\u001a\u00020G2\u0006\u0010l\u001a\u00020ZH\u0086@\u00a2\u0006\u0002\u0010mJ*\u0010n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020K08072\u0006\u0010o\u001a\u00020I2\u0006\u0010L\u001a\u00020]H\u0086@\u00a2\u0006\u0002\u0010pJ.\u0010q\u001a\u00020E2\u0006\u0010r\u001a\u00020e2\u0006\u0010H\u001a\u00020I2\u0006\u0010L\u001a\u00020]2\u0006\u0010J\u001a\u00020K2\u0006\u0010s\u001a\u00020aJ.\u0010t\u001a\u00020E2\u0006\u0010J\u001a\u00020K2\u0006\u0010s\u001a\u00020a2\u0006\u0010r\u001a\u00020e2\u0006\u0010L\u001a\u00020M2\u0006\u0010H\u001a\u00020IJ.\u0010u\u001a\u00020E2\u0006\u0010J\u001a\u00020K2\u0006\u0010r\u001a\u00020e2\u0006\u0010s\u001a\u00020a2\u0006\u0010L\u001a\u00020M2\u0006\u0010H\u001a\u00020IJ\u001e\u0010v\u001a\u00020M2\u0006\u0010L\u001a\u00020M2\u0006\u0010J\u001a\u00020K2\u0006\u0010w\u001a\u00020IJ6\u0010x\u001a\u00020E2\u0006\u0010r\u001a\u00020e2\u0006\u0010s\u001a\u00020a2\u0006\u0010H\u001a\u00020I2\u0006\u0010y\u001a\u00020I2\u0006\u0010J\u001a\u00020K2\u0006\u0010L\u001a\u00020MJ\u001a\u0010z\u001a\u00020E2\b\u0010F\u001a\u0004\u0018\u00010{2\b\b\u0002\u0010|\u001a\u00020<J\u000e\u0010}\u001a\u00020E2\u0006\u0010~\u001a\u00020<J\u000e\u0010\u007f\u001a\u00020E2\u0006\u0010F\u001a\u00020GJ\u0010\u0010\u0080\u0001\u001a\u00020E2\u0007\u0010\u0081\u0001\u001a\u00020<J\u0007\u0010\u0082\u0001\u001a\u00020EJ\u001b\u0010\u0083\u0001\u001a\u00020E2\u0007\u0010\u0084\u0001\u001a\u00020I2\u0007\u0010\u0085\u0001\u001a\u00020GH\u0002J\'\u0010\u0086\u0001\u001a\u0004\u0018\u00010I2\u0006\u0010P\u001a\u00020I2\f\u0010Q\u001a\b\u0012\u0004\u0012\u00020S0RH\u0002\u00a2\u0006\u0003\u0010\u0087\u0001J\u0010\u0010\u0088\u0001\u001a\u00020E2\u0007\u0010\u0089\u0001\u001a\u00020SJ\u0010\u0010\u008a\u0001\u001a\u00020E2\u0007\u0010\u0084\u0001\u001a\u00020IJ\u0010\u0010\u008b\u0001\u001a\u00020E2\u0007\u0010\u008c\u0001\u001a\u00020IJ\t\u0010\u008d\u0001\u001a\u00020EH\u0002J$\u0010\u008e\u0001\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020k08072\u0006\u0010F\u001a\u00020GH\u0086@\u00a2\u0006\u0003\u0010\u008f\u0001J%\u0010\u0090\u0001\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020k08072\u0007\u0010?\u001a\u00030\u0091\u0001H\u0086@\u00a2\u0006\u0003\u0010\u0092\u0001J\u0007\u0010\u0093\u0001\u001a\u00020<J\u0007\u0010\u0094\u0001\u001a\u00020<R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010!R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010#R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010%R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\'R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010)R\u0011\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010+R\u0011\u0010\u0012\u001a\u00020\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010-R\u0011\u0010\u0014\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010/R\u0011\u0010\u0016\u001a\u00020\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u00101R\u0011\u0010\u0018\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u00103R\u0011\u0010\u001a\u001a\u00020\u001b\u00a2\u0006\b\n\u0000\u001a\u0004\b4\u00105\u00a8\u0006\u0097\u0001"}, d2 = {"Lcom/thedasagroup/suminative/ui/products/ProductsScreenViewModel;", "Lcom/airbnb/mvrx/MavericksViewModel;", "Lcom/thedasagroup/suminative/ui/products/ProductsScreenState;", "state", "prefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "stockUseCase", "Lcom/thedasagroup/suminative/ui/stock/StockUseCase;", "orderUseCase", "Lcom/thedasagroup/suminative/ui/products/PlaceOnlineOrderUseCase;", "offlineOrderUseCase", "Lcom/thedasagroup/suminative/ui/products/OrderUseCase;", "cloudPrintUseCase", "Lcom/thedasagroup/suminative/domain/cloud_print/CloudPrintUseCase;", "getOptionDetailsUseCase", "Lcom/thedasagroup/suminative/ui/products/OptionDetailsUseCase;", "salesUseCase", "Lcom/thedasagroup/suminative/ui/sales/TotalSalesUseCase;", "salesReportUseCase", "Lcom/thedasagroup/suminative/domain/sales_report/GetSalesReportUseCase;", "trueTimeImpl", "Lcom/instacart/truetime/time/TrueTimeImpl;", "productsRepository", "Lcom/thedasagroup/suminative/data/repo/ProductRepository;", "downloadProductsUseCase", "Lcom/thedasagroup/suminative/ui/products/DownloadProductsUseCase;", "orderSyncManager", "Lcom/thedasagroup/suminative/work/OrderSyncManager;", "<init>", "(Lcom/thedasagroup/suminative/ui/products/ProductsScreenState;Lcom/thedasagroup/suminative/data/prefs/Prefs;Lcom/thedasagroup/suminative/ui/stock/StockUseCase;Lcom/thedasagroup/suminative/ui/products/PlaceOnlineOrderUseCase;Lcom/thedasagroup/suminative/ui/products/OrderUseCase;Lcom/thedasagroup/suminative/domain/cloud_print/CloudPrintUseCase;Lcom/thedasagroup/suminative/ui/products/OptionDetailsUseCase;Lcom/thedasagroup/suminative/ui/sales/TotalSalesUseCase;Lcom/thedasagroup/suminative/domain/sales_report/GetSalesReportUseCase;Lcom/instacart/truetime/time/TrueTimeImpl;Lcom/thedasagroup/suminative/data/repo/ProductRepository;Lcom/thedasagroup/suminative/ui/products/DownloadProductsUseCase;Lcom/thedasagroup/suminative/work/OrderSyncManager;)V", "getPrefs", "()Lcom/thedasagroup/suminative/data/prefs/Prefs;", "getStockUseCase", "()Lcom/thedasagroup/suminative/ui/stock/StockUseCase;", "getOrderUseCase", "()Lcom/thedasagroup/suminative/ui/products/PlaceOnlineOrderUseCase;", "getOfflineOrderUseCase", "()Lcom/thedasagroup/suminative/ui/products/OrderUseCase;", "getCloudPrintUseCase", "()Lcom/thedasagroup/suminative/domain/cloud_print/CloudPrintUseCase;", "getGetOptionDetailsUseCase", "()Lcom/thedasagroup/suminative/ui/products/OptionDetailsUseCase;", "getSalesUseCase", "()Lcom/thedasagroup/suminative/ui/sales/TotalSalesUseCase;", "getSalesReportUseCase", "()Lcom/thedasagroup/suminative/domain/sales_report/GetSalesReportUseCase;", "getTrueTimeImpl", "()Lcom/instacart/truetime/time/TrueTimeImpl;", "getProductsRepository", "()Lcom/thedasagroup/suminative/data/repo/ProductRepository;", "getDownloadProductsUseCase", "()Lcom/thedasagroup/suminative/ui/products/DownloadProductsUseCase;", "getOrderSyncManager", "()Lcom/thedasagroup/suminative/work/OrderSyncManager;", "getStockItems", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/stock/StockItemsResponse;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "refreshProducts", "", "getTotalSales", "Lcom/thedasagroup/suminative/data/model/response/sales/SalesResponse;", "request", "Lcom/thedasagroup/suminative/data/model/request/sales/SalesRequest;", "(Lcom/thedasagroup/suminative/data/model/request/sales/SalesRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getSalesReport", "Lcom/thedasagroup/suminative/data/model/response/sales/SalesReportResponse;", "updateStock", "", "order", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "stock", "", "optionDetails", "Lcom/thedasagroup/suminative/data/model/response/options_details/OptionDetails;", "stockItem", "Lcom/thedasagroup/suminative/data/model/request/order/StoreItem;", "tableOrders", "", "selectedTableIndex", "selectedTables", "", "Lcom/thedasagroup/suminative/ui/reservations/AreaTableSelectionHelper$AreaTableSelection;", "resetStock", "updateCartStock", "updateCartItemNotes", "cart", "Lcom/thedasagroup/suminative/data/model/request/order/Cart;", "notes", "", "voidCartItem", "addItemToCart", "Lcom/thedasagroup/suminative/data/model/response/stock/StockItem;", "quantity", "areOptionSetsEqual", "optionSets1", "Lcom/thedasagroup/suminative/data/model/request/order/OptionSet;", "optionSets2", "areOptionsEqual", "options1", "Lcom/thedasagroup/suminative/data/model/response/store_orders/Option;", "options2", "toStockItem", "removeItemFromCart", "updateProductDetailsBottomSheetVisibility", "placeOrder", "Lcom/thedasagroup/suminative/data/model/response/order/OrderResponse2;", "transId", "(Lcom/thedasagroup/suminative/data/model/request/order/Order;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getOptionDetails", "itemId", "(ILcom/thedasagroup/suminative/data/model/response/stock/StockItem;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateSelectedOptionCondition1", "option", "currentOptionSet", "addSelectedOptionCondition2", "removeSelectedOptionCondition2", "calculateTotal", "updatedStock", "updateOptionStock", "optionStock", "updateShowPrintingPreview", "Lcom/thedasagroup/suminative/data/model/request/pagination/OrderItem2;", "shouldPrintInstant", "showSalesReportDialog", "show", "updateOrder", "updateCartVisibility", "visible", "clearCart", "updateTableOrder", "tableId", "updatedOrder", "getCurrentTableId", "(ILjava/util/List;)Ljava/lang/Integer;", "addSelectedTable", "selection", "removeSelectedTable", "setSelectedTableIndex", "index", "monitorSyncStatus", "placeOrderOffline", "(Lcom/thedasagroup/suminative/data/model/request/order/Order;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cloudPrint", "Lcom/thedasagroup/suminative/data/model/request/cloud_print/CloudPrintRequest;", "(Lcom/thedasagroup/suminative/data/model/request/cloud_print/CloudPrintRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isMyGuava", "isSumUp", "Factory", "Companion", "app_stagingGeneralDebug"})
public final class ProductsScreenViewModel extends com.airbnb.mvrx.MavericksViewModel<com.thedasagroup.suminative.ui.products.ProductsScreenState> {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.prefs.Prefs prefs = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.stock.StockUseCase stockUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.products.PlaceOnlineOrderUseCase orderUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.products.OrderUseCase offlineOrderUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.domain.cloud_print.CloudPrintUseCase cloudPrintUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.products.OptionDetailsUseCase getOptionDetailsUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.sales.TotalSalesUseCase salesUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.domain.sales_report.GetSalesReportUseCase salesReportUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.instacart.truetime.time.TrueTimeImpl trueTimeImpl = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.repo.ProductRepository productsRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.products.DownloadProductsUseCase downloadProductsUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.work.OrderSyncManager orderSyncManager = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.products.ProductsScreenViewModel.Companion Companion = null;
    
    @dagger.assisted.AssistedInject()
    public ProductsScreenViewModel(@dagger.assisted.Assisted()
    @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.ProductsScreenState state, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.stock.StockUseCase stockUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.PlaceOnlineOrderUseCase orderUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.OrderUseCase offlineOrderUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.cloud_print.CloudPrintUseCase cloudPrintUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.OptionDetailsUseCase getOptionDetailsUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.sales.TotalSalesUseCase salesUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.sales_report.GetSalesReportUseCase salesReportUseCase, @org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl trueTimeImpl, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.ProductRepository productsRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.DownloadProductsUseCase downloadProductsUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.work.OrderSyncManager orderSyncManager) {
        super(null, null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.prefs.Prefs getPrefs() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.stock.StockUseCase getStockUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.products.PlaceOnlineOrderUseCase getOrderUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.products.OrderUseCase getOfflineOrderUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.cloud_print.CloudPrintUseCase getCloudPrintUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.products.OptionDetailsUseCase getGetOptionDetailsUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.sales.TotalSalesUseCase getSalesUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.sales_report.GetSalesReportUseCase getSalesReportUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.instacart.truetime.time.TrueTimeImpl getTrueTimeImpl() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.repo.ProductRepository getProductsRepository() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.products.DownloadProductsUseCase getDownloadProductsUseCase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.work.OrderSyncManager getOrderSyncManager() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getStockItems(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object refreshProducts(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<java.lang.Boolean>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTotalSales(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.sales.SalesRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.sales.SalesResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getSalesReport(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.sales.SalesRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.sales.SalesReportResponse>>> $completion) {
        return null;
    }
    
    public final void updateStock(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, int stock, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.options_details.OptionDetails optionDetails, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.StoreItem stockItem, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.Integer, com.thedasagroup.suminative.data.model.request.order.Order> tableOrders, int selectedTableIndex, @org.jetbrains.annotations.NotNull()
    java.util.List<com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection> selectedTables) {
    }
    
    public final void resetStock() {
    }
    
    public final void updateCartStock(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, int stock, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.StoreItem stockItem, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.options_details.OptionDetails optionDetails, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.Integer, com.thedasagroup.suminative.data.model.request.order.Order> tableOrders, int selectedTableIndex, @org.jetbrains.annotations.NotNull()
    java.util.List<com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection> selectedTables) {
    }
    
    public final void updateCartItemNotes(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Cart cart, @org.jetbrains.annotations.NotNull()
    java.lang.String notes) {
    }
    
    public final void voidCartItem(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Cart cart, @org.jetbrains.annotations.NotNull()
    java.util.List<com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection> selectedTables, int selectedTableIndex, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.Integer, com.thedasagroup.suminative.data.model.request.order.Order> tableOrders) {
    }
    
    public final void addItemToCart(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.stock.StockItem stockItem, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.options_details.OptionDetails optionDetails, int quantity, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.Integer, com.thedasagroup.suminative.data.model.request.order.Order> tableOrders, int selectedTableIndex, @org.jetbrains.annotations.NotNull()
    java.util.List<com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection> selectedTables) {
    }
    
    /**
     * Helper function to compare two option sets for equality
     * Used to determine if an item with the same options already exists in cart
     */
    private final boolean areOptionSetsEqual(java.util.List<com.thedasagroup.suminative.data.model.request.order.OptionSet> optionSets1, java.util.List<com.thedasagroup.suminative.data.model.request.order.OptionSet> optionSets2) {
        return false;
    }
    
    /**
     * Helper function to compare two option lists for equality
     */
    private final boolean areOptionsEqual(java.util.List<com.thedasagroup.suminative.data.model.response.store_orders.Option> options1, java.util.List<com.thedasagroup.suminative.data.model.response.store_orders.Option> options2) {
        return false;
    }
    
    /**
     * Extension function to convert StoreItem to StockItem
     * Used for calculations when updating existing cart items
     */
    private final com.thedasagroup.suminative.data.model.response.stock.StockItem toStockItem(com.thedasagroup.suminative.data.model.request.order.StoreItem $this$toStockItem) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.request.order.Order>> removeItemFromCart(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.StoreItem stockItem, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.Integer, com.thedasagroup.suminative.data.model.request.order.Order> tableOrders, int selectedTableIndex, @org.jetbrains.annotations.NotNull()
    java.util.List<com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection> selectedTables) {
        return null;
    }
    
    public final void updateProductDetailsBottomSheetVisibility(@org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.stock.StockItem stockItem) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object placeOrder(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
    java.lang.String transId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.order.OrderResponse2>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getOptionDetails(int itemId, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.stock.StockItem stockItem, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.options_details.OptionDetails>>> $completion) {
        return null;
    }
    
    public final void updateSelectedOptionCondition1(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.store_orders.Option option, int stock, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.stock.StockItem stockItem, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.options_details.OptionDetails optionDetails, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.OptionSet currentOptionSet) {
    }
    
    public final void addSelectedOptionCondition2(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.options_details.OptionDetails optionDetails, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.OptionSet currentOptionSet, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.store_orders.Option option, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.StoreItem stockItem, int stock) {
    }
    
    public final void removeSelectedOptionCondition2(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.options_details.OptionDetails optionDetails, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.store_orders.Option option, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.OptionSet currentOptionSet, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.StoreItem stockItem, int stock) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.model.request.order.StoreItem calculateTotal(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.StoreItem stockItem, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.options_details.OptionDetails optionDetails, int updatedStock) {
        return null;
    }
    
    public final void updateOptionStock(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.store_orders.Option option, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.OptionSet currentOptionSet, int stock, int optionStock, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.options_details.OptionDetails optionDetails, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.StoreItem stockItem) {
    }
    
    public final void updateShowPrintingPreview(@org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.pagination.OrderItem2 order, boolean shouldPrintInstant) {
    }
    
    public final void showSalesReportDialog(boolean show) {
    }
    
    public final void updateOrder(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order) {
    }
    
    public final void updateCartVisibility(boolean visible) {
    }
    
    public final void clearCart() {
    }
    
    /**
     * Update the order for a specific table
     */
    private final void updateTableOrder(int tableId, com.thedasagroup.suminative.data.model.request.order.Order updatedOrder) {
    }
    
    /**
     * Get the current table ID
     */
    private final java.lang.Integer getCurrentTableId(int selectedTableIndex, java.util.List<com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection> selectedTables) {
        return null;
    }
    
    /**
     * Add a selected table to the list
     */
    public final void addSelectedTable(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection selection) {
    }
    
    /**
     * Remove a selected table from the list
     */
    public final void removeSelectedTable(int tableId) {
    }
    
    /**
     * Set the selected table index
     */
    public final void setSelectedTableIndex(int index) {
    }
    
    /**
     * Get the currently selected table
     */
    private final void monitorSyncStatus() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object placeOrderOffline(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.order.OrderResponse2>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object cloudPrint(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.cloud_print.CloudPrintRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.order.OrderResponse2>>> $completion) {
        return null;
    }
    
    public final boolean isMyGuava() {
        return false;
    }
    
    public final boolean isSumUp() {
        return false;
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u001b\u0010\u0006\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0003H\u0096\u0001J\u0013\u0010\n\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0007\u001a\u00020\bH\u0096\u0001\u00a8\u0006\u000b"}, d2 = {"Lcom/thedasagroup/suminative/ui/products/ProductsScreenViewModel$Companion;", "Lcom/airbnb/mvrx/MavericksViewModelFactory;", "Lcom/thedasagroup/suminative/ui/products/ProductsScreenViewModel;", "Lcom/thedasagroup/suminative/ui/products/ProductsScreenState;", "<init>", "()V", "create", "viewModelContext", "Lcom/airbnb/mvrx/ViewModelContext;", "state", "initialState", "app_stagingGeneralDebug"})
    public static final class Companion implements com.airbnb.mvrx.MavericksViewModelFactory<com.thedasagroup.suminative.ui.products.ProductsScreenViewModel, com.thedasagroup.suminative.ui.products.ProductsScreenState> {
        
        private Companion() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.products.ProductsScreenViewModel create(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.products.ProductsScreenState state) {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.products.ProductsScreenState initialState(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext) {
            return null;
        }
    }
    
    @dagger.assisted.AssistedFactory()
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\bg\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001J\u0010\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0003H&\u00a8\u0006\u0006"}, d2 = {"Lcom/thedasagroup/suminative/ui/products/ProductsScreenViewModel$Factory;", "Lcom/airbnb/mvrx/hilt/AssistedViewModelFactory;", "Lcom/thedasagroup/suminative/ui/products/ProductsScreenViewModel;", "Lcom/thedasagroup/suminative/ui/products/ProductsScreenState;", "create", "state", "app_stagingGeneralDebug"})
    public static abstract interface Factory extends com.airbnb.mvrx.hilt.AssistedViewModelFactory<com.thedasagroup.suminative.ui.products.ProductsScreenViewModel, com.thedasagroup.suminative.ui.products.ProductsScreenState> {
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public abstract com.thedasagroup.suminative.ui.products.ProductsScreenViewModel create(@org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.products.ProductsScreenState state);
    }
}