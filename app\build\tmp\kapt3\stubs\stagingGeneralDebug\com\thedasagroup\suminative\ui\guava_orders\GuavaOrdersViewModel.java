package com.thedasagroup.suminative.ui.guava_orders;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0004\u0018\u0000 \u00122\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0002\u0011\u0012B#\b\u0007\u0012\b\b\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\u000e\u0010\n\u001a\u00020\u000bH\u0086@\u00a2\u0006\u0002\u0010\fJ\u0016\u0010\r\u001a\u00020\u000b2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000fR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/thedasagroup/suminative/ui/guava_orders/GuavaOrdersViewModel;", "Lcom/airbnb/mvrx/MavericksViewModel;", "Lcom/thedasagroup/suminative/ui/guava_orders/GuavaOrdersState;", "initialState", "myGuavaGetOrdersUseCase", "Lcom/thedasagroup/suminative/domain/myguava/MyGuavaGetOrdersUseCase;", "myGuavaRepository", "Lcom/thedasagroup/suminative/data/repo/MyGuavaRepository;", "<init>", "(Lcom/thedasagroup/suminative/ui/guava_orders/GuavaOrdersState;Lcom/thedasagroup/suminative/domain/myguava/MyGuavaGetOrdersUseCase;Lcom/thedasagroup/suminative/data/repo/MyGuavaRepository;)V", "fetchOrders", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "voidOrder", "orderId", "", "amount", "Factory", "Companion", "app_stagingGeneralDebug"})
public final class GuavaOrdersViewModel extends com.airbnb.mvrx.MavericksViewModel<com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersState> {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.domain.myguava.MyGuavaGetOrdersUseCase myGuavaGetOrdersUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.repo.MyGuavaRepository myGuavaRepository = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersViewModel.Companion Companion = null;
    
    @dagger.assisted.AssistedInject()
    public GuavaOrdersViewModel(@dagger.assisted.Assisted()
    @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersState initialState, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.myguava.MyGuavaGetOrdersUseCase myGuavaGetOrdersUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.MyGuavaRepository myGuavaRepository) {
        super(null, null);
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object fetchOrders(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    public final void voidOrder(@org.jetbrains.annotations.NotNull()
    java.lang.String orderId, @org.jetbrains.annotations.NotNull()
    java.lang.String amount) {
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u001b\u0010\u0006\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0003H\u0096\u0001J\u0013\u0010\n\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0007\u001a\u00020\bH\u0096\u0001\u00a8\u0006\u000b"}, d2 = {"Lcom/thedasagroup/suminative/ui/guava_orders/GuavaOrdersViewModel$Companion;", "Lcom/airbnb/mvrx/MavericksViewModelFactory;", "Lcom/thedasagroup/suminative/ui/guava_orders/GuavaOrdersViewModel;", "Lcom/thedasagroup/suminative/ui/guava_orders/GuavaOrdersState;", "<init>", "()V", "create", "viewModelContext", "Lcom/airbnb/mvrx/ViewModelContext;", "state", "initialState", "app_stagingGeneralDebug"})
    public static final class Companion implements com.airbnb.mvrx.MavericksViewModelFactory<com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersViewModel, com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersState> {
        
        private Companion() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersViewModel create(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersState state) {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersState initialState(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext) {
            return null;
        }
    }
    
    @dagger.assisted.AssistedFactory()
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\bg\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001J\u0010\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0003H&\u00a8\u0006\u0006"}, d2 = {"Lcom/thedasagroup/suminative/ui/guava_orders/GuavaOrdersViewModel$Factory;", "Lcom/airbnb/mvrx/hilt/AssistedViewModelFactory;", "Lcom/thedasagroup/suminative/ui/guava_orders/GuavaOrdersViewModel;", "Lcom/thedasagroup/suminative/ui/guava_orders/GuavaOrdersState;", "create", "state", "app_stagingGeneralDebug"})
    public static abstract interface Factory extends com.airbnb.mvrx.hilt.AssistedViewModelFactory<com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersViewModel, com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersState> {
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public abstract com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersViewModel create(@org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersState state);
    }
}