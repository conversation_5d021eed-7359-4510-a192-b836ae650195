{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,559,669,751,857,987,1065,1141,1232,1325,1423,1518,1618,1711,1804,1899,1990,2081,2167,2277,2388,2491,2602,2710,2817,2976,17624", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,554,664,746,852,982,1060,1136,1227,1320,1418,1513,1613,1706,1799,1894,1985,2076,2162,2272,2383,2486,2597,2705,2812,2971,3070,17706"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,235,376,452,538,665,828,1015,1168,1299,1400,1548,1607,1665,1760,1868,1944,2103,2209,2296,2440,2516,2607,2703,2842,2945,3049,3143,3213,3285,3417,3512,3731,3887,3996,4187,4294,4482,4593,4744,4868,5003,5118,5270,5376,5482,5640,5735,5892,5986,6172,6261,6406,6484,6602,6713,6789,6873,6947,7023,7111,7187,7330,7442,7605,7714,7828,7941,8104,8210,8316,8440,8536,8663,8785,8910,9028,9107,9160,9216,9269,9320,9372,9444,9498,9570,9621,9666,9745,9789,9846,9902,9958,10014,10068,10119,10194,10244,10303,10349,10414,10477,10534,10699,10821,10904,10965,11050,11119,11187,11312,11393,11469,11549,11627,11695,11792,11868,11955,12033,12170,12251,12343,12438,12539,12630,12711,12832,12943,13070,13183,13260,13370,13450,13526,13660,13798,13870,13947,14045,14156,16207,16322,16529,16576,16739,16918,17009,17130,17237,17380,17584,17709,17889,17962,18041,18110,18198,18321,18365,18458,18599,18694,18833,18963,19105,19215,19333,19396,19455,19514,19569,19635,19716,19810,19934,20004,20075,20364,20604,20741,20874,21012,21111,21223,21350,21500,21671,21775,21889,21980,22103,22221,22383,22478,22552,22611,22889,22983,23063,23124,23184,23299,23418,23529,23674,23864,23939,24005,24072,24247,24325,24417,24497,24594,24766,24862,24952,25072,25170,25334,25472,25528,25655,25741,25824,25882,25958,26009,26075,26184,26236,26297,26464,26585,26689,26753,26900,27044,27172,27282,27591,27683,27783,27883,28064,28305,28394,28598,28714,28857,28975,29059,29148,29238,29372,29557,29701,29750,29837,29980,30119,30203,30284,30383,30468,30550,30623,30686,30767,30837,30911,31011,31078,31145,31245,31322,31413,31644,31768,31856,31914,31989,32135,32195,32272,32352,32429,32513,32740,32795,32872,32957,33034,33130,33312,33404,33487,33571,33649,33717,33797,33882,34059,34140,34194,34287,34364,34438,34515,34625,34726,34834,34948,35025,35075,35127,35222,35326,35410,35534,35749,35865,35946,36087,36228,36370,36504,36608,36767,36904,37099,37280,37408,37540,37630,37725,37837,37985,38103,38256,38333,38736,38823,38917,39003", "endColumns": "179,140,75,85,126,162,186,152,130,100,147,58,57,94,107,75,158,105,86,143,75,90,95,138,102,103,93,69,71,131,94,218,155,108,190,106,187,110,150,123,134,114,151,105,105,157,94,156,93,185,88,144,77,117,110,75,83,73,75,87,75,142,111,162,108,113,112,162,105,105,123,95,126,121,124,117,78,52,55,52,50,51,71,53,71,50,44,78,43,56,55,55,55,53,50,74,49,58,45,64,62,56,164,121,82,60,84,68,67,124,80,75,79,77,67,96,75,86,77,136,80,91,94,100,90,80,120,110,126,112,76,109,79,75,133,137,71,76,97,110,2050,114,206,46,162,178,90,120,106,142,203,124,179,72,78,68,87,122,43,92,140,94,138,129,141,109,117,62,58,58,54,65,80,93,123,69,70,288,239,136,132,137,98,111,126,149,170,103,113,90,122,117,161,94,73,58,277,93,79,60,59,114,118,110,144,189,74,65,66,174,77,91,79,96,171,95,89,119,97,163,137,55,126,85,82,57,75,50,65,108,51,60,166,120,103,63,146,143,127,109,308,91,99,99,180,240,88,203,115,142,117,83,88,89,133,184,143,48,86,142,138,83,80,98,84,81,72,62,80,69,73,99,66,66,99,76,90,230,123,87,57,74,145,59,76,79,76,83,226,54,76,84,76,95,181,91,82,83,77,67,79,84,176,80,53,92,76,73,76,109,100,107,113,76,49,51,94,103,83,123,214,115,80,140,140,141,133,103,158,136,194,180,127,131,89,94,111,147,117,152,76,402,86,93,85,88", "endOffsets": "230,371,447,533,660,823,1010,1163,1294,1395,1543,1602,1660,1755,1863,1939,2098,2204,2291,2435,2511,2602,2698,2837,2940,3044,3138,3208,3280,3412,3507,3726,3882,3991,4182,4289,4477,4588,4739,4863,4998,5113,5265,5371,5477,5635,5730,5887,5981,6167,6256,6401,6479,6597,6708,6784,6868,6942,7018,7106,7182,7325,7437,7600,7709,7823,7936,8099,8205,8311,8435,8531,8658,8780,8905,9023,9102,9155,9211,9264,9315,9367,9439,9493,9565,9616,9661,9740,9784,9841,9897,9953,10009,10063,10114,10189,10239,10298,10344,10409,10472,10529,10694,10816,10899,10960,11045,11114,11182,11307,11388,11464,11544,11622,11690,11787,11863,11950,12028,12165,12246,12338,12433,12534,12625,12706,12827,12938,13065,13178,13255,13365,13445,13521,13655,13793,13865,13942,14040,14151,16202,16317,16524,16571,16734,16913,17004,17125,17232,17375,17579,17704,17884,17957,18036,18105,18193,18316,18360,18453,18594,18689,18828,18958,19100,19210,19328,19391,19450,19509,19564,19630,19711,19805,19929,19999,20070,20359,20599,20736,20869,21007,21106,21218,21345,21495,21666,21770,21884,21975,22098,22216,22378,22473,22547,22606,22884,22978,23058,23119,23179,23294,23413,23524,23669,23859,23934,24000,24067,24242,24320,24412,24492,24589,24761,24857,24947,25067,25165,25329,25467,25523,25650,25736,25819,25877,25953,26004,26070,26179,26231,26292,26459,26580,26684,26748,26895,27039,27167,27277,27586,27678,27778,27878,28059,28300,28389,28593,28709,28852,28970,29054,29143,29233,29367,29552,29696,29745,29832,29975,30114,30198,30279,30378,30463,30545,30618,30681,30762,30832,30906,31006,31073,31140,31240,31317,31408,31639,31763,31851,31909,31984,32130,32190,32267,32347,32424,32508,32735,32790,32867,32952,33029,33125,33307,33399,33482,33566,33644,33712,33792,33877,34054,34135,34189,34282,34359,34433,34510,34620,34721,34829,34943,35020,35070,35122,35217,35321,35405,35529,35744,35860,35941,36082,36223,36365,36499,36603,36762,36899,37094,37275,37403,37535,37625,37720,37832,37980,38098,38251,38328,38731,38818,38912,38998,39087"}, "to": {"startLines": "193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18360,18540,18681,18757,18843,18970,19133,19320,19473,19604,19705,19853,19912,19970,20065,20173,20249,20408,20514,20601,20745,20821,20912,21008,21147,21250,21354,21448,21518,21590,21722,21817,22036,22192,22301,22492,22599,22787,22898,23049,23173,23308,23423,23575,23681,23787,23945,24040,24197,24291,24477,24566,24711,24789,24907,25018,25094,25178,25252,25328,25416,25492,25635,25747,25910,26019,26133,26246,26409,26515,26621,26745,26841,26968,27090,27215,27333,27412,27465,27521,27574,27625,27677,27749,27803,27875,27926,27971,28050,28094,28151,28207,28263,28319,28373,28424,28499,28549,28608,28654,28719,28782,28839,29004,29126,29209,29270,29355,29424,29492,29617,29698,29774,29854,29932,30000,30097,30173,30260,30338,30475,30556,30648,30743,30844,30935,31016,31137,31248,31375,31488,31565,31675,31755,31831,31965,32103,32175,32252,32350,32461,34512,34627,34834,34881,35044,35223,35314,35435,35542,35685,35889,36014,36194,36267,36346,36415,36503,36626,36670,36763,36904,36999,37138,37268,37410,37520,37638,37701,37760,37819,37874,37940,38021,38115,38239,38309,38380,38669,38909,39046,39179,39317,39416,39528,39655,39805,39976,40080,40194,40285,40408,40526,40688,40783,40857,40916,41194,41288,41368,41429,41489,41604,41723,41834,41979,42169,42244,42310,42377,42552,42630,42722,42802,42899,43071,43167,43257,43377,43475,43639,43777,43833,43960,44046,44129,44187,44263,44314,44380,44489,44541,44602,44769,44890,44994,45058,45205,45349,45477,45587,45896,45988,46088,46188,46369,46610,46699,46903,47019,47162,47280,47364,47453,47543,47677,47862,48006,48055,48142,48285,48424,48508,48589,48688,48773,48855,48928,48991,49072,49142,49216,49316,49383,49450,49550,49627,49718,49949,50073,50161,50219,50294,50440,50500,50577,50657,50734,50818,51045,51100,51177,51262,51339,51435,51617,51709,51792,51876,51954,52022,52102,52187,52364,52445,52499,52592,52669,52743,52820,52930,53031,53139,53253,53330,53380,53432,53527,53631,53715,53839,54054,54170,54251,54392,54533,54675,54809,54913,55072,55209,55404,55585,55713,55845,55935,56030,56142,56290,56408,56561,56638,57041,57128,57222,57308", "endColumns": "179,140,75,85,126,162,186,152,130,100,147,58,57,94,107,75,158,105,86,143,75,90,95,138,102,103,93,69,71,131,94,218,155,108,190,106,187,110,150,123,134,114,151,105,105,157,94,156,93,185,88,144,77,117,110,75,83,73,75,87,75,142,111,162,108,113,112,162,105,105,123,95,126,121,124,117,78,52,55,52,50,51,71,53,71,50,44,78,43,56,55,55,55,53,50,74,49,58,45,64,62,56,164,121,82,60,84,68,67,124,80,75,79,77,67,96,75,86,77,136,80,91,94,100,90,80,120,110,126,112,76,109,79,75,133,137,71,76,97,110,2050,114,206,46,162,178,90,120,106,142,203,124,179,72,78,68,87,122,43,92,140,94,138,129,141,109,117,62,58,58,54,65,80,93,123,69,70,288,239,136,132,137,98,111,126,149,170,103,113,90,122,117,161,94,73,58,277,93,79,60,59,114,118,110,144,189,74,65,66,174,77,91,79,96,171,95,89,119,97,163,137,55,126,85,82,57,75,50,65,108,51,60,166,120,103,63,146,143,127,109,308,91,99,99,180,240,88,203,115,142,117,83,88,89,133,184,143,48,86,142,138,83,80,98,84,81,72,62,80,69,73,99,66,66,99,76,90,230,123,87,57,74,145,59,76,79,76,83,226,54,76,84,76,95,181,91,82,83,77,67,79,84,176,80,53,92,76,73,76,109,100,107,113,76,49,51,94,103,83,123,214,115,80,140,140,141,133,103,158,136,194,180,127,131,89,94,111,147,117,152,76,402,86,93,85,88", "endOffsets": "18535,18676,18752,18838,18965,19128,19315,19468,19599,19700,19848,19907,19965,20060,20168,20244,20403,20509,20596,20740,20816,20907,21003,21142,21245,21349,21443,21513,21585,21717,21812,22031,22187,22296,22487,22594,22782,22893,23044,23168,23303,23418,23570,23676,23782,23940,24035,24192,24286,24472,24561,24706,24784,24902,25013,25089,25173,25247,25323,25411,25487,25630,25742,25905,26014,26128,26241,26404,26510,26616,26740,26836,26963,27085,27210,27328,27407,27460,27516,27569,27620,27672,27744,27798,27870,27921,27966,28045,28089,28146,28202,28258,28314,28368,28419,28494,28544,28603,28649,28714,28777,28834,28999,29121,29204,29265,29350,29419,29487,29612,29693,29769,29849,29927,29995,30092,30168,30255,30333,30470,30551,30643,30738,30839,30930,31011,31132,31243,31370,31483,31560,31670,31750,31826,31960,32098,32170,32247,32345,32456,34507,34622,34829,34876,35039,35218,35309,35430,35537,35680,35884,36009,36189,36262,36341,36410,36498,36621,36665,36758,36899,36994,37133,37263,37405,37515,37633,37696,37755,37814,37869,37935,38016,38110,38234,38304,38375,38664,38904,39041,39174,39312,39411,39523,39650,39800,39971,40075,40189,40280,40403,40521,40683,40778,40852,40911,41189,41283,41363,41424,41484,41599,41718,41829,41974,42164,42239,42305,42372,42547,42625,42717,42797,42894,43066,43162,43252,43372,43470,43634,43772,43828,43955,44041,44124,44182,44258,44309,44375,44484,44536,44597,44764,44885,44989,45053,45200,45344,45472,45582,45891,45983,46083,46183,46364,46605,46694,46898,47014,47157,47275,47359,47448,47538,47672,47857,48001,48050,48137,48280,48419,48503,48584,48683,48768,48850,48923,48986,49067,49137,49211,49311,49378,49445,49545,49622,49713,49944,50068,50156,50214,50289,50435,50495,50572,50652,50729,50813,51040,51095,51172,51257,51334,51430,51612,51704,51787,51871,51949,52017,52097,52182,52359,52440,52494,52587,52664,52738,52815,52925,53026,53134,53248,53325,53375,53427,53522,53626,53710,53834,54049,54165,54246,54387,54528,54670,54804,54908,55067,55204,55399,55580,55708,55840,55930,56025,56137,56285,56403,56556,56633,57036,57123,57217,57303,57392"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,293,415,536,635,729,841,985,1104,1251,1335,1435,1536,1637,1758,1885,1990,2140,2286,2416,2608,2734,2852,2975,3108,3210,3315,3439,3564,3666,3773,3878,4023,4175,4284,4393,4480,4573,4668,4759,4845,4952,5032,5117,5219,5331,5429,5529,5617,5733,5834,5937,6069,6149,6259", "endColumns": "119,117,121,120,98,93,111,143,118,146,83,99,100,100,120,126,104,149,145,129,191,125,117,122,132,101,104,123,124,101,106,104,144,151,108,108,86,92,94,90,85,106,79,84,101,111,97,99,87,115,100,102,131,79,109,97", "endOffsets": "170,288,410,531,630,724,836,980,1099,1246,1330,1430,1531,1632,1753,1880,1985,2135,2281,2411,2603,2729,2847,2970,3103,3205,3310,3434,3559,3661,3768,3873,4018,4170,4279,4388,4475,4568,4663,4754,4840,4947,5027,5112,5214,5326,5424,5524,5612,5728,5829,5932,6064,6144,6254,6352"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6183,6303,6421,6543,6664,6763,6857,6969,7113,7232,7379,7463,7563,7664,7765,7886,8013,8118,8268,8414,8544,8736,8862,8980,9103,9236,9338,9443,9567,9692,9794,9901,10006,10151,10303,10412,10521,10608,10701,10796,10887,10973,11080,11160,11245,11347,11459,11557,11657,11745,11861,11962,12065,12197,12277,12387", "endColumns": "119,117,121,120,98,93,111,143,118,146,83,99,100,100,120,126,104,149,145,129,191,125,117,122,132,101,104,123,124,101,106,104,144,151,108,108,86,92,94,90,85,106,79,84,101,111,97,99,87,115,100,102,131,79,109,97", "endOffsets": "6298,6416,6538,6659,6758,6852,6964,7108,7227,7374,7458,7558,7659,7760,7881,8008,8113,8263,8409,8539,8731,8857,8975,9098,9231,9333,9438,9562,9687,9789,9896,10001,10146,10298,10407,10516,10603,10696,10791,10882,10968,11075,11155,11240,11342,11454,11552,11652,11740,11856,11957,12060,12192,12272,12382,12480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-fr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "160", "endOffsets": "355"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4750", "endColumns": "164", "endOffsets": "4910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,94", "endOffsets": "139,234"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "57666,57755", "endColumns": "88,94", "endOffsets": "57750,57845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1109,1175,1272,1355,1421,1523,1588,1663,1719,1798,1858,1912,2034,2093,2155,2209,2291,2426,2518,2593,2688,2769,2853,2997,3076,3157,3298,3391,3470,3525,3576,3642,3722,3803,3874,3954,4027,4105,4178,4250,4362,4455,4527,4619,4711,4785,4869,4961,5018,5102,5168,5251,5338,5400,5464,5527,5605,5707,5811,5908,6012,6071,6126,6215,6302,6379", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "278,358,439,522,631,726,824,954,1039,1104,1170,1267,1350,1416,1518,1583,1658,1714,1793,1853,1907,2029,2088,2150,2204,2286,2421,2513,2588,2683,2764,2848,2992,3071,3152,3293,3386,3465,3520,3571,3637,3717,3798,3869,3949,4022,4100,4173,4245,4357,4450,4522,4614,4706,4780,4864,4956,5013,5097,5163,5246,5333,5395,5459,5522,5600,5702,5806,5903,6007,6066,6121,6210,6297,6374,6455"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3075,3155,3236,3319,3428,4250,4348,4478,5309,5374,5440,5863,6117,12485,12587,12652,12727,12783,12862,12922,12976,13098,13157,13219,13273,13355,13490,13582,13657,13752,13833,13917,14061,14140,14221,14362,14455,14534,14589,14640,14706,14786,14867,14938,15018,15091,15169,15242,15314,15426,15519,15591,15683,15775,15849,15933,16025,16082,16166,16232,16315,16402,16464,16528,16591,16669,16771,16875,16972,17076,17135,17369,17711,17798,17951", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "328,3150,3231,3314,3423,3518,4343,4473,4558,5369,5435,5532,5941,6178,12582,12647,12722,12778,12857,12917,12971,13093,13152,13214,13268,13350,13485,13577,13652,13747,13828,13912,14056,14135,14216,14357,14450,14529,14584,14635,14701,14781,14862,14933,15013,15086,15164,15237,15309,15421,15514,15586,15678,15770,15844,15928,16020,16077,16161,16227,16310,16397,16459,16523,16586,16664,16766,16870,16967,17071,17130,17185,17453,17793,17870,18027"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,392,492,579,658,750,842,929,1010,1095,1171,1246,1324,1398,1476,1545", "endColumns": "98,87,99,99,86,78,91,91,86,80,84,75,74,77,73,77,68,121", "endOffsets": "199,287,387,487,574,653,745,837,924,1005,1090,1166,1241,1319,1393,1471,1540,1662"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,539,540,541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4563,4662,5022,5122,5222,5946,6025,17190,17282,17458,17539,17875,18032,18107,18185,57397,57475,57544", "endColumns": "98,87,99,99,86,78,91,91,86,80,84,75,74,77,73,77,68,121", "endOffsets": "4657,4745,5117,5217,5304,6020,6112,17277,17364,17534,17619,17946,18102,18180,18254,57470,57539,57661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4915,5537,5639,5758", "endColumns": "106,101,118,104", "endOffsets": "5017,5634,5753,5858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3523,3621,3723,3822,3924,4028,4132,18259", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "3616,3718,3817,3919,4023,4127,4245,18355"}}]}]}