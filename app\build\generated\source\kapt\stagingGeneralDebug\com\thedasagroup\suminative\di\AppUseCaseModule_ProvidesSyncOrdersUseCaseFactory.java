package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.database.LocalOrderRepository;
import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.StockRepository;
import com.thedasagroup.suminative.domain.orders.SyncOrdersUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesSyncOrdersUseCaseFactory implements Factory<SyncOrdersUseCase> {
  private final Provider<LocalOrderRepository> localOrderRepositoryProvider;

  private final Provider<StockRepository> stockRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  public AppUseCaseModule_ProvidesSyncOrdersUseCaseFactory(
      Provider<LocalOrderRepository> localOrderRepositoryProvider,
      Provider<StockRepository> stockRepositoryProvider, Provider<Prefs> prefsProvider) {
    this.localOrderRepositoryProvider = localOrderRepositoryProvider;
    this.stockRepositoryProvider = stockRepositoryProvider;
    this.prefsProvider = prefsProvider;
  }

  @Override
  public SyncOrdersUseCase get() {
    return providesSyncOrdersUseCase(localOrderRepositoryProvider.get(), stockRepositoryProvider.get(), prefsProvider.get());
  }

  public static AppUseCaseModule_ProvidesSyncOrdersUseCaseFactory create(
      Provider<LocalOrderRepository> localOrderRepositoryProvider,
      Provider<StockRepository> stockRepositoryProvider, Provider<Prefs> prefsProvider) {
    return new AppUseCaseModule_ProvidesSyncOrdersUseCaseFactory(localOrderRepositoryProvider, stockRepositoryProvider, prefsProvider);
  }

  public static SyncOrdersUseCase providesSyncOrdersUseCase(
      LocalOrderRepository localOrderRepository, StockRepository stockRepository, Prefs prefs) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesSyncOrdersUseCase(localOrderRepository, stockRepository, prefs));
  }
}
