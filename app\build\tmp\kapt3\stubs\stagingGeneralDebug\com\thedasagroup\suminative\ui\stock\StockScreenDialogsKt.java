package com.thedasagroup.suminative.ui.stock;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000,\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\u001a^\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u0006\u0010\u0006\u001a\u00020\u000726\u0010\b\u001a2\u0012\u0013\u0012\u00110\u0007\u00a2\u0006\f\b\n\u0012\b\b\u000b\u0012\u0004\b\b(\u0006\u0012\u0013\u0012\u00110\f\u00a2\u0006\f\b\n\u0012\b\b\u000b\u0012\u0004\b\b(\r\u0012\u0004\u0012\u00020\u00010\tH\u0007\u00a8\u0006\u000e"}, d2 = {"ChangeStockDialog", "", "viewModel", "Lcom/thedasagroup/suminative/ui/stock/StockScreenViewModel;", "onCancel", "Lkotlin/Function0;", "stockItem", "Lcom/thedasagroup/suminative/data/model/response/stock/StockItem;", "onUpdateStock", "Lkotlin/Function2;", "Lkotlin/ParameterName;", "name", "", "stock", "app_stagingGeneralDebug"})
public final class StockScreenDialogsKt {
    
    @androidx.compose.runtime.Composable()
    public static final void ChangeStockDialog(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.stock.StockScreenViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCancel, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.stock.StockItem stockItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super com.thedasagroup.suminative.data.model.response.stock.StockItem, ? super java.lang.Integer, kotlin.Unit> onUpdateStock) {
    }
}