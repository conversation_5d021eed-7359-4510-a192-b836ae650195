package com.thedasagroup.suminative.ui.stores;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\u0018\u00002\u00020\u00012\u00020\u0002B\u0007\u00a2\u0006\u0004\b\u0003\u0010\u0004J\u0012\u0010\u0005\u001a\u00020\u00062\b\u0010\u0007\u001a\u0004\u0018\u00010\bH\u0014J\b\u0010\t\u001a\u00020\u0006H\u0016J\b\u0010\n\u001a\u00020\u0006H\u0002J\u0010\u0010\u000b\u001a\u00020\u00062\u0006\u0010\f\u001a\u00020\rH\u0002\u00a8\u0006\u000e"}, d2 = {"Lcom/thedasagroup/suminative/ui/stores/ClosedStoreActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "Lcom/airbnb/mvrx/MavericksView;", "<init>", "()V", "onCreate", "", "savedInstanceState", "Landroid/os/Bundle;", "invalidate", "scheduleJob", "actionOnService", "action", "Lcom/thedasagroup/suminative/ui/service/Actions;", "app_stagingGeneralDebug"})
public final class ClosedStoreActivity extends androidx.appcompat.app.AppCompatActivity implements com.airbnb.mvrx.MavericksView {
    
    public ClosedStoreActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    public void invalidate() {
    }
    
    private final void scheduleJob() {
    }
    
    private final void actionOnService(com.thedasagroup.suminative.ui.service.Actions action) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <T extends java.lang.Object>kotlinx.coroutines.Job collectLatest(@org.jetbrains.annotations.NotNull()
    kotlinx.coroutines.flow.Flow<? extends T> $this$collectLatest, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super T, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.airbnb.mvrx.MavericksViewInternalViewModel getMavericksViewInternalViewModel() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String getMvrxViewId() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public androidx.lifecycle.LifecycleOwner getSubscriptionLifecycleOwner() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, T extends java.lang.Object>kotlinx.coroutines.Job onAsync(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onAsync, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends com.airbnb.mvrx.Async<? extends T>> asyncProp, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super java.lang.Throwable, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> onFail, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super T, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> onSuccess) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super S, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super A, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super A, ? super B, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function4<? super A, ? super B, ? super C, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function5<? super A, ? super B, ? super C, ? super D, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object, E extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends E> prop5, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function6<? super A, ? super B, ? super C, ? super D, ? super E, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object, E extends java.lang.Object, F extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends E> prop5, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends F> prop6, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function7<? super A, ? super B, ? super C, ? super D, ? super E, ? super F, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object, E extends java.lang.Object, F extends java.lang.Object, G extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends E> prop5, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends F> prop6, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends G> prop7, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function8<? super A, ? super B, ? super C, ? super D, ? super E, ? super F, ? super G, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    public void postInvalidate() {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.airbnb.mvrx.UniqueOnly uniqueOnly(@org.jetbrains.annotations.Nullable()
    java.lang.String customId) {
        return null;
    }
}