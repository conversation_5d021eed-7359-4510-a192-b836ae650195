package com.thedasagroup.suminative.ui.stores;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000\u001a\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a$\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a$\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\u00062\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u00a8\u0006\t"}, d2 = {"SelectStoreScreen", "", "prefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "onStoreSelect", "Lkotlin/Function1;", "Lcom/thedasagroup/suminative/data/model/response/login/Store;", "StoreCard", "store", "app_stagingGeneralDebug"})
public final class SelectStoreScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void SelectStoreScreen(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.login.Store, kotlin.Unit> onStoreSelect) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void StoreCard(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.login.Store store, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.login.Store, kotlin.Unit> onStoreSelect) {
    }
}