package com.thedasagroup.suminative;

@dagger.hilt.android.HiltAndroidApp()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000p\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u0000 B2\u00020\u00012\u00020\u00022\u00020\u0003:\u0001BB\u0007\u00a2\u0006\u0004\b\u0004\u0010\u0005J\b\u0010+\u001a\u00020,H\u0016J\b\u0010-\u001a\u00020,H\u0002J\u0010\u0010.\u001a\u00020,2\u0006\u0010/\u001a\u000200H\u0002J\u0006\u00101\u001a\u00020,J\u001a\u00102\u001a\u00020,2\u0006\u00103\u001a\u0002042\b\u00105\u001a\u0004\u0018\u000106H\u0016J\u0010\u00107\u001a\u00020,2\u0006\u00103\u001a\u000204H\u0016J\u0010\u00108\u001a\u00020,2\u0006\u00103\u001a\u000204H\u0016J\u0010\u00109\u001a\u00020,2\u0006\u00103\u001a\u000204H\u0016J\u0010\u0010:\u001a\u00020,2\u0006\u00103\u001a\u000204H\u0016J\u0018\u0010;\u001a\u00020,2\u0006\u00103\u001a\u0002042\u0006\u0010<\u001a\u000206H\u0016J\u0010\u0010=\u001a\u00020,2\u0006\u00103\u001a\u000204H\u0016R\u0010\u0010\u0006\u001a\u0004\u0018\u00010\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001e\u0010\b\u001a\u00020\t8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010\u000b\"\u0004\b\f\u0010\rR\u001e\u0010\u000e\u001a\u00020\u000f8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0010\u0010\u0011\"\u0004\b\u0012\u0010\u0013R\u001a\u0010\u0014\u001a\u00020\u0015X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0014\u0010\u0016\"\u0004\b\u0017\u0010\u0018R\u001e\u0010\u0019\u001a\u00020\u001a8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001b\u0010\u001c\"\u0004\b\u001d\u0010\u001eR\u001e\u0010\u001f\u001a\u00020 8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b!\u0010\"\"\u0004\b#\u0010$R\u001e\u0010%\u001a\u00020&8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\'\u0010(\"\u0004\b)\u0010*R\u0014\u0010>\u001a\u00020?8VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\b@\u0010A\u00a8\u0006C"}, d2 = {"Lcom/thedasagroup/suminative/App;", "Landroid/app/Application;", "Landroid/app/Application$ActivityLifecycleCallbacks;", "Landroidx/work/Configuration$Provider;", "<init>", "()V", "TAG", "", "prefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "getPrefs", "()Lcom/thedasagroup/suminative/data/prefs/Prefs;", "setPrefs", "(Lcom/thedasagroup/suminative/data/prefs/Prefs;)V", "hourUtils", "Lcom/thedasagroup/suminative/HourUtils;", "getHourUtils", "()Lcom/thedasagroup/suminative/HourUtils;", "setHourUtils", "(Lcom/thedasagroup/suminative/HourUtils;)V", "isActivityVisible", "", "()Z", "setActivityVisible", "(Z)V", "trueTime", "Lcom/instacart/truetime/time/TrueTimeImpl;", "getTrueTime", "()Lcom/instacart/truetime/time/TrueTimeImpl;", "setTrueTime", "(Lcom/instacart/truetime/time/TrueTimeImpl;)V", "workerFactory", "Landroidx/hilt/work/HiltWorkerFactory;", "getWorkerFactory", "()Landroidx/hilt/work/HiltWorkerFactory;", "setWorkerFactory", "(Landroidx/hilt/work/HiltWorkerFactory;)V", "orderSyncManager", "Lcom/thedasagroup/suminative/work/OrderSyncManager;", "getOrderSyncManager", "()Lcom/thedasagroup/suminative/work/OrderSyncManager;", "setOrderSyncManager", "(Lcom/thedasagroup/suminative/work/OrderSyncManager;)V", "onCreate", "", "scheduleJob", "actionOnService", "action", "Lcom/thedasagroup/suminative/ui/service/Actions;", "initLogger", "onActivityCreated", "activity", "Landroid/app/Activity;", "savedInstanceState", "Landroid/os/Bundle;", "onActivityStarted", "onActivityResumed", "onActivityPaused", "onActivityStopped", "onActivitySaveInstanceState", "outState", "onActivityDestroyed", "workManagerConfiguration", "Landroidx/work/Configuration;", "getWorkManagerConfiguration", "()Landroidx/work/Configuration;", "Companion", "app_stagingGeneralDebug"})
public final class App extends android.app.Application implements android.app.Application.ActivityLifecycleCallbacks, androidx.work.Configuration.Provider {
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String TAG = null;
    @javax.inject.Inject()
    public com.thedasagroup.suminative.data.prefs.Prefs prefs;
    @javax.inject.Inject()
    public com.thedasagroup.suminative.HourUtils hourUtils;
    private boolean isActivityVisible = false;
    @javax.inject.Inject()
    public com.instacart.truetime.time.TrueTimeImpl trueTime;
    @javax.inject.Inject()
    public androidx.hilt.work.HiltWorkerFactory workerFactory;
    @javax.inject.Inject()
    public com.thedasagroup.suminative.work.OrderSyncManager orderSyncManager;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GUAVA_URL_KEY = "guava_url";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GUAVA_TEST_API_KEY = "apiKeyTest";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String USE_GUAVA_TEST = "useGuavaTest";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String WAITER_IDLE_TIME = "WAITER_IDLE_TIME";
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.App.Companion Companion = null;
    
    public App() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.prefs.Prefs getPrefs() {
        return null;
    }
    
    public final void setPrefs(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.HourUtils getHourUtils() {
        return null;
    }
    
    public final void setHourUtils(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.HourUtils p0) {
    }
    
    public final boolean isActivityVisible() {
        return false;
    }
    
    public final void setActivityVisible(boolean p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.instacart.truetime.time.TrueTimeImpl getTrueTime() {
        return null;
    }
    
    public final void setTrueTime(@org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.hilt.work.HiltWorkerFactory getWorkerFactory() {
        return null;
    }
    
    public final void setWorkerFactory(@org.jetbrains.annotations.NotNull()
    androidx.hilt.work.HiltWorkerFactory p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.work.OrderSyncManager getOrderSyncManager() {
        return null;
    }
    
    public final void setOrderSyncManager(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.work.OrderSyncManager p0) {
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
    
    private final void scheduleJob() {
    }
    
    private final void actionOnService(com.thedasagroup.suminative.ui.service.Actions action) {
    }
    
    public final void initLogger() {
    }
    
    @java.lang.Override()
    public void onActivityCreated(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    public void onActivityStarted(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity) {
    }
    
    @java.lang.Override()
    public void onActivityResumed(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity) {
    }
    
    @java.lang.Override()
    public void onActivityPaused(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity) {
    }
    
    @java.lang.Override()
    public void onActivityStopped(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity) {
    }
    
    @java.lang.Override()
    public void onActivitySaveInstanceState(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity, @org.jetbrains.annotations.NotNull()
    android.os.Bundle outState) {
    }
    
    @java.lang.Override()
    public void onActivityDestroyed(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public androidx.work.Configuration getWorkManagerConfiguration() {
        return null;
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0004\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/thedasagroup/suminative/App$Companion;", "", "<init>", "()V", "GUAVA_URL_KEY", "", "GUAVA_TEST_API_KEY", "USE_GUAVA_TEST", "WAITER_IDLE_TIME", "app_stagingGeneralDebug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}