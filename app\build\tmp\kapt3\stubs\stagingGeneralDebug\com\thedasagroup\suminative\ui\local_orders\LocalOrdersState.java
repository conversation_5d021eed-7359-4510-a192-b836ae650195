package com.thedasagroup.suminative.ui.local_orders;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BS\u0012\u0014\b\u0002\u0010\u0002\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u0003\u0012\u0014\b\u0002\u0010\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u0003\u0012\u0014\b\u0002\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u0003\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u00a2\u0006\u0004\b\n\u0010\u000bJ\u0015\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u0003H\u00c6\u0003J\u0015\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u0003H\u00c6\u0003J\u0015\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\tH\u00c6\u0003JU\u0010\u0016\u001a\u00020\u00002\u0014\b\u0002\u0010\u0002\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u00032\u0014\b\u0002\u0010\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u00032\u0014\b\u0002\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u00032\b\b\u0002\u0010\b\u001a\u00020\tH\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u001aH\u00d6\u0003J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001J\t\u0010\u001d\u001a\u00020\u001eH\u00d6\u0001R\u001d\u0010\u0002\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u001d\u0010\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\rR\u001d\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\rR\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011\u00a8\u0006\u001f"}, d2 = {"Lcom/thedasagroup/suminative/ui/local_orders/LocalOrdersState;", "Lcom/airbnb/mvrx/MavericksState;", "orders", "Lcom/airbnb/mvrx/Async;", "", "Lcom/thedasagroup/suminative/database/OrderEntity;", "filteredOrders", "pendingOrders", "selectedFilter", "Lcom/thedasagroup/suminative/ui/local_orders/OrderFilter;", "<init>", "(Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/thedasagroup/suminative/ui/local_orders/OrderFilter;)V", "getOrders", "()Lcom/airbnb/mvrx/Async;", "getFilteredOrders", "getPendingOrders", "getSelectedFilter", "()Lcom/thedasagroup/suminative/ui/local_orders/OrderFilter;", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_stagingGeneralDebug"})
public final class LocalOrdersState implements com.airbnb.mvrx.MavericksState {
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<java.util.List<com.thedasagroup.suminative.database.OrderEntity>> orders = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<java.util.List<com.thedasagroup.suminative.database.OrderEntity>> filteredOrders = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<java.util.List<com.thedasagroup.suminative.database.OrderEntity>> pendingOrders = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.local_orders.OrderFilter selectedFilter = null;
    
    public LocalOrdersState(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.database.OrderEntity>> orders, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.database.OrderEntity>> filteredOrders, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.database.OrderEntity>> pendingOrders, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.local_orders.OrderFilter selectedFilter) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<java.util.List<com.thedasagroup.suminative.database.OrderEntity>> getOrders() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<java.util.List<com.thedasagroup.suminative.database.OrderEntity>> getFilteredOrders() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<java.util.List<com.thedasagroup.suminative.database.OrderEntity>> getPendingOrders() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.local_orders.OrderFilter getSelectedFilter() {
        return null;
    }
    
    public LocalOrdersState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<java.util.List<com.thedasagroup.suminative.database.OrderEntity>> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<java.util.List<com.thedasagroup.suminative.database.OrderEntity>> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<java.util.List<com.thedasagroup.suminative.database.OrderEntity>> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.local_orders.OrderFilter component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.local_orders.LocalOrdersState copy(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.database.OrderEntity>> orders, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.database.OrderEntity>> filteredOrders, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.database.OrderEntity>> pendingOrders, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.local_orders.OrderFilter selectedFilter) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}