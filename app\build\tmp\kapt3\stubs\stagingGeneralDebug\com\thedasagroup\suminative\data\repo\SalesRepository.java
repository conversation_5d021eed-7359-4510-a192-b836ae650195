package com.thedasagroup.suminative.data.repo;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\"\u0010\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\u00072\u0006\u0010\n\u001a\u00020\u000bH\u0086@\u00a2\u0006\u0002\u0010\fJ\"\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\b0\u00072\u0006\u0010\n\u001a\u00020\u000bH\u0086@\u00a2\u0006\u0002\u0010\fR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/thedasagroup/suminative/data/repo/SalesRepository;", "Lcom/thedasagroup/suminative/data/repo/BaseRepository;", "trueTimeImpl", "Lcom/instacart/truetime/time/TrueTimeImpl;", "<init>", "(Lcom/instacart/truetime/time/TrueTimeImpl;)V", "getTotalSales", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/sales/SalesResponse;", "request", "Lcom/thedasagroup/suminative/data/model/request/sales/SalesRequest;", "(Lcom/thedasagroup/suminative/data/model/request/sales/SalesRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getSalesReport", "Lcom/thedasagroup/suminative/data/model/response/sales/SalesReportResponse;", "app_stagingGeneralDebug"})
public final class SalesRepository extends com.thedasagroup.suminative.data.repo.BaseRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.instacart.truetime.time.TrueTimeImpl trueTimeImpl = null;
    
    public SalesRepository(@org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl trueTimeImpl) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTotalSales(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.sales.SalesRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.sales.SalesResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getSalesReport(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.sales.SalesRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.sales.SalesReportResponse>>> $completion) {
        return null;
    }
}