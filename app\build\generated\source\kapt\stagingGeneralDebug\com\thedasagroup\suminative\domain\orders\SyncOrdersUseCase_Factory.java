package com.thedasagroup.suminative.domain.orders;

import com.thedasagroup.suminative.data.database.LocalOrderRepository;
import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.StockRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class SyncOrdersUseCase_Factory implements Factory<SyncOrdersUseCase> {
  private final Provider<LocalOrderRepository> localOrderRepositoryProvider;

  private final Provider<StockRepository> stockRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  public SyncOrdersUseCase_Factory(Provider<LocalOrderRepository> localOrderRepositoryProvider,
      Provider<StockRepository> stockRepositoryProvider, Provider<Prefs> prefsProvider) {
    this.localOrderRepositoryProvider = localOrderRepositoryProvider;
    this.stockRepositoryProvider = stockRepositoryProvider;
    this.prefsProvider = prefsProvider;
  }

  @Override
  public SyncOrdersUseCase get() {
    return newInstance(localOrderRepositoryProvider.get(), stockRepositoryProvider.get(), prefsProvider.get());
  }

  public static SyncOrdersUseCase_Factory create(
      Provider<LocalOrderRepository> localOrderRepositoryProvider,
      Provider<StockRepository> stockRepositoryProvider, Provider<Prefs> prefsProvider) {
    return new SyncOrdersUseCase_Factory(localOrderRepositoryProvider, stockRepositoryProvider, prefsProvider);
  }

  public static SyncOrdersUseCase newInstance(LocalOrderRepository localOrderRepository,
      StockRepository stockRepository, Prefs prefs) {
    return new SyncOrdersUseCase(localOrderRepository, stockRepository, prefs);
  }
}
