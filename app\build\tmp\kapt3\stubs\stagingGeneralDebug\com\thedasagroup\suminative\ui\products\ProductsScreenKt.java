package com.thedasagroup.suminative.ui.products;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000\u009c\u0001\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\t\u001a>\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0011\u0010\u0006\u001a\r\u0012\u0004\u0012\u00020\u00010\u0007\u00a2\u0006\u0002\b\b2\u0011\u0010\t\u001a\r\u0012\u0004\u0012\u00020\u00010\u0007\u00a2\u0006\u0002\b\bH\u0007\u001at\u0010\n\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\u0012\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u00010\r2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u00152\u0012\u0010\u0016\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u00010\r2\u0006\u0010\u0017\u001a\u00020\u0018H\u0007\u001aZ\u0010\u0019\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u001a\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u00010\r2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\u0012\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u00010\r2\u0012\u0010\u0016\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u00010\rH\u0007\u001a@\u0010\u001c\u001a\u00020\u00012\u0006\u0010\u001d\u001a\u00020\u000e2\u0012\u0010\u001e\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u00010\r2\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0016\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u00010\rH\u0007\u001a\u000e\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\"\u001a\'\u0010#\u001a\u00020\u00012\u0006\u0010$\u001a\u00020%2\u0006\u0010&\u001a\u00020%2\u0006\u0010\'\u001a\u00020(H\u0003\u00a2\u0006\u0004\b)\u0010*\u001a5\u0010+\u001a\u00020\u00012\u0006\u0010,\u001a\u00020-2\f\u0010.\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\u0006\u0010/\u001a\u00020%2\u0006\u00100\u001a\u00020 H\u0003\u00a2\u0006\u0004\b1\u00102\u001a\u0018\u00103\u001a\u00020\u00012\u0006\u00104\u001a\u0002052\u0006\u0010\u0012\u001a\u00020\u0013H\u0007\u001a\u001e\u00106\u001a\u00020\u00012\u0006\u00104\u001a\u0002052\u000e\u00107\u001a\n\u0012\u0006\b\u0001\u0012\u00020908\u001a \u0010:\u001a\u00020\u00012\u0006\u0010;\u001a\u00020<2\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0015H\u0007\u001aT\u0010A\u001a\u00020\u00012\f\u0010B\u001a\b\u0012\u0004\u0012\u00020D0C2\u0006\u0010E\u001a\u00020\"2\u0012\u0010F\u001a\u000e\u0012\u0004\u0012\u00020\"\u0012\u0004\u0012\u00020\u00010\r2\f\u0010G\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\u0012\u0010H\u001a\u000e\u0012\u0004\u0012\u00020\"\u0012\u0004\u0012\u00020\u00010\rH\u0007\u001a4\u0010I\u001a\u00020\u00012\u0006\u0010J\u001a\u00020D2\u0006\u0010,\u001a\u00020-2\f\u0010.\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\f\u0010K\u001a\b\u0012\u0004\u0012\u00020\u00010\u0007H\u0007\u001a\u0016\u0010L\u001a\u00020\u00012\f\u0010.\u001a\b\u0012\u0004\u0012\u00020\u00010\u0007H\u0007\"\u0014\u0010=\u001a\u00020>X\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u0010@\u00a8\u0006M"}, d2 = {"SplitScreen", "", "viewModel", "Lcom/thedasagroup/suminative/ui/products/ProductsScreenViewModel;", "order", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "left", "Lkotlin/Function0;", "Landroidx/compose/runtime/Composable;", "right", "ProductsScreen", "onBackClick", "onOpenProductDetails", "Lkotlin/Function1;", "Lcom/thedasagroup/suminative/data/model/response/stock/StockItem;", "openCart", "stockScreenViewModel", "Lcom/thedasagroup/suminative/ui/stock/StockScreenViewModel;", "orderScreenViewModel", "Lcom/thedasagroup/suminative/ui/orders/OrderScreenViewModel;", "mainActivity", "Lcom/thedasagroup/suminative/ui/MainActivity;", "addDirectlyToCart", "reservationsViewModel", "Lcom/thedasagroup/suminative/ui/reservations/ReservationsViewModel;", "tabRow", "onClickUpdateStock", "onOpenDrawer", "ProductItem", "stockItem", "openDrawer", "getStockString", "", "stock", "", "MyTabIndicator", "indicatorWidth", "Landroidx/compose/ui/unit/Dp;", "indicatorOffset", "indicatorColor", "Landroidx/compose/ui/graphics/Color;", "MyTabIndicator-y62ob04", "(FFJ)V", "MyTabItem", "isSelected", "", "onClick", "tabWidth", "text", "MyTabItem-d8LSEHM", "(ZLkotlin/jvm/functions/Function0;FLjava/lang/String;)V", "MyBottomAppBar", "navController", "Landroidx/navigation/NavController;", "navigate", "topLevelRoute", "Lcom/thedasagroup/suminative/ui/TopLevelRoute;", "", "RegularOrdersScreen", "innerPadding", "Landroidx/compose/foundation/layout/PaddingValues;", "CATEGORY_GREEN_COLOR", "", "getCATEGORY_GREEN_COLOR", "()J", "TableSelectionTabs", "selectedTables", "", "Lcom/thedasagroup/suminative/ui/reservations/AreaTableSelectionHelper$AreaTableSelection;", "selectedTableIndex", "onTableSelected", "onAddTableClick", "onRemoveTable", "TableTab", "table", "onRemove", "AddTableButton", "app_stagingGeneralDebug"})
public final class ProductsScreenKt {
    private static final long CATEGORY_GREEN_COLOR = 4281236786L;
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void SplitScreen(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.ProductsScreenViewModel viewModel, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
    androidx.compose.runtime.internal.ComposableFunction0<kotlin.Unit> left, @org.jetbrains.annotations.NotNull()
    androidx.compose.runtime.internal.ComposableFunction0<kotlin.Unit> right) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.foundation.ExperimentalFoundationApi.class})
    @androidx.compose.runtime.Composable()
    public static final void ProductsScreen(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.ProductsScreenViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.stock.StockItem, kotlin.Unit> onOpenProductDetails, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> openCart, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.stock.StockScreenViewModel stockScreenViewModel, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.OrderScreenViewModel orderScreenViewModel, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.MainActivity mainActivity, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.stock.StockItem, kotlin.Unit> addDirectlyToCart, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.reservations.ReservationsViewModel reservationsViewModel) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.foundation.ExperimentalFoundationApi()
    @androidx.compose.runtime.Composable()
    public static final void tabRow(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.ProductsScreenViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.stock.StockItem, kotlin.Unit> onClickUpdateStock, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.stock.StockItem, kotlin.Unit> onOpenDrawer, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.stock.StockItem, kotlin.Unit> addDirectlyToCart) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ProductItem(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.stock.StockItem stockItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.stock.StockItem, kotlin.Unit> openDrawer, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.ProductsScreenViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.stock.StockItem, kotlin.Unit> addDirectlyToCart) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getStockString(int stock) {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MyBottomAppBar(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.OrderScreenViewModel orderScreenViewModel) {
    }
    
    public static final void navigate(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.TopLevelRoute<? extends java.lang.Object> topLevelRoute) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void RegularOrdersScreen(@org.jetbrains.annotations.NotNull()
    androidx.compose.foundation.layout.PaddingValues innerPadding, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.OrderScreenViewModel orderScreenViewModel, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.MainActivity mainActivity) {
    }
    
    public static final long getCATEGORY_GREEN_COLOR() {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TableSelectionTabs(@org.jetbrains.annotations.NotNull()
    java.util.List<com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection> selectedTables, int selectedTableIndex, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onTableSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onAddTableClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onRemoveTable) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TableTab(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper.AreaTableSelection table, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRemove) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AddTableButton(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
}