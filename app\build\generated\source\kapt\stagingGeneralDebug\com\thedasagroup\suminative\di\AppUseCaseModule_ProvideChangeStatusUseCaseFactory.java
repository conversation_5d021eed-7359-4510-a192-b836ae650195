package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.OrdersRepository;
import com.thedasagroup.suminative.ui.orders.ChangeStatusUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvideChangeStatusUseCaseFactory implements Factory<ChangeStatusUseCase> {
  private final Provider<Prefs> prefsProvider;

  private final Provider<OrdersRepository> ordersRepositoryProvider;

  public AppUseCaseModule_ProvideChangeStatusUseCaseFactory(Provider<Prefs> prefsProvider,
      Provider<OrdersRepository> ordersRepositoryProvider) {
    this.prefsProvider = prefsProvider;
    this.ordersRepositoryProvider = ordersRepositoryProvider;
  }

  @Override
  public ChangeStatusUseCase get() {
    return provideChangeStatusUseCase(prefsProvider.get(), ordersRepositoryProvider.get());
  }

  public static AppUseCaseModule_ProvideChangeStatusUseCaseFactory create(
      Provider<Prefs> prefsProvider, Provider<OrdersRepository> ordersRepositoryProvider) {
    return new AppUseCaseModule_ProvideChangeStatusUseCaseFactory(prefsProvider, ordersRepositoryProvider);
  }

  public static ChangeStatusUseCase provideChangeStatusUseCase(Prefs prefs,
      OrdersRepository ordersRepository) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.provideChangeStatusUseCase(prefs, ordersRepository));
  }
}
