package com.thedasagroup.suminative.ui.common;

import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.ui.login.LoginUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class CommonViewModel_Factory {
  private final Provider<LoginUseCase> loginUseCaseProvider;

  private final Provider<Prefs> prefsProvider;

  public CommonViewModel_Factory(Provider<LoginUseCase> loginUseCaseProvider,
      Provider<Prefs> prefsProvider) {
    this.loginUseCaseProvider = loginUseCaseProvider;
    this.prefsProvider = prefsProvider;
  }

  public CommonViewModel get(CommonState state) {
    return newInstance(state, loginUseCaseProvider.get(), prefsProvider.get());
  }

  public static CommonViewModel_Factory create(Provider<LoginUseCase> loginUseCaseProvider,
      Provider<Prefs> prefsProvider) {
    return new CommonViewModel_Factory(loginUseCaseProvider, prefsProvider);
  }

  public static CommonViewModel newInstance(CommonState state, LoginUseCase loginUseCase,
      Prefs prefs) {
    return new CommonViewModel(state, loginUseCase, prefs);
  }
}
