package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.repo.ClockInOutRepository;
import com.thedasagroup.suminative.ui.login.ClockOutUserTimeUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesClockOutUserTimeUseCaseFactory implements Factory<ClockOutUserTimeUseCase> {
  private final Provider<ClockInOutRepository> clockInOutRepositoryProvider;

  public AppUseCaseModule_ProvidesClockOutUserTimeUseCaseFactory(
      Provider<ClockInOutRepository> clockInOutRepositoryProvider) {
    this.clockInOutRepositoryProvider = clockInOutRepositoryProvider;
  }

  @Override
  public ClockOutUserTimeUseCase get() {
    return providesClockOutUserTimeUseCase(clockInOutRepositoryProvider.get());
  }

  public static AppUseCaseModule_ProvidesClockOutUserTimeUseCaseFactory create(
      Provider<ClockInOutRepository> clockInOutRepositoryProvider) {
    return new AppUseCaseModule_ProvidesClockOutUserTimeUseCaseFactory(clockInOutRepositoryProvider);
  }

  public static ClockOutUserTimeUseCase providesClockOutUserTimeUseCase(
      ClockInOutRepository clockInOutRepository) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesClockOutUserTimeUseCase(clockInOutRepository));
  }
}
