package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.HourUtils;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RepoModule_GetHourUtilsFactory implements Factory<HourUtils> {
  @Override
  public HourUtils get() {
    return getHourUtils();
  }

  public static RepoModule_GetHourUtilsFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static HourUtils getHourUtils() {
    return Preconditions.checkNotNullFromProvides(RepoModule.INSTANCE.getHourUtils());
  }

  private static final class InstanceHolder {
    static final RepoModule_GetHourUtilsFactory INSTANCE = new RepoModule_GetHourUtilsFactory();
  }
}
