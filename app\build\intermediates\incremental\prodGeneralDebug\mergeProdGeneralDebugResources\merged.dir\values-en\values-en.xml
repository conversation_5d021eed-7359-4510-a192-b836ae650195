<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="sumup_app_certificate_error">Network certificate validation has failed! Please ensure that your device is connected to a secure internet connection.</string>
    <string name="sumup_app_outdated">The version of the app you are using is outdated, please upgrade to the latest version.</string>
    <string name="sumup_app_update_required_title">Update Required</string>
    <string name="sumup_autoreceipt_btn_stop">Stop automatic receipts</string>
    <string name="sumup_autoreceipt_customer_credentials_error">Please enter only customer credentials</string>
    <string name="sumup_autoreceipt_keypoint_customers">At checkout, provide only the customer’s email or phone.</string>
    <string name="sumup_autoreceipt_keypoint_email_phone">Don’t provide your own email or phone, or all your customer’s receipts will be sent to you instead of them.</string>
    <string name="sumup_autoreceipt_keypoint_merchants">Customers will receive receipts for purchases made at all SumUp merchants.</string>
    <string name="sumup_autoreceipt_keypoint_share">We’ll never share or sell our customer data.</string>
    <string name="sumup_autoreceipt_label_sent">А receipt was automatically sent to %1$s</string>
    <string name="sumup_autoreceipt_label_subscribe_toggle">Automatically send receipts from SumUp merchants from now on</string>
    <string name="sumup_autoreceipt_off_text">Off</string>
    <string name="sumup_autoreceipt_on_text">On</string>
    <string name="sumup_autoreceipt_privacy_policy_link">Privacy Policy</string>
    <string name="sumup_autoreceipt_privacy_policy_text">Learn more in our %1$s</string>
    <string name="sumup_autoreceipt_rules_link">Set of Rules</string>
    <string name="sumup_autoreceipt_rules_text">Please note that our %1$s apply when offering automated receipts to your customers.</string>
    <string name="sumup_autoreceipt_see_privacy_policy">See our %1$s for more info</string>
    <string name="sumup_autoreceipt_send_text">Send automatic receipts</string>
    <string name="sumup_autoreceipt_stop_alert_body">Be sure to confirm that the customer no longer wants to receive receipts before you do this.</string>
    <string name="sumup_autoreceipt_stop_alert_btn_cancel">Cancel</string>
    <string name="sumup_autoreceipt_stop_alert_btn_destructive">Stop receipts</string>
    <string name="sumup_autoreceipt_stop_alert_title">Stop automatic receipts?</string>
    <string name="sumup_autoreceipt_stop_toast_error_subtitle">Please try again later to turn off automatic receipts.</string>
    <string name="sumup_autoreceipt_stop_toast_error_title">Couldn\'t complete request</string>
    <string name="sumup_autoreceipt_stop_toast_success_subtitle">Automatic receipts stopped</string>
    <string name="sumup_autoreceipt_subtitle_label_sent">The receipt was sent to %1$s</string>
    <string name="sumup_autoreceipt_text">Аutomatic receipts</string>
    <string name="sumup_autoreceipt_title_label_sent">Sent!</string>
    <string name="sumup_bt_connection_failure_description">Restart your %1$s and card reader and try again.</string>
    <string name="sumup_bt_connection_failure_title">Couldn’t find your %1$s</string>
    <string name="sumup_bt_reset_air_message">Press and hold the power button down until the display shows %s. This can take a moment. Once %s pops up, promptly release the power button.</string>
    <string name="sumup_bt_reset_instruction_air_1_description">Make sure it’s disconnected from the power supply and press the power button to turn off.</string>
    <string name="sumup_bt_reset_instruction_air_1_title">Unplug and turn off your Air reader</string>
    <string name="sumup_bt_reset_instruction_air_2_description">Hold the power button down for a few seconds until you see \“Release…\”.</string>
    <string name="sumup_bt_reset_instruction_air_2_title">Press and hold the power button</string>
    <string name="sumup_bt_reset_instruction_air_3_description">After releasing the power button, you should hear a beep confirming that Bluetooth has been reset.</string>
    <string name="sumup_bt_reset_instruction_air_3_title">Release button and listen for beep</string>
    <string name="sumup_bt_reset_instruction_solo_1_description">Tap the arrow at the top of Solo to go to \“Connections\”.</string>
    <string name="sumup_bt_reset_instruction_solo_1_title">On Solo, go to the connections menu</string>
    <string name="sumup_bt_reset_instruction_solo_2_description">Go to “Bluetooth” and turn the toggle off and then back on.</string>
    <string name="sumup_bt_reset_instruction_solo_2_title">Turn Solo’s Bluetooth back on</string>
    <string name="sumup_bt_reset_instruction_three_g_1_description">Tap the up or down arrow on the card reader.</string>
    <string name="sumup_bt_reset_instruction_three_g_1_title">Go to \“Device settings\”</string>
    <string name="sumup_bt_reset_instruction_three_g_2_title">Select “Connection type”</string>
    <string name="sumup_bt_reset_instruction_three_g_3_description">Select \“Off\” and tap the green tick to confirm.</string>
    <string name="sumup_bt_reset_instruction_three_g_3_title">Turn Bluetooth off</string>
    <string name="sumup_bt_reset_instruction_three_g_4_description">Select \“On\” and tap the green tick to confirm.</string>
    <string name="sumup_bt_reset_instruction_three_g_4_title">Turn Bluetooth back on</string>
    <string name="sumup_bt_reset_instructions_message">Follow these two steps to solve the connection issue between this device and your card reader.</string>
    <string name="sumup_bt_reset_instructions_title">Resetting Bluetooth</string>
    <string name="sumup_bt_reset_mobile_description">Tap the button below to reset Bluetooth on this device.</string>
    <string name="sumup_bt_reset_on_device_description_done">Done</string>
    <string name="sumup_bt_reset_on_device_description_from_settings">Turn Bluetooth off and back on</string>
    <string name="sumup_bt_reset_on_device_description_not_done">It will only take a second</string>
    <string name="sumup_bt_reset_on_device_title">On this device</string>
    <string name="sumup_bt_reset_on_reader_description">See how to do this</string>
    <string name="sumup_bt_reset_on_reader_title">On your %s</string>
    <string name="sumup_bt_reset_option_phone">On your phone</string>
    <string name="sumup_bt_reset_option_reader">On your card reader</string>
    <string name="sumup_bt_reset_option_tablet">On your tablet</string>
    <string name="sumup_bt_setup_instruction_solo_1_description">Press the power button to turn Solo on.</string>
    <string name="sumup_bt_setup_instruction_solo_1_title">Make sure Solo is on</string>
    <string name="sumup_bt_setup_instruction_solo_2_description">Tap the arrow at the top of the Solo screen and go to \“Connections\”.</string>
    <string name="sumup_bt_setup_instruction_solo_2_title">Go to \“Connections\” in the menu</string>
    <string name="sumup_bt_setup_instruction_solo_3_description">Go to \“Bluetooth\” and turn the toggle on.</string>
    <string name="sumup_bt_setup_instruction_solo_3_title">Turn Solo’s Bluetooth on</string>
    <string name="sumup_bt_setup_instruction_three_g_1_description">Tap the upwards or downwards arrow on the card reader to access.</string>
    <string name="sumup_bt_setup_instruction_three_g_1_title">Go to \“Device settings\”</string>
    <string name="sumup_bt_setup_instruction_three_g_2_title">Select \“Connection type\”</string>
    <string name="sumup_bt_setup_instruction_three_g_3_description">Select \“On\” to turn on Bluetooth.</string>
    <string name="sumup_bt_setup_instruction_three_g_3_title">Choose \“Bluetooth\”</string>
    <string name="sumup_bt_setup_instruction_three_g_4_title">Press the green tick to confirm</string>
    <string name="sumup_bt_troubleshooting_reader_selection_description">Select your card reader</string>
    <string name="sumup_bt_troubleshooting_reader_selection_onboarding_title">Set up your card reader</string>
    <string name="sumup_bt_troubleshooting_reader_selection_title">What are you setting up?</string>
    <string name="sumup_btn_call_support">Call now</string>
    <string name="sumup_btn_cancel">Cancel</string>
    <string name="sumup_btn_connect">Connect</string>
    <string name="sumup_btn_dismiss">Dismiss</string>
    <string name="sumup_btn_done">Done</string>
    <string name="sumup_btn_edit">Edit</string>
    <string name="sumup_btn_go_to_support">Go to support</string>
    <string name="sumup_btn_later">Later</string>
    <string name="sumup_btn_new_set_up">New set-up</string>
    <string name="sumup_btn_next">Next</string>
    <string name="sumup_btn_no">No</string>
    <string name="sumup_btn_not_my_reader">Not my reader</string>
    <string name="sumup_btn_ok">OK</string>
    <string name="sumup_btn_previous">Previous</string>
    <string name="sumup_btn_proceed">Proceed</string>
    <string name="sumup_btn_rescan">Rescan</string>
    <string name="sumup_btn_retry">Retry</string>
    <string name="sumup_btn_scan">Scan</string>
    <string name="sumup_btn_send">Send</string>
    <string name="sumup_btn_send_to_mobile">Send to mobile</string>
    <string name="sumup_btn_skip">Skip</string>
    <string name="sumup_btn_update">Update</string>
    <string name="sumup_btn_yes">Yes</string>
    <string name="sumup_btn_yes_connect">Yes, connect</string>
    <string name="sumup_btn_yes_scan">Yes, scan</string>
    <string name="sumup_button_retry">Retry</string>
    <string name="sumup_camera_permission_request_text">In order to take a picture of your item, SumUp needs access to your camera</string>
    <string name="sumup_cancel_pending_payment_message">Are you sure you want to cancel this checkout?</string>
    <string name="sumup_cancel_pending_payment_title">Payment Pending</string>
    <string name="sumup_card_checkout_cvv_hint">CVV</string>
    <string name="sumup_card_checkout_expiry_date_label">Expiry date</string>
    <string name="sumup_card_checkout_expiry_month_hint">MM</string>
    <string name="sumup_card_checkout_expiry_year_hint">YY</string>
    <string name="sumup_card_checkout_insert_and_turn_on_card_reader">Please connect and turn on the card terminal</string>
    <string name="sumup_card_checkout_security_code">Security code</string>
    <string name="sumup_card_reader_battery_level">%d%% Battery</string>
    <string name="sumup_card_reader_battery_level_label">Battery level</string>
    <string name="sumup_card_reader_battery_level_percentage">%d%%</string>
    <string name="sumup_card_reader_battery_text">Battery</string>
    <string name="sumup_card_reader_bluetooth_software_version_text">Bluetooth software</string>
    <string name="sumup_card_reader_connect_button_text">Connect</string>
    <string name="sumup_card_reader_error_dialog_header">Couldn\'t connect</string>
    <string name="sumup_card_reader_error_dialog_positive">Try again</string>
    <string name="sumup_card_reader_error_dialog_title">Couldn\'t connect to the card reader. Please try again.</string>
    <string name="sumup_card_reader_last_digits_text">Last digits</string>
    <string name="sumup_card_reader_low_battery">Low battery, please charge device</string>
    <string name="sumup_card_reader_not_saved_body">Connect card reader</string>
    <string name="sumup_card_reader_not_saved_title">Get started with card payments</string>
    <string name="sumup_card_reader_not_saved_title_br">Get started with Top / Solo</string>
    <string name="sumup_card_reader_serial_number_text">Serial number</string>
    <string name="sumup_card_reader_setting_enable_generic">Accept payments with card reader</string>
    <string name="sumup_card_reader_setting_enable_generic_br">Accept payments with Top / Solo</string>
    <string name="sumup_card_reader_setting_enable_subtitle">Show as a payment method during checkout.</string>
    <string name="sumup_card_reader_setting_switch_card_readers">Switch to another card reader</string>
    <string name="sumup_card_reader_software_version_text">Software</string>
    <string name="sumup_checking_firmware_info">Checking for firmware updates</string>
    <string name="sumup_checkout_connecting_bt">Connecting…</string>
    <string name="sumup_checkout_power_on_reader">Power on card terminal</string>
    <string name="sumup_confirm_pairing_air">Please confirm the pairing request on your %s AND on the card reader.</string>
    <string name="sumup_confirm_pairing_air_ios">If prompted please confirm the pairing request on your %s AND on the card reader.</string>
    <string name="sumup_connection_lost">Connection lost</string>
    <string name="sumup_contacts_permission_request_title">Contacts</string>
    <string name="sumup_could_not_find_your_reader">Couldn’t find your reader</string>
    <string name="sumup_downloading_firmware">Downloading firmware update</string>
    <string name="sumup_elv_legal_text">Ich ermächtige %1$s, %2$s, %3$s, %4$s, und von diesem hierzu beauftragten Dritte („die Unternehmen“) den heute fälligen, oben genannten Betrag von meinem Bankkonto durch Lastschrift einzuziehen. Ich weise mein Kreditinstitut unwiderruflich an bei Nichteinlösung der Lastschrift den Unternehmen und/oder der HIT GmbH, Eiffestraße 76, 20537 Hamburg auf Anforderung meinen Namen und meine Anschrift zur Geltendmachung der Forderung mitzuteilen. Datenschutzrechtliche Information: Meine Zahlungsdaten (Kontonummer, Bankleitzahl, Kartenverfallsdatum, Kartenfolgenummer, Datum, Uhrzeit, Zahlungsbetrag, Terminalkennung, Ort, Unternehmen und Filiale) werden zur Kartenprüfung und Zahlungsabwicklung an SumUp Ltd, Hospitality House, 16–20 South Cumberland Street, Dublin 2, Irland weitergegeben. Sollte im Rahmen der Zahlungsabwicklung die Lastschrift von meiner Bank nicht eingelöst werden (Rücklastschrift) oder sollte ich der Lastschrift widersprechen, stimme ich zu, dass diese Tatsachen in eine Sperrdatei aufgenommen werden außer wenn ich im Zusammenhang mit dem Widerruf erklärtermaßen Rechte aus dem zu Grunde liegenden Geschäft (z.B. Gewährleistungsrechte) geltend gemacht habe. Die Sperrung wird nach vollständigem Forderungsausgleich wieder aufgehoben und die gespeicherten Daten gelöscht. Dritte, die eine vertragliche Vereinbarung mit den Unternehmen zur Nutzung der Sperrdatei haben, können für Zwecke der Missbrauchsbekämpfung und zur Risikominimierung von Zahlungsausfällen ihre elektronischen Zahlungsvorgänge gegen die Sperrdatei abgleichen. Speichernde Stelle für die Zwecke der Kartenprüfung und Zahlungsabwicklung sind die HIT GmbH, Eiffestr. 76, 20537 Hamburg und die SumUp Ltd, Hospitality House, 16–20 South Cumberland Street, Dublin 2, Irland, die auch die Sperrdatei für teilnehmende Händler führen. Diese Unternehmen erteilen insoweit auch an anderen Händler, die auf die Sperrdatei zugreifen eine Empfehlung, ob eine Zahlung im Wege des Lastschrifteinzugs akzeptiert werden sollte.</string>
    <string name="sumup_elv_legal_text_short">Hiermit erteile ich die Ermächtigung zum Lastschrifteinzug.</string>
    <string name="sumup_err_no_recent_geo_location_received">No location found. Please ensure your GPS or location services are enabled and functioning.  </string>
    <string name="sumup_error">Error</string>
    <string name="sumup_error_cannot_send_email">Cannot send email; no configured email app found.</string>
    <string name="sumup_error_cash_payment_cannot_save_message">Unable to save changes. Please try again or contact support.</string>
    <string name="sumup_error_cash_payment_cannot_save_title">Cash payment</string>
    <string name="sumup_error_io">Could not connect to the server. Please check your network connection and try again.</string>
    <string name="sumup_error_payment_method_required">Payment method required</string>
    <string name="sumup_error_payment_method_required_desc">In order to use SumUp, at least one Payment Method must be active.</string>
    <string name="sumup_error_system_webview_exception">Something went wrong with the Android System WebView. Update it or call support.</string>
    <string name="sumup_error_unexpected_response">We have experienced an unexpected response from the server. Please try again.</string>
    <string name="sumup_external_storage_permission_request_text">In order to download payout reports, SumUp needs access to the external storage of your device</string>
    <string name="sumup_failed_transaction">Transaction failed</string>
    <string name="sumup_firmware_update_canceled">Update canceled</string>
    <string name="sumup_fragment_send_receipt">Send receipt</string>
    <string name="sumup_geolocation_unavailable_title">Geo-location required</string>
    <string name="sumup_grant_usb_permission">To proceed, you must grant the permission to access via USB.</string>
    <string name="sumup_help">Help</string>
    <string name="sumup_home_card_acceptance_cta_button_title">Do it now</string>
    <string name="sumup_home_card_acceptance_subtitle">Set up your card reader and start taking payments.</string>
    <string name="sumup_home_card_acceptance_title">Ready, set, get paid!</string>
    <string name="sumup_identity_login_error_generic_message">Something went wrong!\nPlease try logging in again.</string>
    <string name="sumup_identity_login_error_session_expired_message">Your session has expired. Please log in again.</string>
    <string name="sumup_identity_login_location_permission_denied_message">Location permission is required to log in.</string>
    <string name="sumup_identity_migration_to_auth_login_message_content">Please log in again.</string>
    <string name="sumup_identity_migration_to_auth_login_message_title">We\'ve updated the app</string>
    <string name="sumup_installment_cancelled">Cancelled</string>
    <string name="sumup_items_many">%1$s items</string>
    <string name="sumup_items_none">No items</string>
    <string name="sumup_items_one">One item</string>
    <string name="sumup_l10n_app_processing">Processing…</string>
    <string name="sumup_l10n_card_checkout_card_number">Card number</string>
    <string name="sumup_l10n_card_checkout_checking_device">Checking card reader…</string>
    <string name="sumup_l10n_err_card_payment_expired">Your card has expired. Please use different card.</string>
    <string name="sumup_l10n_order_hint_custom_item">item</string>
    <string name="sumup_l10n_payment_failure">Transaction Failed</string>
    <string name="sumup_l10n_phone_number_confirmation_invalid_for_country">%1$s does not look like a valid mobile number for %2$s.\n\nPlease edit the number you typed or insert a valid international dialling code if it is a mobile number outside of %2$s.</string>
    <string name="sumup_l10n_phone_number_confirmation_outside_of_country">%1$s looks like a mobile number for outside of %2$s.\n\nPlease confirm with the customer that the number is correct before proceeding.</string>
    <string name="sumup_l10n_print_failed_cover_open">Printer cover is open. Close the cover and try again.</string>
    <string name="sumup_l10n_print_failed_generic">Could not print. Check the printer and try again.</string>
    <string name="sumup_l10n_print_failed_image_unavailable">Failed to download receipt info. Try again.</string>
    <string name="sumup_l10n_print_failed_paper_empty">Printer is out of paper.</string>
    <string name="sumup_l10n_print_failed_printer_offline">Could not connect to receipt printer.</string>
    <string name="sumup_l10n_solo_printer_communication_error_description">The connection failed, please retry.</string>
    <string name="sumup_l10n_solo_printer_escpos_receipt_download_failed_description">Failed to download receipt for Solo printer.</string>
    <string name="sumup_l10n_solo_printer_not_attached_description">Make sure Solo is in the printer and the printer battery is charged.</string>
    <string name="sumup_l10n_solo_printer_not_found_title">Printer not found.</string>
    <string name="sumup_l10n_solo_printer_offline_description">Couldn\'t connect to Solo.</string>
    <string name="sumup_l10n_solo_printer_outdated_firmware_action">Update</string>
    <string name="sumup_l10n_solo_printer_outdated_firmware_description">Update software to print.</string>
    <string name="sumup_l10n_solo_printer_outdated_firmware_title">Solo needs an update.</string>
    <string name="sumup_l10n_solo_printer_print_failed_generic_description">Check the printer lid and paper roll and try again.</string>
    <string name="sumup_l10n_solo_printer_print_failed_title">Printing failed.</string>
    <string name="sumup_label_external_storage">External Storage</string>
    <string name="sumup_learn_how">Learn how.</string>
    <string name="sumup_location_permission_request_text">In order to provide a secure service, SumUp needs to know where transactions take place. Therefore, SumUp needs to access your location while using the app.</string>
    <string name="sumup_location_permission_request_title">Location Services</string>
    <string name="sumup_login_btn_forgot_password">Forgot password</string>
    <string name="sumup_login_btn_log_in">Log in</string>
    <string name="sumup_login_btn_title">Log in</string>
    <string name="sumup_login_error_email_invalid">Check that the email address is entered correctly</string>
    <string name="sumup_login_error_email_missing">Enter the email address linked to your SumUp profile.</string>
    <string name="sumup_login_error_password_missing">Enter your SumUp login password</string>
    <string name="sumup_login_error_shelves_not_loaded">Sorry, your profile information couldn\'t be retrieved. Try logging in again.</string>
    <string name="sumup_login_failed_message">A problem occurred configuring your profile. Please try again later or contact our Support team.</string>
    <string name="sumup_login_failed_title">Login failed</string>
    <string name="sumup_login_hint_email">Email address</string>
    <string name="sumup_login_hint_password">Password</string>
    <string name="sumup_login_session_expired">You\'ve been logged out. This is most likely due to you logging into the same profile on another device.</string>
    <string name="sumup_merchant_activation_code">Activation Code</string>
    <string name="sumup_merchant_activation_code_title">Activate Total / Super / On</string>
    <string name="sumup_navigation_payment_methods">Payment methods</string>
    <string name="sumup_navigation_payment_methods_multiple_options">Payment methods</string>
    <string name="sumup_nearby_devices_permission_request_text">To continue, SumUp needs your permission to access devices in the immediate area.</string>
    <string name="sumup_nearby_devices_permission_request_title">Nearby devices</string>
    <string name="sumup_no_account_question">Don\'t have a profile yet?</string>
    <string name="sumup_no_air_usb_device">This device is unknown. Please connect a SumUp Air Card Reader.</string>
    <string name="sumup_no_bluetooth_on_solo">No Bluetooth on Solo?</string>
    <string name="sumup_no_usb_device">Reconnect your card reader via a USB cable.</string>
    <string name="sumup_no_web_browser">To proceed, please install and configure a web browser application.</string>
    <string name="sumup_order_charge">Charge</string>
    <string name="sumup_pairing_in_notification_bar">Please check the notification bar for the pairing request</string>
    <string name="sumup_pax_setup_add_terminal">New terminal</string>
    <string name="sumup_payment_setting_card_reader">Card Reader</string>
    <string name="sumup_payment_to_title">Payed to</string>
    <string name="sumup_permission_explanation_continue">Continue</string>
    <string name="sumup_phone">phone</string>
    <string name="sumup_please_wait">Please wait…</string>
    <string name="sumup_please_wait_connecting">Please wait. Connecting…</string>
    <string name="sumup_pos_lite">POS Lite</string>
    <string name="sumup_pp_btn_fw_abort">Abort update</string>
    <string name="sumup_pp_setup_air_no_btle">Your device does not support Bluetooth 4.0. Please check whether a software update is available for your device.</string>
    <string name="sumup_pp_setup_charge_batteries">Please make sure the batteries in the card reader are charged</string>
    <string name="sumup_pp_setup_check_power">Please ensure the card terminal is switched on</string>
    <string name="sumup_pp_setup_connected">Connected!</string>
    <string name="sumup_pp_setup_connection_failed">Could not connect. Please try again or call SumUp Support at %s</string>
    <string name="sumup_pp_setup_connection_failed_no_support">We could not connect to the card terminal. Please try again.</string>
    <string name="sumup_pp_setup_do_firmware_update_now_mandatory">Please make sure to update before %1$s.</string>
    <string name="sumup_pp_setup_firmware_cancel_sure">Are you sure you want to cancel the update?</string>
    <string name="sumup_pp_setup_firmware_update_do_now">A firmware update is available for your card reader. The update will take a few minutes.\n\nWe strongly recommend that you install the update to ensure optimal performance of your device.</string>
    <string name="sumup_pp_setup_firmware_update_installing">Installing, please wait..</string>
    <string name="sumup_pp_setup_firmware_update_minutes_remaining">%s minutes remaining</string>
    <string name="sumup_pp_setup_firmware_update_one_minute_remaining">One minute remaining</string>
    <string name="sumup_pp_setup_help_additional_steps">It seems you are still having difficulties connecting to the terminal. You can try one of these additional steps:</string>
    <string name="sumup_pp_setup_help_air_reset">Reset the card terminal by holding the power button for 20s. Please make sure that the terminal is turned off and not connected to a charger.</string>
    <string name="sumup_pp_setup_help_bt_toggle_button">Reset Bluetooth</string>
    <string name="sumup_pp_setup_help_bt_toggle_header">We cannot connect to the terminal via Bluetooth.\nPlease try resetting your bluetooth connection before retrying.</string>
    <string name="sumup_pp_setup_help_give_up">Alternatively, please contact our support team at %s</string>
    <string name="sumup_pp_setup_help_optional_support">If you require additional help, please contact our support team at %s</string>
    <string name="sumup_pp_setup_help_pin_plus_replace_batteries">Replace the batteries of the card terminal</string>
    <string name="sumup_pp_setup_help_reboot_device">Restart your %s</string>
    <string name="sumup_pp_setup_multiple_devices">Multiple terminals discovered.</string>
    <string name="sumup_pp_setup_searching">Scanning for card readers</string>
    <string name="sumup_pp_setup_select_bt_device">Compare the last digits on your screen to the serial number on the back of the terminal.</string>
    <string name="sumup_pp_setup_text_firmware_update_failed">The update has failed. Please turn the card terminal off and on again, then retry.</string>
    <string name="sumup_pp_setup_text_firmware_update_ongoing">Please do not use this device until the update is completed.</string>
    <string name="sumup_print">Print</string>
    <string name="sumup_print_autoprint_receipts">Always print receipts</string>
    <string name="sumup_print_failed_bluetooth_disabled">Bluetooth is disabled. Enable bluetooth and try again.</string>
    <string name="sumup_print_failed_no_cash_drawer">Selected printer does not support a cash drawer.</string>
    <string name="sumup_print_no_printers_found">No printers found.</string>
    <string name="sumup_print_open_cash_drawer">Open cash drawer</string>
    <string name="sumup_print_open_drawer_automatically">Open drawer automatically</string>
    <string name="sumup_print_searching_printers">Searching for printers…</string>
    <string name="sumup_print_select_printer">Select Printer</string>
    <string name="sumup_print_test_page">Print test page</string>
    <string name="sumup_printer_available">Available</string>
    <string name="sumup_printer_settings_copies_label">Number of copies</string>
    <string name="sumup_printer_settings_disabled">Disabled</string>
    <string name="sumup_printer_settings_none_paired">Not paired</string>
    <string name="sumup_printer_settings_toggle_label">Enable receipt printing</string>
    <string name="sumup_printer_unavailable">Unavailable</string>
    <string name="sumup_reader_attribute_unknown">Unknown</string>
    <string name="sumup_reader_connecting_text">Connecting to card reader</string>
    <string name="sumup_reader_connecting_title">Connecting</string>
    <string name="sumup_reader_home_item_status_update">Update device</string>
    <string name="sumup_reader_not_found_message">Your card reader ending in %s wasn’t found. Please make sure that the reader is switched on and within range.</string>
    <string name="sumup_reader_off_and_unplugged_message">Make sure the device is switched off and disconnected from the power supply.</string>
    <string name="sumup_reader_off_and_unplugged_title">Unplug and switch off</string>
    <string name="sumup_reader_payment">Payment</string>
    <string name="sumup_reader_payment_cancelled">Payment was cancelled</string>
    <string name="sumup_receipt_contacts_permission_request_text">In order to pick a contact, SumUp needs access to your contacts.</string>
    <string name="sumup_receipt_email_text">Email</string>
    <string name="sumup_receipt_field_customer_email">Customer email</string>
    <string name="sumup_receipt_field_customer_phone">Customer phone</string>
    <string name="sumup_receipt_field_email_address">Email address</string>
    <string name="sumup_receipt_field_mobile_number">Mobile number</string>
    <string name="sumup_receipt_request_failed">Sending the receipt failed. Please check your connection and try again.\n\nAlternatively, you can send a receipt for this transaction at any time from the \'Sales history\' screen.</string>
    <string name="sumup_receipt_sms_text">SMS</string>
    <string name="sumup_receipt_title_send_receipt">Send receipt</string>
    <string name="sumup_receipt_title_send_to_mobile">Send to mobile</string>
    <string name="sumup_reset_bluetooth">Reset Bluetooth</string>
    <string name="sumup_reset_bluetooth_on_reader">Reset Bluetooth on %1$s</string>
    <string name="sumup_reset_reader_confirmation_message">After releasing the power button, you will hear a beep confirming that Bluetooth has been reset.</string>
    <string name="sumup_reset_reader_confirmation_title">Reset complete</string>
    <string name="sumup_reset_reader_title">Start the reset</string>
    <string name="sumup_saved_reader_home_item_status_is_inactive">Inactive</string>
    <string name="sumup_saved_reader_home_item_status_is_ready">Ready</string>
    <string name="sumup_select_an_option">Select an option</string>
    <string name="sumup_send_receipt_email_field_label">Email address</string>
    <string name="sumup_send_receipt_phone_number_label">Mobile phone number</string>
    <string name="sumup_service_unavailable_message">We\'re working hard to fix the issue. Please try again later.</string>
    <string name="sumup_service_unavailable_title">Service unavailable</string>
    <string name="sumup_sign_up">Sign up</string>
    <string name="sumup_signature_btn_close_legal_page">Close Terms</string>
    <string name="sumup_signature_in_parcelados">in %d parcelados</string>
    <string name="sumup_signature_sign_here">Please sign here</string>
    <string name="sumup_signature_try_again_button_title">Retry</string>
    <string name="sumup_solo_app_update_message">Please update your SumUp App to continue using Solo</string>
    <string name="sumup_solo_plug_and_play_connection_disconnected_message">Solo disconnected</string>
    <string name="sumup_solo_plug_and_play_connection_failure_message">Solo couldn\'t connect</string>
    <string name="sumup_solo_plug_and_play_connection_success_message">Solo connected and ready to transact</string>
    <string name="sumup_successful_transaction">Transaction successful</string>
    <string name="sumup_tablet">tablet</string>
    <string name="sumup_tipping">Tipping</string>
    <string name="sumup_tipping_signature_including_tip_hint">(incl. tip %s)</string>
    <string name="sumup_transaction_cancelled_card_not_charged">Card has not been charged.</string>
    <string name="sumup_transaction_cancelled_generic">Transaction cancelled</string>
    <string name="sumup_transaction_cancelled_reader_error">Device did not respond as expected </string>
    <string name="sumup_transaction_cancelled_reader_error_detail">Please ensure your device is charged and your phone volume is set to maximum.</string>
    <string name="sumup_transaction_connection_lost">Connection was lost to the SumUp server</string>
    <string name="sumup_transaction_failed_generic">Transaction failed</string>
    <string name="sumup_transaction_message_checking_status">Please wait - we\'re checking the status of the transaction</string>
    <string name="sumup_transaction_not_processed_desc">The transaction could not be processed due to network issues</string>
    <string name="sumup_transaction_not_processed_details">The card has not been charged. You can safely try the transaction again.</string>
    <string name="sumup_transaction_unexpected_response">An error occurred while communicating with the server</string>
    <string name="sumup_troubleshooting_how_to_turn_on_bluetooth">How to turn on Bluetooth</string>
    <string name="sumup_troubleshooting_reader_not_found_default_case_description">Then turn it on and keep it nearby to connect.</string>
    <string name="sumup_troubleshooting_reader_not_found_default_case_title">Make sure your card reader is charged up</string>
    <string name="sumup_troubleshooting_turn_on_bluetooth_on_reader_description_generic">Make sure your %1$s%2$s is powered on, with Bluetooth on, and it’s close by.</string>
    <string name="sumup_troubleshooting_turn_on_bluetooth_on_reader_description_solo">Make sure the Bluetooth icon is in the top right corner of your Solo.</string>
    <string name="sumup_troubleshooting_turn_on_bluetooth_on_reader_title">Is Bluetooth in %1$s on?</string>
    <string name="sumup_troubleshooting_turn_on_reader_description">Make sure your %1$s%2$s is powered on and close by.</string>
    <string name="sumup_troubleshooting_turn_on_reader_title">Turn on your %s</string>
    <string name="sumup_turn_bluetooth_off_on">Turn Bluetooth off &amp; on</string>
    <string name="sumup_turn_bluetooth_off_on_on_reader">Turn Bluetooth off &amp; on on %1$s</string>
    <string name="sumup_tx_cancel_on_solo">To cancel the payment, please press the cancel button on the card reader.</string>
    <string name="sumup_tx_cancel_please_wait">Transaction is being cancelled, please wait</string>
    <string name="sumup_tx_cancel_power_off">To cancel the transaction, please press the power button on the card terminal</string>
    <string name="sumup_update_software">Update software.</string>
    <string name="sumup_update_solo_message">1. Open the Solo menu by tapping the top of the reader display\n\n2.Tap \'Connections\' and connect to WiFi\n\n3. Log in if needed\n\n4. Tap \'Settings\', \'Software update\' and then \'Download &amp; install\'\n\n5. Once the update is done, tap \'Connections\' and turn Bluetooth® back on</string>
    <string name="sumup_update_solo_title">Update your Solo card reader</string>
    <string name="sumup_verification_camera_permission_request_title">Camera</string>
    <string name="sumup_verify_phone_number">Verify mobile number</string>
    <string name="sumup_waiting_reader_action">Waiting for customer</string>
</resources>