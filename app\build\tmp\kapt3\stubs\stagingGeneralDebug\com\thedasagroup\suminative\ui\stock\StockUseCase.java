package com.thedasagroup.suminative.ui.stock;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\b\u0016\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\u001a\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000bH\u0086B\u00a2\u0006\u0002\u0010\u000eJ\u001c\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u00102\u0006\u0010\u0012\u001a\u00020\u0013H\u0082@\u00a2\u0006\u0002\u0010\u0014J\u001c\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00110\u00102\u0006\u0010\u0012\u001a\u00020\u0013H\u0082@\u00a2\u0006\u0002\u0010\u0014R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"Lcom/thedasagroup/suminative/ui/stock/StockUseCase;", "", "stockRepository", "Lcom/thedasagroup/suminative/data/repo/StockRepository;", "prefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "syncCategoriesUseCase", "Lcom/thedasagroup/suminative/domain/categories/SyncCategoriesUseCase;", "<init>", "(Lcom/thedasagroup/suminative/data/repo/StockRepository;Lcom/thedasagroup/suminative/data/prefs/Prefs;Lcom/thedasagroup/suminative/domain/categories/SyncCategoriesUseCase;)V", "invoke", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/stock/StockItemsResponse;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCategoriesForSorting", "", "", "storeId", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCategoriesFromApiDirectly", "app_stagingGeneralDebug"})
public class StockUseCase {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.repo.StockRepository stockRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.prefs.Prefs prefs = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.domain.categories.SyncCategoriesUseCase syncCategoriesUseCase = null;
    
    public StockUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.StockRepository stockRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.categories.SyncCategoriesUseCase syncCategoriesUseCase) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object invoke(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse>>> $completion) {
        return null;
    }
    
    private final java.lang.Object getCategoriesForSorting(long storeId, kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion) {
        return null;
    }
    
    private final java.lang.Object getCategoriesFromApiDirectly(long storeId, kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion) {
        return null;
    }
}