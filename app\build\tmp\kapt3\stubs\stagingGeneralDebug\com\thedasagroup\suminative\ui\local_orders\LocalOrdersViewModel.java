package com.thedasagroup.suminative.ui.local_orders;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\u0018\u0000 \u00112\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0002\u0010\u0011B\u001b\b\u0007\u0012\b\b\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u0006\u0010\b\u001a\u00020\tJ\u000e\u0010\n\u001a\u00020\t2\u0006\u0010\u000b\u001a\u00020\fJ\u0006\u0010\r\u001a\u00020\tJ\u0006\u0010\u000e\u001a\u00020\tJ\u0006\u0010\u000f\u001a\u00020\tR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/thedasagroup/suminative/ui/local_orders/LocalOrdersViewModel;", "Lcom/airbnb/mvrx/MavericksViewModel;", "Lcom/thedasagroup/suminative/ui/local_orders/LocalOrdersState;", "state", "getLocalOrdersUseCase", "Lcom/thedasagroup/suminative/domain/orders/GetLocalOrdersUseCase;", "<init>", "(Lcom/thedasagroup/suminative/ui/local_orders/LocalOrdersState;Lcom/thedasagroup/suminative/domain/orders/GetLocalOrdersUseCase;)V", "loadAllOrders", "", "loadOrdersByStatus", "status", "", "loadUnsyncedOrders", "loadSyncedOrders", "loadPendingOrders", "Factory", "Companion", "app_stagingGeneralDebug"})
public final class LocalOrdersViewModel extends com.airbnb.mvrx.MavericksViewModel<com.thedasagroup.suminative.ui.local_orders.LocalOrdersState> {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.domain.orders.GetLocalOrdersUseCase getLocalOrdersUseCase = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.local_orders.LocalOrdersViewModel.Companion Companion = null;
    
    @dagger.assisted.AssistedInject()
    public LocalOrdersViewModel(@dagger.assisted.Assisted()
    @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.local_orders.LocalOrdersState state, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.orders.GetLocalOrdersUseCase getLocalOrdersUseCase) {
        super(null, null);
    }
    
    public final void loadAllOrders() {
    }
    
    public final void loadOrdersByStatus(@org.jetbrains.annotations.NotNull()
    java.lang.String status) {
    }
    
    public final void loadUnsyncedOrders() {
    }
    
    public final void loadSyncedOrders() {
    }
    
    public final void loadPendingOrders() {
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u001b\u0010\u0006\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0003H\u0096\u0001J\u0013\u0010\n\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0007\u001a\u00020\bH\u0096\u0001\u00a8\u0006\u000b"}, d2 = {"Lcom/thedasagroup/suminative/ui/local_orders/LocalOrdersViewModel$Companion;", "Lcom/airbnb/mvrx/MavericksViewModelFactory;", "Lcom/thedasagroup/suminative/ui/local_orders/LocalOrdersViewModel;", "Lcom/thedasagroup/suminative/ui/local_orders/LocalOrdersState;", "<init>", "()V", "create", "viewModelContext", "Lcom/airbnb/mvrx/ViewModelContext;", "state", "initialState", "app_stagingGeneralDebug"})
    public static final class Companion implements com.airbnb.mvrx.MavericksViewModelFactory<com.thedasagroup.suminative.ui.local_orders.LocalOrdersViewModel, com.thedasagroup.suminative.ui.local_orders.LocalOrdersState> {
        
        private Companion() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.local_orders.LocalOrdersViewModel create(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.local_orders.LocalOrdersState state) {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.local_orders.LocalOrdersState initialState(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext) {
            return null;
        }
    }
    
    @dagger.assisted.AssistedFactory()
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\bg\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001J\u0010\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0003H&\u00a8\u0006\u0006"}, d2 = {"Lcom/thedasagroup/suminative/ui/local_orders/LocalOrdersViewModel$Factory;", "Lcom/airbnb/mvrx/hilt/AssistedViewModelFactory;", "Lcom/thedasagroup/suminative/ui/local_orders/LocalOrdersViewModel;", "Lcom/thedasagroup/suminative/ui/local_orders/LocalOrdersState;", "create", "state", "app_stagingGeneralDebug"})
    public static abstract interface Factory extends com.airbnb.mvrx.hilt.AssistedViewModelFactory<com.thedasagroup.suminative.ui.local_orders.LocalOrdersViewModel, com.thedasagroup.suminative.ui.local_orders.LocalOrdersState> {
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public abstract com.thedasagroup.suminative.ui.local_orders.LocalOrdersViewModel create(@org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.local_orders.LocalOrdersState state);
    }
}