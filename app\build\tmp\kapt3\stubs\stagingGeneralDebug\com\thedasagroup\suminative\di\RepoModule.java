package com.thedasagroup.suminative.di;

@dagger.Module()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0084\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c1\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0012\u0010\u0004\u001a\u00020\u00052\b\b\u0001\u0010\u0006\u001a\u00020\u0007H\u0007J\b\u0010\b\u001a\u00020\tH\u0007J\b\u0010\n\u001a\u00020\u000bH\u0007J\b\u0010\f\u001a\u00020\rH\u0007J\b\u0010\u000e\u001a\u00020\u000fH\u0007J\u0012\u0010\u0010\u001a\u00020\u00112\b\b\u0001\u0010\u0006\u001a\u00020\u0007H\u0007J\u0012\u0010\u0012\u001a\u00020\u00132\b\b\u0001\u0010\u0006\u001a\u00020\u0007H\u0007J\b\u0010\u0014\u001a\u00020\u0015H\u0007J\u0010\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\rH\u0007J\u0018\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u0018\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u0005H\u0007J\u0010\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001b\u001a\u00020\u0005H\u0007J\b\u0010\u001e\u001a\u00020\u001fH\u0007J\b\u0010 \u001a\u00020!H\u0007J\u0012\u0010\"\u001a\u00020#2\b\b\u0001\u0010\u0006\u001a\u00020\u0007H\u0007J\u0010\u0010$\u001a\u00020%2\u0006\u0010&\u001a\u00020#H\u0007J\u0012\u0010\'\u001a\u00020(2\b\b\u0001\u0010\u0006\u001a\u00020\u0007H\u0007J\b\u0010)\u001a\u00020*H\u0007J\b\u0010+\u001a\u00020,H\u0007\u00a8\u0006-"}, d2 = {"Lcom/thedasagroup/suminative/di/RepoModule;", "", "<init>", "()V", "providesSharedPrefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "context", "Landroid/content/Context;", "providesLoginRepository", "Lcom/thedasagroup/suminative/data/repo/LoginRepository;", "providesOrdersRepository", "Lcom/thedasagroup/suminative/data/repo/OrdersRepository;", "providesTrueTime", "Lcom/instacart/truetime/time/TrueTimeImpl;", "providesSoundPool", "Landroid/media/SoundPool;", "providesSoundPoolPlayer", "Lcom/thedasagroup/suminative/ui/utils/SoundPoolPlayer;", "providesAudioManager", "Landroid/media/AudioManager;", "providesStockRepository", "Lcom/thedasagroup/suminative/data/repo/StockRepository;", "providesSalesRepository", "Lcom/thedasagroup/suminative/data/repo/SalesRepository;", "trueTimeImpl", "providesMyGuavaRepository", "Lcom/thedasagroup/suminative/data/repo/MyGuavaRepository;", "prefs", "providesLogsRepository", "Lcom/thedasagroup/suminative/data/repo/LogsRepository;", "providesWaitersRepository", "Lcom/thedasagroup/suminative/data/repo/WaitersRepository;", "getHourUtils", "Lcom/thedasagroup/suminative/HourUtils;", "providesDatabaseManager", "Lcom/thedasagroup/suminative/data/database/DatabaseManager;", "providesOrderLocalRepository", "Lcom/thedasagroup/suminative/data/database/LocalOrderRepository;", "databaseManager", "providesOrderSyncManager", "Lcom/thedasagroup/suminative/work/OrderSyncManager;", "providesClockInOutRepository", "Lcom/thedasagroup/suminative/data/repo/ClockInOutRepository;", "providesReservationsRepository", "Lcom/thedasagroup/suminative/data/repo/ReservationsRepository;", "app_stagingGeneralDebug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class RepoModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.di.RepoModule INSTANCE = null;
    
    private RepoModule() {
        super();
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.prefs.Prefs providesSharedPrefs(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.repo.LoginRepository providesLoginRepository() {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.repo.OrdersRepository providesOrdersRepository() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.instacart.truetime.time.TrueTimeImpl providesTrueTime() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final android.media.SoundPool providesSoundPool() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.utils.SoundPoolPlayer providesSoundPoolPlayer(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final android.media.AudioManager providesAudioManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.repo.StockRepository providesStockRepository() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.repo.SalesRepository providesSalesRepository(@org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl trueTimeImpl) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.repo.MyGuavaRepository providesMyGuavaRepository(@org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl trueTimeImpl, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.repo.LogsRepository providesLogsRepository(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.repo.WaitersRepository providesWaitersRepository() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.HourUtils getHourUtils() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.database.DatabaseManager providesDatabaseManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.database.LocalOrderRepository providesOrderLocalRepository(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.database.DatabaseManager databaseManager) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.work.OrderSyncManager providesOrderSyncManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.repo.ClockInOutRepository providesClockInOutRepository() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.repo.ReservationsRepository providesReservationsRepository() {
        return null;
    }
}