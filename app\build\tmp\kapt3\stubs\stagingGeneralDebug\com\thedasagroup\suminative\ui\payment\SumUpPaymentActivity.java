package com.thedasagroup.suminative.ui.payment;

@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u0000 #2\u00020\u00012\u00020\u0002:\u0001#B\u0007\u00a2\u0006\u0004\b\u0003\u0010\u0004J\b\u0010\u0016\u001a\u00020\u0017H\u0016J\u0012\u0010\u0018\u001a\u00020\u00172\b\u0010\u0019\u001a\u0004\u0018\u00010\u001aH\u0014J\"\u0010\u001b\u001a\u00020\u00172\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u001d2\b\u0010\u001f\u001a\u0004\u0018\u00010 H\u0014J\u0012\u0010!\u001a\u00020\u00172\b\u0010\u001f\u001a\u0004\u0018\u00010 H\u0002J\u0012\u0010\"\u001a\u00020\u00172\b\u0010\u001f\u001a\u0004\u0018\u00010 H\u0002R\u001b\u0010\u0005\u001a\u00020\u00068BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\t\u0010\n\u001a\u0004\b\u0007\u0010\bR\u001b\u0010\u000b\u001a\u00020\f8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u000f\u0010\n\u001a\u0004\b\r\u0010\u000eR\u001c\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0012\u0010\u0013\"\u0004\b\u0014\u0010\u0015\u00a8\u0006$"}, d2 = {"Lcom/thedasagroup/suminative/ui/payment/SumUpPaymentActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "Lcom/airbnb/mvrx/MavericksView;", "<init>", "()V", "viewModel", "Lcom/thedasagroup/suminative/ui/payment/SumUpPaymentViewModel;", "getViewModel", "()Lcom/thedasagroup/suminative/ui/payment/SumUpPaymentViewModel;", "viewModel$delegate", "Lkotlin/Lazy;", "paymentViewModel", "Lcom/thedasagroup/suminative/ui/payment/PaymentViewModel;", "getPaymentViewModel", "()Lcom/thedasagroup/suminative/ui/payment/PaymentViewModel;", "paymentViewModel$delegate", "order", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "getOrder", "()Lcom/thedasagroup/suminative/data/model/request/order/Order;", "setOrder", "(Lcom/thedasagroup/suminative/data/model/request/order/Order;)V", "invalidate", "", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onActivityResult", "requestCode", "", "resultCode", "data", "Landroid/content/Intent;", "handleLoginResult", "handlePaymentResult", "Companion", "app_stagingGeneralDebug"})
public final class SumUpPaymentActivity extends androidx.appcompat.app.AppCompatActivity implements com.airbnb.mvrx.MavericksView {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy viewModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy paymentViewModel$delegate = null;
    @org.jetbrains.annotations.Nullable()
    private com.thedasagroup.suminative.data.model.request.order.Order order;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String ARG_ORDER = "extra_order";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String RESULT_ORDER = "result_order";
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.payment.SumUpPaymentActivity.Companion Companion = null;
    
    public SumUpPaymentActivity() {
        super();
    }
    
    private final com.thedasagroup.suminative.ui.payment.SumUpPaymentViewModel getViewModel() {
        return null;
    }
    
    private final com.thedasagroup.suminative.ui.payment.PaymentViewModel getPaymentViewModel() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.request.order.Order getOrder() {
        return null;
    }
    
    public final void setOrder(@org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.order.Order p0) {
    }
    
    @java.lang.Override()
    public void invalidate() {
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    protected void onActivityResult(int requestCode, int resultCode, @org.jetbrains.annotations.Nullable()
    android.content.Intent data) {
    }
    
    private final void handleLoginResult(android.content.Intent data) {
    }
    
    private final void handlePaymentResult(android.content.Intent data) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <T extends java.lang.Object>kotlinx.coroutines.Job collectLatest(@org.jetbrains.annotations.NotNull()
    kotlinx.coroutines.flow.Flow<? extends T> $this$collectLatest, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super T, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.airbnb.mvrx.MavericksViewInternalViewModel getMavericksViewInternalViewModel() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String getMvrxViewId() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public androidx.lifecycle.LifecycleOwner getSubscriptionLifecycleOwner() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, T extends java.lang.Object>kotlinx.coroutines.Job onAsync(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onAsync, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends com.airbnb.mvrx.Async<? extends T>> asyncProp, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super java.lang.Throwable, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> onFail, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super T, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> onSuccess) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super S, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super A, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super A, ? super B, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function4<? super A, ? super B, ? super C, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function5<? super A, ? super B, ? super C, ? super D, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object, E extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends E> prop5, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function6<? super A, ? super B, ? super C, ? super D, ? super E, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object, E extends java.lang.Object, F extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends E> prop5, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends F> prop6, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function7<? super A, ? super B, ? super C, ? super D, ? super E, ? super F, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object, E extends java.lang.Object, F extends java.lang.Object, G extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends E> prop5, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends F> prop6, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends G> prop7, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function8<? super A, ? super B, ? super C, ? super D, ? super E, ? super F, ? super G, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    public void postInvalidate() {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.airbnb.mvrx.UniqueOnly uniqueOnly(@org.jetbrains.annotations.Nullable()
    java.lang.String customId) {
        return null;
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0016\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/thedasagroup/suminative/ui/payment/SumUpPaymentActivity$Companion;", "", "<init>", "()V", "ARG_ORDER", "", "RESULT_ORDER", "createIntent", "Landroid/content/Intent;", "context", "Landroid/content/Context;", "order", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "app_stagingGeneralDebug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.content.Intent createIntent(@org.jetbrains.annotations.NotNull()
        android.content.Context context, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.data.model.request.order.Order order) {
            return null;
        }
    }
}