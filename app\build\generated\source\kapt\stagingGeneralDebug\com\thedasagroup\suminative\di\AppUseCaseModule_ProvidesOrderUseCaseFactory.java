package com.thedasagroup.suminative.di;

import com.instacart.truetime.time.TrueTimeImpl;
import com.thedasagroup.suminative.data.database.LocalOrderRepository;
import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.MyGuavaRepository;
import com.thedasagroup.suminative.data.repo.StockRepository;
import com.thedasagroup.suminative.ui.products.OrderUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesOrderUseCaseFactory implements Factory<OrderUseCase> {
  private final Provider<StockRepository> stockRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  private final Provider<TrueTimeImpl> trueTimeImplProvider;

  private final Provider<MyGuavaRepository> myGuavaRepositoryProvider;

  private final Provider<LocalOrderRepository> localOrderRepositoryProvider;

  public AppUseCaseModule_ProvidesOrderUseCaseFactory(
      Provider<StockRepository> stockRepositoryProvider, Provider<Prefs> prefsProvider,
      Provider<TrueTimeImpl> trueTimeImplProvider,
      Provider<MyGuavaRepository> myGuavaRepositoryProvider,
      Provider<LocalOrderRepository> localOrderRepositoryProvider) {
    this.stockRepositoryProvider = stockRepositoryProvider;
    this.prefsProvider = prefsProvider;
    this.trueTimeImplProvider = trueTimeImplProvider;
    this.myGuavaRepositoryProvider = myGuavaRepositoryProvider;
    this.localOrderRepositoryProvider = localOrderRepositoryProvider;
  }

  @Override
  public OrderUseCase get() {
    return providesOrderUseCase(stockRepositoryProvider.get(), prefsProvider.get(), trueTimeImplProvider.get(), myGuavaRepositoryProvider.get(), localOrderRepositoryProvider.get());
  }

  public static AppUseCaseModule_ProvidesOrderUseCaseFactory create(
      Provider<StockRepository> stockRepositoryProvider, Provider<Prefs> prefsProvider,
      Provider<TrueTimeImpl> trueTimeImplProvider,
      Provider<MyGuavaRepository> myGuavaRepositoryProvider,
      Provider<LocalOrderRepository> localOrderRepositoryProvider) {
    return new AppUseCaseModule_ProvidesOrderUseCaseFactory(stockRepositoryProvider, prefsProvider, trueTimeImplProvider, myGuavaRepositoryProvider, localOrderRepositoryProvider);
  }

  public static OrderUseCase providesOrderUseCase(StockRepository stockRepository, Prefs prefs,
      TrueTimeImpl trueTimeImpl, MyGuavaRepository myGuavaRepository,
      LocalOrderRepository localOrderRepository) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesOrderUseCase(stockRepository, prefs, trueTimeImpl, myGuavaRepository, localOrderRepository));
  }
}
