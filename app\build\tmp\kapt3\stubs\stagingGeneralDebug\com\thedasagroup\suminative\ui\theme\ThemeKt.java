package com.thedasagroup.suminative.ui.theme;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u00004\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\u001a/\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00062\u0011\u0010\b\u001a\r\u0012\u0004\u0012\u00020\u00040\t\u00a2\u0006\u0002\b\nH\u0007\"\u000e\u0010\u0000\u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\"\u000e\u0010\u0002\u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\"\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000\"\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011\"\u0011\u0010\u0012\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0011\u00a8\u0006\u0014"}, d2 = {"DarkColorScheme", "Landroidx/compose/material3/ColorScheme;", "LightColorScheme", "SumiNativeTheme", "", "darkTheme", "", "dynamicColor", "content", "Lkotlin/Function0;", "Landroidx/compose/runtime/Composable;", "LocalAppDimens", "Landroidx/compose/runtime/ProvidableCompositionLocal;", "Lcom/thedasagroup/suminative/ui/theme/Dimensions;", "fontPoppins", "Landroidx/compose/ui/text/font/FontFamily;", "getFontPoppins", "()Landroidx/compose/ui/text/font/FontFamily;", "fontNunito", "getFontNunito", "app_stagingGeneralDebug"})
public final class ThemeKt {
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.material3.ColorScheme DarkColorScheme = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.material3.ColorScheme LightColorScheme = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.runtime.ProvidableCompositionLocal<com.thedasagroup.suminative.ui.theme.Dimensions> LocalAppDimens = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.font.FontFamily fontPoppins = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.font.FontFamily fontNunito = null;
    
    @androidx.compose.runtime.Composable()
    public static final void SumiNativeTheme(boolean darkTheme, boolean dynamicColor, @org.jetbrains.annotations.NotNull()
    androidx.compose.runtime.internal.ComposableFunction0<kotlin.Unit> content) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.ui.text.font.FontFamily getFontPoppins() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.ui.text.font.FontFamily getFontNunito() {
        return null;
    }
}