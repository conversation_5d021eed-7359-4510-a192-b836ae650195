package com.thedasagroup.suminative.ui.guava_orders;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = GuavaOrdersActivity.class
)
@GeneratedEntryPoint
@InstallIn(ActivityComponent.class)
public interface GuavaOrdersActivity_GeneratedInjector {
  void injectGuavaOrdersActivity(GuavaOrdersActivity guavaOrdersActivity);
}
