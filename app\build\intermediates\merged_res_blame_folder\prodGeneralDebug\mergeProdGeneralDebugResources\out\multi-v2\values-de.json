{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-de\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4715", "endColumns": "144", "endOffsets": "4855"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,374,463,547,637,719,820,942,1023,1085,1151,1245,1315,1374,1482,1548,1617,1675,1747,1811,1865,1993,2053,2115,2169,2247,2384,2476,2554,2648,2734,2818,2963,3047,3133,3266,3356,3435,3492,3543,3609,3683,3765,3836,3911,3985,4063,4135,4209,4319,4411,4493,4582,4671,4745,4823,4909,4964,5043,5110,5190,5274,5336,5400,5463,5532,5639,5746,5845,5951,6012,6067,6149,6232,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "278,369,458,542,632,714,815,937,1018,1080,1146,1240,1310,1369,1477,1543,1612,1670,1742,1806,1860,1988,2048,2110,2164,2242,2379,2471,2549,2643,2729,2813,2958,3042,3128,3261,3351,3430,3487,3538,3604,3678,3760,3831,3906,3980,4058,4130,4204,4314,4406,4488,4577,4666,4740,4818,4904,4959,5038,5105,5185,5269,5331,5395,5458,5527,5634,5741,5840,5946,6007,6062,6144,6227,6304,6380"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3060,3151,3240,3324,3414,4227,4328,4450,5249,5311,5377,5783,6030,12354,12462,12528,12597,12655,12727,12791,12845,12973,13033,13095,13149,13227,13364,13456,13534,13628,13714,13798,13943,14027,14113,14246,14336,14415,14472,14523,14589,14663,14745,14816,14891,14965,15043,15115,15189,15299,15391,15473,15562,15651,15725,15803,15889,15944,16023,16090,16170,16254,16316,16380,16443,16512,16619,16726,16825,16931,16992,17224,17553,17636,17788", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "328,3146,3235,3319,3409,3491,4323,4445,4526,5306,5372,5466,5848,6084,12457,12523,12592,12650,12722,12786,12840,12968,13028,13090,13144,13222,13359,13451,13529,13623,13709,13793,13938,14022,14108,14241,14331,14410,14467,14518,14584,14658,14740,14811,14886,14960,15038,15110,15184,15294,15386,15468,15557,15646,15720,15798,15884,15939,16018,16085,16165,16249,16311,16375,16438,16507,16614,16721,16820,16926,16987,17042,17301,17631,17708,17859"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,186,315,424,553,663,758,870,1014,1132,1288,1373,1478,1573,1675,1793,1919,2029,2165,2302,2437,2616,2744,2867,2995,3120,3216,3314,3434,3563,3663,3768,3870,4011,4159,4265,4367,4447,4543,4638,4724,4813,4914,5002,5088,5188,5294,5389,5490,5578,5687,5788,5892,6030,6119,6224", "endColumns": "130,128,108,128,109,94,111,143,117,155,84,104,94,101,117,125,109,135,136,134,178,127,122,127,124,95,97,119,128,99,104,101,140,147,105,101,79,95,94,85,88,100,87,85,99,105,94,100,87,108,100,103,137,88,104,95", "endOffsets": "181,310,419,548,658,753,865,1009,1127,1283,1368,1473,1568,1670,1788,1914,2024,2160,2297,2432,2611,2739,2862,2990,3115,3211,3309,3429,3558,3658,3763,3865,4006,4154,4260,4362,4442,4538,4633,4719,4808,4909,4997,5083,5183,5289,5384,5485,5573,5682,5783,5887,6025,6114,6219,6315"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6089,6220,6349,6458,6587,6697,6792,6904,7048,7166,7322,7407,7512,7607,7709,7827,7953,8063,8199,8336,8471,8650,8778,8901,9029,9154,9250,9348,9468,9597,9697,9802,9904,10045,10193,10299,10401,10481,10577,10672,10758,10847,10948,11036,11122,11222,11328,11423,11524,11612,11721,11822,11926,12064,12153,12258", "endColumns": "130,128,108,128,109,94,111,143,117,155,84,104,94,101,117,125,109,135,136,134,178,127,122,127,124,95,97,119,128,99,104,101,140,147,105,101,79,95,94,85,88,100,87,85,99,105,94,100,87,108,100,103,137,88,104,95", "endOffsets": "6215,6344,6453,6582,6692,6787,6899,7043,7161,7317,7402,7507,7602,7704,7822,7948,8058,8194,8331,8466,8645,8773,8896,9024,9149,9245,9343,9463,9592,9692,9797,9899,10040,10188,10294,10396,10476,10572,10667,10753,10842,10943,11031,11117,11217,11323,11418,11519,11607,11716,11817,11921,12059,12148,12253,12349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,241,358,434,517,637,793,980,1112,1245,1347,1474,1533,1590,1680,1781,1849,2003,2112,2195,2350,2428,2516,2608,2763,2874,2980,3079,3150,3223,3378,3478,3691,3874,4018,4173,4276,4481,4603,4753,4871,5035,5149,5296,5402,5501,5649,5742,5889,5988,6166,6262,6416,6493,6610,6700,6776,6857,6931,7005,7084,7158,7283,7398,7559,7663,7786,7893,8067,8173,8272,8393,8487,8608,8724,8841,8955,9014,9069,9125,9181,9231,9281,9348,9399,9466,9516,9562,9640,9684,9738,9791,9843,9904,9955,10005,10074,10130,10182,10227,10291,10350,10414,10559,10685,10768,10829,10906,10975,11043,11163,11241,11317,11397,11475,11539,11636,11712,11810,11895,12058,12136,12228,12314,12403,12488,12566,12676,12783,12905,13020,13097,13233,13319,13405,13536,13671,13738,13815,13924,14023,16074,16189,16390,16437,16599,16786,16868,17039,17128,17277,17463,17620,17806,17886,17964,18033,18135,18248,18293,18380,18514,18615,18765,18896,19029,19150,19262,19330,19388,19447,19503,19573,19651,19739,19868,19938,20016,20314,20560,20699,20829,20966,21058,21177,21327,21493,21667,21759,21886,21977,22103,22204,22358,22453,22528,22584,22860,22944,23023,23084,23140,23263,23399,23502,23666,23884,23962,24028,24091,24258,24334,24425,24502,24596,24771,24864,24946,25079,25163,25324,25466,25522,25661,25736,25813,25875,25952,26003,26063,26163,26215,26282,26455,26581,26698,26762,26911,27084,27232,27352,27617,27719,27814,27920,28115,28369,28457,28692,28814,28974,29092,29171,29264,29344,29477,29907,30050,30098,30177,30330,30451,30531,30611,30701,30782,30855,30923,30985,31063,31135,31214,31297,31367,31436,31548,31642,31727,31958,32113,32213,32270,32359,32506,32566,32643,32727,32804,32885,33166,33221,33295,33374,33447,33539,33719,33812,33886,33970,34050,34118,34198,34276,34434,34518,34573,34641,34718,34798,34882,34992,35102,35222,35350,35431,35479,35531,35625,35730,35816,35932,36133,36247,36335,36458,36599,36755,36885,36981,37148,37293,37509,37704,37824,37978,38074,38164,38269,38413,38513,38653,38735,39185,39279,39365,39442", "endColumns": "185,116,75,82,119,155,186,131,132,101,126,58,56,89,100,67,153,108,82,154,77,87,91,154,110,105,98,70,72,154,99,212,182,143,154,102,204,121,149,117,163,113,146,105,98,147,92,146,98,177,95,153,76,116,89,75,80,73,73,78,73,124,114,160,103,122,106,173,105,98,120,93,120,115,116,113,58,54,55,55,49,49,66,50,66,49,45,77,43,53,52,51,60,50,49,68,55,51,44,63,58,63,144,125,82,60,76,68,67,119,77,75,79,77,63,96,75,97,84,162,77,91,85,88,84,77,109,106,121,114,76,135,85,85,130,134,66,76,108,98,2050,114,200,46,161,186,81,170,88,148,185,156,185,79,77,68,101,112,44,86,133,100,149,130,132,120,111,67,57,58,55,69,77,87,128,69,77,297,245,138,129,136,91,118,149,165,173,91,126,90,125,100,153,94,74,55,275,83,78,60,55,122,135,102,163,217,77,65,62,166,75,90,76,93,174,92,81,132,83,160,141,55,138,74,76,61,76,50,59,99,51,66,172,125,116,63,148,172,147,119,264,101,94,105,194,253,87,234,121,159,117,78,92,79,132,429,142,47,78,152,120,79,79,89,80,72,67,61,77,71,78,82,69,68,111,93,84,230,154,99,56,88,146,59,76,83,76,80,280,54,73,78,72,91,179,92,73,83,79,67,79,77,157,83,54,67,76,79,83,109,109,119,127,80,47,51,93,104,85,115,200,113,87,122,140,155,129,95,166,144,215,194,119,153,95,89,104,143,99,139,81,449,93,85,76,73", "endOffsets": "236,353,429,512,632,788,975,1107,1240,1342,1469,1528,1585,1675,1776,1844,1998,2107,2190,2345,2423,2511,2603,2758,2869,2975,3074,3145,3218,3373,3473,3686,3869,4013,4168,4271,4476,4598,4748,4866,5030,5144,5291,5397,5496,5644,5737,5884,5983,6161,6257,6411,6488,6605,6695,6771,6852,6926,7000,7079,7153,7278,7393,7554,7658,7781,7888,8062,8168,8267,8388,8482,8603,8719,8836,8950,9009,9064,9120,9176,9226,9276,9343,9394,9461,9511,9557,9635,9679,9733,9786,9838,9899,9950,10000,10069,10125,10177,10222,10286,10345,10409,10554,10680,10763,10824,10901,10970,11038,11158,11236,11312,11392,11470,11534,11631,11707,11805,11890,12053,12131,12223,12309,12398,12483,12561,12671,12778,12900,13015,13092,13228,13314,13400,13531,13666,13733,13810,13919,14018,16069,16184,16385,16432,16594,16781,16863,17034,17123,17272,17458,17615,17801,17881,17959,18028,18130,18243,18288,18375,18509,18610,18760,18891,19024,19145,19257,19325,19383,19442,19498,19568,19646,19734,19863,19933,20011,20309,20555,20694,20824,20961,21053,21172,21322,21488,21662,21754,21881,21972,22098,22199,22353,22448,22523,22579,22855,22939,23018,23079,23135,23258,23394,23497,23661,23879,23957,24023,24086,24253,24329,24420,24497,24591,24766,24859,24941,25074,25158,25319,25461,25517,25656,25731,25808,25870,25947,25998,26058,26158,26210,26277,26450,26576,26693,26757,26906,27079,27227,27347,27612,27714,27809,27915,28110,28364,28452,28687,28809,28969,29087,29166,29259,29339,29472,29902,30045,30093,30172,30325,30446,30526,30606,30696,30777,30850,30918,30980,31058,31130,31209,31292,31362,31431,31543,31637,31722,31953,32108,32208,32265,32354,32501,32561,32638,32722,32799,32880,33161,33216,33290,33369,33442,33534,33714,33807,33881,33965,34045,34113,34193,34271,34429,34513,34568,34636,34713,34793,34877,34987,35097,35217,35345,35426,35474,35526,35620,35725,35811,35927,36128,36242,36330,36453,36594,36750,36880,36976,37143,37288,37504,37699,37819,37973,38069,38159,38264,38408,38508,38648,38730,39180,39274,39360,39437,39511"}, "to": {"startLines": "193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18182,18368,18485,18561,18644,18764,18920,19107,19239,19372,19474,19601,19660,19717,19807,19908,19976,20130,20239,20322,20477,20555,20643,20735,20890,21001,21107,21206,21277,21350,21505,21605,21818,22001,22145,22300,22403,22608,22730,22880,22998,23162,23276,23423,23529,23628,23776,23869,24016,24115,24293,24389,24543,24620,24737,24827,24903,24984,25058,25132,25211,25285,25410,25525,25686,25790,25913,26020,26194,26300,26399,26520,26614,26735,26851,26968,27082,27141,27196,27252,27308,27358,27408,27475,27526,27593,27643,27689,27767,27811,27865,27918,27970,28031,28082,28132,28201,28257,28309,28354,28418,28477,28541,28686,28812,28895,28956,29033,29102,29170,29290,29368,29444,29524,29602,29666,29763,29839,29937,30022,30185,30263,30355,30441,30530,30615,30693,30803,30910,31032,31147,31224,31360,31446,31532,31663,31798,31865,31942,32051,32150,34201,34316,34517,34564,34726,34913,34995,35166,35255,35404,35590,35747,35933,36013,36091,36160,36262,36375,36420,36507,36641,36742,36892,37023,37156,37277,37389,37457,37515,37574,37630,37700,37778,37866,37995,38065,38143,38441,38687,38826,38956,39093,39185,39304,39454,39620,39794,39886,40013,40104,40230,40331,40485,40580,40655,40711,40987,41071,41150,41211,41267,41390,41526,41629,41793,42011,42089,42155,42218,42385,42461,42552,42629,42723,42898,42991,43073,43206,43290,43451,43593,43649,43788,43863,43940,44002,44079,44130,44190,44290,44342,44409,44582,44708,44825,44889,45038,45211,45359,45479,45744,45846,45941,46047,46242,46496,46584,46819,46941,47101,47219,47298,47391,47471,47604,48034,48177,48225,48304,48457,48578,48658,48738,48828,48909,48982,49050,49112,49190,49262,49341,49424,49494,49563,49675,49769,49854,50085,50240,50340,50397,50486,50633,50693,50770,50854,50931,51012,51293,51348,51422,51501,51574,51666,51846,51939,52013,52097,52177,52245,52325,52403,52561,52645,52700,52768,52845,52925,53009,53119,53229,53349,53477,53558,53606,53658,53752,53857,53943,54059,54260,54374,54462,54585,54726,54882,55012,55108,55275,55420,55636,55831,55951,56105,56201,56291,56396,56540,56640,56780,56862,57312,57406,57492,57569", "endColumns": "185,116,75,82,119,155,186,131,132,101,126,58,56,89,100,67,153,108,82,154,77,87,91,154,110,105,98,70,72,154,99,212,182,143,154,102,204,121,149,117,163,113,146,105,98,147,92,146,98,177,95,153,76,116,89,75,80,73,73,78,73,124,114,160,103,122,106,173,105,98,120,93,120,115,116,113,58,54,55,55,49,49,66,50,66,49,45,77,43,53,52,51,60,50,49,68,55,51,44,63,58,63,144,125,82,60,76,68,67,119,77,75,79,77,63,96,75,97,84,162,77,91,85,88,84,77,109,106,121,114,76,135,85,85,130,134,66,76,108,98,2050,114,200,46,161,186,81,170,88,148,185,156,185,79,77,68,101,112,44,86,133,100,149,130,132,120,111,67,57,58,55,69,77,87,128,69,77,297,245,138,129,136,91,118,149,165,173,91,126,90,125,100,153,94,74,55,275,83,78,60,55,122,135,102,163,217,77,65,62,166,75,90,76,93,174,92,81,132,83,160,141,55,138,74,76,61,76,50,59,99,51,66,172,125,116,63,148,172,147,119,264,101,94,105,194,253,87,234,121,159,117,78,92,79,132,429,142,47,78,152,120,79,79,89,80,72,67,61,77,71,78,82,69,68,111,93,84,230,154,99,56,88,146,59,76,83,76,80,280,54,73,78,72,91,179,92,73,83,79,67,79,77,157,83,54,67,76,79,83,109,109,119,127,80,47,51,93,104,85,115,200,113,87,122,140,155,129,95,166,144,215,194,119,153,95,89,104,143,99,139,81,449,93,85,76,73", "endOffsets": "18363,18480,18556,18639,18759,18915,19102,19234,19367,19469,19596,19655,19712,19802,19903,19971,20125,20234,20317,20472,20550,20638,20730,20885,20996,21102,21201,21272,21345,21500,21600,21813,21996,22140,22295,22398,22603,22725,22875,22993,23157,23271,23418,23524,23623,23771,23864,24011,24110,24288,24384,24538,24615,24732,24822,24898,24979,25053,25127,25206,25280,25405,25520,25681,25785,25908,26015,26189,26295,26394,26515,26609,26730,26846,26963,27077,27136,27191,27247,27303,27353,27403,27470,27521,27588,27638,27684,27762,27806,27860,27913,27965,28026,28077,28127,28196,28252,28304,28349,28413,28472,28536,28681,28807,28890,28951,29028,29097,29165,29285,29363,29439,29519,29597,29661,29758,29834,29932,30017,30180,30258,30350,30436,30525,30610,30688,30798,30905,31027,31142,31219,31355,31441,31527,31658,31793,31860,31937,32046,32145,34196,34311,34512,34559,34721,34908,34990,35161,35250,35399,35585,35742,35928,36008,36086,36155,36257,36370,36415,36502,36636,36737,36887,37018,37151,37272,37384,37452,37510,37569,37625,37695,37773,37861,37990,38060,38138,38436,38682,38821,38951,39088,39180,39299,39449,39615,39789,39881,40008,40099,40225,40326,40480,40575,40650,40706,40982,41066,41145,41206,41262,41385,41521,41624,41788,42006,42084,42150,42213,42380,42456,42547,42624,42718,42893,42986,43068,43201,43285,43446,43588,43644,43783,43858,43935,43997,44074,44125,44185,44285,44337,44404,44577,44703,44820,44884,45033,45206,45354,45474,45739,45841,45936,46042,46237,46491,46579,46814,46936,47096,47214,47293,47386,47466,47599,48029,48172,48220,48299,48452,48573,48653,48733,48823,48904,48977,49045,49107,49185,49257,49336,49419,49489,49558,49670,49764,49849,50080,50235,50335,50392,50481,50628,50688,50765,50849,50926,51007,51288,51343,51417,51496,51569,51661,51841,51934,52008,52092,52172,52240,52320,52398,52556,52640,52695,52763,52840,52920,53004,53114,53224,53344,53472,53553,53601,53653,53747,53852,53938,54054,54255,54369,54457,54580,54721,54877,55007,55103,55270,55415,55631,55826,55946,56100,56196,56286,56391,56535,56635,56775,56857,57307,57401,57487,57564,57638"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,536,648,734,840,955,1033,1108,1200,1294,1390,1491,1598,1698,1802,1900,1998,2095,2177,2288,2390,2488,2595,2698,2802,2958,17471", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "433,531,643,729,835,950,1028,1103,1195,1289,1385,1486,1593,1693,1797,1895,1993,2090,2172,2283,2385,2483,2590,2693,2797,2953,3055,17548"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,289,387,487,574,659,751,840,928,1009,1093,1168,1243,1315,1385,1464,1530", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,74,71,69,78,65,119", "endOffsets": "196,284,382,482,569,654,746,835,923,1004,1088,1163,1238,1310,1380,1459,1525,1645"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,539,540,541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4531,4627,4964,5062,5162,5853,5938,17047,17136,17306,17387,17713,17864,17939,18011,57643,57722,57788", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,74,71,69,78,65,119", "endOffsets": "4622,4710,5057,5157,5244,5933,6025,17131,17219,17382,17466,17783,17934,18006,18076,57717,57783,57903"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,371", "endColumns": "103,100,110,99", "endOffsets": "154,255,366,466"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4860,5471,5572,5683", "endColumns": "103,100,110,99", "endOffsets": "4959,5567,5678,5778"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3496,3594,3696,3796,3896,4004,4109,18081", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "3589,3691,3791,3891,3999,4104,4222,18177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,89", "endOffsets": "137,227"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "57908,57995", "endColumns": "86,89", "endOffsets": "57990,58080"}}]}]}