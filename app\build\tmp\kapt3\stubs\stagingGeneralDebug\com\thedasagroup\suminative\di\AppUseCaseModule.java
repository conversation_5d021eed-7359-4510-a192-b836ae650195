package com.thedasagroup.suminative.di;

@dagger.Module()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u00c8\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c1\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0018\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u0007J\u0018\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\b\u001a\u00020\tH\u0007J\u0018\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\b\u001a\u00020\tH\u0007J\u0018\u0010\u0010\u001a\u00020\u00112\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\f\u001a\u00020\rH\u0007J \u0010\u0012\u001a\u00020\u00132\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u0014\u001a\u00020\u0015H\u0007J(\u0010\u0016\u001a\u00020\u00172\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\u0019H\u0007J\u0018\u0010\u001a\u001a\u00020\u00192\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u0007J\u0018\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\b\u001a\u00020\tH\u0007J\u0018\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\b\u001a\u00020\tH\u0007J\u0018\u0010\u001f\u001a\u00020 2\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\b\u001a\u00020\tH\u0007J0\u0010!\u001a\u00020\"2\u0006\u0010#\u001a\u00020$2\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010%\u001a\u00020&2\u0006\u0010\'\u001a\u00020(H\u0007J(\u0010)\u001a\u00020*2\u0006\u0010#\u001a\u00020$2\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010%\u001a\u00020&H\u0007J\u0018\u0010+\u001a\u00020,2\u0006\u0010-\u001a\u00020.2\u0006\u0010\b\u001a\u00020\tH\u0007J\u0018\u0010/\u001a\u0002002\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u0007J\u0018\u00101\u001a\u0002022\u0006\u0010-\u001a\u00020.2\u0006\u0010\b\u001a\u00020\tH\u0007J \u00103\u001a\u0002042\u0006\u0010%\u001a\u00020&2\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\b\u001a\u00020\tH\u0007J\u0010\u00105\u001a\u0002062\u0006\u0010%\u001a\u00020&H\u0007J\u0010\u00107\u001a\u0002082\u0006\u0010%\u001a\u00020&H\u0007J \u00109\u001a\u00020:2\u0006\u0010;\u001a\u0002042\u0006\u0010<\u001a\u0002062\u0006\u0010=\u001a\u000208H\u0007J\u0010\u0010>\u001a\u00020?2\u0006\u0010%\u001a\u00020&H\u0007J\u0018\u0010@\u001a\u00020A2\u0006\u0010%\u001a\u00020&2\u0006\u0010\u0014\u001a\u00020\u0015H\u0007J\u0018\u0010B\u001a\u00020C2\u0006\u0010%\u001a\u00020&2\u0006\u0010\u0014\u001a\u00020\u0015H\u0007J \u0010D\u001a\u00020E2\u0006\u0010F\u001a\u00020C2\u0006\u0010G\u001a\u0002062\u0006\u0010=\u001a\u000208H\u0007J\u0018\u0010H\u001a\u00020I2\u0006\u0010\f\u001a\u00020(2\u0006\u0010\b\u001a\u00020\tH\u0007J \u0010J\u001a\u00020K2\u0006\u0010\'\u001a\u00020(2\u0006\u0010#\u001a\u00020$2\u0006\u0010\b\u001a\u00020\tH\u0007J0\u0010L\u001a\u00020M2\u0006\u0010#\u001a\u00020$2\u0006\u0010N\u001a\u00020O2\u0006\u0010P\u001a\u00020Q2\u0006\u0010R\u001a\u00020S2\u0006\u0010\b\u001a\u00020\tH\u0007J\u0010\u0010T\u001a\u00020O2\u0006\u0010U\u001a\u00020VH\u0007J\u0010\u0010W\u001a\u00020Q2\u0006\u0010U\u001a\u00020VH\u0007J\u0010\u0010X\u001a\u00020Y2\u0006\u0010Z\u001a\u00020[H\u0007J\u0010\u0010\\\u001a\u00020]2\u0006\u0010Z\u001a\u00020[H\u0007J\u0010\u0010^\u001a\u00020_2\u0006\u0010Z\u001a\u00020[H\u0007J \u0010`\u001a\u00020a2\u0006\u0010b\u001a\u00020c2\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\u0014\u001a\u00020\u0015H\u0007J \u0010d\u001a\u00020e2\u0006\u0010b\u001a\u00020c2\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\u0014\u001a\u00020\u0015H\u0007J\u0018\u0010f\u001a\u00020g2\u0006\u0010b\u001a\u00020c2\u0006\u0010\b\u001a\u00020\tH\u0007J\u0010\u0010h\u001a\u00020i2\u0006\u0010b\u001a\u00020cH\u0007J\u0010\u0010j\u001a\u00020k2\u0006\u0010b\u001a\u00020cH\u0007J\u0010\u0010l\u001a\u00020m2\u0006\u0010b\u001a\u00020cH\u0007J\u0010\u0010n\u001a\u00020o2\u0006\u0010b\u001a\u00020cH\u0007J\u0010\u0010p\u001a\u00020q2\u0006\u0010#\u001a\u00020$H\u0007\u00a8\u0006r"}, d2 = {"Lcom/thedasagroup/suminative/di/AppUseCaseModule;", "", "<init>", "()V", "providesLoginUseCase", "Lcom/thedasagroup/suminative/ui/login/LoginUseCase;", "loginRepository", "Lcom/thedasagroup/suminative/data/repo/LoginRepository;", "prefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "providesGetOrdersUseCase", "Lcom/thedasagroup/suminative/ui/orders/GetOrdersUseCase;", "ordersRepository", "Lcom/thedasagroup/suminative/data/repo/OrdersRepository;", "providesScheduleOrdersUseCase", "Lcom/thedasagroup/suminative/ui/orders/GetScheduleOrdersUseCase;", "provideChangeStatusUseCase", "Lcom/thedasagroup/suminative/ui/orders/ChangeStatusUseCase;", "providesAcceptDeliveryOrderUseCase", "Lcom/thedasagroup/suminative/ui/orders/ChangeStatusAndOrdersUseCase;", "trueTimeImpl", "Lcom/instacart/truetime/time/TrueTimeImpl;", "providesAcceptOrderWithDelayUseCase", "Lcom/thedasagroup/suminative/ui/orders/AcceptOrderWithDelayUseCase;", "getStoreSettingsUseCase", "Lcom/thedasagroup/suminative/ui/login/GetStoreSettingsUseCase;", "provideStoreSettingsUseCase", "provideCloseOpenStoreUseCase", "Lcom/thedasagroup/suminative/ui/orders/CloseOpenStoreUseCase;", "providePendingOrdersUseCase", "Lcom/thedasagroup/suminative/ui/orders/GetPendingOrdersPagedUseCase;", "provideScheduleOrdersPagedUseCase", "Lcom/thedasagroup/suminative/ui/orders/GetScheduleOrdersPagedUseCase;", "providesOrderUseCase", "Lcom/thedasagroup/suminative/ui/products/OrderUseCase;", "stockRepository", "Lcom/thedasagroup/suminative/data/repo/StockRepository;", "myGuavaRepository", "Lcom/thedasagroup/suminative/data/repo/MyGuavaRepository;", "localOrderRepository", "Lcom/thedasagroup/suminative/data/database/LocalOrderRepository;", "providesGuavaOrderUseCase", "Lcom/thedasagroup/suminative/ui/products/PlaceOnlineOrderUseCase;", "providesSalesUseCase", "Lcom/thedasagroup/suminative/ui/sales/TotalSalesUseCase;", "salesRepository", "Lcom/thedasagroup/suminative/data/repo/SalesRepository;", "providesGetPosSettingsUsecase", "Lcom/thedasagroup/suminative/domain/GetPOSSettingsUseCase;", "providesSalesReportUsecase", "Lcom/thedasagroup/suminative/domain/sales_report/GetSalesReportUseCase;", "providesMyGuavaCreateOrderUseCase", "Lcom/thedasagroup/suminative/domain/myguava/MyGuavaCreateOrderUseCase;", "providesMyGuavaGetTerminalsUseCase", "Lcom/thedasagroup/suminative/domain/myguava/MyGuavaGetTerminalsUseCase;", "providesMyGuavaCreateSessionUseCase", "Lcom/thedasagroup/suminative/domain/myguava/MyGuavaCreateSessionUseCase;", "providesMyGuavaMakePaymentUseCase", "Lcom/thedasagroup/suminative/domain/myguava/MyGuavaMakePaymentUseCase;", "orderUseCase", "terminalsUseCase", "createSessionUseCase", "providesMyGuavaCheckStatusUseCase", "Lcom/thedasagroup/suminative/domain/myguava/MyGuavaCheckStatusUseCase;", "providesMyGuavaGetOrderUseCase", "Lcom/thedasagroup/suminative/domain/myguava/MyGuavaGetOrdersUseCase;", "providesMyGuavaRefundOrderUseCase", "Lcom/thedasagroup/suminative/domain/myguava/MyGuavaCreateRefundOrderUseCase;", "providesMyGuavaMakeRefundUseCase", "Lcom/thedasagroup/suminative/domain/myguava/MyGuavaMakeRefundUseCase;", "createOrderUseCase", "getTerminalsUseCase", "providesLocalOrdersUseCase", "Lcom/thedasagroup/suminative/domain/orders/GetLocalOrdersUseCase;", "providesSyncOrdersUseCase", "Lcom/thedasagroup/suminative/domain/orders/SyncOrdersUseCase;", "provideDownloadProductsUseCase", "Lcom/thedasagroup/suminative/ui/products/DownloadProductsUseCase;", "productRepository", "Lcom/thedasagroup/suminative/data/repo/ProductRepository;", "optionRepository", "Lcom/thedasagroup/suminative/data/repo/OptionRepository;", "categoryRepository", "Lcom/thedasagroup/suminative/data/database/CategoryRepository;", "provideProductRepository", "databaseManager", "Lcom/thedasagroup/suminative/data/database/DatabaseManager;", "provideOptionRepository", "providesStoreUserLoginUseCase", "Lcom/thedasagroup/suminative/ui/login/StoreUserLoginUseCase;", "clockInOutRepository", "Lcom/thedasagroup/suminative/data/repo/ClockInOutRepository;", "providesClockInUserTimeUseCase", "Lcom/thedasagroup/suminative/ui/login/ClockInUserTimeUseCase;", "providesClockOutUserTimeUseCase", "Lcom/thedasagroup/suminative/ui/login/ClockOutUserTimeUseCase;", "providesGetActiveReservationsUseCase", "Lcom/thedasagroup/suminative/ui/reservations/GetActiveReservationsUseCase;", "reservationsRepository", "Lcom/thedasagroup/suminative/data/repo/ReservationsRepository;", "providesGetAllReservationsUseCase", "Lcom/thedasagroup/suminative/ui/reservations/GetAllReservationsUseCase;", "providesGetReservationAreasUseCase", "Lcom/thedasagroup/suminative/ui/reservations/GetReservationAreasUseCase;", "providesGetReservationTablesUseCase", "Lcom/thedasagroup/suminative/ui/reservations/GetReservationTablesUseCase;", "providesCreateReservationUseCase", "Lcom/thedasagroup/suminative/ui/reservations/CreateReservationUseCase;", "providesEditReservationUseCase", "Lcom/thedasagroup/suminative/ui/reservations/EditReservationUseCase;", "providesCancelReservationUseCase", "Lcom/thedasagroup/suminative/ui/reservations/CancelReservationUseCase;", "providesCloudPrintUseCase", "Lcom/thedasagroup/suminative/domain/cloud_print/CloudPrintUseCase;", "app_stagingGeneralDebug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class AppUseCaseModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.di.AppUseCaseModule INSTANCE = null;
    
    private AppUseCaseModule() {
        super();
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.login.LoginUseCase providesLoginUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.LoginRepository loginRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.orders.GetOrdersUseCase providesGetOrdersUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.OrdersRepository ordersRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.orders.GetScheduleOrdersUseCase providesScheduleOrdersUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.OrdersRepository ordersRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.orders.ChangeStatusUseCase provideChangeStatusUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.OrdersRepository ordersRepository) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.orders.ChangeStatusAndOrdersUseCase providesAcceptDeliveryOrderUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.OrdersRepository ordersRepository, @org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl trueTimeImpl) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.orders.AcceptOrderWithDelayUseCase providesAcceptOrderWithDelayUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.OrdersRepository ordersRepository, @org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl trueTimeImpl, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase getStoreSettingsUseCase) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase provideStoreSettingsUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.LoginRepository loginRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.orders.CloseOpenStoreUseCase provideCloseOpenStoreUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.OrdersRepository ordersRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.orders.GetPendingOrdersPagedUseCase providePendingOrdersUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.OrdersRepository ordersRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.orders.GetScheduleOrdersPagedUseCase provideScheduleOrdersPagedUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.OrdersRepository ordersRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.products.OrderUseCase providesOrderUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.StockRepository stockRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs, @org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl trueTimeImpl, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.MyGuavaRepository myGuavaRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.database.LocalOrderRepository localOrderRepository) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.products.PlaceOnlineOrderUseCase providesGuavaOrderUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.StockRepository stockRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs, @org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl trueTimeImpl, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.MyGuavaRepository myGuavaRepository) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.sales.TotalSalesUseCase providesSalesUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.SalesRepository salesRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.GetPOSSettingsUseCase providesGetPosSettingsUsecase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.LoginRepository loginRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.sales_report.GetSalesReportUseCase providesSalesReportUsecase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.SalesRepository salesRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.myguava.MyGuavaCreateOrderUseCase providesMyGuavaCreateOrderUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.MyGuavaRepository myGuavaRepository, @org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl trueTimeImpl, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.myguava.MyGuavaGetTerminalsUseCase providesMyGuavaGetTerminalsUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.MyGuavaRepository myGuavaRepository) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.myguava.MyGuavaCreateSessionUseCase providesMyGuavaCreateSessionUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.MyGuavaRepository myGuavaRepository) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.myguava.MyGuavaMakePaymentUseCase providesMyGuavaMakePaymentUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.myguava.MyGuavaCreateOrderUseCase orderUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.myguava.MyGuavaGetTerminalsUseCase terminalsUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.myguava.MyGuavaCreateSessionUseCase createSessionUseCase) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.myguava.MyGuavaCheckStatusUseCase providesMyGuavaCheckStatusUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.MyGuavaRepository myGuavaRepository) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.myguava.MyGuavaGetOrdersUseCase providesMyGuavaGetOrderUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.MyGuavaRepository myGuavaRepository, @org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl trueTimeImpl) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.myguava.MyGuavaCreateRefundOrderUseCase providesMyGuavaRefundOrderUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.MyGuavaRepository myGuavaRepository, @org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl trueTimeImpl) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.myguava.MyGuavaMakeRefundUseCase providesMyGuavaMakeRefundUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.myguava.MyGuavaCreateRefundOrderUseCase createOrderUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.myguava.MyGuavaGetTerminalsUseCase getTerminalsUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.myguava.MyGuavaCreateSessionUseCase createSessionUseCase) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.orders.GetLocalOrdersUseCase providesLocalOrdersUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.database.LocalOrderRepository ordersRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.orders.SyncOrdersUseCase providesSyncOrdersUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.database.LocalOrderRepository localOrderRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.StockRepository stockRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        return null;
    }
    
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.products.DownloadProductsUseCase provideDownloadProductsUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.StockRepository stockRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.ProductRepository productRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.OptionRepository optionRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.database.CategoryRepository categoryRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.repo.ProductRepository provideProductRepository(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.database.DatabaseManager databaseManager) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.repo.OptionRepository provideOptionRepository(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.database.DatabaseManager databaseManager) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.login.StoreUserLoginUseCase providesStoreUserLoginUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.ClockInOutRepository clockInOutRepository) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.login.ClockInUserTimeUseCase providesClockInUserTimeUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.ClockInOutRepository clockInOutRepository) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.login.ClockOutUserTimeUseCase providesClockOutUserTimeUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.ClockInOutRepository clockInOutRepository) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.reservations.GetActiveReservationsUseCase providesGetActiveReservationsUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.ReservationsRepository reservationsRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs, @org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl trueTimeImpl) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.reservations.GetAllReservationsUseCase providesGetAllReservationsUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.ReservationsRepository reservationsRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs, @org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl trueTimeImpl) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.reservations.GetReservationAreasUseCase providesGetReservationAreasUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.ReservationsRepository reservationsRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.reservations.GetReservationTablesUseCase providesGetReservationTablesUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.ReservationsRepository reservationsRepository) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.reservations.CreateReservationUseCase providesCreateReservationUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.ReservationsRepository reservationsRepository) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.reservations.EditReservationUseCase providesEditReservationUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.ReservationsRepository reservationsRepository) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.reservations.CancelReservationUseCase providesCancelReservationUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.ReservationsRepository reservationsRepository) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.domain.cloud_print.CloudPrintUseCase providesCloudPrintUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.StockRepository stockRepository) {
        return null;
    }
}