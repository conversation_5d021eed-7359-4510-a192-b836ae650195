{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,383,485,573,651,738,829,911,999,1089,1162,1236,1315,1390,1467,1534", "endColumns": "96,83,96,101,87,77,86,90,81,87,89,72,73,78,74,76,66,114", "endOffsets": "197,281,378,480,568,646,733,824,906,994,1084,1157,1231,1310,1385,1462,1529,1644"}, "to": {"startLines": "49,50,53,54,55,63,64,180,181,183,184,188,190,191,192,540,541,542", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4592,4689,5024,5121,5223,5930,6008,17101,17192,17353,17441,17776,17928,18002,18081,56923,57000,57067", "endColumns": "96,83,96,101,87,77,86,90,81,87,89,72,73,78,74,76,66,114", "endOffsets": "4684,4768,5116,5218,5306,6003,6090,17187,17269,17436,17526,17844,17997,18076,18151,56995,57062,57177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,413,501,588,684,774,875,996,1080,1142,1208,1303,1377,1437,1521,1583,1649,1707,1780,1843,1899,2018,2075,2136,2192,2266,2411,2497,2572,2661,2740,2824,2957,3039,3122,3268,3358,3438,3493,3544,3610,3683,3761,3832,3917,3988,4065,4139,4211,4317,4408,4482,4577,4675,4749,4829,4930,4983,5069,5135,5224,5314,5376,5440,5503,5577,5689,5799,5909,6014,6073,6128,6207,6293,6370", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,91,87,86,95,89,100,120,83,61,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,74,88,78,83,132,81,82,145,89,79,54,50,65,72,77,70,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78,85,76,78", "endOffsets": "316,408,496,583,679,769,870,991,1075,1137,1203,1298,1372,1432,1516,1578,1644,1702,1775,1838,1894,2013,2070,2131,2187,2261,2406,2492,2567,2656,2735,2819,2952,3034,3117,3263,3353,3433,3488,3539,3605,3678,3756,3827,3912,3983,4060,4134,4206,4312,4403,4477,4572,4670,4744,4824,4925,4978,5064,5130,5219,5309,5371,5435,5498,5572,5684,5794,5904,6009,6068,6123,6202,6288,6365,6444"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,56,57,58,62,65,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,182,186,187,189", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3106,3198,3286,3373,3469,4286,4387,4508,5311,5373,5439,5856,6095,12410,12494,12556,12622,12680,12753,12816,12872,12991,13048,13109,13165,13239,13384,13470,13545,13634,13713,13797,13930,14012,14095,14241,14331,14411,14466,14517,14583,14656,14734,14805,14890,14961,15038,15112,15184,15290,15381,15455,15550,15648,15722,15802,15903,15956,16042,16108,16197,16287,16349,16413,16476,16550,16662,16772,16882,16987,17046,17274,17613,17699,17849", "endLines": "6,34,35,36,37,38,46,47,48,56,57,58,62,65,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,182,186,187,189", "endColumns": "12,91,87,86,95,89,100,120,83,61,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,74,88,78,83,132,81,82,145,89,79,54,50,65,72,77,70,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78,85,76,78", "endOffsets": "366,3193,3281,3368,3464,3554,4382,4503,4587,5368,5434,5529,5925,6150,12489,12551,12617,12675,12748,12811,12867,12986,13043,13104,13160,13234,13379,13465,13540,13629,13708,13792,13925,14007,14090,14236,14326,14406,14461,14512,14578,14651,14729,14800,14885,14956,15033,15107,15179,15285,15376,15450,15545,15643,15717,15797,15898,15951,16037,16103,16192,16282,16344,16408,16471,16545,16657,16767,16877,16982,17041,17096,17348,17694,17771,17923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-ro\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "139", "endOffsets": "334"}, "to": {"startLines": "51", "startColumns": "4", "startOffsets": "4773", "endColumns": "143", "endOffsets": "4912"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,150", "endColumns": "94,99", "endOffsets": "145,245"}, "to": {"startLines": "543,544", "startColumns": "4,4", "startOffsets": "57182,57277", "endColumns": "94,99", "endOffsets": "57272,57372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "39,40,41,42,43,44,45,193", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3559,3657,3759,3859,3958,4060,4169,18156", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "3652,3754,3854,3953,4055,4164,4281,18252"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,379", "endColumns": "106,101,114,104", "endOffsets": "157,259,374,479"}, "to": {"startLines": "52,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "4917,5534,5636,5751", "endColumns": "106,101,114,104", "endOffsets": "5019,5631,5746,5851"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,334,447,531,636,755,840,920,1011,1104,1199,1293,1393,1486,1581,1675,1766,1858,1939,2049,2157,2255,2367,2473,2577,2739,2840", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "223,329,442,526,631,750,835,915,1006,1099,1194,1288,1388,1481,1576,1670,1761,1853,1934,2044,2152,2250,2362,2468,2572,2734,2835,2917"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "371,494,600,713,797,902,1021,1106,1186,1277,1370,1465,1559,1659,1752,1847,1941,2032,2124,2205,2315,2423,2521,2633,2739,2843,3005,17531", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "489,595,708,792,897,1016,1101,1181,1272,1365,1460,1554,1654,1747,1842,1936,2027,2119,2200,2310,2418,2516,2628,2734,2838,3000,3101,17608"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,185,312,429,555,665,762,876,1013,1133,1276,1360,1462,1557,1655,1775,1902,2009,2147,2283,2424,2600,2737,2856,2979,3105,3201,3297,3424,3565,3665,3770,3881,4021,4167,4279,4383,4459,4554,4646,4732,4819,4920,5002,5085,5184,5288,5383,5484,5571,5682,5782,5888,6009,6091,6206", "endColumns": "129,126,116,125,109,96,113,136,119,142,83,101,94,97,119,126,106,137,135,140,175,136,118,122,125,95,95,126,140,99,104,110,139,145,111,103,75,94,91,85,86,100,81,82,98,103,94,100,86,110,99,105,120,81,114,103", "endOffsets": "180,307,424,550,660,757,871,1008,1128,1271,1355,1457,1552,1650,1770,1897,2004,2142,2278,2419,2595,2732,2851,2974,3100,3196,3292,3419,3560,3660,3765,3876,4016,4162,4274,4378,4454,4549,4641,4727,4814,4915,4997,5080,5179,5283,5378,5479,5566,5677,5777,5883,6004,6086,6201,6305"}, "to": {"startLines": "66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6155,6285,6412,6529,6655,6765,6862,6976,7113,7233,7376,7460,7562,7657,7755,7875,8002,8109,8247,8383,8524,8700,8837,8956,9079,9205,9301,9397,9524,9665,9765,9870,9981,10121,10267,10379,10483,10559,10654,10746,10832,10919,11020,11102,11185,11284,11388,11483,11584,11671,11782,11882,11988,12109,12191,12306", "endColumns": "129,126,116,125,109,96,113,136,119,142,83,101,94,97,119,126,106,137,135,140,175,136,118,122,125,95,95,126,140,99,104,110,139,145,111,103,75,94,91,85,86,100,81,82,98,103,94,100,86,110,99,105,120,81,114,103", "endOffsets": "6280,6407,6524,6650,6760,6857,6971,7108,7228,7371,7455,7557,7652,7750,7870,7997,8104,8242,8378,8519,8695,8832,8951,9074,9200,9296,9392,9519,9660,9760,9865,9976,10116,10262,10374,10478,10554,10649,10741,10827,10914,11015,11097,11180,11279,11383,11478,11579,11666,11777,11877,11983,12104,12186,12301,12405"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,230,387,463,550,672,819,1006,1163,1287,1390,1531,1592,1653,1749,1838,1911,2053,2160,2249,2407,2484,2580,2679,2826,2929,3045,3149,3225,3296,3433,3531,3755,3929,4050,4207,4317,4502,4625,4778,4896,5061,5171,5301,5400,5500,5634,5726,5859,5954,6126,6207,6342,6417,6531,6626,6705,6792,6874,6947,7028,7099,7230,7340,7503,7603,7738,7847,7990,8089,8189,8311,8400,8506,8619,8738,8841,8906,8960,9017,9071,9123,9174,9246,9301,9367,9420,9464,9539,9585,9641,9695,9751,9812,9861,9912,9982,10037,10089,10134,10199,10256,10320,10471,10591,10673,10734,10815,10884,10952,11087,11167,11242,11322,11400,11467,11564,11641,11739,11824,11970,12048,12140,12228,12336,12440,12520,12614,12714,12829,12935,13012,13110,13182,13266,13396,13551,13628,13705,13797,13888,15939,16054,16243,16290,16432,16606,16694,16844,16937,17081,17243,17387,17562,17633,17720,17797,17887,18013,18062,18144,18266,18358,18483,18621,18763,18882,18987,19050,19109,19168,19223,19289,19368,19468,19593,19663,19737,20025,20249,20383,20520,20667,20763,20897,21024,21182,21352,21449,21575,21665,21790,21913,22066,22157,22230,22297,22538,22629,22705,22770,22834,22959,23074,23173,23340,23531,23605,23672,23733,23912,23987,24081,24162,24260,24441,24541,24617,24749,24842,24990,25126,25183,25316,25386,25462,25526,25603,25651,25718,25811,25863,25935,26124,26255,26359,26422,26562,26715,26846,26967,27235,27347,27443,27538,27721,27942,28026,28212,28339,28501,28619,28700,28790,28870,29011,29184,29341,29391,29488,29622,29738,29820,29906,29998,30081,30158,30230,30293,30374,30442,30537,30639,30706,30776,30862,30930,31021,31217,31363,31452,31507,31587,31757,31817,31893,31971,32049,32136,32409,32464,32546,32626,32699,32801,32982,33068,33140,33224,33302,33374,33455,33544,33678,33767,33820,33916,33993,34075,34159,34269,34379,34498,34627,34716,34765,34817,34908,35007,35090,35210,35391,35498,35577,35698,35830,35972,36083,36187,36338,36468,36666,36857,36996,37144,37240,37331,37451,37585,37686,37819,37895,38365,38459,38550,38640", "endColumns": "174,156,75,86,121,146,186,156,123,102,140,60,60,95,88,72,141,106,88,157,76,95,98,146,102,115,103,75,70,136,97,223,173,120,156,109,184,122,152,117,164,109,129,98,99,133,91,132,94,171,80,134,74,113,94,78,86,81,72,80,70,130,109,162,99,134,108,142,98,99,121,88,105,112,118,102,64,53,56,53,51,50,71,54,65,52,43,74,45,55,53,55,60,48,50,69,54,51,44,64,56,63,150,119,81,60,80,68,67,134,79,74,79,77,66,96,76,97,84,145,77,91,87,107,103,79,93,99,114,105,76,97,71,83,129,154,76,76,91,90,2050,114,188,46,141,173,87,149,92,143,161,143,174,70,86,76,89,125,48,81,121,91,124,137,141,118,104,62,58,58,54,65,78,99,124,69,73,287,223,133,136,146,95,133,126,157,169,96,125,89,124,122,152,90,72,66,240,90,75,64,63,124,114,98,166,190,73,66,60,178,74,93,80,97,180,99,75,131,92,147,135,56,132,69,75,63,76,47,66,92,51,71,188,130,103,62,139,152,130,120,267,111,95,94,182,220,83,185,126,161,117,80,89,79,140,172,156,49,96,133,115,81,85,91,82,76,71,62,80,67,94,101,66,69,85,67,90,195,145,88,54,79,169,59,75,77,77,86,272,54,81,79,72,101,180,85,71,83,77,71,80,88,133,88,52,95,76,81,83,109,109,118,128,88,48,51,90,98,82,119,180,106,78,120,131,141,110,103,150,129,197,190,138,147,95,90,119,133,100,132,75,469,93,90,89,80", "endOffsets": "225,382,458,545,667,814,1001,1158,1282,1385,1526,1587,1648,1744,1833,1906,2048,2155,2244,2402,2479,2575,2674,2821,2924,3040,3144,3220,3291,3428,3526,3750,3924,4045,4202,4312,4497,4620,4773,4891,5056,5166,5296,5395,5495,5629,5721,5854,5949,6121,6202,6337,6412,6526,6621,6700,6787,6869,6942,7023,7094,7225,7335,7498,7598,7733,7842,7985,8084,8184,8306,8395,8501,8614,8733,8836,8901,8955,9012,9066,9118,9169,9241,9296,9362,9415,9459,9534,9580,9636,9690,9746,9807,9856,9907,9977,10032,10084,10129,10194,10251,10315,10466,10586,10668,10729,10810,10879,10947,11082,11162,11237,11317,11395,11462,11559,11636,11734,11819,11965,12043,12135,12223,12331,12435,12515,12609,12709,12824,12930,13007,13105,13177,13261,13391,13546,13623,13700,13792,13883,15934,16049,16238,16285,16427,16601,16689,16839,16932,17076,17238,17382,17557,17628,17715,17792,17882,18008,18057,18139,18261,18353,18478,18616,18758,18877,18982,19045,19104,19163,19218,19284,19363,19463,19588,19658,19732,20020,20244,20378,20515,20662,20758,20892,21019,21177,21347,21444,21570,21660,21785,21908,22061,22152,22225,22292,22533,22624,22700,22765,22829,22954,23069,23168,23335,23526,23600,23667,23728,23907,23982,24076,24157,24255,24436,24536,24612,24744,24837,24985,25121,25178,25311,25381,25457,25521,25598,25646,25713,25806,25858,25930,26119,26250,26354,26417,26557,26710,26841,26962,27230,27342,27438,27533,27716,27937,28021,28207,28334,28496,28614,28695,28785,28865,29006,29179,29336,29386,29483,29617,29733,29815,29901,29993,30076,30153,30225,30288,30369,30437,30532,30634,30701,30771,30857,30925,31016,31212,31358,31447,31502,31582,31752,31812,31888,31966,32044,32131,32404,32459,32541,32621,32694,32796,32977,33063,33135,33219,33297,33369,33450,33539,33673,33762,33815,33911,33988,34070,34154,34264,34374,34493,34622,34711,34760,34812,34903,35002,35085,35205,35386,35493,35572,35693,35825,35967,36078,36182,36333,36463,36661,36852,36991,37139,37235,37326,37446,37580,37681,37814,37890,38360,38454,38545,38635,38716"}, "to": {"startLines": "194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18257,18432,18589,18665,18752,18874,19021,19208,19365,19489,19592,19733,19794,19855,19951,20040,20113,20255,20362,20451,20609,20686,20782,20881,21028,21131,21247,21351,21427,21498,21635,21733,21957,22131,22252,22409,22519,22704,22827,22980,23098,23263,23373,23503,23602,23702,23836,23928,24061,24156,24328,24409,24544,24619,24733,24828,24907,24994,25076,25149,25230,25301,25432,25542,25705,25805,25940,26049,26192,26291,26391,26513,26602,26708,26821,26940,27043,27108,27162,27219,27273,27325,27376,27448,27503,27569,27622,27666,27741,27787,27843,27897,27953,28014,28063,28114,28184,28239,28291,28336,28401,28458,28522,28673,28793,28875,28936,29017,29086,29154,29289,29369,29444,29524,29602,29669,29766,29843,29941,30026,30172,30250,30342,30430,30538,30642,30722,30816,30916,31031,31137,31214,31312,31384,31468,31598,31753,31830,31907,31999,32090,34141,34256,34445,34492,34634,34808,34896,35046,35139,35283,35445,35589,35764,35835,35922,35999,36089,36215,36264,36346,36468,36560,36685,36823,36965,37084,37189,37252,37311,37370,37425,37491,37570,37670,37795,37865,37939,38227,38451,38585,38722,38869,38965,39099,39226,39384,39554,39651,39777,39867,39992,40115,40268,40359,40432,40499,40740,40831,40907,40972,41036,41161,41276,41375,41542,41733,41807,41874,41935,42114,42189,42283,42364,42462,42643,42743,42819,42951,43044,43192,43328,43385,43518,43588,43664,43728,43805,43853,43920,44013,44065,44137,44326,44457,44561,44624,44764,44917,45048,45169,45437,45549,45645,45740,45923,46144,46228,46414,46541,46703,46821,46902,46992,47072,47213,47386,47543,47593,47690,47824,47940,48022,48108,48200,48283,48360,48432,48495,48576,48644,48739,48841,48908,48978,49064,49132,49223,49419,49565,49654,49709,49789,49959,50019,50095,50173,50251,50338,50611,50666,50748,50828,50901,51003,51184,51270,51342,51426,51504,51576,51657,51746,51880,51969,52022,52118,52195,52277,52361,52471,52581,52700,52829,52918,52967,53019,53110,53209,53292,53412,53593,53700,53779,53900,54032,54174,54285,54389,54540,54670,54868,55059,55198,55346,55442,55533,55653,55787,55888,56021,56097,56567,56661,56752,56842", "endColumns": "174,156,75,86,121,146,186,156,123,102,140,60,60,95,88,72,141,106,88,157,76,95,98,146,102,115,103,75,70,136,97,223,173,120,156,109,184,122,152,117,164,109,129,98,99,133,91,132,94,171,80,134,74,113,94,78,86,81,72,80,70,130,109,162,99,134,108,142,98,99,121,88,105,112,118,102,64,53,56,53,51,50,71,54,65,52,43,74,45,55,53,55,60,48,50,69,54,51,44,64,56,63,150,119,81,60,80,68,67,134,79,74,79,77,66,96,76,97,84,145,77,91,87,107,103,79,93,99,114,105,76,97,71,83,129,154,76,76,91,90,2050,114,188,46,141,173,87,149,92,143,161,143,174,70,86,76,89,125,48,81,121,91,124,137,141,118,104,62,58,58,54,65,78,99,124,69,73,287,223,133,136,146,95,133,126,157,169,96,125,89,124,122,152,90,72,66,240,90,75,64,63,124,114,98,166,190,73,66,60,178,74,93,80,97,180,99,75,131,92,147,135,56,132,69,75,63,76,47,66,92,51,71,188,130,103,62,139,152,130,120,267,111,95,94,182,220,83,185,126,161,117,80,89,79,140,172,156,49,96,133,115,81,85,91,82,76,71,62,80,67,94,101,66,69,85,67,90,195,145,88,54,79,169,59,75,77,77,86,272,54,81,79,72,101,180,85,71,83,77,71,80,88,133,88,52,95,76,81,83,109,109,118,128,88,48,51,90,98,82,119,180,106,78,120,131,141,110,103,150,129,197,190,138,147,95,90,119,133,100,132,75,469,93,90,89,80", "endOffsets": "18427,18584,18660,18747,18869,19016,19203,19360,19484,19587,19728,19789,19850,19946,20035,20108,20250,20357,20446,20604,20681,20777,20876,21023,21126,21242,21346,21422,21493,21630,21728,21952,22126,22247,22404,22514,22699,22822,22975,23093,23258,23368,23498,23597,23697,23831,23923,24056,24151,24323,24404,24539,24614,24728,24823,24902,24989,25071,25144,25225,25296,25427,25537,25700,25800,25935,26044,26187,26286,26386,26508,26597,26703,26816,26935,27038,27103,27157,27214,27268,27320,27371,27443,27498,27564,27617,27661,27736,27782,27838,27892,27948,28009,28058,28109,28179,28234,28286,28331,28396,28453,28517,28668,28788,28870,28931,29012,29081,29149,29284,29364,29439,29519,29597,29664,29761,29838,29936,30021,30167,30245,30337,30425,30533,30637,30717,30811,30911,31026,31132,31209,31307,31379,31463,31593,31748,31825,31902,31994,32085,34136,34251,34440,34487,34629,34803,34891,35041,35134,35278,35440,35584,35759,35830,35917,35994,36084,36210,36259,36341,36463,36555,36680,36818,36960,37079,37184,37247,37306,37365,37420,37486,37565,37665,37790,37860,37934,38222,38446,38580,38717,38864,38960,39094,39221,39379,39549,39646,39772,39862,39987,40110,40263,40354,40427,40494,40735,40826,40902,40967,41031,41156,41271,41370,41537,41728,41802,41869,41930,42109,42184,42278,42359,42457,42638,42738,42814,42946,43039,43187,43323,43380,43513,43583,43659,43723,43800,43848,43915,44008,44060,44132,44321,44452,44556,44619,44759,44912,45043,45164,45432,45544,45640,45735,45918,46139,46223,46409,46536,46698,46816,46897,46987,47067,47208,47381,47538,47588,47685,47819,47935,48017,48103,48195,48278,48355,48427,48490,48571,48639,48734,48836,48903,48973,49059,49127,49218,49414,49560,49649,49704,49784,49954,50014,50090,50168,50246,50333,50606,50661,50743,50823,50896,50998,51179,51265,51337,51421,51499,51571,51652,51741,51875,51964,52017,52113,52190,52272,52356,52466,52576,52695,52824,52913,52962,53014,53105,53204,53287,53407,53588,53695,53774,53895,54027,54169,54280,54384,54535,54665,54863,55054,55193,55341,55437,55528,55648,55782,55883,56016,56092,56562,56656,56747,56837,56918"}}]}]}