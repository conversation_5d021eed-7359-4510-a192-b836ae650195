package com.thedasagroup.suminative.ui.local_orders;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\u001a\u0012\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0007\u001a&\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a@\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\r2\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00010\u000f2\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\n2\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a\u0010\u0010\u0013\u001a\u00020\u00012\u0006\u0010\u0014\u001a\u00020\u0010H\u0007\u00a8\u0006\u0015"}, d2 = {"LocalOrdersScreen", "", "viewModel", "Lcom/thedasagroup/suminative/ui/local_orders/LocalOrdersViewModel;", "FilterChip", "filter", "Lcom/thedasagroup/suminative/ui/local_orders/OrderFilter;", "isSelected", "", "onClick", "Lkotlin/Function0;", "OrderCard", "order", "Lcom/thedasagroup/suminative/database/OrderEntity;", "onStatusUpdate", "Lkotlin/Function1;", "", "onMarkComplete", "onSyncOrder", "OrderStatusChip", "status", "app_stagingGeneralDebug"})
public final class LocalOrdersScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void LocalOrdersScreen(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.local_orders.LocalOrdersViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void FilterChip(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.local_orders.OrderFilter filter, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void OrderCard(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.database.OrderEntity order, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onStatusUpdate, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onMarkComplete, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSyncOrder) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void OrderStatusChip(@org.jetbrains.annotations.NotNull()
    java.lang.String status) {
    }
}