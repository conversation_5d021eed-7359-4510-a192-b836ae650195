package com.thedasagroup.suminative.ui.guava_orders;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u00000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0003\u001a(\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u0007\u001a&\u0010\b\u001a\u00020\u00012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u0007\u001a \u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u000b2\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u0007\u001a\u0010\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u0010H\u0007\u001a\u0010\u0010\u0011\u001a\u00020\u00102\b\u0010\u0012\u001a\u0004\u0018\u00010\u0010\u00a8\u0006\u0013"}, d2 = {"GuavaOrdersScreen", "", "viewModel", "Lcom/thedasagroup/suminative/ui/guava_orders/GuavaOrdersViewModel;", "onBackPressed", "Lkotlin/Function0;", "activity", "Lcom/thedasagroup/suminative/ui/guava_orders/GuavaOrdersActivity;", "OrdersList", "orders", "", "Lcom/thedasagroup/suminative/data/model/response/my_guava/orders/list_of_orders/GuavaOrder;", "OrderCard", "order", "StatusChip", "status", "", "formatDate", "dateString", "app_stagingGeneralDebug"})
public final class GuavaOrdersScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void GuavaOrdersScreen(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackPressed, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersActivity activity) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void OrdersList(@org.jetbrains.annotations.NotNull()
    java.util.List<com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GuavaOrder> orders, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersViewModel viewModel, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersActivity activity) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void OrderCard(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GuavaOrder order, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersViewModel viewModel, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersActivity activity) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void StatusChip(@org.jetbrains.annotations.NotNull()
    java.lang.String status) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String formatDate(@org.jetbrains.annotations.Nullable()
    java.lang.String dateString) {
        return null;
    }
}