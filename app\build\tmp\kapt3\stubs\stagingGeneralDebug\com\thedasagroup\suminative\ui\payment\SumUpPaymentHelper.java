package com.thedasagroup.suminative.ui.payment;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0006\u0010\t\u001a\u00020\nJ\u000e\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eJ\u0016\u0010\u000f\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u0011J\b\u0010\u0012\u001a\u0004\u0018\u00010\bJ\u0006\u0010\u0013\u001a\u00020\fJ\u0006\u0010\u0014\u001a\u00020\fR\u000e\u0010\u0004\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0015"}, d2 = {"Lcom/thedasagroup/suminative/ui/payment/SumUpPaymentHelper;", "", "<init>", "()V", "REQUEST_CODE_SUMUP_LOGIN", "", "REQUEST_CODE_SUMUP_PAYMENT", "SUMUP_AFFILIATE_KEY", "", "isLoggedIn", "", "startLogin", "", "activity", "Landroid/app/Activity;", "startPayment", "order", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "getCurrentMerchantInfo", "logout", "prepareForCheckout", "app_stagingGeneralDebug"})
public final class SumUpPaymentHelper {
    public static final int REQUEST_CODE_SUMUP_LOGIN = 1001;
    public static final int REQUEST_CODE_SUMUP_PAYMENT = 1002;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String SUMUP_AFFILIATE_KEY = "sup_afk_GwzGZwAVI4oI7RITwJp7CxbfOmN3ze2x";
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.payment.SumUpPaymentHelper INSTANCE = null;
    
    private SumUpPaymentHelper() {
        super();
    }
    
    /**
     * Check if user is logged in to SumUp
     */
    public final boolean isLoggedIn() {
        return false;
    }
    
    /**
     * Start SumUp login flow
     */
    public final void startLogin(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity) {
    }
    
    /**
     * Start SumUp payment flow
     */
    public final void startPayment(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order) {
    }
    
    /**
     * Get current merchant info
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCurrentMerchantInfo() {
        return null;
    }
    
    /**
     * Logout from SumUp
     */
    public final void logout() {
    }
    
    /**
     * Prepare card terminal for checkout
     */
    public final void prepareForCheckout() {
    }
}