{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeStagingGeneralDebugResources-106:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,283,381,484,573,652,745,837,924,1010,1101,1178,1254,1334,1410,1492,1562", "endColumns": "95,81,97,102,88,78,92,91,86,85,90,76,75,79,75,81,69,120", "endOffsets": "196,278,376,479,568,647,740,832,919,1005,1096,1173,1249,1329,1405,1487,1557,1678"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,539,540,541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4542,4638,4987,5085,5188,5901,5980,17176,17268,17438,17524,17857,18011,18087,18167,56702,56784,56854", "endColumns": "95,81,97,102,88,78,92,91,86,85,90,76,75,79,75,81,69,120", "endOffsets": "4633,4715,5080,5183,5272,5975,6068,17263,17350,17519,17610,17929,18082,18162,18238,56779,56849,56970"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3503,3602,3704,3804,3902,4009,4115,18243", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3597,3699,3799,3897,4004,4110,4230,18339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "56975,57075", "endColumns": "99,101", "endOffsets": "57070,57172"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,430,543,651,736,837,965,1051,1132,1224,1318,1415,1509,1609,1703,1799,1895,1987,2079,2161,2268,2379,2478,2586,2694,2801,2960,17615", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "425,538,646,731,832,960,1046,1127,1219,1313,1410,1504,1604,1698,1794,1890,1982,2074,2156,2263,2374,2473,2581,2689,2796,2955,3054,17693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,418,539,639,739,856,999,1117,1265,1350,1457,1554,1656,1770,1888,2000,2138,2275,2419,2588,2724,2844,2966,3096,3194,3290,3411,3546,3649,3763,3878,4015,4156,4267,4372,4459,4555,4651,4738,4824,4935,5018,5102,5203,5309,5409,5512,5601,5712,5813,5922,6041,6124,6241", "endColumns": "120,118,122,120,99,99,116,142,117,147,84,106,96,101,113,117,111,137,136,143,168,135,119,121,129,97,95,120,134,102,113,114,136,140,110,104,86,95,95,86,85,110,82,83,100,105,99,102,88,110,100,108,118,82,116,108", "endOffsets": "171,290,413,534,634,734,851,994,1112,1260,1345,1452,1549,1651,1765,1883,1995,2133,2270,2414,2583,2719,2839,2961,3091,3189,3285,3406,3541,3644,3758,3873,4010,4151,4262,4367,4454,4550,4646,4733,4819,4930,5013,5097,5198,5304,5404,5507,5596,5707,5808,5917,6036,6119,6236,6345"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6136,6257,6376,6499,6620,6720,6820,6937,7080,7198,7346,7431,7538,7635,7737,7851,7969,8081,8219,8356,8500,8669,8805,8925,9047,9177,9275,9371,9492,9627,9730,9844,9959,10096,10237,10348,10453,10540,10636,10732,10819,10905,11016,11099,11183,11284,11390,11490,11593,11682,11793,11894,12003,12122,12205,12322", "endColumns": "120,118,122,120,99,99,116,142,117,147,84,106,96,101,113,117,111,137,136,143,168,135,119,121,129,97,95,120,134,102,113,114,136,140,110,104,86,95,95,86,85,110,82,83,100,105,99,102,88,110,100,108,118,82,116,108", "endOffsets": "6252,6371,6494,6615,6715,6815,6932,7075,7193,7341,7426,7533,7630,7732,7846,7964,8076,8214,8351,8495,8664,8800,8920,9042,9172,9270,9366,9487,9622,9725,9839,9954,10091,10232,10343,10448,10535,10631,10727,10814,10900,11011,11094,11178,11279,11385,11485,11588,11677,11788,11889,11998,12117,12200,12317,12426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,254,404,480,565,671,826,1012,1169,1296,1400,1540,1600,1659,1748,1855,1931,2076,2192,2275,2426,2503,2600,2701,2827,2935,3042,3139,3210,3283,3405,3501,3714,3896,3998,4144,4248,4442,4556,4700,4805,4945,5061,5197,5299,5402,5539,5642,5781,5891,6053,6135,6279,6355,6487,6591,6670,6748,6818,6889,6961,7032,7157,7265,7421,7518,7628,7734,7883,7985,8086,8222,8316,8423,8528,8642,8743,8807,8861,8916,8972,9021,9071,9139,9193,9262,9315,9359,9429,9478,9534,9590,9653,9708,9758,9808,9876,9926,9982,10027,10090,10148,10206,10341,10455,10533,10594,10679,10748,10816,10926,11012,11087,11167,11245,11312,11409,11484,11575,11663,11801,11880,11972,12054,12150,12254,12335,12431,12532,12640,12738,12815,12909,12978,13058,13178,13328,13404,13481,13580,13673,15724,15839,16049,16095,16275,16462,16550,16695,16795,16935,17119,17253,17438,17515,17598,17668,17760,17876,17921,18005,18118,18210,18335,18462,18600,18707,18820,18886,18946,19007,19063,19129,19212,19306,19424,19495,19574,19844,20085,20217,20348,20489,20582,20721,20849,21004,21180,21273,21373,21461,21580,21692,21850,21941,22021,22082,22340,22434,22520,22586,22651,22773,22906,23022,23189,23380,23457,23527,23592,23778,23858,23950,24026,24119,24287,24383,24467,24583,24676,24827,24946,25008,25155,25227,25298,25358,25434,25483,25548,25652,25704,25777,25976,26094,26190,26255,26419,26553,26684,26804,27078,27179,27276,27372,27556,27760,27856,28052,28169,28314,28422,28501,28590,28662,28834,29019,29157,29206,29290,29447,29577,29664,29745,29839,29919,29996,30072,30135,30216,30290,30367,30462,30530,30601,30685,30754,30842,31022,31179,31265,31319,31402,31547,31619,31713,31797,31878,31960,32223,32278,32353,32431,32512,32613,32795,32881,32955,33040,33119,33192,33289,33380,33518,33601,33654,33735,33812,33877,33955,34065,34168,34274,34396,34475,34524,34575,34670,34790,34876,34995,35162,35275,35360,35497,35627,35786,35912,36022,36156,36284,36478,36661,36792,36922,37010,37119,37249,37368,37477,37603,37676,38091,38170,38256,38336", "endColumns": "198,149,75,84,105,154,185,156,126,103,139,59,58,88,106,75,144,115,82,150,76,96,100,125,107,106,96,70,72,121,95,212,181,101,145,103,193,113,143,104,139,115,135,101,102,136,102,138,109,161,81,143,75,131,103,78,77,69,70,71,70,124,107,155,96,109,105,148,101,100,135,93,106,104,113,100,63,53,54,55,48,49,67,53,68,52,43,69,48,55,55,62,54,49,49,67,49,55,44,62,57,57,134,113,77,60,84,68,67,109,85,74,79,77,66,96,74,90,87,137,78,91,81,95,103,80,95,100,107,97,76,93,68,79,119,149,75,76,98,92,2050,114,209,45,179,186,87,144,99,139,183,133,184,76,82,69,91,115,44,83,112,91,124,126,137,106,112,65,59,60,55,65,82,93,117,70,78,269,240,131,130,140,92,138,127,154,175,92,99,87,118,111,157,90,79,60,257,93,85,65,64,121,132,115,166,190,76,69,64,185,79,91,75,92,167,95,83,115,92,150,118,61,146,71,70,59,75,48,64,103,51,72,198,117,95,64,163,133,130,119,273,100,96,95,183,203,95,195,116,144,107,78,88,71,171,184,137,48,83,156,129,86,80,93,79,76,75,62,80,73,76,94,67,70,83,68,87,179,156,85,53,82,144,71,93,83,80,81,262,54,74,77,80,100,181,85,73,84,78,72,96,90,137,82,52,80,76,64,77,109,102,105,121,78,48,50,94,119,85,118,166,112,84,136,129,158,125,109,133,127,193,182,130,129,87,108,129,118,108,125,72,414,78,85,79,76", "endOffsets": "249,399,475,560,666,821,1007,1164,1291,1395,1535,1595,1654,1743,1850,1926,2071,2187,2270,2421,2498,2595,2696,2822,2930,3037,3134,3205,3278,3400,3496,3709,3891,3993,4139,4243,4437,4551,4695,4800,4940,5056,5192,5294,5397,5534,5637,5776,5886,6048,6130,6274,6350,6482,6586,6665,6743,6813,6884,6956,7027,7152,7260,7416,7513,7623,7729,7878,7980,8081,8217,8311,8418,8523,8637,8738,8802,8856,8911,8967,9016,9066,9134,9188,9257,9310,9354,9424,9473,9529,9585,9648,9703,9753,9803,9871,9921,9977,10022,10085,10143,10201,10336,10450,10528,10589,10674,10743,10811,10921,11007,11082,11162,11240,11307,11404,11479,11570,11658,11796,11875,11967,12049,12145,12249,12330,12426,12527,12635,12733,12810,12904,12973,13053,13173,13323,13399,13476,13575,13668,15719,15834,16044,16090,16270,16457,16545,16690,16790,16930,17114,17248,17433,17510,17593,17663,17755,17871,17916,18000,18113,18205,18330,18457,18595,18702,18815,18881,18941,19002,19058,19124,19207,19301,19419,19490,19569,19839,20080,20212,20343,20484,20577,20716,20844,20999,21175,21268,21368,21456,21575,21687,21845,21936,22016,22077,22335,22429,22515,22581,22646,22768,22901,23017,23184,23375,23452,23522,23587,23773,23853,23945,24021,24114,24282,24378,24462,24578,24671,24822,24941,25003,25150,25222,25293,25353,25429,25478,25543,25647,25699,25772,25971,26089,26185,26250,26414,26548,26679,26799,27073,27174,27271,27367,27551,27755,27851,28047,28164,28309,28417,28496,28585,28657,28829,29014,29152,29201,29285,29442,29572,29659,29740,29834,29914,29991,30067,30130,30211,30285,30362,30457,30525,30596,30680,30749,30837,31017,31174,31260,31314,31397,31542,31614,31708,31792,31873,31955,32218,32273,32348,32426,32507,32608,32790,32876,32950,33035,33114,33187,33284,33375,33513,33596,33649,33730,33807,33872,33950,34060,34163,34269,34391,34470,34519,34570,34665,34785,34871,34990,35157,35270,35355,35492,35622,35781,35907,36017,36151,36279,36473,36656,36787,36917,37005,37114,37244,37363,37472,37598,37671,38086,38165,38251,38331,38408"}, "to": {"startLines": "193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18344,18543,18693,18769,18854,18960,19115,19301,19458,19585,19689,19829,19889,19948,20037,20144,20220,20365,20481,20564,20715,20792,20889,20990,21116,21224,21331,21428,21499,21572,21694,21790,22003,22185,22287,22433,22537,22731,22845,22989,23094,23234,23350,23486,23588,23691,23828,23931,24070,24180,24342,24424,24568,24644,24776,24880,24959,25037,25107,25178,25250,25321,25446,25554,25710,25807,25917,26023,26172,26274,26375,26511,26605,26712,26817,26931,27032,27096,27150,27205,27261,27310,27360,27428,27482,27551,27604,27648,27718,27767,27823,27879,27942,27997,28047,28097,28165,28215,28271,28316,28379,28437,28495,28630,28744,28822,28883,28968,29037,29105,29215,29301,29376,29456,29534,29601,29698,29773,29864,29952,30090,30169,30261,30343,30439,30543,30624,30720,30821,30929,31027,31104,31198,31267,31347,31467,31617,31693,31770,31869,31962,34013,34128,34338,34384,34564,34751,34839,34984,35084,35224,35408,35542,35727,35804,35887,35957,36049,36165,36210,36294,36407,36499,36624,36751,36889,36996,37109,37175,37235,37296,37352,37418,37501,37595,37713,37784,37863,38133,38374,38506,38637,38778,38871,39010,39138,39293,39469,39562,39662,39750,39869,39981,40139,40230,40310,40371,40629,40723,40809,40875,40940,41062,41195,41311,41478,41669,41746,41816,41881,42067,42147,42239,42315,42408,42576,42672,42756,42872,42965,43116,43235,43297,43444,43516,43587,43647,43723,43772,43837,43941,43993,44066,44265,44383,44479,44544,44708,44842,44973,45093,45367,45468,45565,45661,45845,46049,46145,46341,46458,46603,46711,46790,46879,46951,47123,47308,47446,47495,47579,47736,47866,47953,48034,48128,48208,48285,48361,48424,48505,48579,48656,48751,48819,48890,48974,49043,49131,49311,49468,49554,49608,49691,49836,49908,50002,50086,50167,50249,50512,50567,50642,50720,50801,50902,51084,51170,51244,51329,51408,51481,51578,51669,51807,51890,51943,52024,52101,52166,52244,52354,52457,52563,52685,52764,52813,52864,52959,53079,53165,53284,53451,53564,53649,53786,53916,54075,54201,54311,54445,54573,54767,54950,55081,55211,55299,55408,55538,55657,55766,55892,55965,56380,56459,56545,56625", "endColumns": "198,149,75,84,105,154,185,156,126,103,139,59,58,88,106,75,144,115,82,150,76,96,100,125,107,106,96,70,72,121,95,212,181,101,145,103,193,113,143,104,139,115,135,101,102,136,102,138,109,161,81,143,75,131,103,78,77,69,70,71,70,124,107,155,96,109,105,148,101,100,135,93,106,104,113,100,63,53,54,55,48,49,67,53,68,52,43,69,48,55,55,62,54,49,49,67,49,55,44,62,57,57,134,113,77,60,84,68,67,109,85,74,79,77,66,96,74,90,87,137,78,91,81,95,103,80,95,100,107,97,76,93,68,79,119,149,75,76,98,92,2050,114,209,45,179,186,87,144,99,139,183,133,184,76,82,69,91,115,44,83,112,91,124,126,137,106,112,65,59,60,55,65,82,93,117,70,78,269,240,131,130,140,92,138,127,154,175,92,99,87,118,111,157,90,79,60,257,93,85,65,64,121,132,115,166,190,76,69,64,185,79,91,75,92,167,95,83,115,92,150,118,61,146,71,70,59,75,48,64,103,51,72,198,117,95,64,163,133,130,119,273,100,96,95,183,203,95,195,116,144,107,78,88,71,171,184,137,48,83,156,129,86,80,93,79,76,75,62,80,73,76,94,67,70,83,68,87,179,156,85,53,82,144,71,93,83,80,81,262,54,74,77,80,100,181,85,73,84,78,72,96,90,137,82,52,80,76,64,77,109,102,105,121,78,48,50,94,119,85,118,166,112,84,136,129,158,125,109,133,127,193,182,130,129,87,108,129,118,108,125,72,414,78,85,79,76", "endOffsets": "18538,18688,18764,18849,18955,19110,19296,19453,19580,19684,19824,19884,19943,20032,20139,20215,20360,20476,20559,20710,20787,20884,20985,21111,21219,21326,21423,21494,21567,21689,21785,21998,22180,22282,22428,22532,22726,22840,22984,23089,23229,23345,23481,23583,23686,23823,23926,24065,24175,24337,24419,24563,24639,24771,24875,24954,25032,25102,25173,25245,25316,25441,25549,25705,25802,25912,26018,26167,26269,26370,26506,26600,26707,26812,26926,27027,27091,27145,27200,27256,27305,27355,27423,27477,27546,27599,27643,27713,27762,27818,27874,27937,27992,28042,28092,28160,28210,28266,28311,28374,28432,28490,28625,28739,28817,28878,28963,29032,29100,29210,29296,29371,29451,29529,29596,29693,29768,29859,29947,30085,30164,30256,30338,30434,30538,30619,30715,30816,30924,31022,31099,31193,31262,31342,31462,31612,31688,31765,31864,31957,34008,34123,34333,34379,34559,34746,34834,34979,35079,35219,35403,35537,35722,35799,35882,35952,36044,36160,36205,36289,36402,36494,36619,36746,36884,36991,37104,37170,37230,37291,37347,37413,37496,37590,37708,37779,37858,38128,38369,38501,38632,38773,38866,39005,39133,39288,39464,39557,39657,39745,39864,39976,40134,40225,40305,40366,40624,40718,40804,40870,40935,41057,41190,41306,41473,41664,41741,41811,41876,42062,42142,42234,42310,42403,42571,42667,42751,42867,42960,43111,43230,43292,43439,43511,43582,43642,43718,43767,43832,43936,43988,44061,44260,44378,44474,44539,44703,44837,44968,45088,45362,45463,45560,45656,45840,46044,46140,46336,46453,46598,46706,46785,46874,46946,47118,47303,47441,47490,47574,47731,47861,47948,48029,48123,48203,48280,48356,48419,48500,48574,48651,48746,48814,48885,48969,49038,49126,49306,49463,49549,49603,49686,49831,49903,49997,50081,50162,50244,50507,50562,50637,50715,50796,50897,51079,51165,51239,51324,51403,51476,51573,51664,51802,51885,51938,52019,52096,52161,52239,52349,52452,52558,52680,52759,52808,52859,52954,53074,53160,53279,53446,53559,53644,53781,53911,54070,54196,54306,54440,54568,54762,54945,55076,55206,55294,55403,55533,55652,55761,55887,55960,56375,56454,56540,56620,56697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,263,378", "endColumns": "106,100,114,104", "endOffsets": "157,258,373,478"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4880,5499,5600,5715", "endColumns": "106,100,114,104", "endOffsets": "4982,5595,5710,5815"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,626,722,826,948,1029,1091,1156,1251,1332,1395,1484,1548,1617,1680,1754,1818,1875,1993,2051,2113,2170,2250,2389,2478,2554,2649,2730,2812,2953,3034,3114,3265,3355,3435,3491,3547,3613,3692,3774,3845,3934,4008,4085,4155,4234,4334,4418,4502,4594,4694,4768,4849,4951,5004,5089,5156,5249,5338,5400,5464,5527,5595,5708,5815,5919,6020,6080,6140,6223,6306,6382", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,78,86,100,95,103,121,80,61,64,94,80,62,88,63,68,62,73,63,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82,82,75,76", "endOffsets": "273,354,433,520,621,717,821,943,1024,1086,1151,1246,1327,1390,1479,1543,1612,1675,1749,1813,1870,1988,2046,2108,2165,2245,2384,2473,2549,2644,2725,2807,2948,3029,3109,3260,3350,3430,3486,3542,3608,3687,3769,3840,3929,4003,4080,4150,4229,4329,4413,4497,4589,4689,4763,4844,4946,4999,5084,5151,5244,5333,5395,5459,5522,5590,5703,5810,5914,6015,6075,6135,6218,6301,6377,6454"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3059,3140,3219,3306,3407,4235,4339,4461,5277,5339,5404,5820,6073,12431,12520,12584,12653,12716,12790,12854,12911,13029,13087,13149,13206,13286,13425,13514,13590,13685,13766,13848,13989,14070,14150,14301,14391,14471,14527,14583,14649,14728,14810,14881,14970,15044,15121,15191,15270,15370,15454,15538,15630,15730,15804,15885,15987,16040,16125,16192,16285,16374,16436,16500,16563,16631,16744,16851,16955,17056,17116,17355,17698,17781,17934", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,80,78,86,100,95,103,121,80,61,64,94,80,62,88,63,68,62,73,63,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82,82,75,76", "endOffsets": "323,3135,3214,3301,3402,3498,4334,4456,4537,5334,5399,5494,5896,6131,12515,12579,12648,12711,12785,12849,12906,13024,13082,13144,13201,13281,13420,13509,13585,13680,13761,13843,13984,14065,14145,14296,14386,14466,14522,14578,14644,14723,14805,14876,14965,15039,15116,15186,15265,15365,15449,15533,15625,15725,15799,15880,15982,16035,16120,16187,16280,16369,16431,16495,16558,16626,16739,16846,16950,17051,17111,17171,17433,17776,17852,18006"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-es\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4720", "endColumns": "159", "endOffsets": "4875"}}]}]}