package com.thedasagroup.suminative.ui;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u00d4\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010$\n\u0002\u0010\b\n\u0002\b\u0018\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u000e\u0018\u00002\u00020\u00012\u00020\u0002B\u0007\u00a2\u0006\u0004\b\u0003\u0010\u0004J\u000e\u0010_\u001a\u00020`2\u0006\u0010a\u001a\u00020]J\b\u0010b\u001a\u00020`H\u0002J\b\u0010c\u001a\u00020`H\u0002J\u0012\u0010r\u001a\u00020`2\b\u0010s\u001a\u0004\u0018\u00010tH\u0015J\b\u0010u\u001a\u00020`H\u0014J\u000e\u0010v\u001a\u00020`H\u0082@\u00a2\u0006\u0002\u0010wJ\u0007\u0010\u0086\u0001\u001a\u00020`J\u0007\u0010\u0087\u0001\u001a\u00020`J\u0007\u0010\u0088\u0001\u001a\u00020`J,\u0010\u0089\u0001\u001a\u00020`2\u0007\u0010\u008a\u0001\u001a\u00020\u00062\u0007\u0010\u008b\u0001\u001a\u00020\u00062\u0007\u0010\u008c\u0001\u001a\u00020\u00062\b\b\u0002\u0010Q\u001a\u000208J\u0019\u0010\u008d\u0001\u001a\u00020`2\u0007\u0010\u008a\u0001\u001a\u00020\u00062\u0007\u0010\u008b\u0001\u001a\u00020\u0006J\t\u0010\u008e\u0001\u001a\u00020`H\u0016J\t\u0010\u008f\u0001\u001a\u00020`H\u0014J\u0013\u0010\u0090\u0001\u001a\u00020`2\b\u0010\u0091\u0001\u001a\u00030\u0092\u0001H\u0007J\u0012\u0010\u0093\u0001\u001a\u00020`2\u0007\u0010\u0094\u0001\u001a\u00020\u000eH\u0007J\u0007\u0010\u0095\u0001\u001a\u00020`J\u0007\u0010\u0096\u0001\u001a\u00020`J\t\u0010\u0097\u0001\u001a\u00020`H\u0002J&\u0010\u0098\u0001\u001a\u00020`2\u0007\u0010\u0099\u0001\u001a\u00020D2\u0007\u0010\u009a\u0001\u001a\u00020D2\t\u0010\u009b\u0001\u001a\u0004\u0018\u00010pH\u0014J\u0014\u0010\u009c\u0001\u001a\u00020`2\t\u0010\u009b\u0001\u001a\u0004\u0018\u00010pH\u0002J\u0014\u0010\u009d\u0001\u001a\u00020`2\t\u0010\u009b\u0001\u001a\u0004\u0018\u00010pH\u0002J\u0012\u0010\u009e\u0001\u001a\u00020`2\u0007\u0010\u009f\u0001\u001a\u00020]H\u0002R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0007\u001a\u00020\b8FX\u0086\u0084\u0002\u00a2\u0006\f\n\u0004\b\u000b\u0010\f\u001a\u0004\b\t\u0010\nR\u001b\u0010\r\u001a\u00020\u000e8FX\u0086\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0011\u0010\f\u001a\u0004\b\u000f\u0010\u0010R\u001b\u0010\u0012\u001a\u00020\u00138FX\u0086\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0016\u0010\f\u001a\u0004\b\u0014\u0010\u0015R\u001b\u0010\u0017\u001a\u00020\u00188FX\u0086\u0084\u0002\u00a2\u0006\f\n\u0004\b\u001b\u0010\f\u001a\u0004\b\u0019\u0010\u001aR\u001b\u0010\u001c\u001a\u00020\u001d8FX\u0086\u0084\u0002\u00a2\u0006\f\n\u0004\b \u0010\f\u001a\u0004\b\u001e\u0010\u001fR\u001b\u0010!\u001a\u00020\"8FX\u0086\u0084\u0002\u00a2\u0006\f\n\u0004\b%\u0010\f\u001a\u0004\b#\u0010$R\u001b\u0010&\u001a\u00020\'8FX\u0086\u0084\u0002\u00a2\u0006\f\n\u0004\b*\u0010\f\u001a\u0004\b(\u0010)RV\u0010+\u001aJ\u0012 \u0012\u001e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020.0-j\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020.`/0,j$\u0012 \u0012\u001e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020.0-j\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020.`/`0X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u00101\u001a\u0004\u0018\u000102X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b3\u00104\"\u0004\b5\u00106R\u001a\u00107\u001a\u000208X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b7\u00109\"\u0004\b:\u0010;R\u001c\u0010<\u001a\u0004\u0018\u00010=X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b>\u0010?\"\u0004\b@\u0010AR(\u0010B\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010D\u0012\u0004\u0012\u00020D0CX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bE\u0010F\"\u0004\bG\u0010HR\u001c\u0010I\u001a\u0004\u0018\u00010\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bJ\u0010K\"\u0004\bL\u0010MR\u001c\u0010N\u001a\u0004\u0018\u00010\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bO\u0010K\"\u0004\bP\u0010MR\u001a\u0010Q\u001a\u000208X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bQ\u00109\"\u0004\bR\u0010;R\u001c\u0010S\u001a\u0004\u0018\u00010\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bT\u0010K\"\u0004\bU\u0010MR\u001c\u0010V\u001a\u0004\u0018\u00010\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bW\u0010K\"\u0004\bX\u0010MR\u001a\u0010Y\u001a\u00020\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bZ\u0010K\"\u0004\b[\u0010MR\u0010\u0010\\\u001a\u0004\u0018\u00010]X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010^\u001a\u0004\u0018\u000102X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001b\u0010d\u001a\u00020e8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\bh\u0010\f\u001a\u0004\bf\u0010gR\u001b\u0010i\u001a\u00020j8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\bm\u0010\f\u001a\u0004\bk\u0010lR\u0014\u0010n\u001a\b\u0012\u0004\u0012\u00020p0oX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010q\u001a\b\u0012\u0004\u0012\u00020p0oX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010x\u001a\u000e\u0012\u0004\u0012\u00020`\u0012\u0004\u0012\u00020`0y\u00a2\u0006\b\n\u0000\u001a\u0004\bz\u0010{R\u001d\u0010|\u001a\u000e\u0012\u0004\u0012\u00020`\u0012\u0004\u0012\u00020`0y\u00a2\u0006\b\n\u0000\u001a\u0004\b}\u0010{R\u001d\u0010~\u001a\u000e\u0012\u0004\u0012\u00020`\u0012\u0004\u0012\u00020`0y\u00a2\u0006\b\n\u0000\u001a\u0004\b\u007f\u0010{R\u001f\u0010\u0080\u0001\u001a\u000e\u0012\u0004\u0012\u00020`\u0012\u0004\u0012\u00020`0y\u00a2\u0006\t\n\u0000\u001a\u0005\b\u0081\u0001\u0010{R\u001f\u0010\u0082\u0001\u001a\u000e\u0012\u0004\u0012\u00020`\u0012\u0004\u0012\u00020`0y\u00a2\u0006\t\n\u0000\u001a\u0005\b\u0083\u0001\u0010{R\u0010\u0010\u0084\u0001\u001a\u00030\u0085\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u00a0\u0001"}, d2 = {"Lcom/thedasagroup/suminative/ui/MainActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "Lcom/airbnb/mvrx/MavericksView;", "<init>", "()V", "TAG", "", "loginViewModel", "Lcom/thedasagroup/suminative/ui/login/LoginScreenViewModel;", "getLoginViewModel", "()Lcom/thedasagroup/suminative/ui/login/LoginScreenViewModel;", "loginViewModel$delegate", "Lkotlin/Lazy;", "orderScreenViewModel", "Lcom/thedasagroup/suminative/ui/orders/OrderScreenViewModel;", "getOrderScreenViewModel", "()Lcom/thedasagroup/suminative/ui/orders/OrderScreenViewModel;", "orderScreenViewModel$delegate", "reservationsViewModel", "Lcom/thedasagroup/suminative/ui/reservations/ReservationsViewModel;", "getReservationsViewModel", "()Lcom/thedasagroup/suminative/ui/reservations/ReservationsViewModel;", "reservationsViewModel$delegate", "productsScreenViewModel", "Lcom/thedasagroup/suminative/ui/products/ProductsScreenViewModel;", "getProductsScreenViewModel", "()Lcom/thedasagroup/suminative/ui/products/ProductsScreenViewModel;", "productsScreenViewModel$delegate", "stockScreenViewModel", "Lcom/thedasagroup/suminative/ui/stock/StockScreenViewModel;", "getStockScreenViewModel", "()Lcom/thedasagroup/suminative/ui/stock/StockScreenViewModel;", "stockScreenViewModel$delegate", "paymentViewModel", "Lcom/thedasagroup/suminative/ui/payment/PaymentViewModel;", "getPaymentViewModel", "()Lcom/thedasagroup/suminative/ui/payment/PaymentViewModel;", "paymentViewModel$delegate", "commonViewModel", "Lcom/thedasagroup/suminative/ui/common/CommonViewModel;", "getCommonViewModel", "()Lcom/thedasagroup/suminative/ui/common/CommonViewModel;", "commonViewModel$delegate", "AL", "Ljava/util/ArrayList;", "Ljava/util/HashMap;", "", "Lkotlin/collections/HashMap;", "Lkotlin/collections/ArrayList;", "dialog", "Lcom/afollestad/materialdialogs/MaterialDialog;", "getDialog", "()Lcom/afollestad/materialdialogs/MaterialDialog;", "setDialog", "(Lcom/afollestad/materialdialogs/MaterialDialog;)V", "isShowAllOrders", "", "()Z", "setShowAllOrders", "(Z)V", "ordersResponse", "Lcom/thedasagroup/suminative/data/model/request/pagination/OrderResponse;", "getOrdersResponse", "()Lcom/thedasagroup/suminative/data/model/request/pagination/OrderResponse;", "setOrdersResponse", "(Lcom/thedasagroup/suminative/data/model/request/pagination/OrderResponse;)V", "mapCount", "", "", "getMapCount", "()Ljava/util/Map;", "setMapCount", "(Ljava/util/Map;)V", "dialogType", "getDialogType", "()Ljava/lang/String;", "setDialogType", "(Ljava/lang/String;)V", "dialogMessage", "getDialogMessage", "setDialogMessage", "isScheduleOrder", "setScheduleOrder", "showDialogProp", "getShowDialogProp", "setShowDialogProp", "dialogTitle", "getDialogTitle", "setDialogTitle", "currentRouteId", "getCurrentRouteId", "setCurrentRouteId", "currentSumUpOrder", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "processingDialog", "startSumUpPayment", "", "order", "showProcessingDialog", "hideProcessingDialog", "printerViewModel", "Lcom/thedasagroup/suminative/ui/printer/PrinterViewModel;", "getPrinterViewModel", "()Lcom/thedasagroup/suminative/ui/printer/PrinterViewModel;", "printerViewModel$delegate", "lcdViewModel", "Lcom/thedasagroup/suminative/ui/lcd/LcdViewModel;", "getLcdViewModel", "()Lcom/thedasagroup/suminative/ui/lcd/LcdViewModel;", "lcdViewModel$delegate", "cashPaymentLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "Landroid/content/Intent;", "cardPaymentLauncher", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onResume", "checkStoreClosed", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "functionSilentOrders", "Lkotlin/Function1;", "getFunctionSilentOrders", "()Lkotlin/jvm/functions/Function1;", "functionOpenCloseStore", "getFunctionOpenCloseStore", "function", "getFunction", "functionNewOrders", "getFunctionNewOrders", "functionOtherOrders", "getFunctionOtherOrders", "mMessageReceiver", "Landroid/content/BroadcastReceiver;", "updateOrderCountMinutes", "showDialogWhilePlaying", "dismissDialog", "showDialog", "message", "title", "type", "showAutoDismissDialog", "invalidate", "onDestroy", "ScheduleOrdersScreen", "innerPadding", "Landroidx/compose/foundation/layout/PaddingValues;", "MyTopAppBar", "viewModel", "callOrders", "callOrdersSilent", "initPrinter", "onActivityResult", "requestCode", "resultCode", "data", "handleSumUpLoginResult", "handleSumUpPaymentResult", "handleCashPaymentCompleteGuava", "finalOrder", "app_stagingGeneralDebug"})
public final class MainActivity extends androidx.appcompat.app.AppCompatActivity implements com.airbnb.mvrx.MavericksView {
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String TAG = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy loginViewModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy orderScreenViewModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy reservationsViewModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy productsScreenViewModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy stockScreenViewModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy paymentViewModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy commonViewModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private java.util.ArrayList<java.util.HashMap<java.lang.String, java.lang.Object>> AL;
    @org.jetbrains.annotations.Nullable()
    private com.afollestad.materialdialogs.MaterialDialog dialog;
    private boolean isShowAllOrders = false;
    @org.jetbrains.annotations.Nullable()
    private com.thedasagroup.suminative.data.model.request.pagination.OrderResponse ordersResponse;
    @org.jetbrains.annotations.NotNull()
    private java.util.Map<java.lang.Integer, java.lang.Integer> mapCount;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String dialogType;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String dialogMessage;
    private boolean isScheduleOrder = false;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String showDialogProp;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String dialogTitle;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String currentRouteId = "0";
    @org.jetbrains.annotations.Nullable()
    private com.thedasagroup.suminative.data.model.request.order.Order currentSumUpOrder;
    @org.jetbrains.annotations.Nullable()
    private com.afollestad.materialdialogs.MaterialDialog processingDialog;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy printerViewModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy lcdViewModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.activity.result.ActivityResultLauncher<android.content.Intent> cashPaymentLauncher = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.activity.result.ActivityResultLauncher<android.content.Intent> cardPaymentLauncher = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<kotlin.Unit, kotlin.Unit> functionSilentOrders = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<kotlin.Unit, kotlin.Unit> functionOpenCloseStore = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<kotlin.Unit, kotlin.Unit> function = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<kotlin.Unit, kotlin.Unit> functionNewOrders = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<kotlin.Unit, kotlin.Unit> functionOtherOrders = null;
    @org.jetbrains.annotations.NotNull()
    private final android.content.BroadcastReceiver mMessageReceiver = null;
    
    public MainActivity() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.login.LoginScreenViewModel getLoginViewModel() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.orders.OrderScreenViewModel getOrderScreenViewModel() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.reservations.ReservationsViewModel getReservationsViewModel() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.products.ProductsScreenViewModel getProductsScreenViewModel() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.stock.StockScreenViewModel getStockScreenViewModel() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.payment.PaymentViewModel getPaymentViewModel() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.common.CommonViewModel getCommonViewModel() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.afollestad.materialdialogs.MaterialDialog getDialog() {
        return null;
    }
    
    public final void setDialog(@org.jetbrains.annotations.Nullable()
    com.afollestad.materialdialogs.MaterialDialog p0) {
    }
    
    public final boolean isShowAllOrders() {
        return false;
    }
    
    public final void setShowAllOrders(boolean p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.request.pagination.OrderResponse getOrdersResponse() {
        return null;
    }
    
    public final void setOrdersResponse(@org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.request.pagination.OrderResponse p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.Integer, java.lang.Integer> getMapCount() {
        return null;
    }
    
    public final void setMapCount(@org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.Integer, java.lang.Integer> p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getDialogType() {
        return null;
    }
    
    public final void setDialogType(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getDialogMessage() {
        return null;
    }
    
    public final void setDialogMessage(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    public final boolean isScheduleOrder() {
        return false;
    }
    
    public final void setScheduleOrder(boolean p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getShowDialogProp() {
        return null;
    }
    
    public final void setShowDialogProp(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getDialogTitle() {
        return null;
    }
    
    public final void setDialogTitle(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCurrentRouteId() {
        return null;
    }
    
    public final void setCurrentRouteId(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public final void startSumUpPayment(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order) {
    }
    
    private final void showProcessingDialog() {
    }
    
    private final void hideProcessingDialog() {
    }
    
    private final com.thedasagroup.suminative.ui.printer.PrinterViewModel getPrinterViewModel() {
        return null;
    }
    
    private final com.thedasagroup.suminative.ui.lcd.LcdViewModel getLcdViewModel() {
        return null;
    }
    
    @java.lang.Override()
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @android.annotation.SuppressLint(value = {"CheckResult"})
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    protected void onResume() {
    }
    
    private final java.lang.Object checkStoreClosed(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlin.jvm.functions.Function1<kotlin.Unit, kotlin.Unit> getFunctionSilentOrders() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlin.jvm.functions.Function1<kotlin.Unit, kotlin.Unit> getFunctionOpenCloseStore() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlin.jvm.functions.Function1<kotlin.Unit, kotlin.Unit> getFunction() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlin.jvm.functions.Function1<kotlin.Unit, kotlin.Unit> getFunctionNewOrders() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlin.jvm.functions.Function1<kotlin.Unit, kotlin.Unit> getFunctionOtherOrders() {
        return null;
    }
    
    public final void updateOrderCountMinutes() {
    }
    
    public final void showDialogWhilePlaying() {
    }
    
    public final void dismissDialog() {
    }
    
    public final void showDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String message, @org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String type, boolean isScheduleOrder) {
    }
    
    public final void showAutoDismissDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String message, @org.jetbrains.annotations.NotNull()
    java.lang.String title) {
    }
    
    @java.lang.Override()
    public void invalidate() {
    }
    
    @java.lang.Override()
    protected void onDestroy() {
    }
    
    @androidx.compose.runtime.Composable()
    public final void ScheduleOrdersScreen(@org.jetbrains.annotations.NotNull()
    androidx.compose.foundation.layout.PaddingValues innerPadding) {
    }
    
    @android.annotation.SuppressLint(value = {"CheckResult"})
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public final void MyTopAppBar(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.orders.OrderScreenViewModel viewModel) {
    }
    
    public final void callOrders() {
    }
    
    public final void callOrdersSilent() {
    }
    
    private final void initPrinter() {
    }
    
    @java.lang.Override()
    protected void onActivityResult(int requestCode, int resultCode, @org.jetbrains.annotations.Nullable()
    android.content.Intent data) {
    }
    
    private final void handleSumUpLoginResult(android.content.Intent data) {
    }
    
    private final void handleSumUpPaymentResult(android.content.Intent data) {
    }
    
    private final void handleCashPaymentCompleteGuava(com.thedasagroup.suminative.data.model.request.order.Order finalOrder) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <T extends java.lang.Object>kotlinx.coroutines.Job collectLatest(@org.jetbrains.annotations.NotNull()
    kotlinx.coroutines.flow.Flow<? extends T> $this$collectLatest, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super T, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.airbnb.mvrx.MavericksViewInternalViewModel getMavericksViewInternalViewModel() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String getMvrxViewId() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public androidx.lifecycle.LifecycleOwner getSubscriptionLifecycleOwner() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, T extends java.lang.Object>kotlinx.coroutines.Job onAsync(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onAsync, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends com.airbnb.mvrx.Async<? extends T>> asyncProp, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super java.lang.Throwable, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> onFail, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super T, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> onSuccess) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super S, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super A, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super A, ? super B, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function4<? super A, ? super B, ? super C, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function5<? super A, ? super B, ? super C, ? super D, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object, E extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends E> prop5, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function6<? super A, ? super B, ? super C, ? super D, ? super E, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object, E extends java.lang.Object, F extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends E> prop5, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends F> prop6, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function7<? super A, ? super B, ? super C, ? super D, ? super E, ? super F, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object, E extends java.lang.Object, F extends java.lang.Object, G extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends E> prop5, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends F> prop6, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends G> prop7, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function8<? super A, ? super B, ? super C, ? super D, ? super E, ? super F, ? super G, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    public void postInvalidate() {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.airbnb.mvrx.UniqueOnly uniqueOnly(@org.jetbrains.annotations.Nullable()
    java.lang.String customId) {
        return null;
    }
}