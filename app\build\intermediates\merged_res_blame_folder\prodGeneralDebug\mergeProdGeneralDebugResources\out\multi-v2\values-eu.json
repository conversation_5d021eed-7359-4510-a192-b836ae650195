{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,382,487,567,674,774,872,987,1070,1136,1203,1302,1370,1431,1519,1582,1648,1712,1783,1846,1900,2009,2068,2131,2185,2259,2384,2474,2552,2641,2724,2804,2949,3032,3114,3252,3343,3426,3478,3531,3597,3668,3748,3819,3899,3977,4055,4128,4203,4310,4397,4484,4575,4668,4740,4816,4908,4959,5041,5107,5191,5277,5339,5403,5466,5534,5641,5750,5846,5951,6007,6064,6147,6232,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,106,104,79,106,99,97,114,82,65,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,77,88,82,79,144,82,81,137,90,82,51,52,65,70,79,70,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,55,56,82,84,76,76", "endOffsets": "270,377,482,562,669,769,867,982,1065,1131,1198,1297,1365,1426,1514,1577,1643,1707,1778,1841,1895,2004,2063,2126,2180,2254,2379,2469,2547,2636,2719,2799,2944,3027,3109,3247,3338,3421,3473,3526,3592,3663,3743,3814,3894,3972,4050,4123,4198,4305,4392,4479,4570,4663,4735,4811,4903,4954,5036,5102,5186,5272,5334,5398,5461,5529,5636,5745,5841,5946,6002,6059,6142,6227,6304,6381"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3177,3282,3362,3469,4300,4398,4513,5308,5374,5441,5860,6091,12480,12568,12631,12697,12761,12832,12895,12949,13058,13117,13180,13234,13308,13433,13523,13601,13690,13773,13853,13998,14081,14163,14301,14392,14475,14527,14580,14646,14717,14797,14868,14948,15026,15104,15177,15252,15359,15446,15533,15624,15717,15789,15865,15957,16008,16090,16156,16240,16326,16388,16452,16515,16583,16690,16799,16895,17000,17056,17286,17630,17715,17866", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,106,104,79,106,99,97,114,82,65,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,77,88,82,79,144,82,81,137,90,82,51,52,65,70,79,70,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,55,56,82,84,76,76", "endOffsets": "320,3172,3277,3357,3464,3564,4393,4508,4591,5369,5436,5535,5923,6147,12563,12626,12692,12756,12827,12890,12944,13053,13112,13175,13229,13303,13428,13518,13596,13685,13768,13848,13993,14076,14158,14296,14387,14470,14522,14575,14641,14712,14792,14863,14943,15021,15099,15172,15247,15354,15441,15528,15619,15712,15784,15860,15952,16003,16085,16151,16235,16321,16383,16447,16510,16578,16685,16794,16890,16995,17051,17108,17364,17710,17787,17938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,378,482,574,650,737,826,910,998,1088,1162,1239,1321,1399,1476,1544", "endColumns": "90,80,100,103,91,75,86,88,83,87,89,73,76,81,77,76,67,119", "endOffsets": "191,272,373,477,569,645,732,821,905,993,1083,1157,1234,1316,1394,1471,1539,1659"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,539,540,541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4596,4687,5011,5112,5216,5928,6004,17113,17202,17369,17457,17792,17943,18020,18102,56639,56716,56784", "endColumns": "90,80,100,103,91,75,86,88,83,87,89,73,76,81,77,76,67,119", "endOffsets": "4682,4763,5107,5211,5303,5999,6086,17197,17281,17452,17542,17861,18015,18097,18175,56711,56779,56899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,349,476,621,750,848,963,1103,1222,1367,1451,1556,1652,1752,1871,1992,2102,2245,2389,2524,2715,2840,2962,3086,3208,3305,3402,3530,3665,3763,3866,3972,4119,4270,4378,4478,4554,4650,4745,4832,4920,5030,5110,5195,5290,5393,5484,5583,5672,5780,5880,5986,6104,6184,6288", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "197,344,471,616,745,843,958,1098,1217,1362,1446,1551,1647,1747,1866,1987,2097,2240,2384,2519,2710,2835,2957,3081,3203,3300,3397,3525,3660,3758,3861,3967,4114,4265,4373,4473,4549,4645,4740,4827,4915,5025,5105,5190,5285,5388,5479,5578,5667,5775,5875,5981,6099,6179,6283,6378"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6152,6299,6446,6573,6718,6847,6945,7060,7200,7319,7464,7548,7653,7749,7849,7968,8089,8199,8342,8486,8621,8812,8937,9059,9183,9305,9402,9499,9627,9762,9860,9963,10069,10216,10367,10475,10575,10651,10747,10842,10929,11017,11127,11207,11292,11387,11490,11581,11680,11769,11877,11977,12083,12201,12281,12385", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "6294,6441,6568,6713,6842,6940,7055,7195,7314,7459,7543,7648,7744,7844,7963,8084,8194,8337,8481,8616,8807,8932,9054,9178,9300,9397,9494,9622,9757,9855,9958,10064,10211,10362,10470,10570,10646,10742,10837,10924,11012,11122,11202,11287,11382,11485,11576,11675,11764,11872,11972,12078,12196,12276,12380,12475"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3569,3667,3770,3870,3973,4078,4181,18180", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "3662,3765,3865,3968,4073,4176,4295,18276"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-eu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4768", "endColumns": "142", "endOffsets": "4906"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,257,370", "endColumns": "99,101,112,104", "endOffsets": "150,252,365,470"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4911,5540,5642,5755", "endColumns": "99,101,112,104", "endOffsets": "5006,5637,5750,5855"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,88", "endOffsets": "136,225"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "56904,56990", "endColumns": "85,88", "endOffsets": "56985,57074"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,434,532,642,728,834,958,1044,1125,1217,1311,1407,1501,1602,1696,1792,1889,1981,2074,2156,2265,2374,2473,2582,2689,2800,2971,17547", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "429,527,637,723,829,953,1039,1120,1212,1306,1402,1496,1597,1691,1787,1884,1976,2069,2151,2260,2369,2468,2577,2684,2795,2966,3065,17625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,254,404,480,565,671,826,1012,1169,1296,1400,1540,1600,1659,1748,1855,1931,2076,2192,2275,2426,2503,2600,2701,2827,2935,3042,3139,3210,3283,3405,3501,3714,3896,3998,4144,4248,4442,4556,4700,4805,4945,5061,5197,5299,5402,5539,5642,5781,5891,6053,6135,6279,6355,6487,6591,6670,6748,6818,6889,6961,7032,7157,7265,7421,7518,7628,7734,7883,7985,8086,8222,8316,8423,8528,8642,8743,8807,8861,8916,8972,9021,9071,9139,9193,9262,9315,9359,9429,9478,9534,9590,9653,9708,9758,9808,9876,9926,9982,10027,10090,10148,10206,10341,10455,10533,10594,10679,10748,10816,10926,11012,11087,11167,11245,11312,11409,11484,11575,11663,11801,11880,11972,12054,12150,12254,12335,12431,12532,12640,12738,12815,12909,12978,13058,13178,13328,13404,13481,13580,13673,15724,15839,16049,16095,16275,16462,16550,16695,16795,16935,17119,17253,17438,17515,17598,17668,17760,17876,17921,18005,18118,18210,18335,18462,18600,18707,18820,18886,18946,19007,19063,19129,19212,19306,19424,19495,19574,19844,20085,20217,20348,20489,20582,20721,20849,21004,21180,21273,21373,21461,21580,21692,21850,21941,22021,22082,22340,22434,22520,22586,22651,22773,22906,23022,23189,23380,23457,23527,23592,23778,23858,23950,24026,24119,24287,24383,24467,24583,24676,24827,24946,25008,25155,25227,25298,25358,25434,25483,25548,25652,25704,25777,25976,26094,26190,26255,26419,26553,26684,26804,27078,27179,27276,27372,27556,27760,27856,28052,28169,28314,28422,28501,28590,28662,28834,29019,29157,29206,29290,29447,29577,29664,29745,29839,29919,29996,30072,30135,30216,30290,30367,30462,30530,30601,30685,30754,30842,31022,31179,31265,31319,31402,31547,31619,31713,31797,31878,31960,32223,32278,32353,32431,32512,32613,32795,32881,32955,33040,33119,33192,33289,33380,33518,33601,33654,33735,33812,33877,33955,34065,34168,34274,34396,34475,34524,34575,34670,34790,34876,34995,35162,35275,35360,35497,35627,35786,35912,36022,36156,36284,36478,36661,36792,36922,37010,37119,37249,37368,37477,37603,37676,38091,38170,38256,38336", "endColumns": "198,149,75,84,105,154,185,156,126,103,139,59,58,88,106,75,144,115,82,150,76,96,100,125,107,106,96,70,72,121,95,212,181,101,145,103,193,113,143,104,139,115,135,101,102,136,102,138,109,161,81,143,75,131,103,78,77,69,70,71,70,124,107,155,96,109,105,148,101,100,135,93,106,104,113,100,63,53,54,55,48,49,67,53,68,52,43,69,48,55,55,62,54,49,49,67,49,55,44,62,57,57,134,113,77,60,84,68,67,109,85,74,79,77,66,96,74,90,87,137,78,91,81,95,103,80,95,100,107,97,76,93,68,79,119,149,75,76,98,92,2050,114,209,45,179,186,87,144,99,139,183,133,184,76,82,69,91,115,44,83,112,91,124,126,137,106,112,65,59,60,55,65,82,93,117,70,78,269,240,131,130,140,92,138,127,154,175,92,99,87,118,111,157,90,79,60,257,93,85,65,64,121,132,115,166,190,76,69,64,185,79,91,75,92,167,95,83,115,92,150,118,61,146,71,70,59,75,48,64,103,51,72,198,117,95,64,163,133,130,119,273,100,96,95,183,203,95,195,116,144,107,78,88,71,171,184,137,48,83,156,129,86,80,93,79,76,75,62,80,73,76,94,67,70,83,68,87,179,156,85,53,82,144,71,93,83,80,81,262,54,74,77,80,100,181,85,73,84,78,72,96,90,137,82,52,80,76,64,77,109,102,105,121,78,48,50,94,119,85,118,166,112,84,136,129,158,125,109,133,127,193,182,130,129,87,108,129,118,108,125,72,414,78,85,79,76", "endOffsets": "249,399,475,560,666,821,1007,1164,1291,1395,1535,1595,1654,1743,1850,1926,2071,2187,2270,2421,2498,2595,2696,2822,2930,3037,3134,3205,3278,3400,3496,3709,3891,3993,4139,4243,4437,4551,4695,4800,4940,5056,5192,5294,5397,5534,5637,5776,5886,6048,6130,6274,6350,6482,6586,6665,6743,6813,6884,6956,7027,7152,7260,7416,7513,7623,7729,7878,7980,8081,8217,8311,8418,8523,8637,8738,8802,8856,8911,8967,9016,9066,9134,9188,9257,9310,9354,9424,9473,9529,9585,9648,9703,9753,9803,9871,9921,9977,10022,10085,10143,10201,10336,10450,10528,10589,10674,10743,10811,10921,11007,11082,11162,11240,11307,11404,11479,11570,11658,11796,11875,11967,12049,12145,12249,12330,12426,12527,12635,12733,12810,12904,12973,13053,13173,13323,13399,13476,13575,13668,15719,15834,16044,16090,16270,16457,16545,16690,16790,16930,17114,17248,17433,17510,17593,17663,17755,17871,17916,18000,18113,18205,18330,18457,18595,18702,18815,18881,18941,19002,19058,19124,19207,19301,19419,19490,19569,19839,20080,20212,20343,20484,20577,20716,20844,20999,21175,21268,21368,21456,21575,21687,21845,21936,22016,22077,22335,22429,22515,22581,22646,22768,22901,23017,23184,23375,23452,23522,23587,23773,23853,23945,24021,24114,24282,24378,24462,24578,24671,24822,24941,25003,25150,25222,25293,25353,25429,25478,25543,25647,25699,25772,25971,26089,26185,26250,26414,26548,26679,26799,27073,27174,27271,27367,27551,27755,27851,28047,28164,28309,28417,28496,28585,28657,28829,29014,29152,29201,29285,29442,29572,29659,29740,29834,29914,29991,30067,30130,30211,30285,30362,30457,30525,30596,30680,30749,30837,31017,31174,31260,31314,31397,31542,31614,31708,31792,31873,31955,32218,32273,32348,32426,32507,32608,32790,32876,32950,33035,33114,33187,33284,33375,33513,33596,33649,33730,33807,33872,33950,34060,34163,34269,34391,34470,34519,34570,34665,34785,34871,34990,35157,35270,35355,35492,35622,35781,35907,36017,36151,36279,36473,36656,36787,36917,37005,37114,37244,37363,37472,37598,37671,38086,38165,38251,38331,38408"}, "to": {"startLines": "193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18281,18480,18630,18706,18791,18897,19052,19238,19395,19522,19626,19766,19826,19885,19974,20081,20157,20302,20418,20501,20652,20729,20826,20927,21053,21161,21268,21365,21436,21509,21631,21727,21940,22122,22224,22370,22474,22668,22782,22926,23031,23171,23287,23423,23525,23628,23765,23868,24007,24117,24279,24361,24505,24581,24713,24817,24896,24974,25044,25115,25187,25258,25383,25491,25647,25744,25854,25960,26109,26211,26312,26448,26542,26649,26754,26868,26969,27033,27087,27142,27198,27247,27297,27365,27419,27488,27541,27585,27655,27704,27760,27816,27879,27934,27984,28034,28102,28152,28208,28253,28316,28374,28432,28567,28681,28759,28820,28905,28974,29042,29152,29238,29313,29393,29471,29538,29635,29710,29801,29889,30027,30106,30198,30280,30376,30480,30561,30657,30758,30866,30964,31041,31135,31204,31284,31404,31554,31630,31707,31806,31899,33950,34065,34275,34321,34501,34688,34776,34921,35021,35161,35345,35479,35664,35741,35824,35894,35986,36102,36147,36231,36344,36436,36561,36688,36826,36933,37046,37112,37172,37233,37289,37355,37438,37532,37650,37721,37800,38070,38311,38443,38574,38715,38808,38947,39075,39230,39406,39499,39599,39687,39806,39918,40076,40167,40247,40308,40566,40660,40746,40812,40877,40999,41132,41248,41415,41606,41683,41753,41818,42004,42084,42176,42252,42345,42513,42609,42693,42809,42902,43053,43172,43234,43381,43453,43524,43584,43660,43709,43774,43878,43930,44003,44202,44320,44416,44481,44645,44779,44910,45030,45304,45405,45502,45598,45782,45986,46082,46278,46395,46540,46648,46727,46816,46888,47060,47245,47383,47432,47516,47673,47803,47890,47971,48065,48145,48222,48298,48361,48442,48516,48593,48688,48756,48827,48911,48980,49068,49248,49405,49491,49545,49628,49773,49845,49939,50023,50104,50186,50449,50504,50579,50657,50738,50839,51021,51107,51181,51266,51345,51418,51515,51606,51744,51827,51880,51961,52038,52103,52181,52291,52394,52500,52622,52701,52750,52801,52896,53016,53102,53221,53388,53501,53586,53723,53853,54012,54138,54248,54382,54510,54704,54887,55018,55148,55236,55345,55475,55594,55703,55829,55902,56317,56396,56482,56562", "endColumns": "198,149,75,84,105,154,185,156,126,103,139,59,58,88,106,75,144,115,82,150,76,96,100,125,107,106,96,70,72,121,95,212,181,101,145,103,193,113,143,104,139,115,135,101,102,136,102,138,109,161,81,143,75,131,103,78,77,69,70,71,70,124,107,155,96,109,105,148,101,100,135,93,106,104,113,100,63,53,54,55,48,49,67,53,68,52,43,69,48,55,55,62,54,49,49,67,49,55,44,62,57,57,134,113,77,60,84,68,67,109,85,74,79,77,66,96,74,90,87,137,78,91,81,95,103,80,95,100,107,97,76,93,68,79,119,149,75,76,98,92,2050,114,209,45,179,186,87,144,99,139,183,133,184,76,82,69,91,115,44,83,112,91,124,126,137,106,112,65,59,60,55,65,82,93,117,70,78,269,240,131,130,140,92,138,127,154,175,92,99,87,118,111,157,90,79,60,257,93,85,65,64,121,132,115,166,190,76,69,64,185,79,91,75,92,167,95,83,115,92,150,118,61,146,71,70,59,75,48,64,103,51,72,198,117,95,64,163,133,130,119,273,100,96,95,183,203,95,195,116,144,107,78,88,71,171,184,137,48,83,156,129,86,80,93,79,76,75,62,80,73,76,94,67,70,83,68,87,179,156,85,53,82,144,71,93,83,80,81,262,54,74,77,80,100,181,85,73,84,78,72,96,90,137,82,52,80,76,64,77,109,102,105,121,78,48,50,94,119,85,118,166,112,84,136,129,158,125,109,133,127,193,182,130,129,87,108,129,118,108,125,72,414,78,85,79,76", "endOffsets": "18475,18625,18701,18786,18892,19047,19233,19390,19517,19621,19761,19821,19880,19969,20076,20152,20297,20413,20496,20647,20724,20821,20922,21048,21156,21263,21360,21431,21504,21626,21722,21935,22117,22219,22365,22469,22663,22777,22921,23026,23166,23282,23418,23520,23623,23760,23863,24002,24112,24274,24356,24500,24576,24708,24812,24891,24969,25039,25110,25182,25253,25378,25486,25642,25739,25849,25955,26104,26206,26307,26443,26537,26644,26749,26863,26964,27028,27082,27137,27193,27242,27292,27360,27414,27483,27536,27580,27650,27699,27755,27811,27874,27929,27979,28029,28097,28147,28203,28248,28311,28369,28427,28562,28676,28754,28815,28900,28969,29037,29147,29233,29308,29388,29466,29533,29630,29705,29796,29884,30022,30101,30193,30275,30371,30475,30556,30652,30753,30861,30959,31036,31130,31199,31279,31399,31549,31625,31702,31801,31894,33945,34060,34270,34316,34496,34683,34771,34916,35016,35156,35340,35474,35659,35736,35819,35889,35981,36097,36142,36226,36339,36431,36556,36683,36821,36928,37041,37107,37167,37228,37284,37350,37433,37527,37645,37716,37795,38065,38306,38438,38569,38710,38803,38942,39070,39225,39401,39494,39594,39682,39801,39913,40071,40162,40242,40303,40561,40655,40741,40807,40872,40994,41127,41243,41410,41601,41678,41748,41813,41999,42079,42171,42247,42340,42508,42604,42688,42804,42897,43048,43167,43229,43376,43448,43519,43579,43655,43704,43769,43873,43925,43998,44197,44315,44411,44476,44640,44774,44905,45025,45299,45400,45497,45593,45777,45981,46077,46273,46390,46535,46643,46722,46811,46883,47055,47240,47378,47427,47511,47668,47798,47885,47966,48060,48140,48217,48293,48356,48437,48511,48588,48683,48751,48822,48906,48975,49063,49243,49400,49486,49540,49623,49768,49840,49934,50018,50099,50181,50444,50499,50574,50652,50733,50834,51016,51102,51176,51261,51340,51413,51510,51601,51739,51822,51875,51956,52033,52098,52176,52286,52389,52495,52617,52696,52745,52796,52891,53011,53097,53216,53383,53496,53581,53718,53848,54007,54133,54243,54377,54505,54699,54882,55013,55143,55231,55340,55470,55589,55698,55824,55897,56312,56391,56477,56557,56634"}}]}]}