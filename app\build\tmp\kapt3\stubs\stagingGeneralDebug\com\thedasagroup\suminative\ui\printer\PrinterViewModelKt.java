package com.thedasagroup.suminative.ui.printer;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\" \u0010\u0000\u001a\b\u0018\u00010\u0001R\u00020\u0002X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0003\u0010\u0004\"\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0007"}, d2 = {"selectPrinter", "Lcom/sunmi/printerx/PrinterSdk$Printer;", "Lcom/sunmi/printerx/PrinterSdk;", "getSelectPrinter", "()Lcom/sunmi/printerx/PrinterSdk$Printer;", "setSelectPrinter", "(Lcom/sunmi/printerx/PrinterSdk$Printer;)V", "app_stagingGeneralDebug"})
public final class PrinterViewModelKt {
    @org.jetbrains.annotations.Nullable()
    private static com.sunmi.printerx.PrinterSdk.Printer selectPrinter;
    
    @org.jetbrains.annotations.Nullable()
    public static final com.sunmi.printerx.PrinterSdk.Printer getSelectPrinter() {
        return null;
    }
    
    public static final void setSelectPrinter(@org.jetbrains.annotations.Nullable()
    com.sunmi.printerx.PrinterSdk.Printer p0) {
    }
}