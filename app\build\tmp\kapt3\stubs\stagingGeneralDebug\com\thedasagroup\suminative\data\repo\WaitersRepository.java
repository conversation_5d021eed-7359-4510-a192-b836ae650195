package com.thedasagroup.suminative.data.repo;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J(\u0010\u0004\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u00060\u00052\u0006\u0010\t\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u000b\u00a8\u0006\f"}, d2 = {"Lcom/thedasagroup/suminative/data/repo/WaitersRepository;", "Lcom/thedasagroup/suminative/data/repo/BaseRepository;", "<init>", "()V", "getWaitersList", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "", "Lcom/thedasagroup/suminative/data/model/response/login/User;", "storeId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_stagingGeneralDebug"})
public final class WaitersRepository extends com.thedasagroup.suminative.data.repo.BaseRepository {
    
    public WaitersRepository() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getWaitersList(@org.jetbrains.annotations.NotNull()
    java.lang.String storeId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.data.model.response.login.User>>>> $completion) {
        return null;
    }
}