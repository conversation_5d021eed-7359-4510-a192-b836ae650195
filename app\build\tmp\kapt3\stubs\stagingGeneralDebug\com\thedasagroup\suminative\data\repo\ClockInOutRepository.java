package com.thedasagroup.suminative.data.repo;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\"\u0010\u0004\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00060\u00052\u0006\u0010\b\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\nJ\"\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00060\u00052\u0006\u0010\b\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\rJ\"\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00060\u00052\u0006\u0010\b\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u0010\u00a8\u0006\u0011"}, d2 = {"Lcom/thedasagroup/suminative/data/repo/ClockInOutRepository;", "Lcom/thedasagroup/suminative/data/repo/BaseRepository;", "<init>", "()V", "storeUserLogin", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/login/LoginResponse;", "request", "Lcom/thedasagroup/suminative/data/model/request/login/StoreUserLoginRequest;", "(Lcom/thedasagroup/suminative/data/model/request/login/StoreUserLoginRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clockInUserTime", "Lcom/thedasagroup/suminative/data/model/request/login/ClockInUserTimeRequest;", "(Lcom/thedasagroup/suminative/data/model/request/login/ClockInUserTimeRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clockOutUserTime", "Lcom/thedasagroup/suminative/data/model/request/login/ClockOutUserTimeRequest;", "(Lcom/thedasagroup/suminative/data/model/request/login/ClockOutUserTimeRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_stagingGeneralDebug"})
public final class ClockInOutRepository extends com.thedasagroup.suminative.data.repo.BaseRepository {
    
    public ClockInOutRepository() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object storeUserLogin(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.login.StoreUserLoginRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clockInUserTime(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.login.ClockInUserTimeRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clockOutUserTime(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.login.ClockOutUserTimeRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse>>> $completion) {
        return null;
    }
}