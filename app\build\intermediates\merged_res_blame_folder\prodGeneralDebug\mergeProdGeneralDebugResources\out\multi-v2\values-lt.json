{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,380,485,580,657,748,835,919,1005,1093,1168,1245,1322,1397,1477,1560", "endColumns": "92,83,97,104,94,76,90,86,83,85,87,74,76,76,74,79,82,121", "endOffsets": "193,277,375,480,575,652,743,830,914,1000,1088,1163,1240,1317,1392,1472,1555,1677"}, "to": {"startLines": "50,51,54,55,56,64,65,181,182,184,185,189,191,192,193,541,542,543", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4667,4760,5108,5206,5311,6037,6114,17214,17301,17467,17553,17887,18040,18117,18194,56673,56753,56836", "endColumns": "92,83,97,104,94,76,90,86,83,85,87,74,76,76,74,79,82,121", "endOffsets": "4755,4839,5201,5306,5401,6109,6200,17296,17380,17548,17636,17957,18112,18189,18264,56748,56831,56953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,265,379", "endColumns": "104,104,113,105", "endOffsets": "155,260,374,480"}, "to": {"startLines": "53,60,61,62", "startColumns": "4,4,4,4", "startOffsets": "5003,5634,5739,5853", "endColumns": "104,104,113,105", "endOffsets": "5103,5734,5848,5954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "40,41,42,43,44,45,46,194", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3618,3716,3826,3925,4028,4139,4249,18269", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "3711,3821,3920,4023,4134,4244,4364,18365"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,87", "endOffsets": "138,226"}, "to": {"startLines": "544,545", "startColumns": "4,4", "startOffsets": "56958,57046", "endColumns": "87,87", "endOffsets": "57041,57129"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,186", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "425,541,645,758,845,947,1069,1152,1232,1326,1422,1519,1615,1718,1814,1912,2008,2102,2196,2279,2388,2496,2596,2706,2811,2917,3093,17641", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "536,640,753,840,942,1064,1147,1227,1321,1417,1514,1610,1713,1809,1907,2003,2097,2191,2274,2383,2491,2591,2701,2806,2912,3088,3189,17720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-lt\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "154", "endOffsets": "349"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "4844", "endColumns": "158", "endOffsets": "4998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,219,337,413,496,599,745,928,1051,1173,1272,1395,1458,1520,1607,1712,1779,1920,2030,2113,2259,2336,2424,2518,2645,2740,2854,2943,3022,3095,3273,3365,3569,3732,3839,4034,4168,4375,4485,4656,4764,4915,5036,5174,5278,5376,5537,5640,5800,5906,6089,6183,6340,6418,6545,6651,6727,6823,6900,6965,7042,7126,7237,7351,7508,7612,7765,7882,8033,8137,8235,8369,8466,8581,8700,8819,8916,8983,9037,9095,9149,9200,9253,9332,9383,9451,9501,9545,9617,9664,9722,9774,9839,9901,9953,10003,10074,10127,10179,10226,10292,10354,10419,10560,10663,10744,10805,10883,10952,11020,11143,11218,11295,11375,11453,11521,11629,11707,11792,11880,12016,12100,12192,12283,12373,12465,12546,12644,12761,12874,12981,13067,13164,13233,13320,13446,13583,13649,13726,13821,13920,15971,16086,16255,16302,16442,16613,16715,16840,16931,17073,17269,17388,17569,17641,17723,17792,17875,17997,18044,18132,18272,18370,18493,18624,18784,18891,18995,19060,19117,19169,19225,19291,19373,19475,19598,19666,19740,20025,20240,20382,20515,20646,20741,20855,20982,21123,21298,21392,21496,21584,21725,21827,21994,22088,22163,22253,22465,22559,22640,22703,22765,22882,23002,23116,23288,23462,23536,23605,23671,23836,23912,24014,24090,24183,24356,24459,24536,24659,24760,24911,25036,25094,25216,25291,25374,25441,25513,25563,25627,25714,25766,25837,26007,26128,26231,26297,26448,26589,26698,26804,27070,27169,27260,27354,27530,27731,27816,28018,28131,28275,28393,28477,28573,28654,28799,28967,29105,29156,29240,29370,29490,29576,29657,29760,29840,29920,29999,30063,30143,30212,30285,30374,30442,30529,30622,30691,30776,30968,31109,31212,31271,31350,31499,31562,31651,31739,31819,31908,32140,32202,32276,32357,32438,32558,32746,32831,32905,32991,33074,33142,33225,33318,33438,33520,33574,33656,33733,33810,33895,34005,34108,34215,34343,34419,34469,34525,34627,34740,34823,34924,35099,35201,35281,35402,35517,35669,35783,35892,36048,36165,36375,36571,36706,36841,36940,37044,37174,37307,37403,37539,37624,38017,38105,38191,38285", "endColumns": "163,117,75,82,102,145,182,122,121,98,122,62,61,86,104,66,140,109,82,145,76,87,93,126,94,113,88,78,72,177,91,203,162,106,194,133,206,109,170,107,150,120,137,103,97,160,102,159,105,182,93,156,77,126,105,75,95,76,64,76,83,110,113,156,103,152,116,150,103,97,133,96,114,118,118,96,66,53,57,53,50,52,78,50,67,49,43,71,46,57,51,64,61,51,49,70,52,51,46,65,61,64,140,102,80,60,77,68,67,122,74,76,79,77,67,107,77,84,87,135,83,91,90,89,91,80,97,116,112,106,85,96,68,86,125,136,65,76,94,98,2050,114,168,46,139,170,101,124,90,141,195,118,180,71,81,68,82,121,46,87,139,97,122,130,159,106,103,64,56,51,55,65,81,101,122,67,73,284,214,141,132,130,94,113,126,140,174,93,103,87,140,101,166,93,74,89,211,93,80,62,61,116,119,113,171,173,73,68,65,164,75,101,75,92,172,102,76,122,100,150,124,57,121,74,82,66,71,49,63,86,51,70,169,120,102,65,150,140,108,105,265,98,90,93,175,200,84,201,112,143,117,83,95,80,144,167,137,50,83,129,119,85,80,102,79,79,78,63,79,68,72,88,67,86,92,68,84,191,140,102,58,78,148,62,88,87,79,88,231,61,73,80,80,119,187,84,73,85,82,67,82,92,119,81,53,81,76,76,84,109,102,106,127,75,49,55,101,112,82,100,174,101,79,120,114,151,113,108,155,116,209,195,134,134,98,103,129,132,95,135,84,392,87,85,93,72", "endOffsets": "214,332,408,491,594,740,923,1046,1168,1267,1390,1453,1515,1602,1707,1774,1915,2025,2108,2254,2331,2419,2513,2640,2735,2849,2938,3017,3090,3268,3360,3564,3727,3834,4029,4163,4370,4480,4651,4759,4910,5031,5169,5273,5371,5532,5635,5795,5901,6084,6178,6335,6413,6540,6646,6722,6818,6895,6960,7037,7121,7232,7346,7503,7607,7760,7877,8028,8132,8230,8364,8461,8576,8695,8814,8911,8978,9032,9090,9144,9195,9248,9327,9378,9446,9496,9540,9612,9659,9717,9769,9834,9896,9948,9998,10069,10122,10174,10221,10287,10349,10414,10555,10658,10739,10800,10878,10947,11015,11138,11213,11290,11370,11448,11516,11624,11702,11787,11875,12011,12095,12187,12278,12368,12460,12541,12639,12756,12869,12976,13062,13159,13228,13315,13441,13578,13644,13721,13816,13915,15966,16081,16250,16297,16437,16608,16710,16835,16926,17068,17264,17383,17564,17636,17718,17787,17870,17992,18039,18127,18267,18365,18488,18619,18779,18886,18990,19055,19112,19164,19220,19286,19368,19470,19593,19661,19735,20020,20235,20377,20510,20641,20736,20850,20977,21118,21293,21387,21491,21579,21720,21822,21989,22083,22158,22248,22460,22554,22635,22698,22760,22877,22997,23111,23283,23457,23531,23600,23666,23831,23907,24009,24085,24178,24351,24454,24531,24654,24755,24906,25031,25089,25211,25286,25369,25436,25508,25558,25622,25709,25761,25832,26002,26123,26226,26292,26443,26584,26693,26799,27065,27164,27255,27349,27525,27726,27811,28013,28126,28270,28388,28472,28568,28649,28794,28962,29100,29151,29235,29365,29485,29571,29652,29755,29835,29915,29994,30058,30138,30207,30280,30369,30437,30524,30617,30686,30771,30963,31104,31207,31266,31345,31494,31557,31646,31734,31814,31903,32135,32197,32271,32352,32433,32553,32741,32826,32900,32986,33069,33137,33220,33313,33433,33515,33569,33651,33728,33805,33890,34000,34103,34210,34338,34414,34464,34520,34622,34735,34818,34919,35094,35196,35276,35397,35512,35664,35778,35887,36043,36160,36370,36566,36701,36836,36935,37039,37169,37302,37398,37534,37619,38012,38100,38186,38280,38353"}, "to": {"startLines": "195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18370,18534,18652,18728,18811,18914,19060,19243,19366,19488,19587,19710,19773,19835,19922,20027,20094,20235,20345,20428,20574,20651,20739,20833,20960,21055,21169,21258,21337,21410,21588,21680,21884,22047,22154,22349,22483,22690,22800,22971,23079,23230,23351,23489,23593,23691,23852,23955,24115,24221,24404,24498,24655,24733,24860,24966,25042,25138,25215,25280,25357,25441,25552,25666,25823,25927,26080,26197,26348,26452,26550,26684,26781,26896,27015,27134,27231,27298,27352,27410,27464,27515,27568,27647,27698,27766,27816,27860,27932,27979,28037,28089,28154,28216,28268,28318,28389,28442,28494,28541,28607,28669,28734,28875,28978,29059,29120,29198,29267,29335,29458,29533,29610,29690,29768,29836,29944,30022,30107,30195,30331,30415,30507,30598,30688,30780,30861,30959,31076,31189,31296,31382,31479,31548,31635,31761,31898,31964,32041,32136,32235,34286,34401,34570,34617,34757,34928,35030,35155,35246,35388,35584,35703,35884,35956,36038,36107,36190,36312,36359,36447,36587,36685,36808,36939,37099,37206,37310,37375,37432,37484,37540,37606,37688,37790,37913,37981,38055,38340,38555,38697,38830,38961,39056,39170,39297,39438,39613,39707,39811,39899,40040,40142,40309,40403,40478,40568,40780,40874,40955,41018,41080,41197,41317,41431,41603,41777,41851,41920,41986,42151,42227,42329,42405,42498,42671,42774,42851,42974,43075,43226,43351,43409,43531,43606,43689,43756,43828,43878,43942,44029,44081,44152,44322,44443,44546,44612,44763,44904,45013,45119,45385,45484,45575,45669,45845,46046,46131,46333,46446,46590,46708,46792,46888,46969,47114,47282,47420,47471,47555,47685,47805,47891,47972,48075,48155,48235,48314,48378,48458,48527,48600,48689,48757,48844,48937,49006,49091,49283,49424,49527,49586,49665,49814,49877,49966,50054,50134,50223,50455,50517,50591,50672,50753,50873,51061,51146,51220,51306,51389,51457,51540,51633,51753,51835,51889,51971,52048,52125,52210,52320,52423,52530,52658,52734,52784,52840,52942,53055,53138,53239,53414,53516,53596,53717,53832,53984,54098,54207,54363,54480,54690,54886,55021,55156,55255,55359,55489,55622,55718,55854,55939,56332,56420,56506,56600", "endColumns": "163,117,75,82,102,145,182,122,121,98,122,62,61,86,104,66,140,109,82,145,76,87,93,126,94,113,88,78,72,177,91,203,162,106,194,133,206,109,170,107,150,120,137,103,97,160,102,159,105,182,93,156,77,126,105,75,95,76,64,76,83,110,113,156,103,152,116,150,103,97,133,96,114,118,118,96,66,53,57,53,50,52,78,50,67,49,43,71,46,57,51,64,61,51,49,70,52,51,46,65,61,64,140,102,80,60,77,68,67,122,74,76,79,77,67,107,77,84,87,135,83,91,90,89,91,80,97,116,112,106,85,96,68,86,125,136,65,76,94,98,2050,114,168,46,139,170,101,124,90,141,195,118,180,71,81,68,82,121,46,87,139,97,122,130,159,106,103,64,56,51,55,65,81,101,122,67,73,284,214,141,132,130,94,113,126,140,174,93,103,87,140,101,166,93,74,89,211,93,80,62,61,116,119,113,171,173,73,68,65,164,75,101,75,92,172,102,76,122,100,150,124,57,121,74,82,66,71,49,63,86,51,70,169,120,102,65,150,140,108,105,265,98,90,93,175,200,84,201,112,143,117,83,95,80,144,167,137,50,83,129,119,85,80,102,79,79,78,63,79,68,72,88,67,86,92,68,84,191,140,102,58,78,148,62,88,87,79,88,231,61,73,80,80,119,187,84,73,85,82,67,82,92,119,81,53,81,76,76,84,109,102,106,127,75,49,55,101,112,82,100,174,101,79,120,114,151,113,108,155,116,209,195,134,134,98,103,129,132,95,135,84,392,87,85,93,72", "endOffsets": "18529,18647,18723,18806,18909,19055,19238,19361,19483,19582,19705,19768,19830,19917,20022,20089,20230,20340,20423,20569,20646,20734,20828,20955,21050,21164,21253,21332,21405,21583,21675,21879,22042,22149,22344,22478,22685,22795,22966,23074,23225,23346,23484,23588,23686,23847,23950,24110,24216,24399,24493,24650,24728,24855,24961,25037,25133,25210,25275,25352,25436,25547,25661,25818,25922,26075,26192,26343,26447,26545,26679,26776,26891,27010,27129,27226,27293,27347,27405,27459,27510,27563,27642,27693,27761,27811,27855,27927,27974,28032,28084,28149,28211,28263,28313,28384,28437,28489,28536,28602,28664,28729,28870,28973,29054,29115,29193,29262,29330,29453,29528,29605,29685,29763,29831,29939,30017,30102,30190,30326,30410,30502,30593,30683,30775,30856,30954,31071,31184,31291,31377,31474,31543,31630,31756,31893,31959,32036,32131,32230,34281,34396,34565,34612,34752,34923,35025,35150,35241,35383,35579,35698,35879,35951,36033,36102,36185,36307,36354,36442,36582,36680,36803,36934,37094,37201,37305,37370,37427,37479,37535,37601,37683,37785,37908,37976,38050,38335,38550,38692,38825,38956,39051,39165,39292,39433,39608,39702,39806,39894,40035,40137,40304,40398,40473,40563,40775,40869,40950,41013,41075,41192,41312,41426,41598,41772,41846,41915,41981,42146,42222,42324,42400,42493,42666,42769,42846,42969,43070,43221,43346,43404,43526,43601,43684,43751,43823,43873,43937,44024,44076,44147,44317,44438,44541,44607,44758,44899,45008,45114,45380,45479,45570,45664,45840,46041,46126,46328,46441,46585,46703,46787,46883,46964,47109,47277,47415,47466,47550,47680,47800,47886,47967,48070,48150,48230,48309,48373,48453,48522,48595,48684,48752,48839,48932,49001,49086,49278,49419,49522,49581,49660,49809,49872,49961,50049,50129,50218,50450,50512,50586,50667,50748,50868,51056,51141,51215,51301,51384,51452,51535,51628,51748,51830,51884,51966,52043,52120,52205,52315,52418,52525,52653,52729,52779,52835,52937,53050,53133,53234,53409,53511,53591,53712,53827,53979,54093,54202,54358,54475,54685,54881,55016,55151,55250,55354,55484,55617,55713,55849,55934,56327,56415,56501,56595,56668"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,375,454,532,615,709,799,895,1013,1097,1160,1226,1325,1403,1468,1578,1641,1713,1772,1846,1907,1961,2085,2146,2208,2262,2340,2474,2562,2639,2732,2813,2897,3038,3117,3201,3344,3441,3518,3574,3628,3694,3769,3848,3919,3999,4075,4153,4226,4303,4410,4497,4578,4668,4760,4832,4913,5005,5060,5142,5208,5293,5380,5442,5506,5569,5641,5752,5868,5969,6078,6138,6196,6278,6364,6440", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,78,77,82,93,89,95,117,83,62,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,76,92,80,83,140,78,83,142,96,76,55,53,65,74,78,70,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81,85,75,77", "endOffsets": "370,449,527,610,704,794,890,1008,1092,1155,1221,1320,1398,1463,1573,1636,1708,1767,1841,1902,1956,2080,2141,2203,2257,2335,2469,2557,2634,2727,2808,2892,3033,3112,3196,3339,3436,3513,3569,3623,3689,3764,3843,3914,3994,4070,4148,4221,4298,4405,4492,4573,4663,4755,4827,4908,5000,5055,5137,5203,5288,5375,5437,5501,5564,5636,5747,5863,5964,6073,6133,6191,6273,6359,6435,6513"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,57,58,59,63,66,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,183,187,188,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3194,3273,3351,3434,3528,4369,4465,4583,5406,5469,5535,5959,6205,12486,12596,12659,12731,12790,12864,12925,12979,13103,13164,13226,13280,13358,13492,13580,13657,13750,13831,13915,14056,14135,14219,14362,14459,14536,14592,14646,14712,14787,14866,14937,15017,15093,15171,15244,15321,15428,15515,15596,15686,15778,15850,15931,16023,16078,16160,16226,16311,16398,16460,16524,16587,16659,16770,16886,16987,17096,17156,17385,17725,17811,17962", "endLines": "7,35,36,37,38,39,47,48,49,57,58,59,63,66,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,183,187,188,190", "endColumns": "12,78,77,82,93,89,95,117,83,62,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,76,92,80,83,140,78,83,142,96,76,55,53,65,74,78,70,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81,85,75,77", "endOffsets": "420,3268,3346,3429,3523,3613,4460,4578,4662,5464,5530,5629,6032,6265,12591,12654,12726,12785,12859,12920,12974,13098,13159,13221,13275,13353,13487,13575,13652,13745,13826,13910,14051,14130,14214,14357,14454,14531,14587,14641,14707,14782,14861,14932,15012,15088,15166,15239,15316,15423,15510,15591,15681,15773,15845,15926,16018,16073,16155,16221,16306,16393,16455,16519,16582,16654,16765,16881,16982,17091,17151,17209,17462,17806,17882,18035"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,411,529,630,724,835,967,1083,1227,1311,1410,1506,1605,1730,1848,1952,2091,2226,2365,2561,2691,2809,2935,3062,3159,3260,3382,3511,3609,3712,3819,3957,4105,4214,4318,4402,4498,4594,4682,4772,4883,4963,5050,5150,5259,5355,5454,5542,5653,5749,5849,5987,6071,6174", "endColumns": "118,119,116,117,100,93,110,131,115,143,83,98,95,98,124,117,103,138,134,138,195,129,117,125,126,96,100,121,128,97,102,106,137,147,108,103,83,95,95,87,89,110,79,86,99,108,95,98,87,110,95,99,137,83,102,96", "endOffsets": "169,289,406,524,625,719,830,962,1078,1222,1306,1405,1501,1600,1725,1843,1947,2086,2221,2360,2556,2686,2804,2930,3057,3154,3255,3377,3506,3604,3707,3814,3952,4100,4209,4313,4397,4493,4589,4677,4767,4878,4958,5045,5145,5254,5350,5449,5537,5648,5744,5844,5982,6066,6169,6266"}, "to": {"startLines": "67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6270,6389,6509,6626,6744,6845,6939,7050,7182,7298,7442,7526,7625,7721,7820,7945,8063,8167,8306,8441,8580,8776,8906,9024,9150,9277,9374,9475,9597,9726,9824,9927,10034,10172,10320,10429,10533,10617,10713,10809,10897,10987,11098,11178,11265,11365,11474,11570,11669,11757,11868,11964,12064,12202,12286,12389", "endColumns": "118,119,116,117,100,93,110,131,115,143,83,98,95,98,124,117,103,138,134,138,195,129,117,125,126,96,100,121,128,97,102,106,137,147,108,103,83,95,95,87,89,110,79,86,99,108,95,98,87,110,95,99,137,83,102,96", "endOffsets": "6384,6504,6621,6739,6840,6934,7045,7177,7293,7437,7521,7620,7716,7815,7940,8058,8162,8301,8436,8575,8771,8901,9019,9145,9272,9369,9470,9592,9721,9819,9922,10029,10167,10315,10424,10528,10612,10708,10804,10892,10982,11093,11173,11260,11360,11469,11565,11664,11752,11863,11959,12059,12197,12281,12384,12481"}}]}]}