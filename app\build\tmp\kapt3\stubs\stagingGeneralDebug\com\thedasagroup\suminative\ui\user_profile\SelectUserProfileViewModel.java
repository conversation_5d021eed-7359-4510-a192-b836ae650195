package com.thedasagroup.suminative.ui.user_profile;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\r\u0018\u0000 ,2\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0002+,BC\b\u0007\u0012\b\b\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0006\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\u0004\b\u0010\u0010\u0011J\u0006\u0010\u0014\u001a\u00020\u0015J*\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190\u00180\u00172\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010\u001dJ\u000e\u0010\u001e\u001a\u00020\u00152\u0006\u0010\u001f\u001a\u00020 J\u000e\u0010!\u001a\u00020\u00152\u0006\u0010\u001c\u001a\u00020\u001bJ\u000e\u0010\"\u001a\u00020\u00152\u0006\u0010#\u001a\u00020\u001bJ\u000e\u0010$\u001a\u00020\u00152\u0006\u0010#\u001a\u00020\u001bJ\u0006\u0010%\u001a\u00020\u0015J\u0016\u0010&\u001a\u00020\u00152\u0006\u0010\'\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010(J\u0016\u0010)\u001a\u00020\u00152\u0006\u0010\'\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010(J\u0016\u0010*\u001a\u00020\u00152\u0006\u0010\'\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010(R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013\u00a8\u0006-"}, d2 = {"Lcom/thedasagroup/suminative/ui/user_profile/SelectUserProfileViewModel;", "Lcom/airbnb/mvrx/MavericksViewModel;", "Lcom/thedasagroup/suminative/ui/user_profile/SelectUserProfileState;", "state", "waitersRepository", "Lcom/thedasagroup/suminative/data/repo/WaitersRepository;", "loginUseCase", "Lcom/thedasagroup/suminative/ui/login/LoginUseCase;", "storeUserLoginUseCase", "Lcom/thedasagroup/suminative/ui/login/StoreUserLoginUseCase;", "clockInUserTimeUseCase", "Lcom/thedasagroup/suminative/ui/login/ClockInUserTimeUseCase;", "clockOutUserTimeUseCase", "Lcom/thedasagroup/suminative/ui/login/ClockOutUserTimeUseCase;", "prefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "<init>", "(Lcom/thedasagroup/suminative/ui/user_profile/SelectUserProfileState;Lcom/thedasagroup/suminative/data/repo/WaitersRepository;Lcom/thedasagroup/suminative/ui/login/LoginUseCase;Lcom/thedasagroup/suminative/ui/login/StoreUserLoginUseCase;Lcom/thedasagroup/suminative/ui/login/ClockInUserTimeUseCase;Lcom/thedasagroup/suminative/ui/login/ClockOutUserTimeUseCase;Lcom/thedasagroup/suminative/data/prefs/Prefs;)V", "getPrefs", "()Lcom/thedasagroup/suminative/data/prefs/Prefs;", "loadWaiters", "", "loginWithWaiter", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/login/LoginResponse;", "email", "", "password", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "selectWaiter", "waiter", "Lcom/thedasagroup/suminative/data/model/response/login/User;", "updatePassword", "updatePasswordError", "error", "updateLoginError", "clearErrors", "waiterSignIn", "userPin", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clockIn", "clockOut", "Factory", "Companion", "app_stagingGeneralDebug"})
public final class SelectUserProfileViewModel extends com.airbnb.mvrx.MavericksViewModel<com.thedasagroup.suminative.ui.user_profile.SelectUserProfileState> {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.repo.WaitersRepository waitersRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.login.LoginUseCase loginUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.login.StoreUserLoginUseCase storeUserLoginUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.login.ClockInUserTimeUseCase clockInUserTimeUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.ui.login.ClockOutUserTimeUseCase clockOutUserTimeUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.prefs.Prefs prefs = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.user_profile.SelectUserProfileViewModel.Companion Companion = null;
    
    @dagger.assisted.AssistedInject()
    public SelectUserProfileViewModel(@dagger.assisted.Assisted()
    @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.user_profile.SelectUserProfileState state, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.WaitersRepository waitersRepository, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.login.LoginUseCase loginUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.login.StoreUserLoginUseCase storeUserLoginUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.login.ClockInUserTimeUseCase clockInUserTimeUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.login.ClockOutUserTimeUseCase clockOutUserTimeUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        super(null, null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.prefs.Prefs getPrefs() {
        return null;
    }
    
    public final void loadWaiters() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object loginWithWaiter(@org.jetbrains.annotations.NotNull()
    java.lang.String email, @org.jetbrains.annotations.NotNull()
    java.lang.String password, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse>>> $completion) {
        return null;
    }
    
    public final void selectWaiter(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.login.User waiter) {
    }
    
    public final void updatePassword(@org.jetbrains.annotations.NotNull()
    java.lang.String password) {
    }
    
    public final void updatePasswordError(@org.jetbrains.annotations.NotNull()
    java.lang.String error) {
    }
    
    public final void updateLoginError(@org.jetbrains.annotations.NotNull()
    java.lang.String error) {
    }
    
    public final void clearErrors() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object waiterSignIn(@org.jetbrains.annotations.NotNull()
    java.lang.String userPin, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clockIn(@org.jetbrains.annotations.NotNull()
    java.lang.String userPin, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clockOut(@org.jetbrains.annotations.NotNull()
    java.lang.String userPin, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u001b\u0010\u0006\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0003H\u0096\u0001J\u0013\u0010\n\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0007\u001a\u00020\bH\u0096\u0001\u00a8\u0006\u000b"}, d2 = {"Lcom/thedasagroup/suminative/ui/user_profile/SelectUserProfileViewModel$Companion;", "Lcom/airbnb/mvrx/MavericksViewModelFactory;", "Lcom/thedasagroup/suminative/ui/user_profile/SelectUserProfileViewModel;", "Lcom/thedasagroup/suminative/ui/user_profile/SelectUserProfileState;", "<init>", "()V", "create", "viewModelContext", "Lcom/airbnb/mvrx/ViewModelContext;", "state", "initialState", "app_stagingGeneralDebug"})
    public static final class Companion implements com.airbnb.mvrx.MavericksViewModelFactory<com.thedasagroup.suminative.ui.user_profile.SelectUserProfileViewModel, com.thedasagroup.suminative.ui.user_profile.SelectUserProfileState> {
        
        private Companion() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.user_profile.SelectUserProfileViewModel create(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.user_profile.SelectUserProfileState state) {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.user_profile.SelectUserProfileState initialState(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext) {
            return null;
        }
    }
    
    @dagger.assisted.AssistedFactory()
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\bg\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001J\u0010\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0003H&\u00a8\u0006\u0006"}, d2 = {"Lcom/thedasagroup/suminative/ui/user_profile/SelectUserProfileViewModel$Factory;", "Lcom/airbnb/mvrx/hilt/AssistedViewModelFactory;", "Lcom/thedasagroup/suminative/ui/user_profile/SelectUserProfileViewModel;", "Lcom/thedasagroup/suminative/ui/user_profile/SelectUserProfileState;", "create", "state", "app_stagingGeneralDebug"})
    public static abstract interface Factory extends com.airbnb.mvrx.hilt.AssistedViewModelFactory<com.thedasagroup.suminative.ui.user_profile.SelectUserProfileViewModel, com.thedasagroup.suminative.ui.user_profile.SelectUserProfileState> {
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public abstract com.thedasagroup.suminative.ui.user_profile.SelectUserProfileViewModel create(@org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.user_profile.SelectUserProfileState state);
    }
}