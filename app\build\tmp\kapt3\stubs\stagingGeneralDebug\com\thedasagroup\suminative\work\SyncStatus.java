package com.thedasagroup.suminative.work;

/**
 * Sealed class representing different sync states
 */
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001:\u0004\u0004\u0005\u0006\u0007B\t\b\u0004\u00a2\u0006\u0004\b\u0002\u0010\u0003\u0082\u0001\u0004\b\t\n\u000b\u00a8\u0006\f"}, d2 = {"Lcom/thedasagroup/suminative/work/SyncStatus;", "", "<init>", "()V", "Idle", "Syncing", "Success", "Error", "Lcom/thedasagroup/suminative/work/SyncStatus$Error;", "Lcom/thedasagroup/suminative/work/SyncStatus$Idle;", "Lcom/thedasagroup/suminative/work/SyncStatus$Success;", "Lcom/thedasagroup/suminative/work/SyncStatus$Syncing;", "app_stagingGeneralDebug"})
public abstract class SyncStatus {
    
    private SyncStatus() {
        super();
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\r\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0005H\u00c6\u0003J\'\u0010\u0011\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0015H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0005H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\f\u00a8\u0006\u0018"}, d2 = {"Lcom/thedasagroup/suminative/work/SyncStatus$Error;", "Lcom/thedasagroup/suminative/work/SyncStatus;", "errorMessage", "", "failedCount", "", "totalCount", "<init>", "(Ljava/lang/String;II)V", "getErrorMessage", "()Ljava/lang/String;", "getFailedCount", "()I", "getTotalCount", "component1", "component2", "component3", "copy", "equals", "", "other", "", "hashCode", "toString", "app_stagingGeneralDebug"})
    public static final class Error extends com.thedasagroup.suminative.work.SyncStatus {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String errorMessage = null;
        private final int failedCount = 0;
        private final int totalCount = 0;
        
        public Error(@org.jetbrains.annotations.NotNull()
        java.lang.String errorMessage, int failedCount, int totalCount) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getErrorMessage() {
            return null;
        }
        
        public final int getFailedCount() {
            return 0;
        }
        
        public final int getTotalCount() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        public final int component2() {
            return 0;
        }
        
        public final int component3() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.thedasagroup.suminative.work.SyncStatus.Error copy(@org.jetbrains.annotations.NotNull()
        java.lang.String errorMessage, int failedCount, int totalCount) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/thedasagroup/suminative/work/SyncStatus$Idle;", "Lcom/thedasagroup/suminative/work/SyncStatus;", "<init>", "()V", "app_stagingGeneralDebug"})
    public static final class Idle extends com.thedasagroup.suminative.work.SyncStatus {
        @org.jetbrains.annotations.NotNull()
        public static final com.thedasagroup.suminative.work.SyncStatus.Idle INSTANCE = null;
        
        private Idle() {
        }
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\n\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0005\u0010\u0006J\t\u0010\n\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\u001d\u0010\f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\r\u001a\u00020\u000e2\b\u0010\u000f\u001a\u0004\u0018\u00010\u0010H\u00d6\u0003J\t\u0010\u0011\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\b\u00a8\u0006\u0014"}, d2 = {"Lcom/thedasagroup/suminative/work/SyncStatus$Success;", "Lcom/thedasagroup/suminative/work/SyncStatus;", "syncedCount", "", "totalCount", "<init>", "(II)V", "getSyncedCount", "()I", "getTotalCount", "component1", "component2", "copy", "equals", "", "other", "", "hashCode", "toString", "", "app_stagingGeneralDebug"})
    public static final class Success extends com.thedasagroup.suminative.work.SyncStatus {
        private final int syncedCount = 0;
        private final int totalCount = 0;
        
        public Success(int syncedCount, int totalCount) {
        }
        
        public final int getSyncedCount() {
            return 0;
        }
        
        public final int getTotalCount() {
            return 0;
        }
        
        public final int component1() {
            return 0;
        }
        
        public final int component2() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.thedasagroup.suminative.work.SyncStatus.Success copy(int syncedCount, int totalCount) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/thedasagroup/suminative/work/SyncStatus$Syncing;", "Lcom/thedasagroup/suminative/work/SyncStatus;", "<init>", "()V", "app_stagingGeneralDebug"})
    public static final class Syncing extends com.thedasagroup.suminative.work.SyncStatus {
        @org.jetbrains.annotations.NotNull()
        public static final com.thedasagroup.suminative.work.SyncStatus.Syncing INSTANCE = null;
        
        private Syncing() {
        }
    }
}