package com.thedasagroup.suminative.domain.orders;

import com.thedasagroup.suminative.data.database.LocalOrderRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class GetLocalOrdersUseCase_Factory implements Factory<GetLocalOrdersUseCase> {
  private final Provider<LocalOrderRepository> orderRepositoryProvider;

  public GetLocalOrdersUseCase_Factory(Provider<LocalOrderRepository> orderRepositoryProvider) {
    this.orderRepositoryProvider = orderRepositoryProvider;
  }

  @Override
  public GetLocalOrdersUseCase get() {
    return newInstance(orderRepositoryProvider.get());
  }

  public static GetLocalOrdersUseCase_Factory create(
      Provider<LocalOrderRepository> orderRepositoryProvider) {
    return new GetLocalOrdersUseCase_Factory(orderRepositoryProvider);
  }

  public static GetLocalOrdersUseCase newInstance(LocalOrderRepository orderRepository) {
    return new GetLocalOrdersUseCase(orderRepository);
  }
}
