package com.thedasagroup.suminative.ui.reservations;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0011\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\"\u0010\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\u00072\u0006\u0010\n\u001a\u00020\u000bH\u0086B\u00a2\u0006\u0002\u0010\fR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/thedasagroup/suminative/ui/reservations/CancelReservationUseCase;", "", "reservationsRepository", "Lcom/thedasagroup/suminative/data/repo/ReservationsRepository;", "<init>", "(Lcom/thedasagroup/suminative/data/repo/ReservationsRepository;)V", "invoke", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/airbnb/mvrx/Async;", "", "reservationId", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_stagingGeneralDebug"})
public final class CancelReservationUseCase {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.repo.ReservationsRepository reservationsRepository = null;
    
    @javax.inject.Inject()
    public CancelReservationUseCase(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.ReservationsRepository reservationsRepository) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object invoke(int reservationId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.StateFlow<? extends com.airbnb.mvrx.Async<java.lang.Boolean>>> $completion) {
        return null;
    }
}