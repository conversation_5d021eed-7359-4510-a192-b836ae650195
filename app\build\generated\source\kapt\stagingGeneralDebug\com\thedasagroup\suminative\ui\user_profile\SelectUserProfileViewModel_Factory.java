package com.thedasagroup.suminative.ui.user_profile;

import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.WaitersRepository;
import com.thedasagroup.suminative.ui.login.ClockInUserTimeUseCase;
import com.thedasagroup.suminative.ui.login.ClockOutUserTimeUseCase;
import com.thedasagroup.suminative.ui.login.LoginUseCase;
import com.thedasagroup.suminative.ui.login.StoreUserLoginUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class SelectUserProfileViewModel_Factory {
  private final Provider<WaitersRepository> waitersRepositoryProvider;

  private final Provider<LoginUseCase> loginUseCaseProvider;

  private final Provider<StoreUserLoginUseCase> storeUserLoginUseCaseProvider;

  private final Provider<ClockInUserTimeUseCase> clockInUserTimeUseCaseProvider;

  private final Provider<ClockOutUserTimeUseCase> clockOutUserTimeUseCaseProvider;

  private final Provider<Prefs> prefsProvider;

  public SelectUserProfileViewModel_Factory(Provider<WaitersRepository> waitersRepositoryProvider,
      Provider<LoginUseCase> loginUseCaseProvider,
      Provider<StoreUserLoginUseCase> storeUserLoginUseCaseProvider,
      Provider<ClockInUserTimeUseCase> clockInUserTimeUseCaseProvider,
      Provider<ClockOutUserTimeUseCase> clockOutUserTimeUseCaseProvider,
      Provider<Prefs> prefsProvider) {
    this.waitersRepositoryProvider = waitersRepositoryProvider;
    this.loginUseCaseProvider = loginUseCaseProvider;
    this.storeUserLoginUseCaseProvider = storeUserLoginUseCaseProvider;
    this.clockInUserTimeUseCaseProvider = clockInUserTimeUseCaseProvider;
    this.clockOutUserTimeUseCaseProvider = clockOutUserTimeUseCaseProvider;
    this.prefsProvider = prefsProvider;
  }

  public SelectUserProfileViewModel get(SelectUserProfileState state) {
    return newInstance(state, waitersRepositoryProvider.get(), loginUseCaseProvider.get(), storeUserLoginUseCaseProvider.get(), clockInUserTimeUseCaseProvider.get(), clockOutUserTimeUseCaseProvider.get(), prefsProvider.get());
  }

  public static SelectUserProfileViewModel_Factory create(
      Provider<WaitersRepository> waitersRepositoryProvider,
      Provider<LoginUseCase> loginUseCaseProvider,
      Provider<StoreUserLoginUseCase> storeUserLoginUseCaseProvider,
      Provider<ClockInUserTimeUseCase> clockInUserTimeUseCaseProvider,
      Provider<ClockOutUserTimeUseCase> clockOutUserTimeUseCaseProvider,
      Provider<Prefs> prefsProvider) {
    return new SelectUserProfileViewModel_Factory(waitersRepositoryProvider, loginUseCaseProvider, storeUserLoginUseCaseProvider, clockInUserTimeUseCaseProvider, clockOutUserTimeUseCaseProvider, prefsProvider);
  }

  public static SelectUserProfileViewModel newInstance(SelectUserProfileState state,
      WaitersRepository waitersRepository, LoginUseCase loginUseCase,
      StoreUserLoginUseCase storeUserLoginUseCase, ClockInUserTimeUseCase clockInUserTimeUseCase,
      ClockOutUserTimeUseCase clockOutUserTimeUseCase, Prefs prefs) {
    return new SelectUserProfileViewModel(state, waitersRepository, loginUseCase, storeUserLoginUseCase, clockInUserTimeUseCase, clockOutUserTimeUseCase, prefs);
  }
}
