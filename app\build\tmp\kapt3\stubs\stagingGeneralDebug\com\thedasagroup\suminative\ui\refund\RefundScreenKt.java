package com.thedasagroup.suminative.ui.refund;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u00004\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\u001aT\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\u0014\b\u0002\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00050\u000b2\u0014\b\u0002\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u00050\u000b2\u000e\b\u0002\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00050\u0010H\u0007\u001a\u0016\u0010\u0011\u001a\u00020\u00052\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00050\u0010H\u0007\u001a\u001e\u0010\u0013\u001a\u00020\u00052\u0006\u0010\u0014\u001a\u00020\u00012\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00050\u0010H\u0007\"\u0014\u0010\u0000\u001a\u00020\u0001X\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0015"}, d2 = {"TAG", "", "getTAG", "()Ljava/lang/String;", "RefundScreen", "", "guavaOrder", "Lcom/thedasagroup/suminative/data/model/response/my_guava/orders/list_of_orders/GuavaOrder;", "paymentViewModel", "Lcom/thedasagroup/suminative/ui/payment/PaymentViewModel;", "onMakePaymentClickSuccess", "Lkotlin/Function1;", "Lcom/thedasagroup/suminative/data/model/response/store_orders/Order2;", "onMakePaymentClickFail", "Lcom/thedasagroup/suminative/data/model/response/my_guava/failure/GuavaFailResponse;", "onPaymentCancelled", "Lkotlin/Function0;", "PaymentSessionTryAgain", "onClick", "PaymentMethodButton", "text", "app_stagingGeneralDebug"})
public final class RefundScreenKt {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "PaymentScreen";
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getTAG() {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void RefundScreen(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GuavaOrder guavaOrder, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.payment.PaymentViewModel paymentViewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.store_orders.Order2, kotlin.Unit> onMakePaymentClickSuccess, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.my_guava.failure.GuavaFailResponse, kotlin.Unit> onMakePaymentClickFail, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPaymentCancelled) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PaymentSessionTryAgain(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PaymentMethodButton(@org.jetbrains.annotations.NotNull()
    java.lang.String text, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
}