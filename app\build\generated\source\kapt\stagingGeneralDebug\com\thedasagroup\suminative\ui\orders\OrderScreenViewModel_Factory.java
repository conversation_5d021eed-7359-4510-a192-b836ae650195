package com.thedasagroup.suminative.ui.orders;

import android.media.AudioManager;
import com.instacart.truetime.time.TrueTimeImpl;
import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.OrdersRepository;
import com.thedasagroup.suminative.domain.GetPOSSettingsUseCase;
import com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase;
import com.thedasagroup.suminative.ui.utils.SoundPoolPlayer;
import dagger.internal.DaggerGenerated;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class OrderScreenViewModel_Factory {
  private final Provider<GetOrdersUseCase> getOrdersUseCaseProvider;

  private final Provider<GetPendingOrdersPagedUseCase> getPendingOrdersPagedUseCaseProvider;

  private final Provider<GetScheduleOrdersUseCase> getScheduleOrdersUseCaseProvider;

  private final Provider<GetScheduleOrdersPagedUseCase> getScheduleOrdersPagedUseCaseProvider;

  private final Provider<ChangeStatusUseCase> changeStatusUseCaseProvider;

  private final Provider<ChangeStatusAndOrdersUseCase> changeStatusProvider;

  private final Provider<AcceptOrderWithDelayUseCase> acceptOrderWithDelayUseCaseProvider;

  private final Provider<TrueTimeImpl> trueTimeImplProvider;

  private final Provider<OrdersRepository> ordersRepositoryProvider;

  private final Provider<GetStoreSettingsUseCase> getStoreSettingsProvider;

  private final Provider<CloseOpenStoreUseCase> closeOpenStoreUseCaseProvider;

  private final Provider<Prefs> prefsProvider;

  private final Provider<SoundPoolPlayer> soundPoolPlayerProvider;

  private final Provider<AudioManager> audioManagerProvider;

  private final Provider<GetPOSSettingsUseCase> getPosSettingsProvider;

  public OrderScreenViewModel_Factory(Provider<GetOrdersUseCase> getOrdersUseCaseProvider,
      Provider<GetPendingOrdersPagedUseCase> getPendingOrdersPagedUseCaseProvider,
      Provider<GetScheduleOrdersUseCase> getScheduleOrdersUseCaseProvider,
      Provider<GetScheduleOrdersPagedUseCase> getScheduleOrdersPagedUseCaseProvider,
      Provider<ChangeStatusUseCase> changeStatusUseCaseProvider,
      Provider<ChangeStatusAndOrdersUseCase> changeStatusProvider,
      Provider<AcceptOrderWithDelayUseCase> acceptOrderWithDelayUseCaseProvider,
      Provider<TrueTimeImpl> trueTimeImplProvider,
      Provider<OrdersRepository> ordersRepositoryProvider,
      Provider<GetStoreSettingsUseCase> getStoreSettingsProvider,
      Provider<CloseOpenStoreUseCase> closeOpenStoreUseCaseProvider, Provider<Prefs> prefsProvider,
      Provider<SoundPoolPlayer> soundPoolPlayerProvider,
      Provider<AudioManager> audioManagerProvider,
      Provider<GetPOSSettingsUseCase> getPosSettingsProvider) {
    this.getOrdersUseCaseProvider = getOrdersUseCaseProvider;
    this.getPendingOrdersPagedUseCaseProvider = getPendingOrdersPagedUseCaseProvider;
    this.getScheduleOrdersUseCaseProvider = getScheduleOrdersUseCaseProvider;
    this.getScheduleOrdersPagedUseCaseProvider = getScheduleOrdersPagedUseCaseProvider;
    this.changeStatusUseCaseProvider = changeStatusUseCaseProvider;
    this.changeStatusProvider = changeStatusProvider;
    this.acceptOrderWithDelayUseCaseProvider = acceptOrderWithDelayUseCaseProvider;
    this.trueTimeImplProvider = trueTimeImplProvider;
    this.ordersRepositoryProvider = ordersRepositoryProvider;
    this.getStoreSettingsProvider = getStoreSettingsProvider;
    this.closeOpenStoreUseCaseProvider = closeOpenStoreUseCaseProvider;
    this.prefsProvider = prefsProvider;
    this.soundPoolPlayerProvider = soundPoolPlayerProvider;
    this.audioManagerProvider = audioManagerProvider;
    this.getPosSettingsProvider = getPosSettingsProvider;
  }

  public OrderScreenViewModel get(OrderState state) {
    return newInstance(state, getOrdersUseCaseProvider.get(), getPendingOrdersPagedUseCaseProvider.get(), getScheduleOrdersUseCaseProvider.get(), getScheduleOrdersPagedUseCaseProvider.get(), changeStatusUseCaseProvider.get(), changeStatusProvider.get(), acceptOrderWithDelayUseCaseProvider.get(), trueTimeImplProvider.get(), ordersRepositoryProvider.get(), getStoreSettingsProvider.get(), closeOpenStoreUseCaseProvider.get(), prefsProvider.get(), soundPoolPlayerProvider.get(), audioManagerProvider.get(), getPosSettingsProvider.get());
  }

  public static OrderScreenViewModel_Factory create(
      Provider<GetOrdersUseCase> getOrdersUseCaseProvider,
      Provider<GetPendingOrdersPagedUseCase> getPendingOrdersPagedUseCaseProvider,
      Provider<GetScheduleOrdersUseCase> getScheduleOrdersUseCaseProvider,
      Provider<GetScheduleOrdersPagedUseCase> getScheduleOrdersPagedUseCaseProvider,
      Provider<ChangeStatusUseCase> changeStatusUseCaseProvider,
      Provider<ChangeStatusAndOrdersUseCase> changeStatusProvider,
      Provider<AcceptOrderWithDelayUseCase> acceptOrderWithDelayUseCaseProvider,
      Provider<TrueTimeImpl> trueTimeImplProvider,
      Provider<OrdersRepository> ordersRepositoryProvider,
      Provider<GetStoreSettingsUseCase> getStoreSettingsProvider,
      Provider<CloseOpenStoreUseCase> closeOpenStoreUseCaseProvider, Provider<Prefs> prefsProvider,
      Provider<SoundPoolPlayer> soundPoolPlayerProvider,
      Provider<AudioManager> audioManagerProvider,
      Provider<GetPOSSettingsUseCase> getPosSettingsProvider) {
    return new OrderScreenViewModel_Factory(getOrdersUseCaseProvider, getPendingOrdersPagedUseCaseProvider, getScheduleOrdersUseCaseProvider, getScheduleOrdersPagedUseCaseProvider, changeStatusUseCaseProvider, changeStatusProvider, acceptOrderWithDelayUseCaseProvider, trueTimeImplProvider, ordersRepositoryProvider, getStoreSettingsProvider, closeOpenStoreUseCaseProvider, prefsProvider, soundPoolPlayerProvider, audioManagerProvider, getPosSettingsProvider);
  }

  public static OrderScreenViewModel newInstance(OrderState state,
      GetOrdersUseCase getOrdersUseCase, GetPendingOrdersPagedUseCase getPendingOrdersPagedUseCase,
      GetScheduleOrdersUseCase getScheduleOrdersUseCase,
      GetScheduleOrdersPagedUseCase getScheduleOrdersPagedUseCase,
      ChangeStatusUseCase changeStatusUseCase, ChangeStatusAndOrdersUseCase changeStatus,
      AcceptOrderWithDelayUseCase acceptOrderWithDelayUseCase, TrueTimeImpl trueTimeImpl,
      OrdersRepository ordersRepository, GetStoreSettingsUseCase getStoreSettings,
      CloseOpenStoreUseCase closeOpenStoreUseCase, Prefs prefs, SoundPoolPlayer soundPoolPlayer,
      AudioManager audioManager, GetPOSSettingsUseCase getPosSettings) {
    return new OrderScreenViewModel(state, getOrdersUseCase, getPendingOrdersPagedUseCase, getScheduleOrdersUseCase, getScheduleOrdersPagedUseCase, changeStatusUseCase, changeStatus, acceptOrderWithDelayUseCase, trueTimeImpl, ordersRepository, getStoreSettings, closeOpenStoreUseCase, prefs, soundPoolPlayer, audioManager, getPosSettings);
  }
}
