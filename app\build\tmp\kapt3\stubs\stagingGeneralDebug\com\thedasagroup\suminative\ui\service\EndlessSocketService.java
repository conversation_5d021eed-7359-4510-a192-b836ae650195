package com.thedasagroup.suminative.ui.service;

@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000t\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u000f\b\u0007\u0018\u0000 I2\u00020\u0001:\u0001IB\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0012\u0010+\u001a\u0004\u0018\u00010,2\u0006\u0010-\u001a\u00020.H\u0016J\"\u0010/\u001a\u0002002\b\u0010-\u001a\u0004\u0018\u00010.2\u0006\u00101\u001a\u0002002\u0006\u00102\u001a\u000200H\u0016J\b\u00103\u001a\u000204H\u0016J\b\u00105\u001a\u000204H\u0016J\b\u00106\u001a\u000204H\u0002J\b\u00107\u001a\u000204H\u0002J\b\u00108\u001a\u000204H\u0002J\u0006\u00109\u001a\u000204J\b\u0010:\u001a\u00020;H\u0002J\u0010\u0010<\u001a\u0002042\u0006\u0010=\u001a\u00020\u0005H\u0002J\u0018\u0010>\u001a\u0002042\u0006\u0010=\u001a\u00020\u00052\u0006\u0010?\u001a\u000200H\u0002J>\u0010@\u001a\u0002042\u0006\u0010=\u001a\u00020\u00052\u0006\u0010A\u001a\u00020\u00052\b\b\u0002\u0010B\u001a\u00020(2\b\b\u0002\u0010C\u001a\u0002002\u0006\u0010D\u001a\u0002002\b\b\u0002\u0010E\u001a\u00020(H\u0002J&\u0010F\u001a\u0002042\u0006\u0010=\u001a\u00020\u00052\u0006\u0010A\u001a\u00020\u00052\u0006\u0010C\u001a\u0002002\u0006\u0010D\u001a\u000200J\u0018\u0010G\u001a\u0002042\u0006\u0010C\u001a\u0002002\b\b\u0002\u0010D\u001a\u000200J\u0006\u0010H\u001a\u000204R\u0010\u0010\u0004\u001a\u0004\u0018\u00010\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001e\u0010\u0006\u001a\u00020\u00078\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\b\u0010\t\"\u0004\b\n\u0010\u000bR\u001e\u0010\f\u001a\u00020\r8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000e\u0010\u000f\"\u0004\b\u0010\u0010\u0011R\u001e\u0010\u0012\u001a\u00020\u00138\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0014\u0010\u0015\"\u0004\b\u0016\u0010\u0017R\u001e\u0010\u0018\u001a\u00020\u00198\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001a\u0010\u001b\"\u0004\b\u001c\u0010\u001dR\u001e\u0010\u001e\u001a\u00020\u001f8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b \u0010!\"\u0004\b\"\u0010#R\u0014\u0010$\u001a\b\u0018\u00010%R\u00020&X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\'\u001a\u00020(X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010)\u001a\u0004\u0018\u00010*X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006J"}, d2 = {"Lcom/thedasagroup/suminative/ui/service/EndlessSocketService;", "Landroid/app/Service;", "<init>", "()V", "TAG", "", "prefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "getPrefs", "()Lcom/thedasagroup/suminative/data/prefs/Prefs;", "setPrefs", "(Lcom/thedasagroup/suminative/data/prefs/Prefs;)V", "orderRepository", "Lcom/thedasagroup/suminative/data/repo/OrdersRepository;", "getOrderRepository", "()Lcom/thedasagroup/suminative/data/repo/OrdersRepository;", "setOrderRepository", "(Lcom/thedasagroup/suminative/data/repo/OrdersRepository;)V", "getStoreSettings", "Lcom/thedasagroup/suminative/ui/login/GetStoreSettingsUseCase;", "getGetStoreSettings", "()Lcom/thedasagroup/suminative/ui/login/GetStoreSettingsUseCase;", "setGetStoreSettings", "(Lcom/thedasagroup/suminative/ui/login/GetStoreSettingsUseCase;)V", "trueTimeImpl", "Lcom/instacart/truetime/time/TrueTimeImpl;", "getTrueTimeImpl", "()Lcom/instacart/truetime/time/TrueTimeImpl;", "setTrueTimeImpl", "(Lcom/instacart/truetime/time/TrueTimeImpl;)V", "soundPoolPlayer", "Lcom/thedasagroup/suminative/ui/utils/SoundPoolPlayer;", "getSoundPoolPlayer", "()Lcom/thedasagroup/suminative/ui/utils/SoundPoolPlayer;", "setSoundPoolPlayer", "(Lcom/thedasagroup/suminative/ui/utils/SoundPoolPlayer;)V", "wakeLock", "Landroid/os/PowerManager$WakeLock;", "Landroid/os/PowerManager;", "isServiceStarted", "", "webSocketClient2", "Lcom/thedasagroup/suminative/ui/utils/ChatWebSocketClient;", "onBind", "Landroid/os/IBinder;", "intent", "Landroid/content/Intent;", "onStartCommand", "", "flags", "startId", "onCreate", "", "onDestroy", "startService", "stopService", "pingFakeServer", "schedule", "createNotification", "Landroid/app/Notification;", "sendNotificationForOrder", "message", "sendSocketNotification", "orderId", "sendMessageOrNotification", "type", "isNotSilent", "sound", "loops", "isScheduleOrder", "soundOnOrder", "playSound", "checkStoreTimings", "Companion", "app_stagingGeneralDebug"})
public final class EndlessSocketService extends android.app.Service {
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String TAG = null;
    @javax.inject.Inject()
    public com.thedasagroup.suminative.data.prefs.Prefs prefs;
    @javax.inject.Inject()
    public com.thedasagroup.suminative.data.repo.OrdersRepository orderRepository;
    @javax.inject.Inject()
    public com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase getStoreSettings;
    @javax.inject.Inject()
    public com.instacart.truetime.time.TrueTimeImpl trueTimeImpl;
    @javax.inject.Inject()
    public com.thedasagroup.suminative.ui.utils.SoundPoolPlayer soundPoolPlayer;
    @org.jetbrains.annotations.Nullable()
    private android.os.PowerManager.WakeLock wakeLock;
    private boolean isServiceStarted = false;
    @org.jetbrains.annotations.Nullable()
    private com.thedasagroup.suminative.ui.utils.ChatWebSocketClient webSocketClient2;
    @org.jetbrains.annotations.NotNull()
    private static java.lang.String dialogType = "";
    @org.jetbrains.annotations.NotNull()
    private static java.lang.String dialogMessage = "";
    @org.jetbrains.annotations.NotNull()
    private static kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> flowIsConnected2;
    @org.jetbrains.annotations.NotNull()
    private static kotlinx.coroutines.flow.MutableStateFlow<java.util.Date> lostConnectionDate;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.service.EndlessSocketService.Companion Companion = null;
    
    public EndlessSocketService() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.prefs.Prefs getPrefs() {
        return null;
    }
    
    public final void setPrefs(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.repo.OrdersRepository getOrderRepository() {
        return null;
    }
    
    public final void setOrderRepository(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.repo.OrdersRepository p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase getGetStoreSettings() {
        return null;
    }
    
    public final void setGetStoreSettings(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.instacart.truetime.time.TrueTimeImpl getTrueTimeImpl() {
        return null;
    }
    
    public final void setTrueTimeImpl(@org.jetbrains.annotations.NotNull()
    com.instacart.truetime.time.TrueTimeImpl p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.utils.SoundPoolPlayer getSoundPoolPlayer() {
        return null;
    }
    
    public final void setSoundPoolPlayer(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.utils.SoundPoolPlayer p0) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.os.IBinder onBind(@org.jetbrains.annotations.NotNull()
    android.content.Intent intent) {
        return null;
    }
    
    @java.lang.Override()
    public int onStartCommand(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent, int flags, int startId) {
        return 0;
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    @kotlin.OptIn(markerClass = {kotlinx.coroutines.DelicateCoroutinesApi.class})
    private final void startService() {
    }
    
    private final void stopService() {
    }
    
    private final void pingFakeServer() {
    }
    
    public final void schedule() {
    }
    
    private final android.app.Notification createNotification() {
        return null;
    }
    
    private final void sendNotificationForOrder(java.lang.String message) {
    }
    
    private final void sendSocketNotification(java.lang.String message, int orderId) {
    }
    
    private final void sendMessageOrNotification(java.lang.String message, java.lang.String type, boolean isNotSilent, int sound, int loops, boolean isScheduleOrder) {
    }
    
    public final void soundOnOrder(@org.jetbrains.annotations.NotNull()
    java.lang.String message, @org.jetbrains.annotations.NotNull()
    java.lang.String type, int sound, int loops) {
    }
    
    public final void playSound(int sound, int loops) {
    }
    
    public final void checkStoreTimings() {
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u001a\u0010\u0004\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0006\u0010\u0007\"\u0004\b\b\u0010\tR\u001a\u0010\n\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000b\u0010\u0007\"\u0004\b\f\u0010\tR \u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0010\u0010\u0011\"\u0004\b\u0012\u0010\u0013R \u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0016\u0010\u0011\"\u0004\b\u0017\u0010\u0013\u00a8\u0006\u0018"}, d2 = {"Lcom/thedasagroup/suminative/ui/service/EndlessSocketService$Companion;", "", "<init>", "()V", "dialogType", "", "getDialogType", "()Ljava/lang/String;", "setDialogType", "(Ljava/lang/String;)V", "dialogMessage", "getDialogMessage", "setDialogMessage", "flowIsConnected2", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "getFlowIsConnected2", "()Lkotlinx/coroutines/flow/MutableStateFlow;", "setFlowIsConnected2", "(Lkotlinx/coroutines/flow/MutableStateFlow;)V", "lostConnectionDate", "Ljava/util/Date;", "getLostConnectionDate", "setLostConnectionDate", "app_stagingGeneralDebug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getDialogType() {
            return null;
        }
        
        public final void setDialogType(@org.jetbrains.annotations.NotNull()
        java.lang.String p0) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getDialogMessage() {
            return null;
        }
        
        public final void setDialogMessage(@org.jetbrains.annotations.NotNull()
        java.lang.String p0) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> getFlowIsConnected2() {
            return null;
        }
        
        public final void setFlowIsConnected2(@org.jetbrains.annotations.NotNull()
        kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> p0) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.coroutines.flow.MutableStateFlow<java.util.Date> getLostConnectionDate() {
            return null;
        }
        
        public final void setLostConnectionDate(@org.jetbrains.annotations.NotNull()
        kotlinx.coroutines.flow.MutableStateFlow<java.util.Date> p0) {
        }
    }
}