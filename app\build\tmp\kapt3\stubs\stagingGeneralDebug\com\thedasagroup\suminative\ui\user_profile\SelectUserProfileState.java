package com.thedasagroup.suminative.ui.user_profile;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\'\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u00a5\u0001\u0012\u0014\b\u0002\u0010\u0002\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0003\u0012\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\n0\u0003\u0012\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\n0\u0003\u0012\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\n0\u0003\u0012\b\b\u0002\u0010\u000e\u001a\u00020\b\u0012\b\b\u0002\u0010\u000f\u001a\u00020\b\u0012\b\b\u0002\u0010\u0010\u001a\u00020\b\u0012\b\b\u0002\u0010\u0011\u001a\u00020\b\u0012\b\b\u0002\u0010\u0012\u001a\u00020\b\u00a2\u0006\u0004\b\u0013\u0010\u0014J\u0015\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u0003H\u00c6\u0003J\u000b\u0010%\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010&\u001a\u00020\bH\u00c6\u0003J\u000f\u0010\'\u001a\b\u0012\u0004\u0012\u00020\n0\u0003H\u00c6\u0003J\u000f\u0010(\u001a\b\u0012\u0004\u0012\u00020\n0\u0003H\u00c6\u0003J\u000f\u0010)\u001a\b\u0012\u0004\u0012\u00020\n0\u0003H\u00c6\u0003J\u000f\u0010*\u001a\b\u0012\u0004\u0012\u00020\n0\u0003H\u00c6\u0003J\t\u0010+\u001a\u00020\bH\u00c6\u0003J\t\u0010,\u001a\u00020\bH\u00c6\u0003J\t\u0010-\u001a\u00020\bH\u00c6\u0003J\t\u0010.\u001a\u00020\bH\u00c6\u0003J\t\u0010/\u001a\u00020\bH\u00c6\u0003J\u00a7\u0001\u00100\u001a\u00020\u00002\u0014\b\u0002\u0010\u0002\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u00032\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\n0\u00032\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\n0\u00032\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\n0\u00032\b\b\u0002\u0010\u000e\u001a\u00020\b2\b\b\u0002\u0010\u000f\u001a\u00020\b2\b\b\u0002\u0010\u0010\u001a\u00020\b2\b\b\u0002\u0010\u0011\u001a\u00020\b2\b\b\u0002\u0010\u0012\u001a\u00020\bH\u00c6\u0001J\u0013\u00101\u001a\u0002022\b\u00103\u001a\u0004\u0018\u000104H\u00d6\u0003J\t\u00105\u001a\u000206H\u00d6\u0001J\t\u00107\u001a\u00020\bH\u00d6\u0001R\u001d\u0010\u0002\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0016R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\n0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0016R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\n0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0016R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\n0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0016R\u0011\u0010\u000e\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u001aR\u0011\u0010\u000f\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001aR\u0011\u0010\u0010\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u001aR\u0011\u0010\u0011\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u001aR\u0011\u0010\u0012\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u001a\u00a8\u00068"}, d2 = {"Lcom/thedasagroup/suminative/ui/user_profile/SelectUserProfileState;", "Lcom/airbnb/mvrx/MavericksState;", "waitersResponse", "Lcom/airbnb/mvrx/Async;", "", "Lcom/thedasagroup/suminative/data/model/response/login/User;", "selectedWaiter", "password", "", "loginResponse", "Lcom/thedasagroup/suminative/data/model/response/login/LoginResponse;", "waiterSignInResponse", "clockInResponse", "clockOutResponse", "loginError", "passwordError", "waiterSignInError", "clockInError", "clockOutError", "<init>", "(Lcom/airbnb/mvrx/Async;Lcom/thedasagroup/suminative/data/model/response/login/User;Ljava/lang/String;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Lcom/airbnb/mvrx/Async;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getWaitersResponse", "()Lcom/airbnb/mvrx/Async;", "getSelectedWaiter", "()Lcom/thedasagroup/suminative/data/model/response/login/User;", "getPassword", "()Ljava/lang/String;", "getLoginResponse", "getWaiterSignInResponse", "getClockInResponse", "getClockOutResponse", "getLoginError", "getPasswordError", "getWaiterSignInError", "getClockInError", "getClockOutError", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "component12", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_stagingGeneralDebug"})
public final class SelectUserProfileState implements com.airbnb.mvrx.MavericksState {
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<java.util.List<com.thedasagroup.suminative.data.model.response.login.User>> waitersResponse = null;
    @org.jetbrains.annotations.Nullable()
    private final com.thedasagroup.suminative.data.model.response.login.User selectedWaiter = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String password = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> loginResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> waiterSignInResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> clockInResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> clockOutResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String loginError = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String passwordError = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String waiterSignInError = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String clockInError = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String clockOutError = null;
    
    public SelectUserProfileState(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.data.model.response.login.User>> waitersResponse, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.login.User selectedWaiter, @org.jetbrains.annotations.NotNull()
    java.lang.String password, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> loginResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> waiterSignInResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> clockInResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> clockOutResponse, @org.jetbrains.annotations.NotNull()
    java.lang.String loginError, @org.jetbrains.annotations.NotNull()
    java.lang.String passwordError, @org.jetbrains.annotations.NotNull()
    java.lang.String waiterSignInError, @org.jetbrains.annotations.NotNull()
    java.lang.String clockInError, @org.jetbrains.annotations.NotNull()
    java.lang.String clockOutError) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<java.util.List<com.thedasagroup.suminative.data.model.response.login.User>> getWaitersResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.response.login.User getSelectedWaiter() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPassword() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> getLoginResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> getWaiterSignInResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> getClockInResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> getClockOutResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getLoginError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPasswordError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getWaiterSignInError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getClockInError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getClockOutError() {
        return null;
    }
    
    public SelectUserProfileState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<java.util.List<com.thedasagroup.suminative.data.model.response.login.User>> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.thedasagroup.suminative.data.model.response.login.User component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.user_profile.SelectUserProfileState copy(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<? extends java.util.List<com.thedasagroup.suminative.data.model.response.login.User>> waitersResponse, @org.jetbrains.annotations.Nullable()
    com.thedasagroup.suminative.data.model.response.login.User selectedWaiter, @org.jetbrains.annotations.NotNull()
    java.lang.String password, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> loginResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> waiterSignInResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> clockInResponse, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.login.LoginResponse> clockOutResponse, @org.jetbrains.annotations.NotNull()
    java.lang.String loginError, @org.jetbrains.annotations.NotNull()
    java.lang.String passwordError, @org.jetbrains.annotations.NotNull()
    java.lang.String waiterSignInError, @org.jetbrains.annotations.NotNull()
    java.lang.String clockInError, @org.jetbrains.annotations.NotNull()
    java.lang.String clockOutError) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}