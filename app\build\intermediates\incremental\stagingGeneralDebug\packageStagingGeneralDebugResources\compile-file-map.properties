#Sat Aug 02 20:04:10 PKT 2025
com.thedasagroup.suminative.app-main-7\:/drawable/buy_some_items.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\drawable\\buy_some_items.xml
com.thedasagroup.suminative.app-main-7\:/drawable/cart_minus.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\drawable\\cart_minus.xml
com.thedasagroup.suminative.app-main-7\:/drawable/cart_plus.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\drawable\\cart_plus.xml
com.thedasagroup.suminative.app-main-7\:/drawable/collapse.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\drawable\\collapse.xml
com.thedasagroup.suminative.app-main-7\:/drawable/dasa_logo.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\drawable\\dasa_logo.png
com.thedasagroup.suminative.app-main-7\:/drawable/ic_launcher_background.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\drawable\\ic_launcher_background.xml
com.thedasagroup.suminative.app-main-7\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\drawable\\ic_launcher_foreground.xml
com.thedasagroup.suminative.app-main-7\:/drawable/ic_note.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\drawable\\ic_note.xml
com.thedasagroup.suminative.app-main-7\:/drawable/profile_icon.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\drawable\\profile_icon.xml
com.thedasagroup.suminative.app-main-7\:/drawable/store_closed.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\drawable\\store_closed.png
com.thedasagroup.suminative.app-main-7\:/drawable/trash.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\drawable\\trash.xml
com.thedasagroup.suminative.app-main-7\:/font/nunito.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\font\\nunito.xml
com.thedasagroup.suminative.app-main-7\:/font/nunito_bold.ttf=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\font\\nunito_bold.ttf
com.thedasagroup.suminative.app-main-7\:/font/nunito_regular.ttf=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\font\\nunito_regular.ttf
com.thedasagroup.suminative.app-main-7\:/font/popins.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\font\\popins.xml
com.thedasagroup.suminative.app-main-7\:/font/poppins_bold.ttf=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\font\\poppins_bold.ttf
com.thedasagroup.suminative.app-main-7\:/font/poppins_regular.ttf=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\font\\poppins_regular.ttf
com.thedasagroup.suminative.app-main-7\:/layout/activity_lcd.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\layout\\activity_lcd.xml
com.thedasagroup.suminative.app-main-7\:/layout/activity_tracking.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\layout\\activity_tracking.xml
com.thedasagroup.suminative.app-main-7\:/layout/dialog_cancel_order.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\layout\\dialog_cancel_order.xml
com.thedasagroup.suminative.app-main-7\:/layout/dialog_new_order.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\layout\\dialog_new_order.xml
com.thedasagroup.suminative.app-main-7\:/layout/dialog_update_order.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\layout\\dialog_update_order.xml
com.thedasagroup.suminative.app-main-7\:/menu/main_menu.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\menu\\main_menu.xml
com.thedasagroup.suminative.app-main-7\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.thedasagroup.suminative.app-main-7\:/mipmap-hdpi/ic_launcher.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\mipmap-hdpi-v4\\ic_launcher.png
com.thedasagroup.suminative.app-main-7\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.thedasagroup.suminative.app-main-7\:/mipmap-mdpi/ic_launcher.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\mipmap-mdpi-v4\\ic_launcher.png
com.thedasagroup.suminative.app-main-7\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.thedasagroup.suminative.app-main-7\:/mipmap-xhdpi/ic_launcher.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\mipmap-xhdpi-v4\\ic_launcher.png
com.thedasagroup.suminative.app-main-7\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.thedasagroup.suminative.app-main-7\:/mipmap-xxhdpi/ic_launcher.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.png
com.thedasagroup.suminative.app-main-7\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.thedasagroup.suminative.app-main-7\:/mipmap-xxxhdpi/ic_launcher.png=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.png
com.thedasagroup.suminative.app-main-7\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.thedasagroup.suminative.app-main-7\:/raw/cancelled_order.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\raw\\cancelled_order.mp3
com.thedasagroup.suminative.app-main-7\:/raw/courier_arriving.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\raw\\courier_arriving.mp3
com.thedasagroup.suminative.app-main-7\:/raw/courier_assigned.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\raw\\courier_assigned.mp3
com.thedasagroup.suminative.app-main-7\:/raw/delivered.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\raw\\delivered.mp3
com.thedasagroup.suminative.app-main-7\:/raw/delivery_imminent.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\raw\\delivery_imminent.mp3
com.thedasagroup.suminative.app-main-7\:/raw/notification.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\raw\\notification.mp3
com.thedasagroup.suminative.app-main-7\:/raw/out_for_delivery.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\raw\\out_for_delivery.mp3
com.thedasagroup.suminative.app-main-7\:/raw/schedule_order.mp3=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\raw\\schedule_order.mp3
com.thedasagroup.suminative.app-main-7\:/xml/backup_rules.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\xml\\backup_rules.xml
com.thedasagroup.suminative.app-main-7\:/xml/data_extraction_rules.xml=C\:\\Users\\sheer\\AndroidStudioProjects\\POS Active\\Tablet POS\\app\\build\\intermediates\\packaged_res\\stagingGeneralDebug\\packageStagingGeneralDebugResources\\xml\\data_extraction_rules.xml
