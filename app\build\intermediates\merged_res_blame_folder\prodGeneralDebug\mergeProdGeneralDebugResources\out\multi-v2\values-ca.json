{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,271,382", "endColumns": "112,102,110,108", "endOffsets": "163,266,377,486"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4853,5485,5588,5699", "endColumns": "112,102,110,108", "endOffsets": "4961,5583,5694,5803"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,523,626,718,819,947,1031,1092,1157,1254,1334,1399,1494,1558,1630,1692,1768,1831,1888,2009,2067,2128,2185,2265,2402,2489,2564,2657,2737,2821,2960,3038,3117,3269,3358,3434,3491,3547,3613,3691,3772,3843,3931,4009,4086,4160,4239,4349,4439,4531,4623,4724,4798,4880,4981,5031,5114,5180,5272,5359,5421,5485,5548,5621,5744,5857,5961,6069,6130,6190,6276,6362,6439", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,87,102,91,100,127,83,60,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,74,92,79,83,138,77,78,151,88,75,56,55,65,77,80,70,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85,85,76,78", "endOffsets": "269,350,430,518,621,713,814,942,1026,1087,1152,1249,1329,1394,1489,1553,1625,1687,1763,1826,1883,2004,2062,2123,2180,2260,2397,2484,2559,2652,2732,2816,2955,3033,3112,3264,3353,3429,3486,3542,3608,3686,3767,3838,3926,4004,4081,4155,4234,4344,4434,4526,4618,4719,4793,4875,4976,5026,5109,5175,5267,5354,5416,5480,5543,5616,5739,5852,5956,6064,6125,6185,6271,6357,6434,6513"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3049,3130,3210,3298,3401,4224,4325,4453,5262,5323,5388,5808,6057,12420,12515,12579,12651,12713,12789,12852,12909,13030,13088,13149,13206,13286,13423,13510,13585,13678,13758,13842,13981,14059,14138,14290,14379,14455,14512,14568,14634,14712,14793,14864,14952,15030,15107,15181,15260,15370,15460,15552,15644,15745,15819,15901,16002,16052,16135,16201,16293,16380,16442,16506,16569,16642,16765,16878,16982,17090,17151,17388,17733,17819,17972", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,80,79,87,102,91,100,127,83,60,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,74,92,79,83,138,77,78,151,88,75,56,55,65,77,80,70,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85,85,76,78", "endOffsets": "319,3125,3205,3293,3396,3488,4320,4448,4532,5318,5383,5480,5883,6117,12510,12574,12646,12708,12784,12847,12904,13025,13083,13144,13201,13281,13418,13505,13580,13673,13753,13837,13976,14054,14133,14285,14374,14450,14507,14563,14629,14707,14788,14859,14947,15025,15102,15176,15255,15365,15455,15547,15639,15740,15814,15896,15997,16047,16130,16196,16288,16375,16437,16501,16564,16637,16760,16873,16977,17085,17146,17206,17469,17814,17891,18046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,390,493,582,660,751,842,928,1014,1105,1181,1256,1335,1410,1492,1563", "endColumns": "96,83,103,102,88,77,90,90,85,85,90,75,74,78,74,81,70,119", "endOffsets": "197,281,385,488,577,655,746,837,923,1009,1100,1176,1251,1330,1405,1487,1558,1678"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,539,540,541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4537,4634,4966,5070,5173,5888,5966,17211,17302,17474,17560,17896,18051,18126,18205,56739,56821,56892", "endColumns": "96,83,103,102,88,77,90,90,85,85,90,75,74,78,74,81,70,119", "endOffsets": "4629,4713,5065,5168,5257,5961,6052,17297,17383,17555,17646,17967,18121,18200,18275,56816,56887,57007"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3493,3589,3691,3790,3887,3993,4098,18280", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "3584,3686,3785,3882,3988,4093,4219,18376"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,101", "endOffsets": "148,250"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "57012,57110", "endColumns": "97,101", "endOffsets": "57105,57207"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-ca\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "130", "endOffsets": "325"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4718", "endColumns": "134", "endOffsets": "4848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,254,404,480,565,671,826,1012,1169,1296,1400,1540,1600,1659,1748,1855,1931,2076,2192,2275,2426,2503,2600,2701,2827,2935,3042,3139,3210,3283,3405,3501,3714,3896,3998,4144,4248,4442,4556,4700,4805,4945,5061,5197,5299,5402,5539,5642,5781,5891,6053,6135,6279,6355,6487,6591,6670,6748,6818,6889,6961,7032,7157,7265,7421,7518,7628,7734,7883,7985,8086,8222,8316,8423,8528,8642,8743,8807,8861,8916,8972,9021,9071,9139,9193,9262,9315,9359,9429,9478,9534,9590,9653,9708,9758,9808,9876,9926,9982,10027,10090,10148,10206,10341,10455,10533,10594,10679,10748,10816,10926,11012,11087,11167,11245,11312,11409,11484,11575,11663,11801,11880,11972,12054,12150,12254,12335,12431,12532,12640,12738,12815,12909,12978,13058,13178,13328,13404,13481,13580,13673,15724,15839,16049,16095,16275,16462,16550,16695,16795,16935,17119,17253,17438,17515,17598,17668,17760,17876,17921,18005,18118,18210,18335,18462,18600,18707,18820,18886,18946,19007,19063,19129,19212,19306,19424,19495,19574,19844,20085,20217,20348,20489,20582,20721,20849,21004,21180,21273,21373,21461,21580,21692,21850,21941,22021,22082,22340,22434,22520,22586,22651,22773,22906,23022,23189,23380,23457,23527,23592,23778,23858,23950,24026,24119,24287,24383,24467,24583,24676,24827,24946,25008,25155,25227,25298,25358,25434,25483,25548,25652,25704,25777,25976,26094,26190,26255,26419,26553,26684,26804,27078,27179,27276,27372,27556,27760,27856,28052,28169,28314,28422,28501,28590,28662,28834,29019,29157,29206,29290,29447,29577,29664,29745,29839,29919,29996,30072,30135,30216,30290,30367,30462,30530,30601,30685,30754,30842,31022,31179,31265,31319,31402,31547,31619,31713,31797,31878,31960,32223,32278,32353,32431,32512,32613,32795,32881,32955,33040,33119,33192,33289,33380,33518,33601,33654,33735,33812,33877,33955,34065,34168,34274,34396,34475,34524,34575,34670,34790,34876,34995,35162,35275,35360,35497,35627,35786,35912,36022,36156,36284,36478,36661,36792,36922,37010,37119,37249,37368,37477,37603,37676,38091,38170,38256,38336", "endColumns": "198,149,75,84,105,154,185,156,126,103,139,59,58,88,106,75,144,115,82,150,76,96,100,125,107,106,96,70,72,121,95,212,181,101,145,103,193,113,143,104,139,115,135,101,102,136,102,138,109,161,81,143,75,131,103,78,77,69,70,71,70,124,107,155,96,109,105,148,101,100,135,93,106,104,113,100,63,53,54,55,48,49,67,53,68,52,43,69,48,55,55,62,54,49,49,67,49,55,44,62,57,57,134,113,77,60,84,68,67,109,85,74,79,77,66,96,74,90,87,137,78,91,81,95,103,80,95,100,107,97,76,93,68,79,119,149,75,76,98,92,2050,114,209,45,179,186,87,144,99,139,183,133,184,76,82,69,91,115,44,83,112,91,124,126,137,106,112,65,59,60,55,65,82,93,117,70,78,269,240,131,130,140,92,138,127,154,175,92,99,87,118,111,157,90,79,60,257,93,85,65,64,121,132,115,166,190,76,69,64,185,79,91,75,92,167,95,83,115,92,150,118,61,146,71,70,59,75,48,64,103,51,72,198,117,95,64,163,133,130,119,273,100,96,95,183,203,95,195,116,144,107,78,88,71,171,184,137,48,83,156,129,86,80,93,79,76,75,62,80,73,76,94,67,70,83,68,87,179,156,85,53,82,144,71,93,83,80,81,262,54,74,77,80,100,181,85,73,84,78,72,96,90,137,82,52,80,76,64,77,109,102,105,121,78,48,50,94,119,85,118,166,112,84,136,129,158,125,109,133,127,193,182,130,129,87,108,129,118,108,125,72,414,78,85,79,76", "endOffsets": "249,399,475,560,666,821,1007,1164,1291,1395,1535,1595,1654,1743,1850,1926,2071,2187,2270,2421,2498,2595,2696,2822,2930,3037,3134,3205,3278,3400,3496,3709,3891,3993,4139,4243,4437,4551,4695,4800,4940,5056,5192,5294,5397,5534,5637,5776,5886,6048,6130,6274,6350,6482,6586,6665,6743,6813,6884,6956,7027,7152,7260,7416,7513,7623,7729,7878,7980,8081,8217,8311,8418,8523,8637,8738,8802,8856,8911,8967,9016,9066,9134,9188,9257,9310,9354,9424,9473,9529,9585,9648,9703,9753,9803,9871,9921,9977,10022,10085,10143,10201,10336,10450,10528,10589,10674,10743,10811,10921,11007,11082,11162,11240,11307,11404,11479,11570,11658,11796,11875,11967,12049,12145,12249,12330,12426,12527,12635,12733,12810,12904,12973,13053,13173,13323,13399,13476,13575,13668,15719,15834,16044,16090,16270,16457,16545,16690,16790,16930,17114,17248,17433,17510,17593,17663,17755,17871,17916,18000,18113,18205,18330,18457,18595,18702,18815,18881,18941,19002,19058,19124,19207,19301,19419,19490,19569,19839,20080,20212,20343,20484,20577,20716,20844,20999,21175,21268,21368,21456,21575,21687,21845,21936,22016,22077,22335,22429,22515,22581,22646,22768,22901,23017,23184,23375,23452,23522,23587,23773,23853,23945,24021,24114,24282,24378,24462,24578,24671,24822,24941,25003,25150,25222,25293,25353,25429,25478,25543,25647,25699,25772,25971,26089,26185,26250,26414,26548,26679,26799,27073,27174,27271,27367,27551,27755,27851,28047,28164,28309,28417,28496,28585,28657,28829,29014,29152,29201,29285,29442,29572,29659,29740,29834,29914,29991,30067,30130,30211,30285,30362,30457,30525,30596,30680,30749,30837,31017,31174,31260,31314,31397,31542,31614,31708,31792,31873,31955,32218,32273,32348,32426,32507,32608,32790,32876,32950,33035,33114,33187,33284,33375,33513,33596,33649,33730,33807,33872,33950,34060,34163,34269,34391,34470,34519,34570,34665,34785,34871,34990,35157,35270,35355,35492,35622,35781,35907,36017,36151,36279,36473,36656,36787,36917,37005,37114,37244,37363,37472,37598,37671,38086,38165,38251,38331,38408"}, "to": {"startLines": "193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18381,18580,18730,18806,18891,18997,19152,19338,19495,19622,19726,19866,19926,19985,20074,20181,20257,20402,20518,20601,20752,20829,20926,21027,21153,21261,21368,21465,21536,21609,21731,21827,22040,22222,22324,22470,22574,22768,22882,23026,23131,23271,23387,23523,23625,23728,23865,23968,24107,24217,24379,24461,24605,24681,24813,24917,24996,25074,25144,25215,25287,25358,25483,25591,25747,25844,25954,26060,26209,26311,26412,26548,26642,26749,26854,26968,27069,27133,27187,27242,27298,27347,27397,27465,27519,27588,27641,27685,27755,27804,27860,27916,27979,28034,28084,28134,28202,28252,28308,28353,28416,28474,28532,28667,28781,28859,28920,29005,29074,29142,29252,29338,29413,29493,29571,29638,29735,29810,29901,29989,30127,30206,30298,30380,30476,30580,30661,30757,30858,30966,31064,31141,31235,31304,31384,31504,31654,31730,31807,31906,31999,34050,34165,34375,34421,34601,34788,34876,35021,35121,35261,35445,35579,35764,35841,35924,35994,36086,36202,36247,36331,36444,36536,36661,36788,36926,37033,37146,37212,37272,37333,37389,37455,37538,37632,37750,37821,37900,38170,38411,38543,38674,38815,38908,39047,39175,39330,39506,39599,39699,39787,39906,40018,40176,40267,40347,40408,40666,40760,40846,40912,40977,41099,41232,41348,41515,41706,41783,41853,41918,42104,42184,42276,42352,42445,42613,42709,42793,42909,43002,43153,43272,43334,43481,43553,43624,43684,43760,43809,43874,43978,44030,44103,44302,44420,44516,44581,44745,44879,45010,45130,45404,45505,45602,45698,45882,46086,46182,46378,46495,46640,46748,46827,46916,46988,47160,47345,47483,47532,47616,47773,47903,47990,48071,48165,48245,48322,48398,48461,48542,48616,48693,48788,48856,48927,49011,49080,49168,49348,49505,49591,49645,49728,49873,49945,50039,50123,50204,50286,50549,50604,50679,50757,50838,50939,51121,51207,51281,51366,51445,51518,51615,51706,51844,51927,51980,52061,52138,52203,52281,52391,52494,52600,52722,52801,52850,52901,52996,53116,53202,53321,53488,53601,53686,53823,53953,54112,54238,54348,54482,54610,54804,54987,55118,55248,55336,55445,55575,55694,55803,55929,56002,56417,56496,56582,56662", "endColumns": "198,149,75,84,105,154,185,156,126,103,139,59,58,88,106,75,144,115,82,150,76,96,100,125,107,106,96,70,72,121,95,212,181,101,145,103,193,113,143,104,139,115,135,101,102,136,102,138,109,161,81,143,75,131,103,78,77,69,70,71,70,124,107,155,96,109,105,148,101,100,135,93,106,104,113,100,63,53,54,55,48,49,67,53,68,52,43,69,48,55,55,62,54,49,49,67,49,55,44,62,57,57,134,113,77,60,84,68,67,109,85,74,79,77,66,96,74,90,87,137,78,91,81,95,103,80,95,100,107,97,76,93,68,79,119,149,75,76,98,92,2050,114,209,45,179,186,87,144,99,139,183,133,184,76,82,69,91,115,44,83,112,91,124,126,137,106,112,65,59,60,55,65,82,93,117,70,78,269,240,131,130,140,92,138,127,154,175,92,99,87,118,111,157,90,79,60,257,93,85,65,64,121,132,115,166,190,76,69,64,185,79,91,75,92,167,95,83,115,92,150,118,61,146,71,70,59,75,48,64,103,51,72,198,117,95,64,163,133,130,119,273,100,96,95,183,203,95,195,116,144,107,78,88,71,171,184,137,48,83,156,129,86,80,93,79,76,75,62,80,73,76,94,67,70,83,68,87,179,156,85,53,82,144,71,93,83,80,81,262,54,74,77,80,100,181,85,73,84,78,72,96,90,137,82,52,80,76,64,77,109,102,105,121,78,48,50,94,119,85,118,166,112,84,136,129,158,125,109,133,127,193,182,130,129,87,108,129,118,108,125,72,414,78,85,79,76", "endOffsets": "18575,18725,18801,18886,18992,19147,19333,19490,19617,19721,19861,19921,19980,20069,20176,20252,20397,20513,20596,20747,20824,20921,21022,21148,21256,21363,21460,21531,21604,21726,21822,22035,22217,22319,22465,22569,22763,22877,23021,23126,23266,23382,23518,23620,23723,23860,23963,24102,24212,24374,24456,24600,24676,24808,24912,24991,25069,25139,25210,25282,25353,25478,25586,25742,25839,25949,26055,26204,26306,26407,26543,26637,26744,26849,26963,27064,27128,27182,27237,27293,27342,27392,27460,27514,27583,27636,27680,27750,27799,27855,27911,27974,28029,28079,28129,28197,28247,28303,28348,28411,28469,28527,28662,28776,28854,28915,29000,29069,29137,29247,29333,29408,29488,29566,29633,29730,29805,29896,29984,30122,30201,30293,30375,30471,30575,30656,30752,30853,30961,31059,31136,31230,31299,31379,31499,31649,31725,31802,31901,31994,34045,34160,34370,34416,34596,34783,34871,35016,35116,35256,35440,35574,35759,35836,35919,35989,36081,36197,36242,36326,36439,36531,36656,36783,36921,37028,37141,37207,37267,37328,37384,37450,37533,37627,37745,37816,37895,38165,38406,38538,38669,38810,38903,39042,39170,39325,39501,39594,39694,39782,39901,40013,40171,40262,40342,40403,40661,40755,40841,40907,40972,41094,41227,41343,41510,41701,41778,41848,41913,42099,42179,42271,42347,42440,42608,42704,42788,42904,42997,43148,43267,43329,43476,43548,43619,43679,43755,43804,43869,43973,44025,44098,44297,44415,44511,44576,44740,44874,45005,45125,45399,45500,45597,45693,45877,46081,46177,46373,46490,46635,46743,46822,46911,46983,47155,47340,47478,47527,47611,47768,47898,47985,48066,48160,48240,48317,48393,48456,48537,48611,48688,48783,48851,48922,49006,49075,49163,49343,49500,49586,49640,49723,49868,49940,50034,50118,50199,50281,50544,50599,50674,50752,50833,50934,51116,51202,51276,51361,51440,51513,51610,51701,51839,51922,51975,52056,52133,52198,52276,52386,52489,52595,52717,52796,52845,52896,52991,53111,53197,53316,53483,53596,53681,53818,53948,54107,54233,54343,54477,54605,54799,54982,55113,55243,55331,55440,55570,55689,55798,55924,55997,56412,56491,56577,56657,56734"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "324,447,552,659,742,848,974,1058,1137,1228,1321,1414,1509,1607,1700,1793,1887,1978,2069,2150,2261,2369,2467,2577,2682,2790,2950,17651", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "442,547,654,737,843,969,1053,1132,1223,1316,1409,1504,1602,1695,1788,1882,1973,2064,2145,2256,2364,2462,2572,2677,2785,2945,3044,17728"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,414,534,634,732,847,989,1104,1263,1347,1445,1543,1644,1761,1890,1993,2134,2274,2415,2581,2714,2831,2952,3081,3180,3277,3398,3543,3649,3762,3876,4015,4160,4269,4376,4462,4563,4664,4750,4836,4947,5027,5111,5212,5320,5419,5523,5610,5723,5823,5930,6049,6129,6246", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "171,290,409,529,629,727,842,984,1099,1258,1342,1440,1538,1639,1756,1885,1988,2129,2269,2410,2576,2709,2826,2947,3076,3175,3272,3393,3538,3644,3757,3871,4010,4155,4264,4371,4457,4558,4659,4745,4831,4942,5022,5106,5207,5315,5414,5518,5605,5718,5818,5925,6044,6124,6241,6348"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6122,6243,6362,6481,6601,6701,6799,6914,7056,7171,7330,7414,7512,7610,7711,7828,7957,8060,8201,8341,8482,8648,8781,8898,9019,9148,9247,9344,9465,9610,9716,9829,9943,10082,10227,10336,10443,10529,10630,10731,10817,10903,11014,11094,11178,11279,11387,11486,11590,11677,11790,11890,11997,12116,12196,12313", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "6238,6357,6476,6596,6696,6794,6909,7051,7166,7325,7409,7507,7605,7706,7823,7952,8055,8196,8336,8477,8643,8776,8893,9014,9143,9242,9339,9460,9605,9711,9824,9938,10077,10222,10331,10438,10524,10625,10726,10812,10898,11009,11089,11173,11274,11382,11481,11585,11672,11785,11885,11992,12111,12191,12308,12415"}}]}]}