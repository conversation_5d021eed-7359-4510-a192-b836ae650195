package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.repo.StockRepository;
import com.thedasagroup.suminative.domain.cloud_print.CloudPrintUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesCloudPrintUseCaseFactory implements Factory<CloudPrintUseCase> {
  private final Provider<StockRepository> stockRepositoryProvider;

  public AppUseCaseModule_ProvidesCloudPrintUseCaseFactory(
      Provider<StockRepository> stockRepositoryProvider) {
    this.stockRepositoryProvider = stockRepositoryProvider;
  }

  @Override
  public CloudPrintUseCase get() {
    return providesCloudPrintUseCase(stockRepositoryProvider.get());
  }

  public static AppUseCaseModule_ProvidesCloudPrintUseCaseFactory create(
      Provider<StockRepository> stockRepositoryProvider) {
    return new AppUseCaseModule_ProvidesCloudPrintUseCaseFactory(stockRepositoryProvider);
  }

  public static CloudPrintUseCase providesCloudPrintUseCase(StockRepository stockRepository) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesCloudPrintUseCase(stockRepository));
  }
}
