{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-gl/values-gl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,294,417,537,638,734,847,985,1103,1256,1340,1443,1540,1641,1759,1877,1985,2124,2262,2414,2599,2734,2853,2975,3100,3198,3295,3416,3564,3665,3778,3892,4032,4177,4287,4393,4479,4575,4671,4758,4844,4950,5032,5116,5217,5324,5415,5514,5602,5715,5816,5919,6043,6125,6243", "endColumns": "120,117,122,119,100,95,112,137,117,152,83,102,96,100,117,117,107,138,137,151,184,134,118,121,124,97,96,120,147,100,112,113,139,144,109,105,85,95,95,86,85,105,81,83,100,106,90,98,87,112,100,102,123,81,117,107", "endOffsets": "171,289,412,532,633,729,842,980,1098,1251,1335,1438,1535,1636,1754,1872,1980,2119,2257,2409,2594,2729,2848,2970,3095,3193,3290,3411,3559,3660,3773,3887,4027,4172,4282,4388,4474,4570,4666,4753,4839,4945,5027,5111,5212,5319,5410,5509,5597,5710,5811,5914,6038,6120,6238,6346"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6154,6275,6393,6516,6636,6737,6833,6946,7084,7202,7355,7439,7542,7639,7740,7858,7976,8084,8223,8361,8513,8698,8833,8952,9074,9199,9297,9394,9515,9663,9764,9877,9991,10131,10276,10386,10492,10578,10674,10770,10857,10943,11049,11131,11215,11316,11423,11514,11613,11701,11814,11915,12018,12142,12224,12342", "endColumns": "120,117,122,119,100,95,112,137,117,152,83,102,96,100,117,117,107,138,137,151,184,134,118,121,124,97,96,120,147,100,112,113,139,144,109,105,85,95,95,86,85,105,81,83,100,106,90,98,87,112,100,102,123,81,117,107", "endOffsets": "6270,6388,6511,6631,6732,6828,6941,7079,7197,7350,7434,7537,7634,7735,7853,7971,8079,8218,8356,8508,8693,8828,8947,9069,9194,9292,9389,9510,9658,9759,9872,9986,10126,10271,10381,10487,10573,10669,10765,10852,10938,11044,11126,11210,11311,11418,11509,11608,11696,11809,11910,12013,12137,12219,12337,12445"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,313,421,506,607,735,821,902,994,1088,1185,1279,1379,1473,1569,1664,1756,1848,1929,2037,2144,2251,2360,2465,2579,2756,2855", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "204,308,416,501,602,730,816,897,989,1083,1180,1274,1374,1468,1564,1659,1751,1843,1924,2032,2139,2246,2355,2460,2574,2751,2850,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,429,533,641,726,827,955,1041,1122,1214,1308,1405,1499,1599,1693,1789,1884,1976,2068,2149,2257,2364,2471,2580,2685,2799,2976,17678", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "424,528,636,721,822,950,1036,1117,1209,1303,1400,1494,1594,1688,1784,1879,1971,2063,2144,2252,2359,2466,2575,2680,2794,2971,3070,17756"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,254,404,480,565,671,826,1012,1169,1296,1400,1540,1600,1659,1748,1855,1931,2076,2192,2275,2426,2503,2600,2701,2827,2935,3042,3139,3210,3283,3405,3501,3714,3896,3998,4144,4248,4442,4556,4700,4805,4945,5061,5197,5299,5402,5539,5642,5781,5891,6053,6135,6279,6355,6487,6591,6670,6748,6818,6889,6961,7032,7157,7265,7421,7518,7628,7734,7883,7985,8086,8222,8316,8423,8528,8642,8743,8807,8861,8916,8972,9021,9071,9139,9193,9262,9315,9359,9429,9478,9534,9590,9653,9708,9758,9808,9876,9926,9982,10027,10090,10148,10206,10341,10455,10533,10594,10679,10748,10816,10926,11012,11087,11167,11245,11312,11409,11484,11575,11663,11801,11880,11972,12054,12150,12254,12335,12431,12532,12640,12738,12815,12909,12978,13058,13178,13328,13404,13481,13580,13673,15724,15839,16049,16095,16275,16462,16550,16695,16795,16935,17119,17253,17438,17515,17598,17668,17760,17876,17921,18005,18118,18210,18335,18462,18600,18707,18820,18886,18946,19007,19063,19129,19212,19306,19424,19495,19574,19844,20085,20217,20348,20489,20582,20721,20849,21004,21180,21273,21373,21461,21580,21692,21850,21941,22021,22082,22340,22434,22520,22586,22651,22773,22906,23022,23189,23380,23457,23527,23592,23778,23858,23950,24026,24119,24287,24383,24467,24583,24676,24827,24946,25008,25155,25227,25298,25358,25434,25483,25548,25652,25704,25777,25976,26094,26190,26255,26419,26553,26684,26804,27078,27179,27276,27372,27556,27760,27856,28052,28169,28314,28422,28501,28590,28662,28834,29019,29157,29206,29290,29447,29577,29664,29745,29839,29919,29996,30072,30135,30216,30290,30367,30462,30530,30601,30685,30754,30842,31022,31179,31265,31319,31402,31547,31619,31713,31797,31878,31960,32223,32278,32353,32431,32512,32613,32795,32881,32955,33040,33119,33192,33289,33380,33518,33601,33654,33735,33812,33877,33955,34065,34168,34274,34396,34475,34524,34575,34670,34790,34876,34995,35162,35275,35360,35497,35627,35786,35912,36022,36156,36284,36478,36661,36792,36922,37010,37119,37249,37368,37477,37603,37676,38091,38170,38256,38336", "endColumns": "198,149,75,84,105,154,185,156,126,103,139,59,58,88,106,75,144,115,82,150,76,96,100,125,107,106,96,70,72,121,95,212,181,101,145,103,193,113,143,104,139,115,135,101,102,136,102,138,109,161,81,143,75,131,103,78,77,69,70,71,70,124,107,155,96,109,105,148,101,100,135,93,106,104,113,100,63,53,54,55,48,49,67,53,68,52,43,69,48,55,55,62,54,49,49,67,49,55,44,62,57,57,134,113,77,60,84,68,67,109,85,74,79,77,66,96,74,90,87,137,78,91,81,95,103,80,95,100,107,97,76,93,68,79,119,149,75,76,98,92,2050,114,209,45,179,186,87,144,99,139,183,133,184,76,82,69,91,115,44,83,112,91,124,126,137,106,112,65,59,60,55,65,82,93,117,70,78,269,240,131,130,140,92,138,127,154,175,92,99,87,118,111,157,90,79,60,257,93,85,65,64,121,132,115,166,190,76,69,64,185,79,91,75,92,167,95,83,115,92,150,118,61,146,71,70,59,75,48,64,103,51,72,198,117,95,64,163,133,130,119,273,100,96,95,183,203,95,195,116,144,107,78,88,71,171,184,137,48,83,156,129,86,80,93,79,76,75,62,80,73,76,94,67,70,83,68,87,179,156,85,53,82,144,71,93,83,80,81,262,54,74,77,80,100,181,85,73,84,78,72,96,90,137,82,52,80,76,64,77,109,102,105,121,78,48,50,94,119,85,118,166,112,84,136,129,158,125,109,133,127,193,182,130,129,87,108,129,118,108,125,72,414,78,85,79,76", "endOffsets": "249,399,475,560,666,821,1007,1164,1291,1395,1535,1595,1654,1743,1850,1926,2071,2187,2270,2421,2498,2595,2696,2822,2930,3037,3134,3205,3278,3400,3496,3709,3891,3993,4139,4243,4437,4551,4695,4800,4940,5056,5192,5294,5397,5534,5637,5776,5886,6048,6130,6274,6350,6482,6586,6665,6743,6813,6884,6956,7027,7152,7260,7416,7513,7623,7729,7878,7980,8081,8217,8311,8418,8523,8637,8738,8802,8856,8911,8967,9016,9066,9134,9188,9257,9310,9354,9424,9473,9529,9585,9648,9703,9753,9803,9871,9921,9977,10022,10085,10143,10201,10336,10450,10528,10589,10674,10743,10811,10921,11007,11082,11162,11240,11307,11404,11479,11570,11658,11796,11875,11967,12049,12145,12249,12330,12426,12527,12635,12733,12810,12904,12973,13053,13173,13323,13399,13476,13575,13668,15719,15834,16044,16090,16270,16457,16545,16690,16790,16930,17114,17248,17433,17510,17593,17663,17755,17871,17916,18000,18113,18205,18330,18457,18595,18702,18815,18881,18941,19002,19058,19124,19207,19301,19419,19490,19569,19839,20080,20212,20343,20484,20577,20716,20844,20999,21175,21268,21368,21456,21575,21687,21845,21936,22016,22077,22335,22429,22515,22581,22646,22768,22901,23017,23184,23375,23452,23522,23587,23773,23853,23945,24021,24114,24282,24378,24462,24578,24671,24822,24941,25003,25150,25222,25293,25353,25429,25478,25543,25647,25699,25772,25971,26089,26185,26250,26414,26548,26679,26799,27073,27174,27271,27367,27551,27755,27851,28047,28164,28309,28417,28496,28585,28657,28829,29014,29152,29201,29285,29442,29572,29659,29740,29834,29914,29991,30067,30130,30211,30285,30362,30457,30525,30596,30680,30749,30837,31017,31174,31260,31314,31397,31542,31614,31708,31792,31873,31955,32218,32273,32348,32426,32507,32608,32790,32876,32950,33035,33114,33187,33284,33375,33513,33596,33649,33730,33807,33872,33950,34060,34163,34269,34391,34470,34519,34570,34665,34785,34871,34990,35157,35270,35355,35492,35622,35781,35907,36017,36151,36279,36473,36656,36787,36917,37005,37114,37244,37363,37472,37598,37671,38086,38165,38251,38331,38408"}, "to": {"startLines": "193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18419,18618,18768,18844,18929,19035,19190,19376,19533,19660,19764,19904,19964,20023,20112,20219,20295,20440,20556,20639,20790,20867,20964,21065,21191,21299,21406,21503,21574,21647,21769,21865,22078,22260,22362,22508,22612,22806,22920,23064,23169,23309,23425,23561,23663,23766,23903,24006,24145,24255,24417,24499,24643,24719,24851,24955,25034,25112,25182,25253,25325,25396,25521,25629,25785,25882,25992,26098,26247,26349,26450,26586,26680,26787,26892,27006,27107,27171,27225,27280,27336,27385,27435,27503,27557,27626,27679,27723,27793,27842,27898,27954,28017,28072,28122,28172,28240,28290,28346,28391,28454,28512,28570,28705,28819,28897,28958,29043,29112,29180,29290,29376,29451,29531,29609,29676,29773,29848,29939,30027,30165,30244,30336,30418,30514,30618,30699,30795,30896,31004,31102,31179,31273,31342,31422,31542,31692,31768,31845,31944,32037,34088,34203,34413,34459,34639,34826,34914,35059,35159,35299,35483,35617,35802,35879,35962,36032,36124,36240,36285,36369,36482,36574,36699,36826,36964,37071,37184,37250,37310,37371,37427,37493,37576,37670,37788,37859,37938,38208,38449,38581,38712,38853,38946,39085,39213,39368,39544,39637,39737,39825,39944,40056,40214,40305,40385,40446,40704,40798,40884,40950,41015,41137,41270,41386,41553,41744,41821,41891,41956,42142,42222,42314,42390,42483,42651,42747,42831,42947,43040,43191,43310,43372,43519,43591,43662,43722,43798,43847,43912,44016,44068,44141,44340,44458,44554,44619,44783,44917,45048,45168,45442,45543,45640,45736,45920,46124,46220,46416,46533,46678,46786,46865,46954,47026,47198,47383,47521,47570,47654,47811,47941,48028,48109,48203,48283,48360,48436,48499,48580,48654,48731,48826,48894,48965,49049,49118,49206,49386,49543,49629,49683,49766,49911,49983,50077,50161,50242,50324,50587,50642,50717,50795,50876,50977,51159,51245,51319,51404,51483,51556,51653,51744,51882,51965,52018,52099,52176,52241,52319,52429,52532,52638,52760,52839,52888,52939,53034,53154,53240,53359,53526,53639,53724,53861,53991,54150,54276,54386,54520,54648,54842,55025,55156,55286,55374,55483,55613,55732,55841,55967,56040,56455,56534,56620,56700", "endColumns": "198,149,75,84,105,154,185,156,126,103,139,59,58,88,106,75,144,115,82,150,76,96,100,125,107,106,96,70,72,121,95,212,181,101,145,103,193,113,143,104,139,115,135,101,102,136,102,138,109,161,81,143,75,131,103,78,77,69,70,71,70,124,107,155,96,109,105,148,101,100,135,93,106,104,113,100,63,53,54,55,48,49,67,53,68,52,43,69,48,55,55,62,54,49,49,67,49,55,44,62,57,57,134,113,77,60,84,68,67,109,85,74,79,77,66,96,74,90,87,137,78,91,81,95,103,80,95,100,107,97,76,93,68,79,119,149,75,76,98,92,2050,114,209,45,179,186,87,144,99,139,183,133,184,76,82,69,91,115,44,83,112,91,124,126,137,106,112,65,59,60,55,65,82,93,117,70,78,269,240,131,130,140,92,138,127,154,175,92,99,87,118,111,157,90,79,60,257,93,85,65,64,121,132,115,166,190,76,69,64,185,79,91,75,92,167,95,83,115,92,150,118,61,146,71,70,59,75,48,64,103,51,72,198,117,95,64,163,133,130,119,273,100,96,95,183,203,95,195,116,144,107,78,88,71,171,184,137,48,83,156,129,86,80,93,79,76,75,62,80,73,76,94,67,70,83,68,87,179,156,85,53,82,144,71,93,83,80,81,262,54,74,77,80,100,181,85,73,84,78,72,96,90,137,82,52,80,76,64,77,109,102,105,121,78,48,50,94,119,85,118,166,112,84,136,129,158,125,109,133,127,193,182,130,129,87,108,129,118,108,125,72,414,78,85,79,76", "endOffsets": "18613,18763,18839,18924,19030,19185,19371,19528,19655,19759,19899,19959,20018,20107,20214,20290,20435,20551,20634,20785,20862,20959,21060,21186,21294,21401,21498,21569,21642,21764,21860,22073,22255,22357,22503,22607,22801,22915,23059,23164,23304,23420,23556,23658,23761,23898,24001,24140,24250,24412,24494,24638,24714,24846,24950,25029,25107,25177,25248,25320,25391,25516,25624,25780,25877,25987,26093,26242,26344,26445,26581,26675,26782,26887,27001,27102,27166,27220,27275,27331,27380,27430,27498,27552,27621,27674,27718,27788,27837,27893,27949,28012,28067,28117,28167,28235,28285,28341,28386,28449,28507,28565,28700,28814,28892,28953,29038,29107,29175,29285,29371,29446,29526,29604,29671,29768,29843,29934,30022,30160,30239,30331,30413,30509,30613,30694,30790,30891,30999,31097,31174,31268,31337,31417,31537,31687,31763,31840,31939,32032,34083,34198,34408,34454,34634,34821,34909,35054,35154,35294,35478,35612,35797,35874,35957,36027,36119,36235,36280,36364,36477,36569,36694,36821,36959,37066,37179,37245,37305,37366,37422,37488,37571,37665,37783,37854,37933,38203,38444,38576,38707,38848,38941,39080,39208,39363,39539,39632,39732,39820,39939,40051,40209,40300,40380,40441,40699,40793,40879,40945,41010,41132,41265,41381,41548,41739,41816,41886,41951,42137,42217,42309,42385,42478,42646,42742,42826,42942,43035,43186,43305,43367,43514,43586,43657,43717,43793,43842,43907,44011,44063,44136,44335,44453,44549,44614,44778,44912,45043,45163,45437,45538,45635,45731,45915,46119,46215,46411,46528,46673,46781,46860,46949,47021,47193,47378,47516,47565,47649,47806,47936,48023,48104,48198,48278,48355,48431,48494,48575,48649,48726,48821,48889,48960,49044,49113,49201,49381,49538,49624,49678,49761,49906,49978,50072,50156,50237,50319,50582,50637,50712,50790,50871,50972,51154,51240,51314,51399,51478,51551,51648,51739,51877,51960,52013,52094,52171,52236,52314,52424,52527,52633,52755,52834,52883,52934,53029,53149,53235,53354,53521,53634,53719,53856,53986,54145,54271,54381,54515,54643,54837,55020,55151,55281,55369,55478,55608,55727,55836,55962,56035,56450,56529,56615,56695,56772"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,284,393,494,583,662,755,847,935,1020,1110,1187,1271,1351,1427,1509,1581", "endColumns": "95,82,108,100,88,78,92,91,87,84,89,76,83,79,75,81,71,121", "endOffsets": "196,279,388,489,578,657,750,842,930,1015,1105,1182,1266,1346,1422,1504,1576,1698"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,539,540,541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4565,4661,4985,5094,5195,5919,5998,17238,17330,17503,17588,17923,18078,18162,18242,56777,56859,56931", "endColumns": "95,82,108,100,88,78,92,91,87,84,89,76,83,79,75,81,71,121", "endOffsets": "4656,4739,5089,5190,5279,5993,6086,17325,17413,17583,17673,17995,18157,18237,18313,56854,56926,57048"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,783", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "149,251,351,449,556,662,778,879"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3519,3618,3720,3820,3918,4025,4131,18318", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "3613,3715,3815,3913,4020,4126,4242,18414"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,376", "endColumns": "106,101,111,105", "endOffsets": "157,259,371,477"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4878,5520,5622,5734", "endColumns": "106,101,111,105", "endOffsets": "4980,5617,5729,5835"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,154", "endColumns": "98,100", "endOffsets": "149,250"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "57053,57152", "endColumns": "98,100", "endOffsets": "57147,57248"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,356,436,521,623,719,824,957,1037,1099,1177,1273,1352,1415,1510,1574,1643,1706,1780,1844,1900,2021,2079,2141,2197,2274,2413,2501,2578,2674,2758,2838,2978,3058,3138,3287,3377,3458,3514,3570,3636,3715,3796,3867,3955,4034,4111,4193,4282,4383,4467,4559,4652,4753,4827,4919,5021,5073,5157,5223,5315,5403,5465,5529,5592,5662,5773,5878,5984,6083,6143,6203,6288,6371,6450", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,84,101,95,104,132,79,61,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,76,95,83,79,139,79,79,148,89,80,55,55,65,78,80,70,87,78,76,81,88,100,83,91,92,100,73,91,101,51,83,65,91,87,61,63,62,69,110,104,105,98,59,59,84,82,78,77", "endOffsets": "270,351,431,516,618,714,819,952,1032,1094,1172,1268,1347,1410,1505,1569,1638,1701,1775,1839,1895,2016,2074,2136,2192,2269,2408,2496,2573,2669,2753,2833,2973,3053,3133,3282,3372,3453,3509,3565,3631,3710,3791,3862,3950,4029,4106,4188,4277,4378,4462,4554,4647,4748,4822,4914,5016,5068,5152,5218,5310,5398,5460,5524,5587,5657,5768,5873,5979,6078,6138,6198,6283,6366,6445,6523"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3075,3156,3236,3321,3423,4247,4352,4485,5284,5346,5424,5840,6091,12450,12545,12609,12678,12741,12815,12879,12935,13056,13114,13176,13232,13309,13448,13536,13613,13709,13793,13873,14013,14093,14173,14322,14412,14493,14549,14605,14671,14750,14831,14902,14990,15069,15146,15228,15317,15418,15502,15594,15687,15788,15862,15954,16056,16108,16192,16258,16350,16438,16500,16564,16627,16697,16808,16913,17019,17118,17178,17418,17761,17844,18000", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,80,79,84,101,95,104,132,79,61,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,76,95,83,79,139,79,79,148,89,80,55,55,65,78,80,70,87,78,76,81,88,100,83,91,92,100,73,91,101,51,83,65,91,87,61,63,62,69,110,104,105,98,59,59,84,82,78,77", "endOffsets": "320,3151,3231,3316,3418,3514,4347,4480,4560,5341,5419,5515,5914,6149,12540,12604,12673,12736,12810,12874,12930,13051,13109,13171,13227,13304,13443,13531,13608,13704,13788,13868,14008,14088,14168,14317,14407,14488,14544,14600,14666,14745,14826,14897,14985,15064,15141,15223,15312,15413,15497,15589,15682,15783,15857,15949,16051,16103,16187,16253,16345,16433,16495,16559,16622,16692,16803,16908,17014,17113,17173,17233,17498,17839,17918,18073"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-gl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "129", "endOffsets": "324"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4744", "endColumns": "133", "endOffsets": "4873"}}]}]}