package com.thedasagroup.suminative.ui;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000,\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0005\u001a\u000e\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003\u001a\u0016\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\b\"\u001f\u0010\t\u001a\u0010\u0012\f\u0012\n\u0012\u0006\b\u0001\u0012\u00020\f0\u000b0\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\"\u001f\u0010\u000f\u001a\u0010\u0012\f\u0012\n\u0012\u0006\b\u0001\u0012\u00020\f0\u000b0\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000e\u00a8\u0006\u0011"}, d2 = {"createBitmapFromPicture", "Landroid/graphics/Bitmap;", "picture", "Landroid/graphics/Picture;", "printOrderBitmap", "", "bitmap", "context", "Landroid/content/Context;", "topLevelRoutes", "", "Lcom/thedasagroup/suminative/ui/TopLevelRoute;", "", "getTopLevelRoutes", "()Ljava/util/List;", "topLevelRoutesReservations", "getTopLevelRoutesReservations", "app_stagingGeneralDebug"})
public final class MainActivityKt {
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<com.thedasagroup.suminative.ui.TopLevelRoute<? extends java.lang.Object>> topLevelRoutes = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<com.thedasagroup.suminative.ui.TopLevelRoute<? extends java.lang.Object>> topLevelRoutesReservations = null;
    
    @org.jetbrains.annotations.NotNull()
    public static final android.graphics.Bitmap createBitmapFromPicture(@org.jetbrains.annotations.NotNull()
    android.graphics.Picture picture) {
        return null;
    }
    
    public static final void printOrderBitmap(@org.jetbrains.annotations.NotNull()
    android.graphics.Bitmap bitmap, @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<com.thedasagroup.suminative.ui.TopLevelRoute<? extends java.lang.Object>> getTopLevelRoutes() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<com.thedasagroup.suminative.ui.TopLevelRoute<? extends java.lang.Object>> getTopLevelRoutesReservations() {
        return null;
    }
}