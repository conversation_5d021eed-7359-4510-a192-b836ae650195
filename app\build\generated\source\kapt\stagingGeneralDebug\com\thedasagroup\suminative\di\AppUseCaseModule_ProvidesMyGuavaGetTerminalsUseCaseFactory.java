package com.thedasagroup.suminative.di;

import com.thedasagroup.suminative.data.repo.MyGuavaRepository;
import com.thedasagroup.suminative.domain.myguava.MyGuavaGetTerminalsUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesMyGuavaGetTerminalsUseCaseFactory implements Factory<MyGuavaGetTerminalsUseCase> {
  private final Provider<MyGuavaRepository> myGuavaRepositoryProvider;

  public AppUseCaseModule_ProvidesMyGuavaGetTerminalsUseCaseFactory(
      Provider<MyGuavaRepository> myGuavaRepositoryProvider) {
    this.myGuavaRepositoryProvider = myGuavaRepositoryProvider;
  }

  @Override
  public MyGuavaGetTerminalsUseCase get() {
    return providesMyGuavaGetTerminalsUseCase(myGuavaRepositoryProvider.get());
  }

  public static AppUseCaseModule_ProvidesMyGuavaGetTerminalsUseCaseFactory create(
      Provider<MyGuavaRepository> myGuavaRepositoryProvider) {
    return new AppUseCaseModule_ProvidesMyGuavaGetTerminalsUseCaseFactory(myGuavaRepositoryProvider);
  }

  public static MyGuavaGetTerminalsUseCase providesMyGuavaGetTerminalsUseCase(
      MyGuavaRepository myGuavaRepository) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesMyGuavaGetTerminalsUseCase(myGuavaRepository));
  }
}
