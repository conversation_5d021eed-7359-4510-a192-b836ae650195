package com.thedasagroup.suminative.ui.payment;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000,\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\\\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u00072\u0006\u0010\t\u001a\u00020\n2\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010\u00072\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u00a8\u0006\u000e"}, d2 = {"PaymentActivityScreen", "", "order", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "onDismiss", "Lkotlin/Function0;", "onPaymentSuccess", "Lkotlin/Function1;", "Lcom/thedasagroup/suminative/data/model/response/store_orders/Order2;", "viewModel", "Lcom/thedasagroup/suminative/ui/payment/PaymentViewModel;", "onMakePaymentClickFail", "Lcom/thedasagroup/suminative/data/model/response/my_guava/failure/GuavaFailResponse;", "onPaymentCancelled", "app_stagingGeneralDebug"})
public final class PaymentActivityScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void PaymentActivityScreen(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.store_orders.Order2, kotlin.Unit> onPaymentSuccess, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.payment.PaymentViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.my_guava.failure.GuavaFailResponse, kotlin.Unit> onMakePaymentClickFail, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPaymentCancelled) {
    }
}