package com.thedasagroup.suminative.ui.service;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ServiceComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = MySocketJobService.class
)
@GeneratedEntryPoint
@InstallIn(ServiceComponent.class)
public interface MySocketJobService_GeneratedInjector {
  void injectMySocketJobService(MySocketJobService mySocketJobService);
}
