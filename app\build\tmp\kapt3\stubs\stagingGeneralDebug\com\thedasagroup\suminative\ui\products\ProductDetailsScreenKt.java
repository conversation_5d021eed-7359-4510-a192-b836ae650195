package com.thedasagroup.suminative.ui.products;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000f\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\u001a\u00c6\u0001\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000b2\u001e\u0010\f\u001a\u001a\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\r2\u0018\u0010\u0010\u001a\u0014\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00010\u00112,\u0010\u0012\u001a(\u0012\u0006\u0012\u0004\u0018\u00010\u0014\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u00132&\u0010\u0016\u001a\"\u0012\u0006\u0012\u0004\u0018\u00010\u0014\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u0017H\u0007\u001a\u0014\u0010\u0018\u001a\u00020\u0001*\u00020\u00192\u0006\u0010\b\u001a\u00020\tH\u0003\u001a&\u0010\u001a\u001a\u00020\u00012\b\b\u0002\u0010\u001b\u001a\u00020\u000e2\u0012\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u00010\u001dH\u0007\u001a4\u0010\u001e\u001a\u00020\u00012\u0006\u0010\u001f\u001a\u00020\u00142\u0018\u0010\u001c\u001a\u0014\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u00010\u00112\b\b\u0002\u0010 \u001a\u00020!H\u0007\u001a\\\u0010\"\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010#\u001a\u00020\u00152 \u0010$\u001a\u001c\u0012\u0006\u0012\u0004\u0018\u00010\u0014\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u00010\r2 \u0010%\u001a\u001c\u0012\u0006\u0012\u0004\u0018\u00010\u0014\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u00010\rH\u0007\u001a4\u0010&\u001a\u00020\u00012\u0006\u0010#\u001a\u00020\u00152\u0006\u0010\n\u001a\u00020\u000b2\u001a\u0010$\u001a\u0016\u0012\u0006\u0012\u0004\u0018\u00010\u0014\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u00010\u0011H\u0007\u001a\\\u0010\'\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010#\u001a\u00020\u00152 \u0010$\u001a\u001c\u0012\u0006\u0012\u0004\u0018\u00010\u0014\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u00010\r2 \u0010%\u001a\u001c\u0012\u0006\u0012\u0004\u0018\u00010\u0014\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u00010\rH\u0007\u00a8\u0006("}, d2 = {"ProductDetailsBottomSheet", "", "isBottomSheetVisible", "", "sheetState", "Landroidx/compose/material3/SheetState;", "onDismiss", "Lkotlin/Function0;", "stockItem", "Lcom/thedasagroup/suminative/data/model/response/stock/StockItem;", "viewModel", "Lcom/thedasagroup/suminative/ui/products/ProductsScreenViewModel;", "updateStock", "Lkotlin/Function3;", "", "Lcom/thedasagroup/suminative/data/model/response/options_details/OptionDetails;", "addToCart", "Lkotlin/Function2;", "selectOptionCondition2", "Lkotlin/Function5;", "Lcom/thedasagroup/suminative/data/model/response/store_orders/Option;", "Lcom/thedasagroup/suminative/data/model/request/order/OptionSet;", "selectOptionCondition1", "Lkotlin/Function4;", "ProductImageAndDesc", "Landroidx/compose/foundation/lazy/LazyListScope;", "StockUpdateCounter", "initialStock", "onStockChange", "Lkotlin/Function1;", "OptionStockUpdateCounter", "option", "modifier", "Landroidx/compose/ui/Modifier;", "OptionsUICondition0", "optionSet", "onOptionSelected", "onUpdateStock", "OptionsUICondition1", "OptionsUICondition2", "app_stagingGeneralDebug"})
public final class ProductDetailsScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class, androidx.compose.foundation.ExperimentalFoundationApi.class})
    @androidx.compose.runtime.Composable()
    public static final void ProductDetailsBottomSheet(boolean isBottomSheetVisible, @org.jetbrains.annotations.NotNull()
    androidx.compose.material3.SheetState sheetState, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.stock.StockItem stockItem, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.ProductsScreenViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.Integer, ? super com.thedasagroup.suminative.data.model.response.options_details.OptionDetails, ? super com.thedasagroup.suminative.data.model.response.stock.StockItem, kotlin.Unit> updateStock, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super com.thedasagroup.suminative.data.model.response.stock.StockItem, ? super com.thedasagroup.suminative.data.model.response.options_details.OptionDetails, kotlin.Unit> addToCart, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function5<? super com.thedasagroup.suminative.data.model.response.store_orders.Option, ? super java.lang.Boolean, ? super com.thedasagroup.suminative.data.model.request.order.OptionSet, ? super com.thedasagroup.suminative.data.model.response.options_details.OptionDetails, ? super com.thedasagroup.suminative.data.model.response.stock.StockItem, kotlin.Unit> selectOptionCondition2, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function4<? super com.thedasagroup.suminative.data.model.response.store_orders.Option, ? super com.thedasagroup.suminative.data.model.request.order.OptionSet, ? super com.thedasagroup.suminative.data.model.response.options_details.OptionDetails, ? super com.thedasagroup.suminative.data.model.response.stock.StockItem, kotlin.Unit> selectOptionCondition1) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ProductImageAndDesc(androidx.compose.foundation.lazy.LazyListScope $this$ProductImageAndDesc, com.thedasagroup.suminative.data.model.response.stock.StockItem stockItem) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void StockUpdateCounter(int initialStock, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onStockChange) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void OptionStockUpdateCounter(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.store_orders.Option option, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super com.thedasagroup.suminative.data.model.response.store_orders.Option, kotlin.Unit> onStockChange, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void OptionsUICondition0(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.ProductsScreenViewModel viewModel, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.OptionSet optionSet, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super com.thedasagroup.suminative.data.model.response.store_orders.Option, ? super java.lang.Boolean, ? super com.thedasagroup.suminative.data.model.request.order.OptionSet, kotlin.Unit> onOptionSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super com.thedasagroup.suminative.data.model.response.store_orders.Option, ? super com.thedasagroup.suminative.data.model.request.order.OptionSet, ? super java.lang.Integer, kotlin.Unit> onUpdateStock) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void OptionsUICondition1(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.OptionSet optionSet, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.ProductsScreenViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super com.thedasagroup.suminative.data.model.response.store_orders.Option, ? super com.thedasagroup.suminative.data.model.request.order.OptionSet, kotlin.Unit> onOptionSelected) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void OptionsUICondition2(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.products.ProductsScreenViewModel viewModel, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.OptionSet optionSet, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super com.thedasagroup.suminative.data.model.response.store_orders.Option, ? super java.lang.Boolean, ? super com.thedasagroup.suminative.data.model.request.order.OptionSet, kotlin.Unit> onOptionSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super com.thedasagroup.suminative.data.model.response.store_orders.Option, ? super com.thedasagroup.suminative.data.model.request.order.OptionSet, ? super java.lang.Integer, kotlin.Unit> onUpdateStock) {
    }
}