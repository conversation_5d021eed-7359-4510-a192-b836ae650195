package com.thedasagroup.suminative.ui.reservations;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000v\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010 \n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\u001a\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0007\u001a$\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\b2\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\nH\u0003\u001a>\u0010\u000b\u001a\u00020\u00012\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\r2\u0012\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00010\n2\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\nH\u0003\u001a\u0016\u0010\u0012\u001a\u00020\u00012\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\rH\u0003\u001aF\u0010\u0013\u001a\u00020\u00012\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00100\u00152\u0006\u0010\u0016\u001a\u00020\u00172\u0012\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00010\n2\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\nH\u0003\u001a\u001c\u0010\u0018\u001a\u00020\u0001*\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001dH\u0003\u001a@\u0010\u001e\u001a\u00020\u00012\u0006\u0010\u001f\u001a\u00020\u00102\u0006\u0010\u0016\u001a\u00020\u00172\u0012\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00010\n2\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\nH\u0003\u001a\u001c\u0010 \u001a\u00020\u0001*\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001dH\u0003\u001a\u0010\u0010!\u001a\u00020\u001b2\u0006\u0010\"\u001a\u00020\u001bH\u0002\u001a\u0010\u0010#\u001a\u00020\u001b2\u0006\u0010$\u001a\u00020\bH\u0002\u001a\u0015\u0010%\u001a\u00020&2\u0006\u0010$\u001a\u00020\bH\u0002\u00a2\u0006\u0002\u0010\'\u001a@\u0010(\u001a\u00020\u00012\u0006\u0010)\u001a\u00020*2\f\u0010+\u001a\b\u0012\u0004\u0012\u00020\u00010,2\u0012\u0010-\u001a\u000e\u0012\u0004\u0012\u00020.\u0012\u0004\u0012\u00020\u00010\n2\f\u0010/\u001a\b\u0012\u0004\u0012\u0002000\rH\u0003\u00a8\u00061"}, d2 = {"ReservationsScreen", "", "modifier", "Landroidx/compose/ui/Modifier;", "viewModel", "Lcom/thedasagroup/suminative/ui/reservations/ReservationsViewModel;", "ReservationsTabRow", "selectedTabIndex", "", "onTabSelected", "Lkotlin/Function1;", "ActiveReservationsContent", "reservationsResponse", "Lcom/airbnb/mvrx/Async;", "Lcom/thedasagroup/suminative/data/model/response/reservations/ReservationsResponse;", "onEditReservation", "Lcom/thedasagroup/suminative/data/model/response/reservations/Reservation;", "onCancelReservation", "HistoryReservationsContent", "ReservationsTable", "reservations", "", "showActions", "", "TableHeaderCell", "Landroidx/compose/foundation/layout/RowScope;", "text", "", "weight", "", "ReservationRow", "reservation", "TableCell", "formatDateTime", "dateTimeString", "getReservationStatusText", "status", "getReservationStatusColor", "Landroidx/compose/ui/graphics/Color;", "(I)J", "EditReservationDialog", "editData", "Lcom/thedasagroup/suminative/ui/reservations/EditReservationData;", "onDismiss", "Lkotlin/Function0;", "onSave", "Lcom/thedasagroup/suminative/data/model/request/reservations/CreateReservationRequest;", "createReservationResponse", "Lcom/thedasagroup/suminative/data/model/response/reservations/CreateReservationResponse;", "app_stagingGeneralDebug"})
public final class ReservationsScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ReservationsScreen(@org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.reservations.ReservationsViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ReservationsTabRow(int selectedTabIndex, kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onTabSelected) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ActiveReservationsContent(com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse> reservationsResponse, kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.reservations.Reservation, kotlin.Unit> onEditReservation, kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onCancelReservation) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void HistoryReservationsContent(com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse> reservationsResponse) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ReservationsTable(java.util.List<com.thedasagroup.suminative.data.model.response.reservations.Reservation> reservations, boolean showActions, kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.reservations.Reservation, kotlin.Unit> onEditReservation, kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onCancelReservation) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void TableHeaderCell(androidx.compose.foundation.layout.RowScope $this$TableHeaderCell, java.lang.String text, float weight) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ReservationRow(com.thedasagroup.suminative.data.model.response.reservations.Reservation reservation, boolean showActions, kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.reservations.Reservation, kotlin.Unit> onEditReservation, kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onCancelReservation) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void TableCell(androidx.compose.foundation.layout.RowScope $this$TableCell, java.lang.String text, float weight) {
    }
    
    private static final java.lang.String formatDateTime(java.lang.String dateTimeString) {
        return null;
    }
    
    private static final java.lang.String getReservationStatusText(int status) {
        return null;
    }
    
    private static final long getReservationStatusColor(int status) {
        return 0L;
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void EditReservationDialog(com.thedasagroup.suminative.ui.reservations.EditReservationData editData, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.reservations.CreateReservationRequest, kotlin.Unit> onSave, com.airbnb.mvrx.Async<com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse> createReservationResponse) {
    }
}