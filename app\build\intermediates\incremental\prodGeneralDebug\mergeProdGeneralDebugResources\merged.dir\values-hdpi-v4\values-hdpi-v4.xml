<?xml version="1.0" encoding="utf-8"?>
<resources>
    <dimen name="charge_button_min_height">52dp</dimen>
    <dimen name="login_margin">16dp</dimen>
    <dimen name="setup_instruction_image_size">180dp</dimen>
    <dimen name="setup_instruction_text_size">17sp</dimen>
    <dimen name="setup_margin_top">@dimen/margin_12x</dimen>
    <dimen name="sumup_numeric_key_gap">2dp</dimen>
    <dimen name="sumup_numeric_key_text_size">22sp</dimen>
    <dimen name="terminal_description_margin">@dimen/margin_8x</dimen>
    <dimen name="terminal_money_text_size">72sp</dimen>
    <dimen name="welcome_text_size">14dp</dimen>
    <integer name="setup_instruction_text_lines">6</integer>
    <string name="sumup_setting_image_folder">hdpi</string>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle" parent="Base.Widget.AppCompat.DrawerArrowToggle.Common">
          <item name="barLength">18.66dp</item>
          <item name="gapBetweenBars">3.33dp</item>
          <item name="drawableSize">24dp</item>
     </style>
</resources>