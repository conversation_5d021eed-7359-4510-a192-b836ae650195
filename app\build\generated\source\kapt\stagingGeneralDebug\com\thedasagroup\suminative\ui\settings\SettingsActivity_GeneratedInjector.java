package com.thedasagroup.suminative.ui.settings;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = SettingsActivity.class
)
@GeneratedEntryPoint
@InstallIn(ActivityComponent.class)
public interface SettingsActivity_GeneratedInjector {
  void injectSettingsActivity(SettingsActivity settingsActivity);
}
