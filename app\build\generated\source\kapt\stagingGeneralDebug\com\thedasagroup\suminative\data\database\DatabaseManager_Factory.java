package com.thedasagroup.suminative.data.database;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DatabaseManager_Factory implements Factory<DatabaseManager> {
  private final Provider<Context> contextProvider;

  public DatabaseManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public DatabaseManager get() {
    return newInstance(contextProvider.get());
  }

  public static DatabaseManager_Factory create(Provider<Context> contextProvider) {
    return new DatabaseManager_Factory(contextProvider);
  }

  public static DatabaseManager newInstance(Context context) {
    return new DatabaseManager(context);
  }
}
