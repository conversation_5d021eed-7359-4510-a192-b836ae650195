{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3348,3442,3544,3641,3740,3848,3954,17416", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3437,3539,3636,3735,3843,3949,4069,17512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,87", "endOffsets": "140,228"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "53874,53964", "endColumns": "89,87", "endOffsets": "53959,54047"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4678,5288,5389,5501", "endColumns": "109,100,111,96", "endOffsets": "4783,5384,5496,5593"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,226,355,431,517,631,747,933,1071,1178,1279,1409,1467,1524,1610,1700,1764,1906,2003,2089,2236,2311,2403,2499,2628,2731,2838,2936,3012,3082,3203,3287,3470,3629,3733,3874,3973,4148,4247,4386,4487,4623,4722,4842,4946,5040,5176,5264,5400,5494,5643,5734,5870,5947,6058,6157,6233,6323,6390,6463,6539,6616,6731,6822,6967,7072,7185,7284,7416,7520,7618,7735,7827,7939,8036,8146,8250,8309,8361,8417,8469,8519,8570,8637,8688,8750,8799,8844,8911,8955,9010,9065,9122,9177,9226,9275,9343,9396,9448,9493,9557,9614,9672,9809,9918,9999,10060,10137,10206,10274,10398,10475,10550,10630,10708,10775,10875,10951,11038,11117,11230,11305,11397,11478,11570,11661,11738,11833,11939,12043,12145,12225,12314,12383,12462,12590,12735,12805,12882,12974,13058,15109,15224,15399,15444,15561,15696,15783,15916,16003,16130,16270,16394,16560,16635,16714,16786,16866,16978,17023,17107,17216,17302,17413,17537,17667,17766,17870,17934,17993,18053,18109,18174,18250,18338,18455,18522,18599,18878,19085,19201,19314,19434,19524,19635,19757,19904,20064,20153,20252,20338,20458,20563,20706,20797,20870,20931,21148,21231,21305,21365,21424,21527,21649,21740,21882,22044,22118,22183,22245,22409,22484,22576,22654,22749,22922,23015,23095,23210,23290,23418,23538,23592,23714,23783,23855,23917,23992,24040,24102,24187,24239,24308,24474,24579,24681,24745,24884,25007,25112,25225,25484,25584,25681,25778,25940,26145,26233,26424,26532,26676,26794,26884,26967,27043,27171,27323,27450,27496,27584,27699,27800,27878,27951,28039,28120,28188,28256,28321,28399,28470,28548,28645,28717,28784,28864,28933,29013,29179,29317,29402,29460,29542,29683,29743,29821,29900,29976,30050,30308,30363,30440,30518,30591,30682,30846,30937,31014,31098,31176,31244,31323,31408,31538,31621,31677,31760,31837,31912,31992,32102,32202,32308,32429,32508,32559,32606,32695,32792,32876,32979,33141,33240,33323,33442,33564,33707,33823,33924,34076,34199,34378,34534,34642,34767,34850,34929,35026,35158,35253,35387,35459,35836,35916,36002,36076", "endColumns": "170,128,75,85,113,115,185,137,106,100,129,57,56,85,89,63,141,96,85,146,74,91,95,128,102,106,97,75,69,120,83,182,158,103,140,98,174,98,138,100,135,98,119,103,93,135,87,135,93,148,90,135,76,110,98,75,89,66,72,75,76,114,90,144,104,112,98,131,103,97,116,91,111,96,109,103,58,51,55,51,49,50,66,50,61,48,44,66,43,54,54,56,54,48,48,67,52,51,44,63,56,57,136,108,80,60,76,68,67,123,76,74,79,77,66,99,75,86,78,112,74,91,80,91,90,76,94,105,103,101,79,88,68,78,127,144,69,76,91,83,2050,114,174,44,116,134,86,132,86,126,139,123,165,74,78,71,79,111,44,83,108,85,110,123,129,98,103,63,58,59,55,64,75,87,116,66,76,278,206,115,112,119,89,110,121,146,159,88,98,85,119,104,142,90,72,60,216,82,73,59,58,102,121,90,141,161,73,64,61,163,74,91,77,94,172,92,79,114,79,127,119,53,121,68,71,61,74,47,61,84,51,68,165,104,101,63,138,122,104,112,258,99,96,96,161,204,87,190,107,143,117,89,82,75,127,151,126,45,87,114,100,77,72,87,80,67,67,64,77,70,77,96,71,66,79,68,79,165,137,84,57,81,140,59,77,78,75,73,257,54,76,77,72,90,163,90,76,83,77,67,78,84,129,82,55,82,76,74,79,109,99,105,120,78,50,46,88,96,83,102,161,98,82,118,121,142,115,100,151,122,178,155,107,124,82,78,96,131,94,133,71,376,79,85,73,71", "endOffsets": "221,350,426,512,626,742,928,1066,1173,1274,1404,1462,1519,1605,1695,1759,1901,1998,2084,2231,2306,2398,2494,2623,2726,2833,2931,3007,3077,3198,3282,3465,3624,3728,3869,3968,4143,4242,4381,4482,4618,4717,4837,4941,5035,5171,5259,5395,5489,5638,5729,5865,5942,6053,6152,6228,6318,6385,6458,6534,6611,6726,6817,6962,7067,7180,7279,7411,7515,7613,7730,7822,7934,8031,8141,8245,8304,8356,8412,8464,8514,8565,8632,8683,8745,8794,8839,8906,8950,9005,9060,9117,9172,9221,9270,9338,9391,9443,9488,9552,9609,9667,9804,9913,9994,10055,10132,10201,10269,10393,10470,10545,10625,10703,10770,10870,10946,11033,11112,11225,11300,11392,11473,11565,11656,11733,11828,11934,12038,12140,12220,12309,12378,12457,12585,12730,12800,12877,12969,13053,15104,15219,15394,15439,15556,15691,15778,15911,15998,16125,16265,16389,16555,16630,16709,16781,16861,16973,17018,17102,17211,17297,17408,17532,17662,17761,17865,17929,17988,18048,18104,18169,18245,18333,18450,18517,18594,18873,19080,19196,19309,19429,19519,19630,19752,19899,20059,20148,20247,20333,20453,20558,20701,20792,20865,20926,21143,21226,21300,21360,21419,21522,21644,21735,21877,22039,22113,22178,22240,22404,22479,22571,22649,22744,22917,23010,23090,23205,23285,23413,23533,23587,23709,23778,23850,23912,23987,24035,24097,24182,24234,24303,24469,24574,24676,24740,24879,25002,25107,25220,25479,25579,25676,25773,25935,26140,26228,26419,26527,26671,26789,26879,26962,27038,27166,27318,27445,27491,27579,27694,27795,27873,27946,28034,28115,28183,28251,28316,28394,28465,28543,28640,28712,28779,28859,28928,29008,29174,29312,29397,29455,29537,29678,29738,29816,29895,29971,30045,30303,30358,30435,30513,30586,30677,30841,30932,31009,31093,31171,31239,31318,31403,31533,31616,31672,31755,31832,31907,31987,32097,32197,32303,32424,32503,32554,32601,32690,32787,32871,32974,33136,33235,33318,33437,33559,33702,33818,33919,34071,34194,34373,34529,34637,34762,34845,34924,35021,35153,35248,35382,35454,35831,35911,35997,36071,36143"}, "to": {"startLines": "193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17517,17688,17817,17893,17979,18093,18209,18395,18533,18640,18741,18871,18929,18986,19072,19162,19226,19368,19465,19551,19698,19773,19865,19961,20090,20193,20300,20398,20474,20544,20665,20749,20932,21091,21195,21336,21435,21610,21709,21848,21949,22085,22184,22304,22408,22502,22638,22726,22862,22956,23105,23196,23332,23409,23520,23619,23695,23785,23852,23925,24001,24078,24193,24284,24429,24534,24647,24746,24878,24982,25080,25197,25289,25401,25498,25608,25712,25771,25823,25879,25931,25981,26032,26099,26150,26212,26261,26306,26373,26417,26472,26527,26584,26639,26688,26737,26805,26858,26910,26955,27019,27076,27134,27271,27380,27461,27522,27599,27668,27736,27860,27937,28012,28092,28170,28237,28337,28413,28500,28579,28692,28767,28859,28940,29032,29123,29200,29295,29401,29505,29607,29687,29776,29845,29924,30052,30197,30267,30344,30436,30520,32571,32686,32861,32906,33023,33158,33245,33378,33465,33592,33732,33856,34022,34097,34176,34248,34328,34440,34485,34569,34678,34764,34875,34999,35129,35228,35332,35396,35455,35515,35571,35636,35712,35800,35917,35984,36061,36340,36547,36663,36776,36896,36986,37097,37219,37366,37526,37615,37714,37800,37920,38025,38168,38259,38332,38393,38610,38693,38767,38827,38886,38989,39111,39202,39344,39506,39580,39645,39707,39871,39946,40038,40116,40211,40384,40477,40557,40672,40752,40880,41000,41054,41176,41245,41317,41379,41454,41502,41564,41649,41701,41770,41936,42041,42143,42207,42346,42469,42574,42687,42946,43046,43143,43240,43402,43607,43695,43886,43994,44138,44256,44346,44429,44505,44633,44785,44912,44958,45046,45161,45262,45340,45413,45501,45582,45650,45718,45783,45861,45932,46010,46107,46179,46246,46326,46395,46475,46641,46779,46864,46922,47004,47145,47205,47283,47362,47438,47512,47770,47825,47902,47980,48053,48144,48308,48399,48476,48560,48638,48706,48785,48870,49000,49083,49139,49222,49299,49374,49454,49564,49664,49770,49891,49970,50021,50068,50157,50254,50338,50441,50603,50702,50785,50904,51026,51169,51285,51386,51538,51661,51840,51996,52104,52229,52312,52391,52488,52620,52715,52849,52921,53298,53378,53464,53538", "endColumns": "170,128,75,85,113,115,185,137,106,100,129,57,56,85,89,63,141,96,85,146,74,91,95,128,102,106,97,75,69,120,83,182,158,103,140,98,174,98,138,100,135,98,119,103,93,135,87,135,93,148,90,135,76,110,98,75,89,66,72,75,76,114,90,144,104,112,98,131,103,97,116,91,111,96,109,103,58,51,55,51,49,50,66,50,61,48,44,66,43,54,54,56,54,48,48,67,52,51,44,63,56,57,136,108,80,60,76,68,67,123,76,74,79,77,66,99,75,86,78,112,74,91,80,91,90,76,94,105,103,101,79,88,68,78,127,144,69,76,91,83,2050,114,174,44,116,134,86,132,86,126,139,123,165,74,78,71,79,111,44,83,108,85,110,123,129,98,103,63,58,59,55,64,75,87,116,66,76,278,206,115,112,119,89,110,121,146,159,88,98,85,119,104,142,90,72,60,216,82,73,59,58,102,121,90,141,161,73,64,61,163,74,91,77,94,172,92,79,114,79,127,119,53,121,68,71,61,74,47,61,84,51,68,165,104,101,63,138,122,104,112,258,99,96,96,161,204,87,190,107,143,117,89,82,75,127,151,126,45,87,114,100,77,72,87,80,67,67,64,77,70,77,96,71,66,79,68,79,165,137,84,57,81,140,59,77,78,75,73,257,54,76,77,72,90,163,90,76,83,77,67,78,84,129,82,55,82,76,74,79,109,99,105,120,78,50,46,88,96,83,102,161,98,82,118,121,142,115,100,151,122,178,155,107,124,82,78,96,131,94,133,71,376,79,85,73,71", "endOffsets": "17683,17812,17888,17974,18088,18204,18390,18528,18635,18736,18866,18924,18981,19067,19157,19221,19363,19460,19546,19693,19768,19860,19956,20085,20188,20295,20393,20469,20539,20660,20744,20927,21086,21190,21331,21430,21605,21704,21843,21944,22080,22179,22299,22403,22497,22633,22721,22857,22951,23100,23191,23327,23404,23515,23614,23690,23780,23847,23920,23996,24073,24188,24279,24424,24529,24642,24741,24873,24977,25075,25192,25284,25396,25493,25603,25707,25766,25818,25874,25926,25976,26027,26094,26145,26207,26256,26301,26368,26412,26467,26522,26579,26634,26683,26732,26800,26853,26905,26950,27014,27071,27129,27266,27375,27456,27517,27594,27663,27731,27855,27932,28007,28087,28165,28232,28332,28408,28495,28574,28687,28762,28854,28935,29027,29118,29195,29290,29396,29500,29602,29682,29771,29840,29919,30047,30192,30262,30339,30431,30515,32566,32681,32856,32901,33018,33153,33240,33373,33460,33587,33727,33851,34017,34092,34171,34243,34323,34435,34480,34564,34673,34759,34870,34994,35124,35223,35327,35391,35450,35510,35566,35631,35707,35795,35912,35979,36056,36335,36542,36658,36771,36891,36981,37092,37214,37361,37521,37610,37709,37795,37915,38020,38163,38254,38327,38388,38605,38688,38762,38822,38881,38984,39106,39197,39339,39501,39575,39640,39702,39866,39941,40033,40111,40206,40379,40472,40552,40667,40747,40875,40995,41049,41171,41240,41312,41374,41449,41497,41559,41644,41696,41765,41931,42036,42138,42202,42341,42464,42569,42682,42941,43041,43138,43235,43397,43602,43690,43881,43989,44133,44251,44341,44424,44500,44628,44780,44907,44953,45041,45156,45257,45335,45408,45496,45577,45645,45713,45778,45856,45927,46005,46102,46174,46241,46321,46390,46470,46636,46774,46859,46917,46999,47140,47200,47278,47357,47433,47507,47765,47820,47897,47975,48048,48139,48303,48394,48471,48555,48633,48701,48780,48865,48995,49078,49134,49217,49294,49369,49449,49559,49659,49765,49886,49965,50016,50063,50152,50249,50333,50436,50598,50697,50780,50899,51021,51164,51280,51381,51533,51656,51835,51991,52099,52224,52307,52386,52483,52615,52710,52844,52916,53293,53373,53459,53533,53605"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1025,1089,1181,1249,1309,1396,1460,1522,1586,1654,1719,1773,1882,1940,2002,2056,2131,2251,2333,2410,2500,2584,2664,2798,2876,2956,3079,3167,3245,3299,3350,3416,3484,3558,3629,3705,3776,3854,3924,3994,4094,4183,4261,4349,4439,4511,4583,4667,4718,4796,4862,4943,5026,5088,5152,5215,5284,5384,5488,5581,5681,5739,5794,5872,5956,6034", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "256,333,406,493,581,661,760,879,961,1020,1084,1176,1244,1304,1391,1455,1517,1581,1649,1714,1768,1877,1935,1997,2051,2126,2246,2328,2405,2495,2579,2659,2793,2871,2951,3074,3162,3240,3294,3345,3411,3479,3553,3624,3700,3771,3849,3919,3989,4089,4178,4256,4344,4434,4506,4578,4662,4713,4791,4857,4938,5021,5083,5147,5210,5279,5379,5483,5576,5676,5734,5789,5867,5951,6029,6101"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2943,3020,3093,3180,3268,4074,4173,4292,5073,5132,5196,5598,5830,11921,12008,12072,12134,12198,12266,12331,12385,12494,12552,12614,12668,12743,12863,12945,13022,13112,13196,13276,13410,13488,13568,13691,13779,13857,13911,13962,14028,14096,14170,14241,14317,14388,14466,14536,14606,14706,14795,14873,14961,15051,15123,15195,15279,15330,15408,15474,15555,15638,15700,15764,15827,15896,15996,16100,16193,16293,16351,16577,16897,16981,17129", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "306,3015,3088,3175,3263,3343,4168,4287,4369,5127,5191,5283,5661,5885,12003,12067,12129,12193,12261,12326,12380,12489,12547,12609,12663,12738,12858,12940,13017,13107,13191,13271,13405,13483,13563,13686,13774,13852,13906,13957,14023,14091,14165,14236,14312,14383,14461,14531,14601,14701,14790,14868,14956,15046,15118,15190,15274,15325,15403,15469,15550,15633,15695,15759,15822,15891,15991,16095,16188,16288,16346,16401,16650,16976,17054,17196"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,414,509,623,709,809,922,999,1074,1165,1258,1352,1446,1546,1639,1734,1832,1923,2014,2092,2195,2293,2389,2493,2592,2693,2846,16817", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "409,504,618,704,804,917,994,1069,1160,1253,1347,1441,1541,1634,1729,1827,1918,2009,2087,2190,2288,2384,2488,2587,2688,2841,2938,16892"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,979,1061,1131,1205,1276,1346,1423,1490", "endColumns": "92,80,96,99,87,75,87,88,81,79,81,69,73,70,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,974,1056,1126,1200,1271,1341,1418,1485,1605"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,539,540,541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4374,4467,4788,4885,4985,5666,5742,16406,16495,16655,16735,17059,17201,17275,17346,53610,53687,53754", "endColumns": "92,80,96,99,87,75,87,88,81,79,81,69,73,70,69,76,66,119", "endOffsets": "4462,4543,4880,4980,5068,5737,5825,16490,16572,16730,16812,17124,17270,17341,17411,53682,53749,53869"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,394,507,606,700,811,955,1077,1227,1311,1411,1500,1594,1701,1819,1924,2051,2173,2306,2473,2600,2716,2837,2958,3048,3146,3265,3396,3497,3607,3710,3844,3985,4090,4188,4268,4362,4453,4537,4621,4732,4812,4896,4997,5096,5187,5287,5375,5480,5582,5687,5804,5884,5987", "endColumns": "116,114,106,112,98,93,110,143,121,149,83,99,88,93,106,117,104,126,121,132,166,126,115,120,120,89,97,118,130,100,109,102,133,140,104,97,79,93,90,83,83,110,79,83,100,98,90,99,87,104,101,104,116,79,102,98", "endOffsets": "167,282,389,502,601,695,806,950,1072,1222,1306,1406,1495,1589,1696,1814,1919,2046,2168,2301,2468,2595,2711,2832,2953,3043,3141,3260,3391,3492,3602,3705,3839,3980,4085,4183,4263,4357,4448,4532,4616,4727,4807,4891,4992,5091,5182,5282,5370,5475,5577,5682,5799,5879,5982,6081"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5890,6007,6122,6229,6342,6441,6535,6646,6790,6912,7062,7146,7246,7335,7429,7536,7654,7759,7886,8008,8141,8308,8435,8551,8672,8793,8883,8981,9100,9231,9332,9442,9545,9679,9820,9925,10023,10103,10197,10288,10372,10456,10567,10647,10731,10832,10931,11022,11122,11210,11315,11417,11522,11639,11719,11822", "endColumns": "116,114,106,112,98,93,110,143,121,149,83,99,88,93,106,117,104,126,121,132,166,126,115,120,120,89,97,118,130,100,109,102,133,140,104,97,79,93,90,83,83,110,79,83,100,98,90,99,87,104,101,104,116,79,102,98", "endOffsets": "6002,6117,6224,6337,6436,6530,6641,6785,6907,7057,7141,7241,7330,7424,7531,7649,7754,7881,8003,8136,8303,8430,8546,8667,8788,8878,8976,9095,9226,9327,9437,9540,9674,9815,9920,10018,10098,10192,10283,10367,10451,10562,10642,10726,10827,10926,11017,11117,11205,11310,11412,11517,11634,11714,11817,11916"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4548", "endColumns": "129", "endOffsets": "4673"}}]}]}