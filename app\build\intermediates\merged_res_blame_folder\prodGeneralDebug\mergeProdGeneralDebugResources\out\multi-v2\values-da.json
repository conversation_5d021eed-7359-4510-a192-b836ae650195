{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,277,372,471,553,630,719,808,890,971,1055,1125,1199,1271,1342,1420,1487", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,73,71,70,77,66,119", "endOffsets": "192,272,367,466,548,625,714,803,885,966,1050,1120,1194,1266,1337,1415,1482,1602"}, "to": {"startLines": "48,49,52,53,54,62,63,179,180,182,183,187,189,190,191,539,540,541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4437,4529,4848,4943,5042,5709,5786,16495,16584,16746,16827,17149,17296,17370,17442,53827,53905,53972", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,73,71,70,77,66,119", "endOffsets": "4524,4604,4938,5037,5119,5781,5870,16579,16661,16822,16906,17214,17365,17437,17508,53900,53967,54087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,350,426,504,601,681,781,930,1008,1067,1131,1217,1290,1350,1437,1501,1563,1625,1693,1758,1812,1930,1988,2049,2105,2180,2306,2392,2469,2560,2644,2724,2865,2943,3023,3145,3231,3309,3365,3416,3482,3550,3624,3695,3770,3842,3920,3990,4063,4167,4251,4328,4416,4505,4579,4652,4737,4786,4864,4930,5010,5093,5155,5219,5282,5351,5459,5562,5663,5762,5822,5877,5957,6037,6115", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "267,345,421,499,596,676,776,925,1003,1062,1126,1212,1285,1345,1432,1496,1558,1620,1688,1753,1807,1925,1983,2044,2100,2175,2301,2387,2464,2555,2639,2719,2860,2938,3018,3140,3226,3304,3360,3411,3477,3545,3619,3690,3765,3837,3915,3985,4058,4162,4246,4323,4411,4500,4574,4647,4732,4781,4859,4925,5005,5088,5150,5214,5277,5346,5454,5557,5658,5757,5817,5872,5952,6032,6110,6187"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2974,3052,3128,3206,3303,4110,4210,4359,5124,5183,5247,5636,5875,11968,12055,12119,12181,12243,12311,12376,12430,12548,12606,12667,12723,12798,12924,13010,13087,13178,13262,13342,13483,13561,13641,13763,13849,13927,13983,14034,14100,14168,14242,14313,14388,14460,14538,14608,14681,14785,14869,14946,15034,15123,15197,15270,15355,15404,15482,15548,15628,15711,15773,15837,15900,15969,16077,16180,16281,16380,16440,16666,16991,17071,17219", "endLines": "5,33,34,35,36,37,45,46,47,55,56,57,61,64,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,185,186,188", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "317,3047,3123,3201,3298,3378,4205,4354,4432,5178,5242,5328,5704,5930,12050,12114,12176,12238,12306,12371,12425,12543,12601,12662,12718,12793,12919,13005,13082,13173,13257,13337,13478,13556,13636,13758,13844,13922,13978,14029,14095,14163,14237,14308,14383,14455,14533,14603,14676,14780,14864,14941,15029,15118,15192,15265,15350,15399,15477,15543,15623,15706,15768,15832,15895,15964,16072,16175,16276,16375,16435,16490,16741,17066,17144,17291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,86", "endOffsets": "140,227"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "54092,54182", "endColumns": "89,86", "endOffsets": "54177,54264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-da\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "122", "endOffsets": "317"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4609", "endColumns": "126", "endOffsets": "4731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3383,3479,3581,3678,3776,3883,3992,17513", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "3474,3576,3673,3771,3878,3987,4105,17609"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,391,505,605,700,812,956,1078,1227,1311,1411,1500,1594,1708,1826,1931,2056,2176,2312,2485,2615,2732,2854,2973,3063,3161,3280,3416,3514,3632,3734,3860,3993,4098,4196,4276,4369,4462,4546,4631,4732,4812,4896,4997,5096,5191,5291,5378,5483,5585,5690,5807,5887,5989", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "166,279,386,500,600,695,807,951,1073,1222,1306,1406,1495,1589,1703,1821,1926,2051,2171,2307,2480,2610,2727,2849,2968,3058,3156,3275,3411,3509,3627,3729,3855,3988,4093,4191,4271,4364,4457,4541,4626,4727,4807,4891,4992,5091,5186,5286,5373,5478,5580,5685,5802,5882,5984,6083"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5935,6051,6164,6271,6385,6485,6580,6692,6836,6958,7107,7191,7291,7380,7474,7588,7706,7811,7936,8056,8192,8365,8495,8612,8734,8853,8943,9041,9160,9296,9394,9512,9614,9740,9873,9978,10076,10156,10249,10342,10426,10511,10612,10692,10776,10877,10976,11071,11171,11258,11363,11465,11570,11687,11767,11869", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "6046,6159,6266,6380,6480,6575,6687,6831,6953,7102,7186,7286,7375,7469,7583,7701,7806,7931,8051,8187,8360,8490,8607,8729,8848,8938,9036,9155,9291,9389,9507,9609,9735,9868,9973,10071,10151,10244,10337,10421,10506,10607,10687,10771,10872,10971,11066,11166,11253,11358,11460,11565,11682,11762,11864,11963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,266,373", "endColumns": "111,98,106,96", "endOffsets": "162,261,368,465"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4736,5333,5432,5539", "endColumns": "111,98,106,96", "endOffsets": "4843,5427,5534,5631"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,226,336,412,497,599,737,925,1070,1177,1279,1409,1468,1526,1613,1702,1768,1916,2025,2111,2268,2343,2434,2528,2667,2771,2878,2977,3053,3123,3239,3327,3535,3708,3824,3969,4064,4245,4346,4480,4587,4719,4822,4940,5040,5135,5271,5360,5496,5590,5749,5836,5966,6043,6152,6251,6325,6410,6481,6552,6626,6697,6820,6919,7060,7159,7279,7377,7512,7612,7709,7823,7911,8025,8126,8238,8337,8396,8448,8502,8554,8604,8653,8720,8771,8835,8884,8929,9000,9044,9099,9153,9208,9262,9310,9358,9426,9481,9533,9578,9640,9696,9755,9896,10014,10095,10156,10233,10302,10370,10480,10557,10632,10712,10790,10857,10954,11028,11125,11201,11325,11401,11493,11574,11666,11755,11832,11933,12036,12156,12259,12336,12426,12494,12570,12683,12840,12916,12993,13085,13170,15221,15336,15504,15549,15668,15803,15891,16015,16104,16235,16382,16498,16675,16750,16828,16900,16984,17100,17145,17232,17352,17443,17555,17672,17791,17888,17992,18056,18112,18169,18221,18286,18362,18455,18560,18627,18706,18959,19159,19258,19370,19492,19566,19678,19799,19946,20103,20199,20312,20397,20514,20611,20744,20840,20913,20973,21185,21273,21351,21410,21468,21578,21701,21810,21951,22117,22188,22254,22316,22476,22551,22639,22717,22812,22988,23081,23164,23280,23372,23514,23630,23683,23799,23869,23941,24003,24077,24125,24179,24255,24307,24375,24540,24650,24744,24809,24949,25086,25195,25310,25562,25661,25760,25857,26050,26239,26322,26503,26605,26746,26854,26934,27020,27098,27226,27361,27496,27542,27628,27750,27862,27946,28020,28114,28195,28263,28330,28392,28469,28541,28616,28708,28780,28846,28928,28997,29076,29244,29373,29463,29521,29607,29757,29824,29902,29987,30063,30137,30354,30409,30486,30564,30632,30718,30883,30970,31043,31127,31205,31273,31352,31430,31550,31631,31685,31767,31844,31922,31999,32109,32212,32328,32443,32522,32570,32624,32720,32823,32910,33013,33168,33272,33357,33474,33604,33751,33871,33974,34139,34263,34457,34622,34738,34861,34950,35031,35134,35261,35350,35480,35549,35957,36036,36122,36196", "endColumns": "170,109,75,84,101,137,187,144,106,101,129,58,57,86,88,65,147,108,85,156,74,90,93,138,103,106,98,75,69,115,87,207,172,115,144,94,180,100,133,106,131,102,117,99,94,135,88,135,93,158,86,129,76,108,98,73,84,70,70,73,70,122,98,140,98,119,97,134,99,96,113,87,113,100,111,98,58,51,53,51,49,48,66,50,63,48,44,70,43,54,53,54,53,47,47,67,54,51,44,61,55,58,140,117,80,60,76,68,67,109,76,74,79,77,66,96,73,96,75,123,75,91,80,91,88,76,100,102,119,102,76,89,67,75,112,156,75,76,91,84,2050,114,167,44,118,134,87,123,88,130,146,115,176,74,77,71,83,115,44,86,119,90,111,116,118,96,103,63,55,56,51,64,75,92,104,66,78,252,199,98,111,121,73,111,120,146,156,95,112,84,116,96,132,95,72,59,211,87,77,58,57,109,122,108,140,165,70,65,61,159,74,87,77,94,175,92,82,115,91,141,115,52,115,69,71,61,73,47,53,75,51,67,164,109,93,64,139,136,108,114,251,98,98,96,192,188,82,180,101,140,107,79,85,77,127,134,134,45,85,121,111,83,73,93,80,67,66,61,76,71,74,91,71,65,81,68,78,167,128,89,57,85,149,66,77,84,75,73,216,54,76,77,67,85,164,86,72,83,77,67,78,77,119,80,53,81,76,77,76,109,102,115,114,78,47,53,95,102,86,102,154,103,84,116,129,146,119,102,164,123,193,164,115,122,88,80,102,126,88,129,68,407,78,85,73,71", "endOffsets": "221,331,407,492,594,732,920,1065,1172,1274,1404,1463,1521,1608,1697,1763,1911,2020,2106,2263,2338,2429,2523,2662,2766,2873,2972,3048,3118,3234,3322,3530,3703,3819,3964,4059,4240,4341,4475,4582,4714,4817,4935,5035,5130,5266,5355,5491,5585,5744,5831,5961,6038,6147,6246,6320,6405,6476,6547,6621,6692,6815,6914,7055,7154,7274,7372,7507,7607,7704,7818,7906,8020,8121,8233,8332,8391,8443,8497,8549,8599,8648,8715,8766,8830,8879,8924,8995,9039,9094,9148,9203,9257,9305,9353,9421,9476,9528,9573,9635,9691,9750,9891,10009,10090,10151,10228,10297,10365,10475,10552,10627,10707,10785,10852,10949,11023,11120,11196,11320,11396,11488,11569,11661,11750,11827,11928,12031,12151,12254,12331,12421,12489,12565,12678,12835,12911,12988,13080,13165,15216,15331,15499,15544,15663,15798,15886,16010,16099,16230,16377,16493,16670,16745,16823,16895,16979,17095,17140,17227,17347,17438,17550,17667,17786,17883,17987,18051,18107,18164,18216,18281,18357,18450,18555,18622,18701,18954,19154,19253,19365,19487,19561,19673,19794,19941,20098,20194,20307,20392,20509,20606,20739,20835,20908,20968,21180,21268,21346,21405,21463,21573,21696,21805,21946,22112,22183,22249,22311,22471,22546,22634,22712,22807,22983,23076,23159,23275,23367,23509,23625,23678,23794,23864,23936,23998,24072,24120,24174,24250,24302,24370,24535,24645,24739,24804,24944,25081,25190,25305,25557,25656,25755,25852,26045,26234,26317,26498,26600,26741,26849,26929,27015,27093,27221,27356,27491,27537,27623,27745,27857,27941,28015,28109,28190,28258,28325,28387,28464,28536,28611,28703,28775,28841,28923,28992,29071,29239,29368,29458,29516,29602,29752,29819,29897,29982,30058,30132,30349,30404,30481,30559,30627,30713,30878,30965,31038,31122,31200,31268,31347,31425,31545,31626,31680,31762,31839,31917,31994,32104,32207,32323,32438,32517,32565,32619,32715,32818,32905,33008,33163,33267,33352,33469,33599,33746,33866,33969,34134,34258,34452,34617,34733,34856,34945,35026,35129,35256,35345,35475,35544,35952,36031,36117,36191,36263"}, "to": {"startLines": "193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17614,17785,17895,17971,18056,18158,18296,18484,18629,18736,18838,18968,19027,19085,19172,19261,19327,19475,19584,19670,19827,19902,19993,20087,20226,20330,20437,20536,20612,20682,20798,20886,21094,21267,21383,21528,21623,21804,21905,22039,22146,22278,22381,22499,22599,22694,22830,22919,23055,23149,23308,23395,23525,23602,23711,23810,23884,23969,24040,24111,24185,24256,24379,24478,24619,24718,24838,24936,25071,25171,25268,25382,25470,25584,25685,25797,25896,25955,26007,26061,26113,26163,26212,26279,26330,26394,26443,26488,26559,26603,26658,26712,26767,26821,26869,26917,26985,27040,27092,27137,27199,27255,27314,27455,27573,27654,27715,27792,27861,27929,28039,28116,28191,28271,28349,28416,28513,28587,28684,28760,28884,28960,29052,29133,29225,29314,29391,29492,29595,29715,29818,29895,29985,30053,30129,30242,30399,30475,30552,30644,30729,32780,32895,33063,33108,33227,33362,33450,33574,33663,33794,33941,34057,34234,34309,34387,34459,34543,34659,34704,34791,34911,35002,35114,35231,35350,35447,35551,35615,35671,35728,35780,35845,35921,36014,36119,36186,36265,36518,36718,36817,36929,37051,37125,37237,37358,37505,37662,37758,37871,37956,38073,38170,38303,38399,38472,38532,38744,38832,38910,38969,39027,39137,39260,39369,39510,39676,39747,39813,39875,40035,40110,40198,40276,40371,40547,40640,40723,40839,40931,41073,41189,41242,41358,41428,41500,41562,41636,41684,41738,41814,41866,41934,42099,42209,42303,42368,42508,42645,42754,42869,43121,43220,43319,43416,43609,43798,43881,44062,44164,44305,44413,44493,44579,44657,44785,44920,45055,45101,45187,45309,45421,45505,45579,45673,45754,45822,45889,45951,46028,46100,46175,46267,46339,46405,46487,46556,46635,46803,46932,47022,47080,47166,47316,47383,47461,47546,47622,47696,47913,47968,48045,48123,48191,48277,48442,48529,48602,48686,48764,48832,48911,48989,49109,49190,49244,49326,49403,49481,49558,49668,49771,49887,50002,50081,50129,50183,50279,50382,50469,50572,50727,50831,50916,51033,51163,51310,51430,51533,51698,51822,52016,52181,52297,52420,52509,52590,52693,52820,52909,53039,53108,53516,53595,53681,53755", "endColumns": "170,109,75,84,101,137,187,144,106,101,129,58,57,86,88,65,147,108,85,156,74,90,93,138,103,106,98,75,69,115,87,207,172,115,144,94,180,100,133,106,131,102,117,99,94,135,88,135,93,158,86,129,76,108,98,73,84,70,70,73,70,122,98,140,98,119,97,134,99,96,113,87,113,100,111,98,58,51,53,51,49,48,66,50,63,48,44,70,43,54,53,54,53,47,47,67,54,51,44,61,55,58,140,117,80,60,76,68,67,109,76,74,79,77,66,96,73,96,75,123,75,91,80,91,88,76,100,102,119,102,76,89,67,75,112,156,75,76,91,84,2050,114,167,44,118,134,87,123,88,130,146,115,176,74,77,71,83,115,44,86,119,90,111,116,118,96,103,63,55,56,51,64,75,92,104,66,78,252,199,98,111,121,73,111,120,146,156,95,112,84,116,96,132,95,72,59,211,87,77,58,57,109,122,108,140,165,70,65,61,159,74,87,77,94,175,92,82,115,91,141,115,52,115,69,71,61,73,47,53,75,51,67,164,109,93,64,139,136,108,114,251,98,98,96,192,188,82,180,101,140,107,79,85,77,127,134,134,45,85,121,111,83,73,93,80,67,66,61,76,71,74,91,71,65,81,68,78,167,128,89,57,85,149,66,77,84,75,73,216,54,76,77,67,85,164,86,72,83,77,67,78,77,119,80,53,81,76,77,76,109,102,115,114,78,47,53,95,102,86,102,154,103,84,116,129,146,119,102,164,123,193,164,115,122,88,80,102,126,88,129,68,407,78,85,73,71", "endOffsets": "17780,17890,17966,18051,18153,18291,18479,18624,18731,18833,18963,19022,19080,19167,19256,19322,19470,19579,19665,19822,19897,19988,20082,20221,20325,20432,20531,20607,20677,20793,20881,21089,21262,21378,21523,21618,21799,21900,22034,22141,22273,22376,22494,22594,22689,22825,22914,23050,23144,23303,23390,23520,23597,23706,23805,23879,23964,24035,24106,24180,24251,24374,24473,24614,24713,24833,24931,25066,25166,25263,25377,25465,25579,25680,25792,25891,25950,26002,26056,26108,26158,26207,26274,26325,26389,26438,26483,26554,26598,26653,26707,26762,26816,26864,26912,26980,27035,27087,27132,27194,27250,27309,27450,27568,27649,27710,27787,27856,27924,28034,28111,28186,28266,28344,28411,28508,28582,28679,28755,28879,28955,29047,29128,29220,29309,29386,29487,29590,29710,29813,29890,29980,30048,30124,30237,30394,30470,30547,30639,30724,32775,32890,33058,33103,33222,33357,33445,33569,33658,33789,33936,34052,34229,34304,34382,34454,34538,34654,34699,34786,34906,34997,35109,35226,35345,35442,35546,35610,35666,35723,35775,35840,35916,36009,36114,36181,36260,36513,36713,36812,36924,37046,37120,37232,37353,37500,37657,37753,37866,37951,38068,38165,38298,38394,38467,38527,38739,38827,38905,38964,39022,39132,39255,39364,39505,39671,39742,39808,39870,40030,40105,40193,40271,40366,40542,40635,40718,40834,40926,41068,41184,41237,41353,41423,41495,41557,41631,41679,41733,41809,41861,41929,42094,42204,42298,42363,42503,42640,42749,42864,43116,43215,43314,43411,43604,43793,43876,44057,44159,44300,44408,44488,44574,44652,44780,44915,45050,45096,45182,45304,45416,45500,45574,45668,45749,45817,45884,45946,46023,46095,46170,46262,46334,46400,46482,46551,46630,46798,46927,47017,47075,47161,47311,47378,47456,47541,47617,47691,47908,47963,48040,48118,48186,48272,48437,48524,48597,48681,48759,48827,48906,48984,49104,49185,49239,49321,49398,49476,49553,49663,49766,49882,49997,50076,50124,50178,50274,50377,50464,50567,50722,50826,50911,51028,51158,51305,51425,51528,51693,51817,52011,52176,52292,52415,52504,52585,52688,52815,52904,53034,53103,53511,53590,53676,53750,53822"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,422,516,632,717,817,930,1008,1084,1175,1268,1361,1455,1549,1642,1737,1835,1926,2017,2096,2204,2311,2407,2520,2623,2724,2877,16911", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "417,511,627,712,812,925,1003,1079,1170,1263,1356,1450,1544,1637,1732,1830,1921,2012,2091,2199,2306,2402,2515,2618,2719,2872,2969,16986"}}]}]}