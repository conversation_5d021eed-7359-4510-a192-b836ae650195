{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,265,377", "endColumns": "105,103,111,101", "endOffsets": "156,260,372,474"}, "to": {"startLines": "53,60,61,62", "startColumns": "4,4,4,4", "startOffsets": "4967,5576,5680,5792", "endColumns": "105,103,111,101", "endOffsets": "5068,5675,5787,5889"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-sl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "4827", "endColumns": "139", "endOffsets": "4962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "40,41,42,43,44,45,46,194", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3612,3709,3811,3909,4013,4116,4218,18072", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "3704,3806,3904,4008,4111,4213,4330,18168"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,367,456,545,633,731,822,928,1054,1138,1202,1268,1362,1438,1501,1613,1673,1738,1792,1862,1922,1978,2090,2147,2209,2265,2338,2472,2557,2634,2723,2804,2889,3032,3116,3199,3333,3422,3499,3555,3610,3676,3749,3826,3897,3976,4050,4126,4201,4274,4379,4467,4540,4630,4721,4793,4867,4958,5010,5092,5159,5243,5330,5392,5456,5519,5588,5691,5799,5897,6001,6061,6120,6197,6284,6360", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,88,88,87,97,90,105,125,83,63,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76,86,75,77", "endOffsets": "362,451,540,628,726,817,923,1049,1133,1197,1263,1357,1433,1496,1608,1668,1733,1787,1857,1917,1973,2085,2142,2204,2260,2333,2467,2552,2629,2718,2799,2884,3027,3111,3194,3328,3417,3494,3550,3605,3671,3744,3821,3892,3971,4045,4121,4196,4269,4374,4462,4535,4625,4716,4788,4862,4953,5005,5087,5154,5238,5325,5387,5451,5514,5583,5686,5794,5892,5996,6056,6115,6192,6279,6355,6433"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,57,58,59,63,66,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,183,187,188,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3157,3246,3335,3423,3521,4335,4441,4567,5352,5416,5482,5894,6134,12402,12514,12574,12639,12693,12763,12823,12879,12991,13048,13110,13166,13239,13373,13458,13535,13624,13705,13790,13933,14017,14100,14234,14323,14400,14456,14511,14577,14650,14727,14798,14877,14951,15027,15102,15175,15280,15368,15441,15531,15622,15694,15768,15859,15911,15993,16060,16144,16231,16293,16357,16420,16489,16592,16700,16798,16902,16962,17195,17524,17611,17759", "endLines": "7,35,36,37,38,39,47,48,49,57,58,59,63,66,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,183,187,188,190", "endColumns": "12,88,88,87,97,90,105,125,83,63,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76,86,75,77", "endOffsets": "412,3241,3330,3418,3516,3607,4436,4562,4646,5411,5477,5571,5965,6192,12509,12569,12634,12688,12758,12818,12874,12986,13043,13105,13161,13234,13368,13453,13530,13619,13700,13785,13928,14012,14095,14229,14318,14395,14451,14506,14572,14645,14722,14793,14872,14946,15022,15097,15170,15275,15363,15436,15526,15617,15689,15763,15854,15906,15988,16055,16139,16226,16288,16352,16415,16484,16587,16695,16793,16897,16957,17016,17267,17606,17682,17832"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,186", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "417,529,631,739,826,929,1048,1129,1207,1299,1393,1488,1582,1677,1771,1867,1967,2059,2151,2235,2343,2451,2551,2664,2772,2877,3057,17440", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "524,626,734,821,924,1043,1124,1202,1294,1388,1483,1577,1672,1766,1862,1962,2054,2146,2230,2338,2446,2546,2659,2767,2872,3052,3152,17519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,313,431,560,670,766,879,1019,1145,1288,1373,1472,1565,1662,1779,1901,2005,2142,2276,2407,2591,2718,2841,2966,3088,3182,3280,3400,3524,3624,3733,3839,3982,4129,4238,4340,4424,4519,4615,4703,4789,4892,4974,5057,5152,5252,5343,5440,5528,5632,5729,5831,5973,6055,6161", "endColumns": "128,128,117,128,109,95,112,139,125,142,84,98,92,96,116,121,103,136,133,130,183,126,122,124,121,93,97,119,123,99,108,105,142,146,108,101,83,94,95,87,85,102,81,82,94,99,90,96,87,103,96,101,141,81,105,98", "endOffsets": "179,308,426,555,665,761,874,1014,1140,1283,1368,1467,1560,1657,1774,1896,2000,2137,2271,2402,2586,2713,2836,2961,3083,3177,3275,3395,3519,3619,3728,3834,3977,4124,4233,4335,4419,4514,4610,4698,4784,4887,4969,5052,5147,5247,5338,5435,5523,5627,5724,5826,5968,6050,6156,6255"}, "to": {"startLines": "67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6197,6326,6455,6573,6702,6812,6908,7021,7161,7287,7430,7515,7614,7707,7804,7921,8043,8147,8284,8418,8549,8733,8860,8983,9108,9230,9324,9422,9542,9666,9766,9875,9981,10124,10271,10380,10482,10566,10661,10757,10845,10931,11034,11116,11199,11294,11394,11485,11582,11670,11774,11871,11973,12115,12197,12303", "endColumns": "128,128,117,128,109,95,112,139,125,142,84,98,92,96,116,121,103,136,133,130,183,126,122,124,121,93,97,119,123,99,108,105,142,146,108,101,83,94,95,87,85,102,81,82,94,99,90,96,87,103,96,101,141,81,105,98", "endOffsets": "6321,6450,6568,6697,6807,6903,7016,7156,7282,7425,7510,7609,7702,7799,7916,8038,8142,8279,8413,8544,8728,8855,8978,9103,9225,9319,9417,9537,9661,9761,9870,9976,10119,10266,10375,10477,10561,10656,10752,10840,10926,11029,11111,11194,11289,11389,11480,11577,11665,11769,11866,11968,12110,12192,12298,12397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,231,364,440,521,627,776,962,1114,1236,1355,1487,1550,1611,1701,1801,1871,2014,2117,2199,2348,2425,2514,2613,2739,2841,2949,3049,3119,3191,3317,3405,3615,3796,3907,4077,4182,4351,4453,4594,4702,4848,4956,5096,5198,5297,5445,5536,5683,5779,5954,6040,6165,6243,6363,6464,6537,6631,6710,6783,6866,6938,7061,7166,7315,7414,7531,7633,7783,7885,7984,8106,8201,8312,8426,8545,8638,8702,8756,8809,8861,8912,8961,9030,9082,9147,9197,9241,9308,9352,9405,9460,9518,9576,9626,9676,9754,9806,9858,9903,9964,10022,10083,10208,10326,10415,10476,10555,10624,10692,10816,10893,10969,11049,11127,11195,11301,11374,11471,11555,11695,11774,11866,11951,12049,12145,12228,12326,12431,12551,12659,12745,12840,12910,12995,13111,13243,13313,13390,13487,13581,15632,15747,15922,15969,16116,16261,16351,16491,16581,16708,16879,17011,17184,17259,17341,17413,17498,17620,17665,17754,17876,17973,18096,18217,18349,18452,18559,18626,18689,18746,18801,18870,18952,19042,19164,19234,19311,19563,19767,19895,20017,20144,20241,20360,20493,20640,20798,20899,21024,21110,21241,21351,21503,21594,21667,21728,21952,22040,22117,22176,22234,22347,22469,22565,22716,22891,22968,23028,23088,23241,23318,23414,23490,23583,23742,23834,23907,24023,24112,24248,24403,24458,24570,24639,24718,24788,24863,24911,24976,25065,25117,25191,25348,25448,25545,25608,25750,25899,26027,26141,26406,26507,26598,26690,26823,27004,27094,27286,27390,27527,27643,27727,27817,27897,28044,28193,28327,28375,28457,28590,28701,28791,28863,28955,29034,29106,29179,29240,29318,29390,29465,29554,29620,29687,29777,29847,29932,30114,30265,30353,30410,30497,30636,30697,30775,30859,30929,31019,31254,31309,31386,31474,31549,31648,31818,31910,31984,32070,32155,32223,32296,32390,32528,32609,32659,32737,32814,32892,32973,33083,33184,33299,33425,33502,33551,33603,33697,33798,33885,33993,34158,34268,34351,34471,34591,34738,34854,34953,35115,35254,35440,35623,35746,35878,35971,36068,36190,36322,36431,36556,36636,37028,37113,37199,37277", "endColumns": "175,132,75,80,105,148,185,151,121,118,131,62,60,89,99,69,142,102,81,148,76,88,98,125,101,107,99,69,71,125,87,209,180,110,169,104,168,101,140,107,145,107,139,101,98,147,90,146,95,174,85,124,77,119,100,72,93,78,72,82,71,122,104,148,98,116,101,149,101,98,121,94,110,113,118,92,63,53,52,51,50,48,68,51,64,49,43,66,43,52,54,57,57,49,49,77,51,51,44,60,57,60,124,117,88,60,78,68,67,123,76,75,79,77,67,105,72,96,83,139,78,91,84,97,95,82,97,104,119,107,85,94,69,84,115,131,69,76,96,93,2050,114,174,46,146,144,89,139,89,126,170,131,172,74,81,71,84,121,44,88,121,96,122,120,131,102,106,66,62,56,54,68,81,89,121,69,76,251,203,127,121,126,96,118,132,146,157,100,124,85,130,109,151,90,72,60,223,87,76,58,57,112,121,95,150,174,76,59,59,152,76,95,75,92,158,91,72,115,88,135,154,54,111,68,78,69,74,47,64,88,51,73,156,99,96,62,141,148,127,113,264,100,90,91,132,180,89,191,103,136,115,83,89,79,146,148,133,47,81,132,110,89,71,91,78,71,72,60,77,71,74,88,65,66,89,69,84,181,150,87,56,86,138,60,77,83,69,89,234,54,76,87,74,98,169,91,73,85,84,67,72,93,137,80,49,77,76,77,80,109,100,114,125,76,48,51,93,100,86,107,164,109,82,119,119,146,115,98,161,138,185,182,122,131,92,96,121,131,108,124,79,391,84,85,77,74", "endOffsets": "226,359,435,516,622,771,957,1109,1231,1350,1482,1545,1606,1696,1796,1866,2009,2112,2194,2343,2420,2509,2608,2734,2836,2944,3044,3114,3186,3312,3400,3610,3791,3902,4072,4177,4346,4448,4589,4697,4843,4951,5091,5193,5292,5440,5531,5678,5774,5949,6035,6160,6238,6358,6459,6532,6626,6705,6778,6861,6933,7056,7161,7310,7409,7526,7628,7778,7880,7979,8101,8196,8307,8421,8540,8633,8697,8751,8804,8856,8907,8956,9025,9077,9142,9192,9236,9303,9347,9400,9455,9513,9571,9621,9671,9749,9801,9853,9898,9959,10017,10078,10203,10321,10410,10471,10550,10619,10687,10811,10888,10964,11044,11122,11190,11296,11369,11466,11550,11690,11769,11861,11946,12044,12140,12223,12321,12426,12546,12654,12740,12835,12905,12990,13106,13238,13308,13385,13482,13576,15627,15742,15917,15964,16111,16256,16346,16486,16576,16703,16874,17006,17179,17254,17336,17408,17493,17615,17660,17749,17871,17968,18091,18212,18344,18447,18554,18621,18684,18741,18796,18865,18947,19037,19159,19229,19306,19558,19762,19890,20012,20139,20236,20355,20488,20635,20793,20894,21019,21105,21236,21346,21498,21589,21662,21723,21947,22035,22112,22171,22229,22342,22464,22560,22711,22886,22963,23023,23083,23236,23313,23409,23485,23578,23737,23829,23902,24018,24107,24243,24398,24453,24565,24634,24713,24783,24858,24906,24971,25060,25112,25186,25343,25443,25540,25603,25745,25894,26022,26136,26401,26502,26593,26685,26818,26999,27089,27281,27385,27522,27638,27722,27812,27892,28039,28188,28322,28370,28452,28585,28696,28786,28858,28950,29029,29101,29174,29235,29313,29385,29460,29549,29615,29682,29772,29842,29927,30109,30260,30348,30405,30492,30631,30692,30770,30854,30924,31014,31249,31304,31381,31469,31544,31643,31813,31905,31979,32065,32150,32218,32291,32385,32523,32604,32654,32732,32809,32887,32968,33078,33179,33294,33420,33497,33546,33598,33692,33793,33880,33988,34153,34263,34346,34466,34586,34733,34849,34948,35110,35249,35435,35618,35741,35873,35966,36063,36185,36317,36426,36551,36631,37023,37108,37194,37272,37347"}, "to": {"startLines": "195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18173,18349,18482,18558,18639,18745,18894,19080,19232,19354,19473,19605,19668,19729,19819,19919,19989,20132,20235,20317,20466,20543,20632,20731,20857,20959,21067,21167,21237,21309,21435,21523,21733,21914,22025,22195,22300,22469,22571,22712,22820,22966,23074,23214,23316,23415,23563,23654,23801,23897,24072,24158,24283,24361,24481,24582,24655,24749,24828,24901,24984,25056,25179,25284,25433,25532,25649,25751,25901,26003,26102,26224,26319,26430,26544,26663,26756,26820,26874,26927,26979,27030,27079,27148,27200,27265,27315,27359,27426,27470,27523,27578,27636,27694,27744,27794,27872,27924,27976,28021,28082,28140,28201,28326,28444,28533,28594,28673,28742,28810,28934,29011,29087,29167,29245,29313,29419,29492,29589,29673,29813,29892,29984,30069,30167,30263,30346,30444,30549,30669,30777,30863,30958,31028,31113,31229,31361,31431,31508,31605,31699,33750,33865,34040,34087,34234,34379,34469,34609,34699,34826,34997,35129,35302,35377,35459,35531,35616,35738,35783,35872,35994,36091,36214,36335,36467,36570,36677,36744,36807,36864,36919,36988,37070,37160,37282,37352,37429,37681,37885,38013,38135,38262,38359,38478,38611,38758,38916,39017,39142,39228,39359,39469,39621,39712,39785,39846,40070,40158,40235,40294,40352,40465,40587,40683,40834,41009,41086,41146,41206,41359,41436,41532,41608,41701,41860,41952,42025,42141,42230,42366,42521,42576,42688,42757,42836,42906,42981,43029,43094,43183,43235,43309,43466,43566,43663,43726,43868,44017,44145,44259,44524,44625,44716,44808,44941,45122,45212,45404,45508,45645,45761,45845,45935,46015,46162,46311,46445,46493,46575,46708,46819,46909,46981,47073,47152,47224,47297,47358,47436,47508,47583,47672,47738,47805,47895,47965,48050,48232,48383,48471,48528,48615,48754,48815,48893,48977,49047,49137,49372,49427,49504,49592,49667,49766,49936,50028,50102,50188,50273,50341,50414,50508,50646,50727,50777,50855,50932,51010,51091,51201,51302,51417,51543,51620,51669,51721,51815,51916,52003,52111,52276,52386,52469,52589,52709,52856,52972,53071,53233,53372,53558,53741,53864,53996,54089,54186,54308,54440,54549,54674,54754,55146,55231,55317,55395", "endColumns": "175,132,75,80,105,148,185,151,121,118,131,62,60,89,99,69,142,102,81,148,76,88,98,125,101,107,99,69,71,125,87,209,180,110,169,104,168,101,140,107,145,107,139,101,98,147,90,146,95,174,85,124,77,119,100,72,93,78,72,82,71,122,104,148,98,116,101,149,101,98,121,94,110,113,118,92,63,53,52,51,50,48,68,51,64,49,43,66,43,52,54,57,57,49,49,77,51,51,44,60,57,60,124,117,88,60,78,68,67,123,76,75,79,77,67,105,72,96,83,139,78,91,84,97,95,82,97,104,119,107,85,94,69,84,115,131,69,76,96,93,2050,114,174,46,146,144,89,139,89,126,170,131,172,74,81,71,84,121,44,88,121,96,122,120,131,102,106,66,62,56,54,68,81,89,121,69,76,251,203,127,121,126,96,118,132,146,157,100,124,85,130,109,151,90,72,60,223,87,76,58,57,112,121,95,150,174,76,59,59,152,76,95,75,92,158,91,72,115,88,135,154,54,111,68,78,69,74,47,64,88,51,73,156,99,96,62,141,148,127,113,264,100,90,91,132,180,89,191,103,136,115,83,89,79,146,148,133,47,81,132,110,89,71,91,78,71,72,60,77,71,74,88,65,66,89,69,84,181,150,87,56,86,138,60,77,83,69,89,234,54,76,87,74,98,169,91,73,85,84,67,72,93,137,80,49,77,76,77,80,109,100,114,125,76,48,51,93,100,86,107,164,109,82,119,119,146,115,98,161,138,185,182,122,131,92,96,121,131,108,124,79,391,84,85,77,74", "endOffsets": "18344,18477,18553,18634,18740,18889,19075,19227,19349,19468,19600,19663,19724,19814,19914,19984,20127,20230,20312,20461,20538,20627,20726,20852,20954,21062,21162,21232,21304,21430,21518,21728,21909,22020,22190,22295,22464,22566,22707,22815,22961,23069,23209,23311,23410,23558,23649,23796,23892,24067,24153,24278,24356,24476,24577,24650,24744,24823,24896,24979,25051,25174,25279,25428,25527,25644,25746,25896,25998,26097,26219,26314,26425,26539,26658,26751,26815,26869,26922,26974,27025,27074,27143,27195,27260,27310,27354,27421,27465,27518,27573,27631,27689,27739,27789,27867,27919,27971,28016,28077,28135,28196,28321,28439,28528,28589,28668,28737,28805,28929,29006,29082,29162,29240,29308,29414,29487,29584,29668,29808,29887,29979,30064,30162,30258,30341,30439,30544,30664,30772,30858,30953,31023,31108,31224,31356,31426,31503,31600,31694,33745,33860,34035,34082,34229,34374,34464,34604,34694,34821,34992,35124,35297,35372,35454,35526,35611,35733,35778,35867,35989,36086,36209,36330,36462,36565,36672,36739,36802,36859,36914,36983,37065,37155,37277,37347,37424,37676,37880,38008,38130,38257,38354,38473,38606,38753,38911,39012,39137,39223,39354,39464,39616,39707,39780,39841,40065,40153,40230,40289,40347,40460,40582,40678,40829,41004,41081,41141,41201,41354,41431,41527,41603,41696,41855,41947,42020,42136,42225,42361,42516,42571,42683,42752,42831,42901,42976,43024,43089,43178,43230,43304,43461,43561,43658,43721,43863,44012,44140,44254,44519,44620,44711,44803,44936,45117,45207,45399,45503,45640,45756,45840,45930,46010,46157,46306,46440,46488,46570,46703,46814,46904,46976,47068,47147,47219,47292,47353,47431,47503,47578,47667,47733,47800,47890,47960,48045,48227,48378,48466,48523,48610,48749,48810,48888,48972,49042,49132,49367,49422,49499,49587,49662,49761,49931,50023,50097,50183,50268,50336,50409,50503,50641,50722,50772,50850,50927,51005,51086,51196,51297,51412,51538,51615,51664,51716,51810,51911,51998,52106,52271,52381,52464,52584,52704,52851,52967,53066,53228,53367,53553,53736,53859,53991,54084,54181,54303,54435,54544,54669,54749,55141,55226,55312,55390,55465"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,90", "endOffsets": "140,231"}, "to": {"startLines": "544,545", "startColumns": "4,4", "startOffsets": "55739,55829", "endColumns": "89,90", "endOffsets": "55824,55915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,281,377,475,560,637,724,816,898,980,1066,1138,1215,1295,1373,1451,1521", "endColumns": "94,80,95,97,84,76,86,91,81,81,85,71,76,79,77,77,69,120", "endOffsets": "195,276,372,470,555,632,719,811,893,975,1061,1133,1210,1290,1368,1446,1516,1637"}, "to": {"startLines": "50,51,54,55,56,64,65,181,182,184,185,189,191,192,193,541,542,543", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4651,4746,5073,5169,5267,5970,6047,17021,17113,17272,17354,17687,17837,17914,17994,55470,55548,55618", "endColumns": "94,80,95,97,84,76,86,91,81,81,85,71,76,79,77,77,69,120", "endOffsets": "4741,4822,5164,5262,5347,6042,6129,17108,17190,17349,17435,17754,17909,17989,18067,55543,55613,55734"}}]}]}