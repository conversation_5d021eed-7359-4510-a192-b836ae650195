package com.thedasagroup.suminative.ui.user_profile;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000.\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0005\u001aF\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a\u001e\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\b2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\nH\u0007\"\u0014\u0010\u000e\u001a\u00020\u000fX\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011\"\u0014\u0010\u0012\u001a\u00020\u000fX\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0011\u00a8\u0006\u0014"}, d2 = {"SelectUserProfileScreen", "", "viewModel", "Lcom/thedasagroup/suminative/ui/user_profile/SelectUserProfileViewModel;", "onLoginSuccess", "Lkotlin/Function1;", "Lcom/thedasagroup/suminative/data/model/response/login/LoginResponse;", "onLoginError", "", "onBackClick", "Lkotlin/Function0;", "KeypadButton", "text", "onClick", "ButtonColorDarkGreen", "", "getButtonColorDarkGreen", "()J", "ButtonColorBGGreen", "getButtonColorBGGreen", "app_stagingGeneralDebug"})
public final class SelectUserProfileScreenKt {
    private static final long ButtonColorDarkGreen = 4278335267L;
    private static final long ButtonColorBGGreen = 4281236786L;
    
    @androidx.compose.runtime.Composable()
    public static final void SelectUserProfileScreen(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.user_profile.SelectUserProfileViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.login.LoginResponse, kotlin.Unit> onLoginSuccess, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onLoginError, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void KeypadButton(@org.jetbrains.annotations.NotNull()
    java.lang.String text, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    public static final long getButtonColorDarkGreen() {
        return 0L;
    }
    
    public static final long getButtonColorBGGreen() {
        return 0L;
    }
}