package com.thedasagroup.suminative.ui.service;

@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0012\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0012H\u0016J\u0012\u0010\u0013\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0012H\u0016J\b\u0010\u0014\u001a\u00020\u0015H\u0002J\"\u0010\u0016\u001a\u00020\u00172\b\u0010\u0018\u001a\u0004\u0018\u00010\u00192\u0006\u0010\u001a\u001a\u00020\u00172\u0006\u0010\u001b\u001a\u00020\u0017H\u0016J\u0010\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u000bH\u0002R\u001e\u0010\u0004\u001a\u00020\u00058\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0006\u0010\u0007\"\u0004\b\b\u0010\tR\u0010\u0010\n\u001a\u0004\u0018\u00010\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000e\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001f"}, d2 = {"Lcom/thedasagroup/suminative/ui/service/MySocketJobService;", "Landroid/app/job/JobService;", "<init>", "()V", "prefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "getPrefs", "()Lcom/thedasagroup/suminative/data/prefs/Prefs;", "setPrefs", "(Lcom/thedasagroup/suminative/data/prefs/Prefs;)V", "TAG", "", "webSocketClient", "Lcom/thedasagroup/suminative/ui/utils/ChatWebSocketClient;", "webSocketClient2", "onStartJob", "", "p0", "Landroid/app/job/JobParameters;", "onStopJob", "createNotification", "Landroid/app/Notification;", "onStartCommand", "", "intent", "Landroid/content/Intent;", "flags", "startId", "sendMessage", "", "message", "app_stagingGeneralDebug"})
public final class MySocketJobService extends android.app.job.JobService {
    @javax.inject.Inject()
    public com.thedasagroup.suminative.data.prefs.Prefs prefs;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String TAG = null;
    @org.jetbrains.annotations.Nullable()
    private com.thedasagroup.suminative.ui.utils.ChatWebSocketClient webSocketClient;
    @org.jetbrains.annotations.Nullable()
    private com.thedasagroup.suminative.ui.utils.ChatWebSocketClient webSocketClient2;
    
    public MySocketJobService() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.prefs.Prefs getPrefs() {
        return null;
    }
    
    public final void setPrefs(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs p0) {
    }
    
    @java.lang.Override()
    public boolean onStartJob(@org.jetbrains.annotations.Nullable()
    android.app.job.JobParameters p0) {
        return false;
    }
    
    @java.lang.Override()
    public boolean onStopJob(@org.jetbrains.annotations.Nullable()
    android.app.job.JobParameters p0) {
        return false;
    }
    
    private final android.app.Notification createNotification() {
        return null;
    }
    
    @java.lang.Override()
    public int onStartCommand(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent, int flags, int startId) {
        return 0;
    }
    
    private final void sendMessage(java.lang.String message) {
    }
}