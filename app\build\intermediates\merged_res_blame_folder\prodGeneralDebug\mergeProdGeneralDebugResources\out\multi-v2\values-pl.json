{"logs": [{"outputFile": "com.thedasagroup.suminative.app-mergeProdGeneralDebugResources-106:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a5d9350484b77cf3f3146c53b5d0ada1\\transformed\\foundation-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,87", "endOffsets": "138,226"}, "to": {"startLines": "544,545", "startColumns": "4,4", "startOffsets": "55803,55891", "endColumns": "87,87", "endOffsets": "55886,55974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56f1fdbf647eaff96ebf00a56cd72a54\\transformed\\material-1.12.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,386,461,536,615,719,814,899,1016,1098,1163,1227,1308,1372,1433,1544,1608,1676,1730,1799,1861,1915,2026,2087,2149,2203,2275,2404,2493,2572,2667,2752,2834,2983,3065,3148,3285,3372,3449,3503,3554,3620,3691,3767,3838,3921,3998,4076,4154,4230,4338,4428,4501,4596,4693,4765,4839,4939,4991,5076,5142,5230,5320,5382,5446,5509,5580,5687,5799,5898,6005,6063,6118,6194,6278,6355", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,74,74,78,103,94,84,116,81,64,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,78,94,84,81,148,81,82,136,86,76,53,50,65,70,75,70,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,57,54,75,83,76,77", "endOffsets": "381,456,531,610,714,809,894,1011,1093,1158,1222,1303,1367,1428,1539,1603,1671,1725,1794,1856,1910,2021,2082,2144,2198,2270,2399,2488,2567,2662,2747,2829,2978,3060,3143,3280,3367,3444,3498,3549,3615,3686,3762,3833,3916,3993,4071,4149,4225,4333,4423,4496,4591,4688,4760,4834,4934,4986,5071,5137,5225,5315,5377,5441,5504,5575,5682,5794,5893,6000,6058,6113,6189,6273,6350,6428"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,57,58,59,63,66,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,183,187,188,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3148,3223,3298,3377,3481,4313,4398,4515,5308,5373,5437,5836,6070,12278,12389,12453,12521,12575,12644,12706,12760,12871,12932,12994,13048,13120,13249,13338,13417,13512,13597,13679,13828,13910,13993,14130,14217,14294,14348,14399,14465,14536,14612,14683,14766,14843,14921,14999,15075,15183,15273,15346,15441,15538,15610,15684,15784,15836,15921,15987,16075,16165,16227,16291,16354,16425,16532,16644,16743,16850,16908,17136,17465,17549,17698", "endLines": "7,35,36,37,38,39,47,48,49,57,58,59,63,66,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,183,187,188,190", "endColumns": "12,74,74,78,103,94,84,116,81,64,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,78,94,84,81,148,81,82,136,86,76,53,50,65,70,75,70,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,57,54,75,83,76,77", "endOffsets": "431,3218,3293,3372,3476,3571,4393,4510,4592,5368,5432,5513,5895,6126,12384,12448,12516,12570,12639,12701,12755,12866,12927,12989,13043,13115,13244,13333,13412,13507,13592,13674,13823,13905,13988,14125,14212,14289,14343,14394,14460,14531,14607,14678,14761,14838,14916,14994,15070,15178,15268,15341,15436,15533,15605,15679,15779,15831,15916,15982,16070,16160,16222,16286,16349,16420,16527,16639,16738,16845,16903,16958,17207,17544,17621,17771"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b890659f6db25116dd25448a00dbe8e9\\transformed\\ui-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,394,499,576,653,746,836,919,1002,1089,1161,1237,1315,1391,1473,1541", "endColumns": "94,84,108,104,76,76,92,89,82,82,86,71,75,77,75,81,67,119", "endOffsets": "195,280,389,494,571,648,741,831,914,997,1084,1156,1232,1310,1386,1468,1536,1656"}, "to": {"startLines": "50,51,54,55,56,64,65,181,182,184,185,189,191,192,193,541,542,543", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4597,4692,5017,5126,5231,5900,5977,16963,17053,17212,17295,17626,17776,17852,17930,55533,55615,55683", "endColumns": "94,84,108,104,76,76,92,89,82,82,86,71,75,77,75,81,67,119", "endOffsets": "4687,4772,5121,5226,5303,5972,6065,17048,17131,17290,17377,17693,17847,17925,18001,55610,55678,55798"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d352988101819e006c7e123d3310a04a\\transformed\\browser-1.4.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,254,369", "endColumns": "99,98,114,103", "endOffsets": "150,249,364,468"}, "to": {"startLines": "53,60,61,62", "startColumns": "4,4,4,4", "startOffsets": "4917,5518,5617,5732", "endColumns": "99,98,114,103", "endOffsets": "5012,5612,5727,5831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\966abd985cb3a4de813b1dc1fc749c3e\\transformed\\core-1.13.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "40,41,42,43,44,45,46,194", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3576,3673,3775,3873,3972,4086,4191,18006", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "3668,3770,3868,3967,4081,4186,4308,18102"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\208a76efc944b3a8edf00fa399129d7c\\transformed\\appcompat-1.7.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,186", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "436,551,653,761,847,954,1073,1152,1228,1319,1412,1507,1601,1702,1795,1890,1985,2076,2167,2249,2358,2458,2557,2666,2778,2889,3052,17382", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "546,648,756,842,949,1068,1147,1223,1314,1407,1502,1596,1697,1790,1885,1980,2071,2162,2244,2353,2453,2552,2661,2773,2884,3047,3143,17460"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9ab28c4c4a3aadf9599b12b43718d1cb\\transformed\\play-services-basement-18.4.0\\res\\values-pl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "4777", "endColumns": "139", "endOffsets": "4912"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bafe51bd12d3646532745d656825df70\\transformed\\material3-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,409,524,624,723,839,977,1099,1241,1325,1424,1516,1612,1729,1853,1957,2097,2233,2377,2538,2670,2791,2916,3037,3130,3230,3350,3474,3573,3677,3783,3924,4071,4182,4281,4355,4450,4546,4633,4720,4832,4912,4999,5094,5199,5290,5399,5487,5593,5694,5804,5922,6002,6105", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,86,86,111,79,86,94,104,90,108,87,105,100,109,117,79,102,96", "endOffsets": "165,282,404,519,619,718,834,972,1094,1236,1320,1419,1511,1607,1724,1848,1952,2092,2228,2372,2533,2665,2786,2911,3032,3125,3225,3345,3469,3568,3672,3778,3919,4066,4177,4276,4350,4445,4541,4628,4715,4827,4907,4994,5089,5194,5285,5394,5482,5588,5689,5799,5917,5997,6100,6197"}, "to": {"startLines": "67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6131,6246,6363,6485,6600,6700,6799,6915,7053,7175,7317,7401,7500,7592,7688,7805,7929,8033,8173,8309,8453,8614,8746,8867,8992,9113,9206,9306,9426,9550,9649,9753,9859,10000,10147,10258,10357,10431,10526,10622,10709,10796,10908,10988,11075,11170,11275,11366,11475,11563,11669,11770,11880,11998,12078,12181", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,86,86,111,79,86,94,104,90,108,87,105,100,109,117,79,102,96", "endOffsets": "6241,6358,6480,6595,6695,6794,6910,7048,7170,7312,7396,7495,7587,7683,7800,7924,8028,8168,8304,8448,8609,8741,8862,8987,9108,9201,9301,9421,9545,9644,9748,9854,9995,10142,10253,10352,10426,10521,10617,10704,10791,10903,10983,11070,11165,11270,11361,11470,11558,11664,11765,11875,11993,12073,12176,12273"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bdcff7e5329d1bfe023fc8af73cd330f\\transformed\\merchant-sdk-5.0.3\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,247,370,446,535,636,785,969,1124,1250,1365,1500,1560,1618,1705,1809,1876,2021,2124,2215,2356,2431,2528,2629,2768,2867,2979,3083,3161,3233,3370,3467,3680,3843,3952,4123,4232,4413,4535,4662,4762,4934,5034,5170,5284,5383,5523,5615,5754,5850,6033,6128,6274,6351,6464,6561,6638,6725,6802,6871,6952,7021,7142,7251,7385,7489,7604,7695,7851,7965,8064,8184,8275,8396,8510,8632,8726,8785,8837,8890,8942,8992,9041,9123,9175,9242,9291,9336,9405,9449,9503,9555,9609,9661,9713,9763,9834,9883,9939,9985,10047,10108,10163,10307,10416,10499,10560,10640,10709,10777,10891,10961,11043,11136,11214,11281,11384,11457,11546,11631,11762,11840,11940,12028,12127,12223,12302,12400,12514,12636,12743,12826,12919,12991,13065,13189,13317,13383,13460,13561,13671,15722,15837,16003,16048,16164,16324,16412,16518,16620,16763,16939,17050,17228,17302,17384,17461,17549,17658,17703,17789,17914,17990,18109,18225,18381,18486,18595,18661,18728,18788,18846,18915,18992,19084,19197,19267,19341,19648,19844,19969,20093,20223,20312,20408,20539,20688,20836,20929,21032,21120,21244,21346,21503,21603,21678,21750,21950,22039,22120,22183,22245,22359,22472,22559,22716,22893,22973,23037,23097,23270,23345,23437,23515,23610,23793,23888,23968,24076,24154,24282,24422,24487,24595,24666,24747,24808,24880,24929,24991,25092,25144,25201,25373,25483,25587,25651,25820,25958,26073,26180,26417,26506,26620,26718,26892,27112,27196,27403,27540,27701,27819,27902,27995,28076,28210,28368,28499,28546,28633,28764,28887,28969,29054,29164,29241,29313,29387,29448,29525,29595,29671,29758,29824,29892,29982,30049,30137,30339,30471,30562,30620,30706,30858,30924,31002,31088,31163,31240,31488,31543,31625,31706,31778,31877,32052,32140,32216,32303,32383,32451,32529,32610,32754,32833,32887,32968,33045,33117,33192,33302,33403,33512,33631,33712,33760,33810,33904,34006,34091,34197,34367,34462,34544,34659,34791,34923,35036,35133,35281,35415,35609,35777,35897,36036,36118,36199,36305,36434,36539,36670,36748,37143,37237,37323,37402", "endColumns": "191,122,75,88,100,148,183,154,125,114,134,59,57,86,103,66,144,102,90,140,74,96,100,138,98,111,103,77,71,136,96,212,162,108,170,108,180,121,126,99,171,99,135,113,98,139,91,138,95,182,94,145,76,112,96,76,86,76,68,80,68,120,108,133,103,114,90,155,113,98,119,90,120,113,121,93,58,51,52,51,49,48,81,51,66,48,44,68,43,53,51,53,51,51,49,70,48,55,45,61,60,54,143,108,82,60,79,68,67,113,69,81,92,77,66,102,72,88,84,130,77,99,87,98,95,78,97,113,121,106,82,92,71,73,123,127,65,76,100,109,2050,114,165,44,115,159,87,105,101,142,175,110,177,73,81,76,87,108,44,85,124,75,118,115,155,104,108,65,66,59,57,68,76,91,112,69,73,306,195,124,123,129,88,95,130,148,147,92,102,87,123,101,156,99,74,71,199,88,80,62,61,113,112,86,156,176,79,63,59,172,74,91,77,94,182,94,79,107,77,127,139,64,107,70,80,60,71,48,61,100,51,56,171,109,103,63,168,137,114,106,236,88,113,97,173,219,83,206,136,160,117,82,92,80,133,157,130,46,86,130,122,81,84,109,76,71,73,60,76,69,75,86,65,67,89,66,87,201,131,90,57,85,151,65,77,85,74,76,247,54,81,80,71,98,174,87,75,86,79,67,77,80,143,78,53,80,76,71,74,109,100,108,118,80,47,49,93,101,84,105,169,94,81,114,131,131,112,96,147,133,193,167,119,138,81,80,105,128,104,130,77,394,93,85,78,78", "endOffsets": "242,365,441,530,631,780,964,1119,1245,1360,1495,1555,1613,1700,1804,1871,2016,2119,2210,2351,2426,2523,2624,2763,2862,2974,3078,3156,3228,3365,3462,3675,3838,3947,4118,4227,4408,4530,4657,4757,4929,5029,5165,5279,5378,5518,5610,5749,5845,6028,6123,6269,6346,6459,6556,6633,6720,6797,6866,6947,7016,7137,7246,7380,7484,7599,7690,7846,7960,8059,8179,8270,8391,8505,8627,8721,8780,8832,8885,8937,8987,9036,9118,9170,9237,9286,9331,9400,9444,9498,9550,9604,9656,9708,9758,9829,9878,9934,9980,10042,10103,10158,10302,10411,10494,10555,10635,10704,10772,10886,10956,11038,11131,11209,11276,11379,11452,11541,11626,11757,11835,11935,12023,12122,12218,12297,12395,12509,12631,12738,12821,12914,12986,13060,13184,13312,13378,13455,13556,13666,15717,15832,15998,16043,16159,16319,16407,16513,16615,16758,16934,17045,17223,17297,17379,17456,17544,17653,17698,17784,17909,17985,18104,18220,18376,18481,18590,18656,18723,18783,18841,18910,18987,19079,19192,19262,19336,19643,19839,19964,20088,20218,20307,20403,20534,20683,20831,20924,21027,21115,21239,21341,21498,21598,21673,21745,21945,22034,22115,22178,22240,22354,22467,22554,22711,22888,22968,23032,23092,23265,23340,23432,23510,23605,23788,23883,23963,24071,24149,24277,24417,24482,24590,24661,24742,24803,24875,24924,24986,25087,25139,25196,25368,25478,25582,25646,25815,25953,26068,26175,26412,26501,26615,26713,26887,27107,27191,27398,27535,27696,27814,27897,27990,28071,28205,28363,28494,28541,28628,28759,28882,28964,29049,29159,29236,29308,29382,29443,29520,29590,29666,29753,29819,29887,29977,30044,30132,30334,30466,30557,30615,30701,30853,30919,30997,31083,31158,31235,31483,31538,31620,31701,31773,31872,32047,32135,32211,32298,32378,32446,32524,32605,32749,32828,32882,32963,33040,33112,33187,33297,33398,33507,33626,33707,33755,33805,33899,34001,34086,34192,34362,34457,34539,34654,34786,34918,35031,35128,35276,35410,35604,35772,35892,36031,36113,36194,36300,36429,36534,36665,36743,37138,37232,37318,37397,37476"}, "to": {"startLines": "195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18107,18299,18422,18498,18587,18688,18837,19021,19176,19302,19417,19552,19612,19670,19757,19861,19928,20073,20176,20267,20408,20483,20580,20681,20820,20919,21031,21135,21213,21285,21422,21519,21732,21895,22004,22175,22284,22465,22587,22714,22814,22986,23086,23222,23336,23435,23575,23667,23806,23902,24085,24180,24326,24403,24516,24613,24690,24777,24854,24923,25004,25073,25194,25303,25437,25541,25656,25747,25903,26017,26116,26236,26327,26448,26562,26684,26778,26837,26889,26942,26994,27044,27093,27175,27227,27294,27343,27388,27457,27501,27555,27607,27661,27713,27765,27815,27886,27935,27991,28037,28099,28160,28215,28359,28468,28551,28612,28692,28761,28829,28943,29013,29095,29188,29266,29333,29436,29509,29598,29683,29814,29892,29992,30080,30179,30275,30354,30452,30566,30688,30795,30878,30971,31043,31117,31241,31369,31435,31512,31613,31723,33774,33889,34055,34100,34216,34376,34464,34570,34672,34815,34991,35102,35280,35354,35436,35513,35601,35710,35755,35841,35966,36042,36161,36277,36433,36538,36647,36713,36780,36840,36898,36967,37044,37136,37249,37319,37393,37700,37896,38021,38145,38275,38364,38460,38591,38740,38888,38981,39084,39172,39296,39398,39555,39655,39730,39802,40002,40091,40172,40235,40297,40411,40524,40611,40768,40945,41025,41089,41149,41322,41397,41489,41567,41662,41845,41940,42020,42128,42206,42334,42474,42539,42647,42718,42799,42860,42932,42981,43043,43144,43196,43253,43425,43535,43639,43703,43872,44010,44125,44232,44469,44558,44672,44770,44944,45164,45248,45455,45592,45753,45871,45954,46047,46128,46262,46420,46551,46598,46685,46816,46939,47021,47106,47216,47293,47365,47439,47500,47577,47647,47723,47810,47876,47944,48034,48101,48189,48391,48523,48614,48672,48758,48910,48976,49054,49140,49215,49292,49540,49595,49677,49758,49830,49929,50104,50192,50268,50355,50435,50503,50581,50662,50806,50885,50939,51020,51097,51169,51244,51354,51455,51564,51683,51764,51812,51862,51956,52058,52143,52249,52419,52514,52596,52711,52843,52975,53088,53185,53333,53467,53661,53829,53949,54088,54170,54251,54357,54486,54591,54722,54800,55195,55289,55375,55454", "endColumns": "191,122,75,88,100,148,183,154,125,114,134,59,57,86,103,66,144,102,90,140,74,96,100,138,98,111,103,77,71,136,96,212,162,108,170,108,180,121,126,99,171,99,135,113,98,139,91,138,95,182,94,145,76,112,96,76,86,76,68,80,68,120,108,133,103,114,90,155,113,98,119,90,120,113,121,93,58,51,52,51,49,48,81,51,66,48,44,68,43,53,51,53,51,51,49,70,48,55,45,61,60,54,143,108,82,60,79,68,67,113,69,81,92,77,66,102,72,88,84,130,77,99,87,98,95,78,97,113,121,106,82,92,71,73,123,127,65,76,100,109,2050,114,165,44,115,159,87,105,101,142,175,110,177,73,81,76,87,108,44,85,124,75,118,115,155,104,108,65,66,59,57,68,76,91,112,69,73,306,195,124,123,129,88,95,130,148,147,92,102,87,123,101,156,99,74,71,199,88,80,62,61,113,112,86,156,176,79,63,59,172,74,91,77,94,182,94,79,107,77,127,139,64,107,70,80,60,71,48,61,100,51,56,171,109,103,63,168,137,114,106,236,88,113,97,173,219,83,206,136,160,117,82,92,80,133,157,130,46,86,130,122,81,84,109,76,71,73,60,76,69,75,86,65,67,89,66,87,201,131,90,57,85,151,65,77,85,74,76,247,54,81,80,71,98,174,87,75,86,79,67,77,80,143,78,53,80,76,71,74,109,100,108,118,80,47,49,93,101,84,105,169,94,81,114,131,131,112,96,147,133,193,167,119,138,81,80,105,128,104,130,77,394,93,85,78,78", "endOffsets": "18294,18417,18493,18582,18683,18832,19016,19171,19297,19412,19547,19607,19665,19752,19856,19923,20068,20171,20262,20403,20478,20575,20676,20815,20914,21026,21130,21208,21280,21417,21514,21727,21890,21999,22170,22279,22460,22582,22709,22809,22981,23081,23217,23331,23430,23570,23662,23801,23897,24080,24175,24321,24398,24511,24608,24685,24772,24849,24918,24999,25068,25189,25298,25432,25536,25651,25742,25898,26012,26111,26231,26322,26443,26557,26679,26773,26832,26884,26937,26989,27039,27088,27170,27222,27289,27338,27383,27452,27496,27550,27602,27656,27708,27760,27810,27881,27930,27986,28032,28094,28155,28210,28354,28463,28546,28607,28687,28756,28824,28938,29008,29090,29183,29261,29328,29431,29504,29593,29678,29809,29887,29987,30075,30174,30270,30349,30447,30561,30683,30790,30873,30966,31038,31112,31236,31364,31430,31507,31608,31718,33769,33884,34050,34095,34211,34371,34459,34565,34667,34810,34986,35097,35275,35349,35431,35508,35596,35705,35750,35836,35961,36037,36156,36272,36428,36533,36642,36708,36775,36835,36893,36962,37039,37131,37244,37314,37388,37695,37891,38016,38140,38270,38359,38455,38586,38735,38883,38976,39079,39167,39291,39393,39550,39650,39725,39797,39997,40086,40167,40230,40292,40406,40519,40606,40763,40940,41020,41084,41144,41317,41392,41484,41562,41657,41840,41935,42015,42123,42201,42329,42469,42534,42642,42713,42794,42855,42927,42976,43038,43139,43191,43248,43420,43530,43634,43698,43867,44005,44120,44227,44464,44553,44667,44765,44939,45159,45243,45450,45587,45748,45866,45949,46042,46123,46257,46415,46546,46593,46680,46811,46934,47016,47101,47211,47288,47360,47434,47495,47572,47642,47718,47805,47871,47939,48029,48096,48184,48386,48518,48609,48667,48753,48905,48971,49049,49135,49210,49287,49535,49590,49672,49753,49825,49924,50099,50187,50263,50350,50430,50498,50576,50657,50801,50880,50934,51015,51092,51164,51239,51349,51450,51559,51678,51759,51807,51857,51951,52053,52138,52244,52414,52509,52591,52706,52838,52970,53083,53180,53328,53462,53656,53824,53944,54083,54165,54246,54352,54481,54586,54717,54795,55190,55284,55370,55449,55528"}}]}]}