package com.thedasagroup.suminative.ui.payment;

import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.MyGuavaRepository;
import com.thedasagroup.suminative.domain.myguava.MyGuavaCheckStatusUseCase;
import com.thedasagroup.suminative.domain.myguava.MyGuavaCreateOrderUseCase;
import com.thedasagroup.suminative.domain.myguava.MyGuavaCreateSessionUseCase;
import com.thedasagroup.suminative.domain.myguava.MyGuavaGetTerminalsUseCase;
import com.thedasagroup.suminative.domain.myguava.MyGuavaMakePaymentUseCase;
import com.thedasagroup.suminative.domain.myguava.MyGuavaMakeRefundUseCase;
import com.thedasagroup.suminative.ui.products.OrderUseCase;
import com.thedasagroup.suminative.ui.products.PlaceOnlineOrderUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class PaymentViewModel_Factory {
  private final Provider<MyGuavaCreateOrderUseCase> myGuavaCreateOrderUseCaseProvider;

  private final Provider<MyGuavaGetTerminalsUseCase> getTerminalsUseCaseProvider;

  private final Provider<MyGuavaCreateSessionUseCase> createSessionUseCaseProvider;

  private final Provider<MyGuavaMakePaymentUseCase> makePaymentUseCaseProvider;

  private final Provider<MyGuavaMakeRefundUseCase> makeRefundUseCaseProvider;

  private final Provider<MyGuavaCheckStatusUseCase> checkStatusUseCaseProvider;

  private final Provider<OrderUseCase> orderUseCaseProvider;

  private final Provider<PlaceOnlineOrderUseCase> onlineOrderUseCaseProvider;

  private final Provider<MyGuavaRepository> guavaRepositoryProvider;

  private final Provider<Prefs> prefsProvider;

  public PaymentViewModel_Factory(
      Provider<MyGuavaCreateOrderUseCase> myGuavaCreateOrderUseCaseProvider,
      Provider<MyGuavaGetTerminalsUseCase> getTerminalsUseCaseProvider,
      Provider<MyGuavaCreateSessionUseCase> createSessionUseCaseProvider,
      Provider<MyGuavaMakePaymentUseCase> makePaymentUseCaseProvider,
      Provider<MyGuavaMakeRefundUseCase> makeRefundUseCaseProvider,
      Provider<MyGuavaCheckStatusUseCase> checkStatusUseCaseProvider,
      Provider<OrderUseCase> orderUseCaseProvider,
      Provider<PlaceOnlineOrderUseCase> onlineOrderUseCaseProvider,
      Provider<MyGuavaRepository> guavaRepositoryProvider, Provider<Prefs> prefsProvider) {
    this.myGuavaCreateOrderUseCaseProvider = myGuavaCreateOrderUseCaseProvider;
    this.getTerminalsUseCaseProvider = getTerminalsUseCaseProvider;
    this.createSessionUseCaseProvider = createSessionUseCaseProvider;
    this.makePaymentUseCaseProvider = makePaymentUseCaseProvider;
    this.makeRefundUseCaseProvider = makeRefundUseCaseProvider;
    this.checkStatusUseCaseProvider = checkStatusUseCaseProvider;
    this.orderUseCaseProvider = orderUseCaseProvider;
    this.onlineOrderUseCaseProvider = onlineOrderUseCaseProvider;
    this.guavaRepositoryProvider = guavaRepositoryProvider;
    this.prefsProvider = prefsProvider;
  }

  public PaymentViewModel get(PaymentState initialState) {
    return newInstance(initialState, myGuavaCreateOrderUseCaseProvider.get(), getTerminalsUseCaseProvider.get(), createSessionUseCaseProvider.get(), makePaymentUseCaseProvider.get(), makeRefundUseCaseProvider.get(), checkStatusUseCaseProvider.get(), orderUseCaseProvider.get(), onlineOrderUseCaseProvider.get(), guavaRepositoryProvider.get(), prefsProvider.get());
  }

  public static PaymentViewModel_Factory create(
      Provider<MyGuavaCreateOrderUseCase> myGuavaCreateOrderUseCaseProvider,
      Provider<MyGuavaGetTerminalsUseCase> getTerminalsUseCaseProvider,
      Provider<MyGuavaCreateSessionUseCase> createSessionUseCaseProvider,
      Provider<MyGuavaMakePaymentUseCase> makePaymentUseCaseProvider,
      Provider<MyGuavaMakeRefundUseCase> makeRefundUseCaseProvider,
      Provider<MyGuavaCheckStatusUseCase> checkStatusUseCaseProvider,
      Provider<OrderUseCase> orderUseCaseProvider,
      Provider<PlaceOnlineOrderUseCase> onlineOrderUseCaseProvider,
      Provider<MyGuavaRepository> guavaRepositoryProvider, Provider<Prefs> prefsProvider) {
    return new PaymentViewModel_Factory(myGuavaCreateOrderUseCaseProvider, getTerminalsUseCaseProvider, createSessionUseCaseProvider, makePaymentUseCaseProvider, makeRefundUseCaseProvider, checkStatusUseCaseProvider, orderUseCaseProvider, onlineOrderUseCaseProvider, guavaRepositoryProvider, prefsProvider);
  }

  public static PaymentViewModel newInstance(PaymentState initialState,
      MyGuavaCreateOrderUseCase myGuavaCreateOrderUseCase,
      MyGuavaGetTerminalsUseCase getTerminalsUseCase,
      MyGuavaCreateSessionUseCase createSessionUseCase,
      MyGuavaMakePaymentUseCase makePaymentUseCase, MyGuavaMakeRefundUseCase makeRefundUseCase,
      MyGuavaCheckStatusUseCase checkStatusUseCase, OrderUseCase orderUseCase,
      PlaceOnlineOrderUseCase onlineOrderUseCase, MyGuavaRepository guavaRepository, Prefs prefs) {
    return new PaymentViewModel(initialState, myGuavaCreateOrderUseCase, getTerminalsUseCase, createSessionUseCase, makePaymentUseCase, makeRefundUseCase, checkStatusUseCase, orderUseCase, onlineOrderUseCase, guavaRepository, prefs);
  }
}
