package com.thedasagroup.suminative.di;

import com.instacart.truetime.time.TrueTimeImpl;
import com.thedasagroup.suminative.data.repo.MyGuavaRepository;
import com.thedasagroup.suminative.domain.myguava.MyGuavaCreateRefundOrderUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUseCaseModule_ProvidesMyGuavaRefundOrderUseCaseFactory implements Factory<MyGuavaCreateRefundOrderUseCase> {
  private final Provider<MyGuavaRepository> myGuavaRepositoryProvider;

  private final Provider<TrueTimeImpl> trueTimeImplProvider;

  public AppUseCaseModule_ProvidesMyGuavaRefundOrderUseCaseFactory(
      Provider<MyGuavaRepository> myGuavaRepositoryProvider,
      Provider<TrueTimeImpl> trueTimeImplProvider) {
    this.myGuavaRepositoryProvider = myGuavaRepositoryProvider;
    this.trueTimeImplProvider = trueTimeImplProvider;
  }

  @Override
  public MyGuavaCreateRefundOrderUseCase get() {
    return providesMyGuavaRefundOrderUseCase(myGuavaRepositoryProvider.get(), trueTimeImplProvider.get());
  }

  public static AppUseCaseModule_ProvidesMyGuavaRefundOrderUseCaseFactory create(
      Provider<MyGuavaRepository> myGuavaRepositoryProvider,
      Provider<TrueTimeImpl> trueTimeImplProvider) {
    return new AppUseCaseModule_ProvidesMyGuavaRefundOrderUseCaseFactory(myGuavaRepositoryProvider, trueTimeImplProvider);
  }

  public static MyGuavaCreateRefundOrderUseCase providesMyGuavaRefundOrderUseCase(
      MyGuavaRepository myGuavaRepository, TrueTimeImpl trueTimeImpl) {
    return Preconditions.checkNotNullFromProvides(AppUseCaseModule.INSTANCE.providesMyGuavaRefundOrderUseCase(myGuavaRepository, trueTimeImpl));
  }
}
