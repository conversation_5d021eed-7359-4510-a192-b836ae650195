package com.thedasagroup.suminative.ui.payment;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000\u001a\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a.\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0014\u0010\u0004\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u0003\u0012\u0004\u0012\u00020\u00010\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u0007\u00a8\u0006\b"}, d2 = {"SumUpPaymentScreen", "", "order", "Lcom/thedasagroup/suminative/data/model/request/order/Order;", "onFinish", "Lkotlin/Function1;", "paymentViewModel", "Lcom/thedasagroup/suminative/ui/payment/PaymentViewModel;", "app_stagingGeneralDebug"})
public final class SumUpPaymentActivityKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void SumUpPaymentScreen(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.request.order.Order order, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.request.order.Order, kotlin.Unit> onFinish, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.payment.PaymentViewModel paymentViewModel) {
    }
}